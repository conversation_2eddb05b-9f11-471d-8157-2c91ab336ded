# 测试验证方案

## 一、测试目标与范围

### 1.1 测试目标

1. **功能正确性**：确保所有功能按预期工作
2. **性能表现**：验证渲染性能和响应速度
3. **用户体验**：确保交互流畅、视觉效果良好
4. **兼容性**：确保在不同环境下正常工作
5. **稳定性**：确保长时间使用不出现问题

### 1.2 测试范围

- 思维导图渲染功能
- Markdown解析功能
- 双向同步功能
- AI集成功能（基础框架）
- 用户界面交互
- 设置配置功能

## 二、测试用例设计

### 2.1 基础渲染测试

#### 2.1.1 简单文档测试

**测试文档**：
```markdown
# 主标题

## 二级标题1
- 列表项1
- 列表项2
  - 子列表项1
  - 子列表项2

## 二级标题2
- 另一个列表项
- 包含**粗体**和*斜体*的文本

### 三级标题
普通段落文本，用于测试段落处理。

```代码块
console.log('测试代码块');
```
```

**预期结果**：
- 主标题作为根节点
- 二级标题作为主要分支
- 列表项正确嵌套
- 段落文本作为描述内容
- 代码块保持格式

#### 2.1.2 复杂结构测试

**测试文档**：
```markdown
# 复杂文档结构测试

## 第一部分：基础概念
### 1.1 定义
- 概念A
  - 子概念A1
  - 子概念A2
    - 详细说明1
    - 详细说明2
- 概念B
- 概念C

### 1.2 特点
1. 有序列表项1
2. 有序列表项2
3. 有序列表项3

## 第二部分：实际应用
### 2.1 场景一
这是一个段落描述，包含多行文本。
应该被正确处理为描述内容。

- 应用点1
- 应用点2

### 2.2 场景二
```javascript
// 代码示例
function example() {
  return "测试";
}
```

#### 2.2.1 子场景
- 详细步骤1
- 详细步骤2

## 第三部分：总结
- 要点1
- 要点2
- 要点3
```

**预期结果**：
- 深层嵌套结构正确显示
- 有序和无序列表都能正确处理
- 段落文本不创建额外节点
- 代码块保持完整格式

### 2.2 响应式布局测试

#### 2.2.1 窗口大小变化测试

**测试步骤**：
1. 打开思维导图视图
2. 加载中等复杂度文档
3. 调整Obsidian窗口大小（从最小到最大）
4. 观察思维导图的适应性

**预期结果**：
- 思维导图自动适应容器大小
- 节点布局重新计算
- 文本不被截断或重叠
- 缩放比例自动调整

#### 2.2.2 分屏模式测试

**测试步骤**：
1. 开启Obsidian分屏模式
2. 在不同面板大小下测试思维导图
3. 动态调整面板大小

**预期结果**：
- 在小面板中正常显示
- 布局算法适应窄屏
- 交互功能不受影响

### 2.3 性能测试

#### 2.3.1 大文档渲染测试

**测试文档特征**：
- 超过500行Markdown内容
- 包含6级标题嵌套
- 超过100个列表项
- 多个代码块

**测试指标**：
- 初始渲染时间 < 2秒
- 内存使用 < 50MB
- 交互响应时间 < 100ms

#### 2.3.2 实时同步性能测试

**测试步骤**：
1. 打开大文档
2. 快速编辑Markdown内容
3. 观察同步延迟和性能

**预期结果**：
- 防抖机制正常工作
- 增量更新减少重渲染
- 编辑过程中不卡顿

### 2.4 交互功能测试

#### 2.4.1 节点点击测试

**测试步骤**：
1. 点击不同类型的节点
2. 观察高亮效果
3. 检查事件回调

**预期结果**：
- 节点正确高亮
- 点击事件正确触发
- 视觉反馈明显

#### 2.4.2 缩放和平移测试

**测试步骤**：
1. 使用鼠标滚轮缩放
2. 拖拽移动视图
3. 使用工具栏按钮

**预期结果**：
- 缩放范围合理（0.1x - 3x）
- 平移操作流畅
- 居中功能正常

### 2.5 样式和主题测试

#### 2.5.1 主题适配测试

**测试步骤**：
1. 在浅色主题下测试
2. 切换到深色主题
3. 尝试不同的社区主题

**预期结果**：
- 颜色自动适配主题
- 文本对比度足够
- 视觉效果协调

#### 2.5.2 节点样式测试

**测试内容**：
- 标题节点样式区分
- 列表节点样式
- AI生成节点特殊标识
- 代码块节点样式

**预期结果**：
- 不同类型节点有明显区别
- AI节点有特殊标识
- 样式美观且功能性强

## 三、自动化测试

### 3.1 单元测试

**测试文件**：`tests/unit/`

```typescript
// MarkdownParser.test.ts
describe('MarkdownParser', () => {
  test('解析简单标题', () => {
    const parser = new MarkdownParser(new Logger(false));
    const result = parser.parse('# 测试标题');
    expect(result).toHaveLength(1);
    expect(result[0].content).toBe('测试标题');
    expect(result[0].type).toBe('heading');
    expect(result[0].level).toBe(1);
  });

  test('解析嵌套列表', () => {
    const markdown = `
- 项目1
  - 子项目1
  - 子项目2
- 项目2
    `;
    const parser = new MarkdownParser(new Logger(false));
    const result = parser.parse(markdown);
    expect(result[0].children).toHaveLength(2);
  });
});

// MindMapRenderer.test.ts
describe('MindMapRenderer', () => {
  test('容器初始化', () => {
    const container = document.createElement('div');
    const renderer = new MindMapRenderer(container, new Logger(false));
    expect(container.querySelector('svg')).toBeTruthy();
  });

  test('节点渲染', () => {
    const container = document.createElement('div');
    const renderer = new MindMapRenderer(container, new Logger(false));
    renderer.render('# 测试\n- 项目1\n- 项目2');
    expect(container.querySelectorAll('.node')).toHaveLength(3);
  });
});
```

### 3.2 集成测试

**测试文件**：`tests/integration/`

```typescript
// mindmap-sync.test.ts
describe('思维导图同步', () => {
  test('Markdown到思维导图同步', async () => {
    // 模拟Obsidian环境
    const mockApp = createMockApp();
    const plugin = new MindMapSyncPlugin(mockApp, manifest);
    
    // 测试同步功能
    await plugin.onload();
    // ... 测试逻辑
  });
});
```

### 3.3 端到端测试

**测试工具**：Playwright或Cypress

```typescript
// e2e/mindmap.spec.ts
test('完整工作流程', async ({ page }) => {
  // 打开Obsidian
  await page.goto('app://obsidian.md');
  
  // 创建测试文档
  await page.click('[data-testid="new-note"]');
  await page.fill('.cm-editor', '# 测试\n- 项目1\n- 项目2');
  
  // 打开思维导图
  await page.click('[data-testid="mindmap-ribbon"]');
  
  // 验证渲染结果
  await expect(page.locator('.mindmap-container svg')).toBeVisible();
  await expect(page.locator('.node')).toHaveCount(3);
});
```

## 四、性能基准测试

### 4.1 渲染性能基准

**测试场景**：
- 小文档（<50行）：< 100ms
- 中文档（50-200行）：< 500ms
- 大文档（200-500行）：< 1000ms
- 超大文档（>500行）：< 2000ms

### 4.2 内存使用基准

**测试指标**：
- 空闲状态：< 10MB
- 小文档：< 20MB
- 中文档：< 35MB
- 大文档：< 50MB

### 4.3 交互响应基准

**测试指标**：
- 节点点击响应：< 50ms
- 缩放操作响应：< 100ms
- 同步延迟：< 300ms

## 五、测试执行计划

### 5.1 测试阶段

1. **开发阶段测试**（每次代码提交）
   - 单元测试自动执行
   - 基础功能手动测试

2. **集成测试**（每个功能完成后）
   - 集成测试执行
   - 性能基准测试

3. **发布前测试**（版本发布前）
   - 完整测试套件执行
   - 用户验收测试
   - 兼容性测试

### 5.2 测试环境

**开发环境**：
- Windows 10/11
- macOS 12+
- Linux Ubuntu 20.04+

**Obsidian版本**：
- 当前稳定版
- 最新测试版

### 5.3 测试报告

每次测试完成后生成测试报告，包含：
- 测试用例执行结果
- 性能指标对比
- 发现的问题和修复建议
- 回归测试结果

## 六、问题跟踪与修复

### 6.1 问题分类

- **P0 - 阻塞性问题**：影响基础功能
- **P1 - 严重问题**：影响用户体验
- **P2 - 一般问题**：功能缺陷
- **P3 - 优化建议**：性能或体验优化

### 6.2 修复流程

1. 问题记录和分类
2. 根因分析
3. 修复方案设计
4. 代码修复
5. 测试验证
6. 回归测试

这个测试方案确保了插件的质量和稳定性，为用户提供可靠的思维导图体验。
