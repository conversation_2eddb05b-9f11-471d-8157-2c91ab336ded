# Obsidian 思维导图插件 - 项目概览

## 📋 项目简介

这是一个为 Obsidian 开发的思维导图插件，能够将 Markdown 文档自动转换为交互式思维导图，支持双向同步、实时预览和丰富的交互功能。

### 🎯 核心功能

- **智能解析**：自动解析 Markdown 标题结构生成思维导图
- **双向同步**：Markdown 文件与思维导图实时同步
- **交互编辑**：支持节点编辑、添加、删除等操作
- **快捷切换**：`Ctrl+M` 快速在 Markdown 和思维导图视图间切换
- **响应式设计**：自适应不同窗口大小，支持缩放和拖拽

### 🛠️ 技术栈

- **前端框架**：TypeScript + Obsidian API
- **图形渲染**：D3.js + Markmap
- **构建工具**：esbuild + npm
- **样式系统**：CSS3 + CSS Variables
- **开发工具**：TypeScript + ESLint

## 📁 项目结构

```
obsidian-sample-plugin/
├── 📄 main.ts                 # 主插件文件
├── 🎨 styles.css             # 样式文件
├── 📋 manifest.json          # 插件清单
├── ⚙️ package.json           # 项目配置
├── 🔧 tsconfig.json          # TypeScript配置
├── 🏗️ esbuild.config.mjs     # 构建配置
├── 📚 src/                   # 模块化源码目录
│   ├── types/               # 类型定义
│   ├── core/                # 核心功能
│   ├── views/               # 视图组件
│   └── utils/               # 工具函数
└── 📖 docs/                 # 文档目录
```

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 使用方法
1. 打开任意 Markdown 文件
2. 按 `Ctrl+M` 或使用命令面板
3. 选择"切换思维导图/Markdown视图"
4. 在右侧查看生成的思维导图

## 🔧 核心技术方案

### 1. Markdown 解析引擎
- 使用正则表达式解析标题层级
- 支持 `#` 标题和 `-` 列表项
- 智能处理嵌套结构

### 2. 思维导图渲染
- 基于 D3.js 的 SVG 渲染
- Markmap 库提供布局算法
- 响应式设计适配不同屏幕

### 3. 双向数据同步
- 文件监听器实时检测变化
- 增量更新避免全量重渲染
- 防抖机制优化性能

### 4. 交互系统
- 事件委托处理节点交互
- 键盘快捷键支持
- 拖拽和缩放功能

## 📈 性能优化

- **懒加载**：按需渲染大型思维导图
- **虚拟化**：处理超大节点数量
- **缓存机制**：避免重复解析
- **防抖优化**：减少频繁更新

## 🔮 未来规划

- [ ] 支持更多 Markdown 语法
- [ ] 添加主题自定义功能
- [ ] 实现协作编辑
- [ ] 移动端适配
- [ ] 导出功能增强
