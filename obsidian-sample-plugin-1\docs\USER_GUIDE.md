# 用户使用指南

## 🚀 快速开始

### 安装插件

1. **开发模式安装**：
   - 将插件文件夹复制到 `.obsidian/plugins/` 目录
   - 在 Obsidian 设置中启用插件

2. **构建插件**：
   ```bash
   npm install
   npm run build
   ```

3. **重新加载**：
   - 按 `Ctrl+Shift+P` 打开命令面板
   - 搜索并执行 "Reload app without saving"

### 基本使用

1. **创建思维导图**：
   - 打开任意 Markdown 文件
   - 按 `Ctrl+M` 快捷键
   - 或使用命令面板搜索"切换思维导图/Markdown视图"

2. **查看思维导图**：
   - 思维导图会在右侧窗格中显示
   - 自动解析 Markdown 的标题结构
   - 支持实时同步更新

## 📝 支持的 Markdown 语法

### 标题结构
```markdown
# 一级标题 (根节点)
## 二级标题 (子节点)
### 三级标题 (孙节点)
#### 四级标题
##### 五级标题
###### 六级标题
```

### 列表结构
```markdown
# 主题
- 列表项 1
- 列表项 2
  - 嵌套列表项
* 星号列表项
+ 加号列表项
```

### 混合结构
```markdown
# 项目计划
## 第一阶段
- 需求分析
- 技术选型
## 第二阶段
- 开发实现
- 测试验证
### 详细任务
- 单元测试
- 集成测试
```

## 🎮 交互功能

### 基本操作

| 操作 | 方法 | 说明 |
|------|------|------|
| 切换视图 | `Ctrl+M` | 在 Markdown 和思维导图间切换 |
| 选择节点 | 单击节点 | 高亮显示选中的节点 |
| 编辑节点 | 双击节点 | 弹出编辑框修改节点内容 |
| 展开/折叠 | 单击折叠按钮 | 展开或折叠子节点 |
| 缩放视图 | 鼠标滚轮 | 放大或缩小思维导图 |
| 拖拽视图 | 鼠标拖拽 | 移动思维导图位置 |

### 键盘快捷键

| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl+M` | 切换视图 | 主要功能快捷键 |
| `Ctrl+Enter` | 添加子节点 | 在选中节点下添加子节点 |
| `Enter` | 添加同级节点 | 在选中节点后添加同级节点 |
| `Delete` | 删除节点 | 删除选中的节点 |
| `F2` | 编辑节点 | 编辑选中节点的内容 |
| `Escape` | 取消编辑 | 取消当前的编辑操作 |

### 节点编辑

1. **编辑现有节点**：
   - 双击节点或按 `F2`
   - 在弹出的输入框中修改内容
   - 按 `Enter` 确认或 `Escape` 取消

2. **添加新节点**：
   - 选中父节点
   - 按 `Ctrl+Enter` 添加子节点
   - 按 `Enter` 添加同级节点

3. **删除节点**：
   - 选中要删除的节点
   - 按 `Delete` 键
   - 注意：根节点无法删除

## 🔄 双向同步功能

### 自动同步
- **Markdown → 思维导图**：修改 Markdown 文件时，思维导图自动更新
- **思维导图 → Markdown**：编辑思维导图节点时，可选择同步回 Markdown 文件

### 同步规则
1. **标题映射**：
   - 一级标题 → 根节点
   - 二级标题 → 一级子节点
   - 以此类推

2. **列表映射**：
   - 无序列表项 → 子节点
   - 嵌套列表 → 多级子节点

3. **内容保持**：
   - 保持原有的 Markdown 格式
   - 保留非标题内容（段落、代码块等）

## 🎨 视觉定制

### 主题适配
- 自动适配 Obsidian 的明暗主题
- 使用 Obsidian 的颜色变量
- 支持自定义 CSS 样式

### 样式调整
可以通过修改 `styles.css` 文件来自定义样式：

```css
/* 修改节点颜色 */
.markmap-node {
    fill: #your-color;
}

/* 修改连接线样式 */
.markmap-link {
    stroke: #your-color;
    stroke-width: 2px;
}

/* 修改选中状态 */
.selected-node {
    stroke: #highlight-color;
    stroke-width: 3px;
}
```

## 🔧 高级功能

### 导出功能
1. **导出为 Markdown**：
   - 使用命令面板搜索"导出思维导图到Markdown"
   - 生成新的 Markdown 文件

2. **导出为图片**：
   - 右键思维导图区域
   - 选择"复制为图片"或"保存为图片"

### 批量操作
1. **批量展开/折叠**：
   - `Ctrl+Shift+E` 展开所有节点
   - `Ctrl+Shift+C` 折叠所有节点

2. **搜索节点**：
   - `Ctrl+F` 在思维导图中搜索
   - 高亮匹配的节点

## 🐛 常见问题

### 问题排查

1. **思维导图不显示**：
   - 检查 Markdown 文件是否有标题结构
   - 确认插件已正确安装和启用
   - 查看浏览器控制台是否有错误信息

2. **同步不工作**：
   - 确认文件监听器正常工作
   - 检查文件权限
   - 重新打开思维导图视图

3. **性能问题**：
   - 大型思维导图可能渲染较慢
   - 尝试折叠部分节点
   - 考虑拆分为多个文件

### 调试模式
1. 打开浏览器开发者工具 (`F12`)
2. 查看 Console 面板的日志信息
3. 检查 Network 面板的资源加载
4. 使用 Elements 面板检查 DOM 结构

## 💡 使用技巧

### 最佳实践

1. **文件组织**：
   - 使用清晰的标题层级
   - 避免过深的嵌套（建议不超过6级）
   - 保持节点内容简洁

2. **性能优化**：
   - 大型思维导图分段展示
   - 定期清理不需要的节点
   - 使用折叠功能管理复杂结构

3. **协作工作**：
   - 使用版本控制管理 Markdown 文件
   - 定期同步思维导图更改
   - 建立团队的标题规范

### 工作流建议

1. **笔记整理**：
   - 先用 Markdown 记录想法
   - 用思维导图整理结构
   - 在两种视图间切换完善内容

2. **项目规划**：
   - 用标题定义项目阶段
   - 用列表细化具体任务
   - 用思维导图可视化进度

3. **学习总结**：
   - 用标题组织知识点
   - 用思维导图梳理关系
   - 用双向同步保持一致性

## 🔮 更新和维护

### 版本更新
- 关注插件更新通知
- 备份重要的思维导图数据
- 测试新功能的兼容性

### 数据备份
- 定期备份 `.obsidian/plugins/` 目录
- 导出重要的思维导图为 Markdown
- 使用版本控制管理配置文件
