/**
 * Vis.js思维导图渲染器
 * 基于Vis.js Network实现高性能思维导图渲染和交互
 */

import { Network, Node, Edge, Options } from 'vis-network';
import { MindMapAPI, NodeData } from '../types';
import { Logger } from '../utils';

interface VisNode extends Node {
  id: string;
  label: string;
  level: number;
  type: string;
  group: string;
  originalData: NodeData;
}

interface VisEdge extends Edge {
  id?: string;
  from: string;
  to: string;
}

// 增强的节点数据接口
interface EnhancedNodeData {
  id: string;
  content: string;
  rawContent: string;        // 原始内容（含样式标记）
  level: number;             // 精确层级
  type: 'heading' | 'list' | 'code' | 'quote' | 'text';
  headingLevel?: number;     // H1-H6层级
  listLevel?: number;        // 列表嵌套层级
  indentLevel?: number;      // 缩进层级
  styles: StyleInfo;         // 样式信息
  children: EnhancedNodeData[];
  parent?: EnhancedNodeData;
  position: { line: number; ch: number };
}

// 样式信息接口
interface StyleInfo {
  bold: boolean;
  italic: boolean;
  code: boolean;
  link?: string;
  emoji: string[];
  strikethrough: boolean;
  highlight: boolean;
}

// 行结构分析接口
interface LineStructure {
  level: number;
  type: 'heading' | 'list' | 'code' | 'quote' | 'text';
  headingLevel: number;
  listLevel: number;
  indentLevel: number;
  content: string;
  rawContent: string;
  styles: StyleInfo;
}

export class VisJsRenderer implements MindMapAPI {
  private container: HTMLElement;
  private network: Network | null = null;
  private nodes: any;
  private edges: any;
  private logger: Logger;
  private onNodeChangeCallback?: (nodeId: string, changes: any) => void;
  private nodeMap: Map<string, NodeData> = new Map();
  private enhancedNodeMap: Map<string, EnhancedNodeData> = new Map();

  constructor(container: HTMLElement, logger: Logger) {
    this.container = container;
    this.logger = logger;

    // 动态导入DataSet
    const visData = require('vis-data');
    this.nodes = new visData.DataSet();
    this.edges = new visData.DataSet();

    this.initializeNetwork();
  }

  private initializeNetwork(): void {
    // 清空容器
    this.container.innerHTML = '';
    
    // 配置网络选项
    const options: Options = {
      layout: {
        hierarchical: {
          enabled: true,
          direction: 'LR',              // 从左到右布局
          sortMethod: 'defined',        // 使用预定义顺序，适配精准层级
          shakeTowards: 'roots',        // 向根节点收缩
          levelSeparation: 150,         // 层级间距优化
          nodeSpacing: 100,             // 节点间距优化
          treeSpacing: 200,             // 树间距
          blockShifting: true,          // 启用块移动优化
          edgeMinimization: true,       // 启用边最小化
          parentCentralization: true    // 父节点居中
        }
      },
      physics: {
        enabled: false // 禁用物理引擎，使用层次布局
      },
      nodes: {
        shape: 'box',
        margin: { top: 10, right: 10, bottom: 10, left: 10 },
        font: {
          size: 14,
          face: 'var(--font-text)',
          color: 'var(--text-normal)'
        },
        borderWidth: 2,
        shadow: {
          enabled: true,
          color: 'rgba(0,0,0,0.1)',
          size: 5,
          x: 2,
          y: 2
        },
        chosen: {
          node: (values: any, _id: string, selected: boolean, hovering: boolean) => {
            if (selected || hovering) {
              values.borderWidth = 3;
              values.shadow.size = 8;
            }
          },
          label: false
        }
      },
      edges: {
        arrows: {
          to: { enabled: false }
        },
        color: {
          color: 'var(--text-muted)',
          opacity: 0.8                     // 提高透明度
        },
        width: 2,
        smooth: {
          enabled: true,
          type: 'cubicBezier',
          roundness: 0.4                   // 调整曲线
        },
        length: 150                        // 设置理想长度
      },
      groups: {
        heading1: {
          color: { background: '#e3f2fd', border: '#1976d2' },
          font: { size: 18, face: 'var(--font-text)', color: '#1976d2' },
          shape: 'box',
          margin: { top: 15, right: 15, bottom: 15, left: 15 }
        },
        heading2: {
          color: { background: '#f3e5f5', border: '#7b1fa2' },
          font: { size: 16, face: 'var(--font-text)', color: '#7b1fa2' },
          shape: 'box',
          margin: { top: 12, right: 12, bottom: 12, left: 12 }
        },
        heading3: {
          color: { background: '#e8f5e8', border: '#388e3c' },
          font: { size: 14, face: 'var(--font-text)', color: '#388e3c' },
          shape: 'box',
          margin: { top: 10, right: 10, bottom: 10, left: 10 }
        },
        list: {
          color: { background: 'var(--background-secondary)', border: 'var(--text-muted)' },
          font: { size: 12, face: 'var(--font-text)', color: 'var(--text-normal)' },
          shape: 'ellipse',
          margin: { top: 8, right: 8, bottom: 8, left: 8 }
        },
        quote: {
          color: { background: '#fff3e0', border: '#f57c00' },
          font: { size: 12, face: 'var(--font-text)', color: '#e65100', style: 'italic' },
          shape: 'box',
          margin: { top: 8, right: 8, bottom: 8, left: 8 }
        },
        code: {
          color: { background: 'var(--background-primary-alt)', border: 'var(--text-accent)' },
          font: { size: 11, face: 'var(--font-monospace)', color: 'var(--text-normal)' },
          shape: 'box',
          margin: { top: 6, right: 6, bottom: 6, left: 6 }
        },
        'ai-generated': {
          color: { background: 'rgba(34, 197, 94, 0.1)', border: 'var(--color-green)' },
          font: { size: 12, face: 'var(--font-text)', color: 'var(--text-normal)' },
          shape: 'ellipse'
        }
      },
      interaction: {
        dragNodes: true,
        dragView: true,
        zoomView: true,
        selectConnectedEdges: false,
        hover: true,
        hoverConnectedEdges: false
      }
    };

    // 创建网络
    this.network = new Network(this.container, {
      nodes: this.nodes,
      edges: this.edges
    }, options);

    // 设置事件监听
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    if (!this.network) return;

    // 节点点击事件
    this.network.on('click', (params) => {
      if (params.nodes.length > 0) {
        const nodeId = params.nodes[0];
        this.handleNodeClick(nodeId);
      }
    });

    // 节点双击事件
    this.network.on('doubleClick', (params) => {
      if (params.nodes.length > 0) {
        const nodeId = params.nodes[0];
        this.handleNodeEdit(nodeId);
      }
    });

    // 节点悬停事件
    this.network.on('hoverNode', (params) => {
      this.handleNodeHover(params.node, true);
    });

    this.network.on('blurNode', (params) => {
      this.handleNodeHover(params.node, false);
    });

    // 拖拽结束事件
    this.network.on('dragEnd', (params) => {
      if (params.nodes.length > 0) {
        this.handleNodeDrag(params.nodes[0], params.pointer.canvas);
      }
    });
  }

  render(markdown: string): void {
    this.logger.debug('开始使用Vis.js渲染思维导图');
    this.logger.debug('输入Markdown内容:', markdown);

    try {
      // 解析Markdown为节点数据
      const nodeData = this.parseMarkdownToNodes(markdown);
      this.logger.debug('解析得到节点数:', nodeData.length);
      this.logger.debug('节点数据结构:', nodeData);

      if (nodeData.length === 0) {
        this.logger.debug('没有解析到任何节点，显示空状态');
        this.renderEmptyState();
        return;
      }

      // 转换为Vis.js格式
      const { visNodes, visEdges } = this.convertToVisFormat(nodeData);
      this.logger.debug('转换后Vis节点数:', visNodes.length);
      this.logger.debug('转换后Vis边数:', visEdges.length);
      this.logger.debug('边数据详情:', visEdges);

      // 更新数据
      this.nodes.clear();
      this.edges.clear();
      this.nodes.add(visNodes);
      this.edges.add(visEdges);
      this.logger.debug('数据已更新到Vis.js网络');

      // 适应视图
      setTimeout(() => {
        this.fit();
        this.logger.debug('视图适应完成');
      }, 100);

      this.logger.debug('Vis.js思维导图渲染完成');
    } catch (error) {
      this.logger.error('Vis.js思维导图渲染失败', error);
      this.renderErrorState(error);
    }
  }

  private parseMarkdownToNodes(markdown: string): NodeData[] {
    // 使用增强的解析逻辑
    const enhancedNodes = this.parseMarkdownToEnhancedNodes(markdown);

    // 转换为兼容的NodeData格式
    return this.convertEnhancedToNodeData(enhancedNodes);
  }

  private parseMarkdownToEnhancedNodes(markdown: string): EnhancedNodeData[] {
    const lines = markdown.split('\n');
    const rootNodes: EnhancedNodeData[] = [];
    const nodeStack: EnhancedNodeData[] = [];

    lines.forEach((line, index) => {
      if (!line.trim()) return;

      const structure = this.analyzeLineStructure(line, index, nodeStack);
      const node = this.createNodeFromStructure(structure, index);

      // 找到正确的父节点
      const parentNode = this.findParentNode(node, nodeStack);

      if (parentNode) {
        parentNode.children.push(node);
        node.parent = parentNode;
      } else {
        rootNodes.push(node);
      }

      // 更新节点栈
      this.updateNodeStack(node, nodeStack);
    });

    return rootNodes;
  }

  private analyzeLineStructure(line: string, lineIndex: number, nodeStack?: EnhancedNodeData[]): LineStructure {
    const structure: LineStructure = {
      level: 1,
      type: 'text',
      headingLevel: 0,
      listLevel: 0,
      indentLevel: 0,
      content: line.trim(),
      rawContent: line,
      styles: this.extractStyles(line)
    };

    // 标题识别 (H1-H6)
    const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
    if (headingMatch) {
      structure.type = 'heading';
      structure.headingLevel = headingMatch[1].length;
      structure.level = headingMatch[1].length;
      structure.content = headingMatch[2];
      return structure;
    }

    // 列表识别 (支持多种bullet符号和精确嵌套)
    const listMatch = line.match(/^(\s*)([•\-*+]|\d+\.)\s+(.+)$/);
    if (listMatch) {
      structure.type = 'list';
      structure.indentLevel = listMatch[1].length;
      structure.listLevel = Math.floor(listMatch[1].length / 2) + 1;
      structure.level = structure.listLevel + this.getParentHeadingLevel(nodeStack);
      structure.content = listMatch[3];
      return structure;
    }

    // 代码块识别
    if (line.trim().startsWith('```')) {
      structure.type = 'code';
      structure.level = this.getCurrentContextLevel(nodeStack);
      structure.content = line.trim();
      return structure;
    }

    // 引用识别
    if (line.trim().startsWith('>')) {
      structure.type = 'quote';
      structure.level = this.getCurrentContextLevel(nodeStack);
      structure.content = line.replace(/^\s*>\s*/, '');
      return structure;
    }

    return structure;
  }

  private extractStyles(content: string): StyleInfo {
    const styles: StyleInfo = {
      bold: false,
      italic: false,
      code: false,
      link: undefined,
      emoji: [],
      strikethrough: false,
      highlight: false
    };

    // 提取emoji
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
    const emojiMatches = content.match(emojiRegex);
    if (emojiMatches) {
      styles.emoji = emojiMatches;
    }

    // 检测粗体 **text** 或 __text__
    styles.bold = /\*\*.*?\*\*|__.*?__/.test(content);

    // 检测斜体 *text* 或 _text_
    styles.italic = /\*.*?\*|_.*?_/.test(content);

    // 检测代码 `code`
    styles.code = /`.*?`/.test(content);

    // 检测链接 [text](url)
    const linkMatch = content.match(/\[([^\]]+)\]\(([^)]+)\)/);
    if (linkMatch) {
      styles.link = linkMatch[2];
    }

    // 检测删除线 ~~text~~
    styles.strikethrough = /~~.*?~~/.test(content);

    // 检测高亮 ==text==
    styles.highlight = /==.*?==/.test(content);

    return styles;
  }

  private createNodeFromStructure(structure: LineStructure, lineIndex: number): EnhancedNodeData {
    return {
      id: this.generateId(),
      content: structure.content,
      rawContent: structure.rawContent,
      level: structure.level,
      type: structure.type,
      headingLevel: structure.headingLevel || undefined,
      listLevel: structure.listLevel || undefined,
      indentLevel: structure.indentLevel || undefined,
      styles: structure.styles,
      children: [],
      parent: undefined,
      position: { line: lineIndex, ch: 0 }
    };
  }

  private findParentNode(currentNode: EnhancedNodeData, nodeStack: EnhancedNodeData[]): EnhancedNodeData | null {
    // 从栈顶向下查找合适的父节点
    for (let i = nodeStack.length - 1; i >= 0; i--) {
      const candidate = nodeStack[i];
      if (candidate.level < currentNode.level) {
        return candidate;
      }
    }
    return null;
  }

  private updateNodeStack(node: EnhancedNodeData, nodeStack: EnhancedNodeData[]): void {
    // 移除层级大于等于当前节点的节点
    while (nodeStack.length > 0 && nodeStack[nodeStack.length - 1].level >= node.level) {
      nodeStack.pop();
    }

    // 添加当前节点到栈
    nodeStack.push(node);
  }

  private convertEnhancedToNodeData(enhancedNodes: EnhancedNodeData[]): NodeData[] {
    const convertNode = (enhanced: EnhancedNodeData): NodeData => {
      const node: NodeData = {
        id: enhanced.id,
        content: enhanced.content,
        level: enhanced.level,
        type: enhanced.type as 'heading' | 'list' | 'code' | 'ai-generated',
        children: enhanced.children.map(convertNode),
        position: enhanced.position
      };

      // 将增强节点存储到映射中以便后续使用
      this.nodeMap.set(node.id, node);
      this.enhancedNodeMap.set(enhanced.id, enhanced);
      return node;
    };

    return enhancedNodes.map(convertNode);
  }

  private getParentHeadingLevel(nodeStack?: EnhancedNodeData[]): number {
    if (!nodeStack || nodeStack.length === 0) return 1;

    // 从栈顶向下查找最近的标题节点
    for (let i = nodeStack.length - 1; i >= 0; i--) {
      const node = nodeStack[i];
      if (node.type === 'heading' && node.headingLevel) {
        return node.headingLevel;
      }
    }

    return 1;
  }

  private getCurrentContextLevel(nodeStack?: EnhancedNodeData[]): number {
    if (!nodeStack || nodeStack.length === 0) return 1;

    // 返回栈顶节点的层级 + 1
    const topNode = nodeStack[nodeStack.length - 1];
    return topNode.level + 1;
  }

  private convertToVisFormat(nodeData: NodeData[]): { visNodes: VisNode[], visEdges: VisEdge[] } {
    const visNodes: VisNode[] = [];
    const visEdges: VisEdge[] = [];

    const processNode = (node: NodeData, parentId?: string) => {
      // 创建Vis节点
      const visNode: VisNode = {
        id: node.id,
        label: this.formatNodeLabel(node),
        level: node.level,
        type: node.type,
        group: this.getNodeGroup(node),
        originalData: node
      };

      visNodes.push(visNode);

      // 创建边（如果有父节点）
      if (parentId) {
        const edge: VisEdge = {
          id: `edge-${parentId}-${node.id}`,
          from: parentId,
          to: node.id
        };
        visEdges.push(edge);
        this.logger.debug(`创建边: ${parentId} -> ${node.id}`);
      }

      // 递归处理子节点
      node.children.forEach(child => {
        processNode(child, node.id);
      });
    };

    nodeData.forEach(node => processNode(node));
    return { visNodes, visEdges };
  }

  private formatNodeLabel(node: NodeData): string {
    // 尝试获取增强的节点信息
    const enhancedNode = this.enhancedNodeMap.get(node.id);

    if (enhancedNode) {
      return this.formatEnhancedNodeLabel(enhancedNode);
    }

    // 回退到原始格式化逻辑
    let label = node.content;

    // 限制标签长度
    if (label.length > 50) {
      label = label.substring(0, 47) + '...';
    }

    // AI节点添加特殊标识
    if (node.type === 'ai-generated') {
      label = '🤖 ' + label;
    }

    return label;
  }

  private formatEnhancedNodeLabel(node: EnhancedNodeData): string {
    let label = node.content;

    // 保留emoji（已经在content中）
    // emoji在解析时已经保留在content中

    // 处理粗体（在Vis.js中用HTML标签）
    if (node.styles.bold) {
      // 移除原始的**标记，用HTML标签替换
      label = label.replace(/\*\*(.*?)\*\*/g, '<b>$1</b>');
      label = label.replace(/__(.*?)__/g, '<b>$1</b>');
    }

    // 处理斜体
    if (node.styles.italic) {
      // 移除原始的*标记，用HTML标签替换
      label = label.replace(/\*(.*?)\*/g, '<i>$1</i>');
      label = label.replace(/_(.*?)_/g, '<i>$1</i>');
    }

    // 处理代码
    if (node.styles.code) {
      // 移除原始的`标记，用HTML标签替换
      label = label.replace(/`(.*?)`/g, '<code>$1</code>');
    }

    // 处理删除线
    if (node.styles.strikethrough) {
      label = label.replace(/~~(.*?)~~/g, '<s>$1</s>');
    }

    // 长文本截断
    if (label.length > 50) {
      label = label.substring(0, 47) + '...';
    }

    return label;
  }

  private getNodeGroup(node: NodeData): string {
    if (node.type === 'heading') {
      return `heading${Math.min(node.level, 3)}`;
    }
    return node.type;
  }

  // 实现MindMapAPI接口方法
  updateNode(nodeId: string, data: Partial<NodeData>): void {
    const node = this.nodeMap.get(nodeId);
    if (!node) return;

    // 更新节点数据
    Object.assign(node, data);

    // 更新Vis节点
    const visNode = this.nodes.get(nodeId);
    if (visNode) {
      this.nodes.update({
        ...visNode,
        label: this.formatNodeLabel(node),
        group: this.getNodeGroup(node)
      });
    }

    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, data);
    }
  }

  addNode(parentId: string, data: NodeData): string {
    this.nodeMap.set(data.id, data);

    // 添加Vis节点
    const visNode: VisNode = {
      id: data.id,
      label: this.formatNodeLabel(data),
      level: data.level,
      type: data.type,
      group: this.getNodeGroup(data),
      originalData: data
    };

    this.nodes.add(visNode);

    // 添加边
    this.edges.add({
      from: parentId,
      to: data.id
    });

    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(data.id, { action: 'add', parentId });
    }

    return data.id;
  }

  deleteNode(nodeId: string): void {
    this.nodeMap.delete(nodeId);
    this.nodes.remove(nodeId);

    // 删除相关的边
    const edgesToRemove = this.edges.get({
      filter: (edge: any) => edge.from === nodeId || edge.to === nodeId
    });

    this.edges.remove(edgesToRemove.map((edge: any) => edge.id!));

    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'delete' });
    }
  }

  moveNode(nodeId: string, targetParentId: string): void {
    // 删除旧的边
    const oldEdges = this.edges.get({
      filter: (edge: any) => edge.to === nodeId
    });
    this.edges.remove(oldEdges.map((edge: any) => edge.id!));

    // 添加新的边
    this.edges.add({
      from: targetParentId,
      to: nodeId
    });

    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'move', targetParentId });
    }
  }

  getNodeByPosition(_pos: { line: number; ch: number }): NodeData | null {
    // 暂时返回null，后续可以实现位置映射
    return null;
  }

  getPositionByNode(_nodeId: string): { line: number; ch: number } | null {
    // 暂时返回null，后续可以实现位置映射
    return null;
  }

  setNodeChangeCallback(callback: (nodeId: string, changes: any) => void): void {
    this.onNodeChangeCallback = callback;
  }

  fit(): void {
    if (this.network) {
      this.network.fit({
        animation: {
          duration: 500,
          easingFunction: 'easeInOutQuad'
        }
      });
    }
  }

  destroy(): void {
    if (this.network) {
      this.network.destroy();
      this.network = null;
    }
    this.nodes.clear();
    this.edges.clear();
    this.nodeMap.clear();
  }

  // 私有辅助方法
  private getLineLevel(line: string): number {
    const headingMatch = line.match(/^(#{1,6})\s/);
    if (headingMatch) return headingMatch[1].length;

    // 扩展支持: •, -, *, +, 数字列表
    const listMatch = line.match(/^(\s*)([•\-*+]|\d+\.)\s/);
    if (listMatch) return Math.floor(listMatch[1].length / 2) + 1;

    return 1;
  }

  private cleanLineContent(line: string): string {
    return line
      .replace(/^#{1,6}\s+/, '')
      .replace(/^\s*[•\-*+]\s+/, '')  // 扩展支持 • 符号
      .replace(/^\s*\d+\.\s+/, '')
      .trim();
  }

  private getNodeType(line: string): 'heading' | 'list' | 'code' | 'ai-generated' {
    if (line.match(/^#{1,6}\s/)) return 'heading';
    if (line.match(/^\s*[•\-*+]\s/)) return 'list';  // 扩展支持 • 符号
    if (line.match(/^\s*\d+\.\s/)) return 'list';
    if (line.includes('```')) return 'code';
    return 'list'; // 默认返回list而不是text
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 11);
  }

  private handleNodeClick(nodeId: string): void {
    this.logger.debug('节点被点击', nodeId);
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'click' });
    }
  }

  private handleNodeEdit(nodeId: string): void {
    this.logger.debug('节点被双击编辑', nodeId);
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'edit' });
    }
  }

  private handleNodeHover(_nodeId: string, _isHover: boolean): void {
    // 可以在这里添加悬停效果
  }

  private handleNodeDrag(nodeId: string, position: any): void {
    this.logger.debug('节点被拖拽', { nodeId, position });
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'drag', position });
    }
  }

  private renderEmptyState(): void {
    this.container.innerHTML = `
      <div class="mindmap-empty">
        <div class="empty-icon">🧠</div>
        <div class="empty-message">暂无内容</div>
        <div class="empty-hint">请在Markdown文档中添加标题或列表</div>
      </div>
    `;
  }

  private renderErrorState(error: any): void {
    this.container.innerHTML = `
      <div class="mindmap-error">
        <div class="error-icon">⚠️</div>
        <div class="error-message">思维导图渲染失败</div>
        <div class="error-detail">${error.message || '未知错误'}</div>
      </div>
    `;
  }
}
