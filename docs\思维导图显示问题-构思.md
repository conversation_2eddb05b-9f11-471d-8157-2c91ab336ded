# 思维导图显示问题 - 构思

## 2.1 问题根因确认

通过分析用户的Markdown内容，我发现了核心问题：

**用户使用的格式**:
```markdown
• 🧠 智能解析rwr: 自动解析 Markdown 标题结构生成思维导图
• 📱 双向同步: Markdown 文件与思维导图实时同步
```

**当前正则表达式**:
```typescript
const listMatch = line.match(/^(\s*)([-*+]|\d+\.)\s/);
```

**问题**: 正则表达式只匹配 `-`, `*`, `+` 和数字列表，不匹配 `•` 符号！

## 2.2 解决方案对比

### 方案一：扩展正则表达式支持 ⭐⭐⭐⭐⭐
**描述**: 修改正则表达式支持更多bullet符号

**优点**:
- 修改简单，风险最低
- 立即解决当前问题
- 向后兼容现有格式

**缺点**:
- 需要考虑更多边界情况

**实施方案**:
```typescript
// 修改前
const listMatch = line.match(/^(\s*)([-*+]|\d+\.)\s/);

// 修改后  
const listMatch = line.match(/^(\s*)([•\-*+]|\d+\.)\s/);
```

### 方案二：重写解析器 ⭐⭐⭐
**描述**: 使用更强大的Markdown解析库

**优点**:
- 支持完整的Markdown语法
- 更健壮的解析能力
- 未来扩展性好

**缺点**:
- 工作量大，风险高
- 可能引入新的依赖
- 不符合快速修复目标

### 方案三：智能检测格式 ⭐⭐⭐⭐
**描述**: 自动检测并适配不同的bullet格式

**优点**:
- 用户体验最好
- 支持各种格式
- 智能化程度高

**缺点**:
- 实现复杂度中等
- 需要更多测试

## 2.3 推荐方案详细设计

### 选择方案一：扩展正则表达式支持

基于快速修复和低风险的原则，选择方案一作为主要解决方案。

## 2.4 技术实施设计

### 2.4.1 正则表达式增强
```typescript
// 支持更多bullet符号
private getLineLevel(line: string): number {
    const headingMatch = line.match(/^(#{1,6})\s/);
    if (headingMatch) return headingMatch[1].length;

    // 扩展支持: •, -, *, +, 数字列表
    const listMatch = line.match(/^(\s*)([•\-*+]|\d+\.)\s/);
    if (listMatch) return Math.floor(listMatch[1].length / 2) + 1;

    return 1;
}

private cleanLineContent(line: string): string {
    return line
        .replace(/^#{1,6}\s+/, '')
        .replace(/^\s*[•\-*+]\s+/, '')  // 扩展支持
        .replace(/^\s*\d+\.\s+/, '')
        .trim();
}

private getNodeType(line: string): 'heading' | 'list' | 'code' | 'ai-generated' {
    if (line.match(/^#{1,6}\s/)) return 'heading';
    if (line.match(/^\s*[•\-*+]\s/)) return 'list';  // 扩展支持
    if (line.match(/^\s*\d+\.\s/)) return 'list';
    if (line.includes('```')) return 'code';
    return 'list';
}
```

### 2.4.2 调试信息增强
```typescript
render(markdown: string): void {
    this.logger.debug('开始使用Vis.js渲染思维导图');
    this.logger.debug('输入Markdown:', markdown);

    try {
        const nodeData = this.parseMarkdownToNodes(markdown);
        this.logger.debug('解析得到节点数:', nodeData.length);
        this.logger.debug('节点数据:', nodeData);
        
        if (nodeData.length === 0) {
            this.renderEmptyState();
            return;
        }

        const { visNodes, visEdges } = this.convertToVisFormat(nodeData);
        this.logger.debug('Vis节点数:', visNodes.length);
        this.logger.debug('Vis边数:', visEdges.length);
        this.logger.debug('边数据:', visEdges);

        // 更新数据
        this.nodes.clear();
        this.edges.clear();
        this.nodes.add(visNodes);
        this.edges.add(visEdges);

        // 适应视图
        setTimeout(() => {
            this.fit();
        }, 100);

        this.logger.debug('Vis.js思维导图渲染完成');
    } catch (error) {
        this.logger.error('Vis.js思维导图渲染失败', error);
        this.renderErrorState(error);
    }
}
```

### 2.4.3 边生成验证
```typescript
private convertToVisFormat(nodeData: NodeData[]): { visNodes: VisNode[], visEdges: VisEdge[] } {
    const visNodes: VisNode[] = [];
    const visEdges: VisEdge[] = [];

    const processNode = (node: NodeData, parentId?: string) => {
        // 创建Vis节点
        const visNode: VisNode = {
            id: node.id,
            label: this.formatNodeLabel(node),
            level: node.level,
            type: node.type,
            group: this.getNodeGroup(node),
            originalData: node
        };

        visNodes.push(visNode);

        // 创建边（如果有父节点）
        if (parentId) {
            const edge: VisEdge = {
                id: `edge-${parentId}-${node.id}`,  // 添加边ID
                from: parentId,
                to: node.id
            };
            visEdges.push(edge);
            this.logger.debug(`创建边: ${parentId} -> ${node.id}`);
        }

        // 递归处理子节点
        node.children.forEach(child => {
            processNode(child, node.id);
        });
    };

    nodeData.forEach(node => processNode(node));
    return { visNodes, visEdges };
}
```

## 2.5 测试验证策略

### 2.5.1 单元测试用例
```typescript
// 测试用例1: 标准bullet符号
const test1 = `
# 主标题
- 项目1
- 项目2
  - 子项目1
`;

// 测试用例2: 特殊bullet符号
const test2 = `
# 主标题
• 项目1
• 项目2
  • 子项目1
`;

// 测试用例3: 混合格式
const test3 = `
# 主标题
• 项目1
- 项目2
* 项目3
`;
```

### 2.5.2 集成测试
1. 测试完整的渲染流程
2. 验证节点和边的正确生成
3. 检查布局效果
4. 确认交互功能正常

## 2.6 风险控制措施

### 2.6.1 代码安全
- 保留原始代码作为备份
- 使用渐进式修改方式
- 每个修改后立即测试

### 2.6.2 兼容性保证
- 确保现有格式仍然工作
- 测试各种边界情况
- 提供降级方案

## 2.7 性能考虑

### 2.7.1 正则表达式优化
- 使用高效的正则表达式
- 避免回溯问题
- 缓存编译结果

### 2.7.2 渲染优化
- 保持现有的性能特性
- 不增加额外的计算开销
- 优化大文档处理

## 2.8 用户体验改进

### 2.8.1 错误提示
- 提供更清晰的错误信息
- 显示解析状态
- 给出修复建议

### 2.8.2 调试支持
- 增加调试日志
- 提供解析详情
- 便于问题排查

## 2.9 实施计划

### 总时间：20分钟

1. **代码修改** (10分钟)
   - 修改正则表达式
   - 增加调试日志
   - 完善边生成逻辑

2. **编译测试** (5分钟)
   - 编译项目
   - 修复可能的错误

3. **功能验证** (5分钟)
   - 测试用户的Markdown
   - 验证显示效果
   - 确认连接线正常

## 2.10 成功标准

### 功能标准
- ✅ 支持 `•` 符号的列表项
- ✅ 正确显示层次结构
- ✅ 连接线正常显示
- ✅ 布局美观合理

### 兼容性标准
- ✅ 现有格式仍然工作
- ✅ 不破坏其他功能
- ✅ 性能不降低

### 用户体验标准
- ✅ 立即解决用户问题
- ✅ 操作流程不变
- ✅ 视觉效果改善

这个方案可以在20分钟内快速解决用户遇到的显示问题，是最优的技术选择。
