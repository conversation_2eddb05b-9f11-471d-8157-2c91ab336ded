<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="173" name="Python" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyArgumentListInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="98">
            <item index="0" class="java.lang.String" itemvalue="dask" />
            <item index="1" class="java.lang.String" itemvalue="oslo.messaging" />
            <item index="2" class="java.lang.String" itemvalue="alembic" />
            <item index="3" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="4" class="java.lang.String" itemvalue="httplib2" />
            <item index="5" class="java.lang.String" itemvalue="oslo.rootwrap" />
            <item index="6" class="java.lang.String" itemvalue="oslo.service" />
            <item index="7" class="java.lang.String" itemvalue="PasteDeploy" />
            <item index="8" class="java.lang.String" itemvalue="oslo.middleware" />
            <item index="9" class="java.lang.String" itemvalue="os-resource-classes" />
            <item index="10" class="java.lang.String" itemvalue="tooz" />
            <item index="11" class="java.lang.String" itemvalue="neutron-lib" />
            <item index="12" class="java.lang.String" itemvalue="eventlet" />
            <item index="13" class="java.lang.String" itemvalue="oslo.db" />
            <item index="14" class="java.lang.String" itemvalue="oslo.concurrency" />
            <item index="15" class="java.lang.String" itemvalue="osprofiler" />
            <item index="16" class="java.lang.String" itemvalue="python-neutronclient" />
            <item index="17" class="java.lang.String" itemvalue="tenacity" />
            <item index="18" class="java.lang.String" itemvalue="ovsdbapp" />
            <item index="19" class="java.lang.String" itemvalue="oslo.versionedobjects" />
            <item index="20" class="java.lang.String" itemvalue="futurist" />
            <item index="21" class="java.lang.String" itemvalue="pecan" />
            <item index="22" class="java.lang.String" itemvalue="oslo.upgradecheck" />
            <item index="23" class="java.lang.String" itemvalue="oslo.utils" />
            <item index="24" class="java.lang.String" itemvalue="Paste" />
            <item index="25" class="java.lang.String" itemvalue="oslo.reports" />
            <item index="26" class="java.lang.String" itemvalue="openstacksdk" />
            <item index="27" class="java.lang.String" itemvalue="Routes" />
            <item index="28" class="java.lang.String" itemvalue="oslo.privsep" />
            <item index="29" class="java.lang.String" itemvalue="oslo.policy" />
            <item index="30" class="java.lang.String" itemvalue="ovs" />
            <item index="31" class="java.lang.String" itemvalue="oslo.config" />
            <item index="32" class="java.lang.String" itemvalue="oslo.log" />
            <item index="33" class="java.lang.String" itemvalue="python-designateclient" />
            <item index="34" class="java.lang.String" itemvalue="python-novaclient" />
            <item index="35" class="java.lang.String" itemvalue="os-vif" />
            <item index="36" class="java.lang.String" itemvalue="os-ken" />
            <item index="37" class="java.lang.String" itemvalue="tiktoken" />
            <item index="38" class="java.lang.String" itemvalue="PyYAML" />
            <item index="39" class="java.lang.String" itemvalue="nbclient" />
            <item index="40" class="java.lang.String" itemvalue="setuptools" />
            <item index="41" class="java.lang.String" itemvalue="redis" />
            <item index="42" class="java.lang.String" itemvalue="qdrant-client" />
            <item index="43" class="java.lang.String" itemvalue="tree_sitter" />
            <item index="44" class="java.lang.String" itemvalue="faiss_cpu" />
            <item index="45" class="java.lang.String" itemvalue="semantic-kernel" />
            <item index="46" class="java.lang.String" itemvalue="playwright" />
            <item index="47" class="java.lang.String" itemvalue="anthropic" />
            <item index="48" class="java.lang.String" itemvalue="spark_ai_python" />
            <item index="49" class="java.lang.String" itemvalue="fire" />
            <item index="50" class="java.lang.String" itemvalue="lancedb" />
            <item index="51" class="java.lang.String" itemvalue="gitpython" />
            <item index="52" class="java.lang.String" itemvalue="agentops" />
            <item index="53" class="java.lang.String" itemvalue="libcst" />
            <item index="54" class="java.lang.String" itemvalue="gitignore-parser" />
            <item index="55" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="56" class="java.lang.String" itemvalue="gymnasium" />
            <item index="57" class="java.lang.String" itemvalue="wrapt" />
            <item index="58" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="59" class="java.lang.String" itemvalue="loguru" />
            <item index="60" class="java.lang.String" itemvalue="ipykernel" />
            <item index="61" class="java.lang.String" itemvalue="openai" />
            <item index="62" class="java.lang.String" itemvalue="boto3" />
            <item index="63" class="java.lang.String" itemvalue="channels" />
            <item index="64" class="java.lang.String" itemvalue="rank-bm25" />
            <item index="65" class="java.lang.String" itemvalue="curl-cffi" />
            <item index="66" class="java.lang.String" itemvalue="networkx" />
            <item index="67" class="java.lang.String" itemvalue="jieba" />
            <item index="68" class="java.lang.String" itemvalue="zhipuai" />
            <item index="69" class="java.lang.String" itemvalue="scikit_learn" />
            <item index="70" class="java.lang.String" itemvalue="tree_sitter_python" />
            <item index="71" class="java.lang.String" itemvalue="numpy" />
            <item index="72" class="java.lang.String" itemvalue="google-generativeai" />
            <item index="73" class="java.lang.String" itemvalue="grpcio-status" />
            <item index="74" class="java.lang.String" itemvalue="anytree" />
            <item index="75" class="java.lang.String" itemvalue="volcengine-python-sdk" />
            <item index="76" class="java.lang.String" itemvalue="websockets" />
            <item index="77" class="java.lang.String" itemvalue="ipywidgets" />
            <item index="78" class="java.lang.String" itemvalue="typer" />
            <item index="79" class="java.lang.String" itemvalue="meilisearch" />
            <item index="80" class="java.lang.String" itemvalue="aiofiles" />
            <item index="81" class="java.lang.String" itemvalue="nbformat" />
            <item index="82" class="java.lang.String" itemvalue="rich" />
            <item index="83" class="java.lang.String" itemvalue="ipython" />
            <item index="84" class="java.lang.String" itemvalue="socksio" />
            <item index="85" class="java.lang.String" itemvalue="grpcio-tools" />
            <item index="86" class="java.lang.String" itemvalue="ta" />
            <item index="87" class="java.lang.String" itemvalue="imap_tools" />
            <item index="88" class="java.lang.String" itemvalue="pandas" />
            <item index="89" class="java.lang.String" itemvalue="python_docx" />
            <item index="90" class="java.lang.String" itemvalue="tqdm" />
            <item index="91" class="java.lang.String" itemvalue="typing-inspect" />
            <item index="92" class="java.lang.String" itemvalue="dashscope" />
            <item index="93" class="java.lang.String" itemvalue="aiohttp" />
            <item index="94" class="java.lang.String" itemvalue="qianfan" />
            <item index="95" class="java.lang.String" itemvalue="grpcio" />
            <item index="96" class="java.lang.String" itemvalue="openpyxl" />
            <item index="97" class="java.lang.String" itemvalue="akshare" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N801" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="typing.Iterator.next" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="TsLint" enabled="true" level="WARNING" enabled_by_default="true" />
  </profile>
</component>