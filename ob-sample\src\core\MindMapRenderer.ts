/**
 * 思维导图渲染器
 * 基于D3.js实现简化版思维导图的渲染和交互
 */

import * as d3 from 'd3';
import { NodeData, MindMapAPI } from '../types';
import { Logger } from '../utils';

interface TreeNode {
  id: string;
  content: string;
  level: number;
  type: string;
  children: TreeNode[];
  x?: number;
  y?: number;
}

export class MindMapRenderer implements MindMapAPI {
  private container: HTMLElement;
  private svg: d3.Selection<SVGSVGElement, unknown, null, undefined> | null = null;
  private logger: Logger;
  private nodeMap: Map<string, TreeNode> = new Map();
  private onNodeChangeCallback?: (nodeId: string, changes: any) => void;
  private width = 800;
  private height = 600;

  constructor(container: HTMLElement, logger: Logger) {
    this.container = container;
    this.logger = logger;

    this.initializeContainer();
  }
  
  /**
   * 初始化容器
   */
  private initializeContainer(): void {
    this.container.style.width = '100%';
    this.container.style.height = '100%';
    this.container.style.position = 'relative';
    this.container.style.overflow = 'hidden';

    // 创建SVG
    this.svg = d3.select(this.container)
      .append('svg')
      .attr('width', '100%')
      .attr('height', '100%')
      .style('background', 'var(--background-primary)');

    // 添加缩放功能
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 3])
      .on('zoom', (event) => {
        this.svg?.select('g').attr('transform', event.transform);
      });

    this.svg.call(zoom);

    // 创建主要的g元素
    this.svg.append('g').attr('class', 'mindmap-content');
  }

  /**
   * 渲染思维导图
   */
  render(markdown: string): void {
    this.logger.debug('开始渲染思维导图');

    try {
      // 解析Markdown为树结构
      const nodes = this.parseMarkdownToTree(markdown);

      if (nodes.length === 0) {
        this.renderEmptyState();
        return;
      }

      // 计算节点位置
      this.calculateNodePositions(nodes);

      // 渲染节点和连线
      this.renderNodes(nodes);

      this.logger.debug('思维导图渲染完成');
    } catch (error) {
      this.logger.error('思维导图渲染失败', error);
      this.renderErrorState(error);
    }
  }

  /**
   * 解析Markdown为树结构
   */
  private parseMarkdownToTree(markdown: string): TreeNode[] {
    const lines = markdown.split('\n').filter(line => line.trim());
    const nodes: TreeNode[] = [];
    const stack: TreeNode[] = [];

    for (const line of lines) {
      const level = this.getLineLevel(line);
      const content = this.cleanLineContent(line);

      if (!content) continue;

      const node: TreeNode = {
        id: this.generateId(),
        content,
        level,
        type: this.getNodeType(line),
        children: []
      };

      // 清理栈
      while (stack.length > 0 && stack[stack.length - 1].level >= level) {
        stack.pop();
      }

      if (stack.length === 0) {
        nodes.push(node);
      } else {
        stack[stack.length - 1].children.push(node);
      }

      stack.push(node);
      this.nodeMap.set(node.id, node);
    }

    return nodes;
  }
  
  /**
   * 获取行的层级
   */
  private getLineLevel(line: string): number {
    const headingMatch = line.match(/^(#{1,6})\s/);
    if (headingMatch) return headingMatch[1].length;

    const listMatch = line.match(/^(\s*)([-*+]|\d+\.)\s/);
    if (listMatch) return Math.floor(listMatch[1].length / 2) + 1;

    return 1;
  }

  /**
   * 清理行内容
   */
  private cleanLineContent(line: string): string {
    return line
      .replace(/^#{1,6}\s+/, '')
      .replace(/^\s*[-*+]\s+/, '')
      .replace(/^\s*\d+\.\s+/, '')
      .trim();
  }

  /**
   * 获取节点类型
   */
  private getNodeType(line: string): string {
    if (line.match(/^#{1,6}\s/)) return 'heading';
    if (line.match(/^\s*[-*+]\s/)) return 'list';
    if (line.match(/^\s*\d+\.\s/)) return 'list';
    return 'text';
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  /**
   * 计算节点位置
   */
  private calculateNodePositions(nodes: TreeNode[]): void {
    const centerX = this.width / 2;
    const centerY = this.height / 2;

    this.layoutNodes(nodes, centerX, centerY, 0);
  }

  /**
   * 递归布局节点
   */
  private layoutNodes(nodes: TreeNode[], startX: number, startY: number, depth: number): void {
    const nodeHeight = 40;
    const levelWidth = 200;

    let currentY = startY - (nodes.length - 1) * nodeHeight / 2;

    for (const node of nodes) {
      node.x = startX + depth * levelWidth;
      node.y = currentY;

      if (node.children.length > 0) {
        this.layoutNodes(node.children, node.x, node.y, depth + 1);
      }

      currentY += nodeHeight;
    }
  }
  
  /**
   * 渲染节点
   */
  private renderNodes(nodes: TreeNode[]): void {
    if (!this.svg) return;

    const g = this.svg.select('.mindmap-content');
    g.selectAll('*').remove(); // 清空之前的内容

    // 收集所有节点和连线
    const allNodes: TreeNode[] = [];
    const links: { source: TreeNode; target: TreeNode }[] = [];

    this.collectNodesAndLinks(nodes, allNodes, links);

    // 渲染连线
    g.selectAll('.link')
      .data(links)
      .enter()
      .append('line')
      .attr('class', 'link')
      .attr('x1', d => d.source.x || 0)
      .attr('y1', d => d.source.y || 0)
      .attr('x2', d => d.target.x || 0)
      .attr('y2', d => d.target.y || 0)
      .style('stroke', 'var(--text-muted)')
      .style('stroke-width', 2);

    // 渲染节点
    const nodeGroups = g.selectAll('.node')
      .data(allNodes)
      .enter()
      .append('g')
      .attr('class', 'node')
      .attr('transform', d => `translate(${d.x || 0}, ${d.y || 0})`)
      .style('cursor', 'pointer');

    // 添加节点背景
    nodeGroups.append('rect')
      .attr('width', d => Math.max(d.content.length * 8, 80))
      .attr('height', 30)
      .attr('x', d => -Math.max(d.content.length * 4, 40))
      .attr('y', -15)
      .attr('rx', 5)
      .style('fill', d => this.getNodeColor(d))
      .style('stroke', 'var(--background-modifier-border)')
      .style('stroke-width', 1);

    // 添加节点文本
    nodeGroups.append('text')
      .attr('text-anchor', 'middle')
      .attr('dy', '0.35em')
      .style('fill', 'var(--text-normal)')
      .style('font-size', '12px')
      .text(d => d.content.length > 20 ? d.content.substring(0, 20) + '...' : d.content);

    // 添加事件监听
    nodeGroups
      .on('click', (event, d) => this.handleNodeClick(d))
      .on('dblclick', (event, d) => this.handleNodeEdit(d));
  }

  /**
   * 收集所有节点和连线
   */
  private collectNodesAndLinks(nodes: TreeNode[], allNodes: TreeNode[], links: { source: TreeNode; target: TreeNode }[]): void {
    for (const node of nodes) {
      allNodes.push(node);

      for (const child of node.children) {
        links.push({ source: node, target: child });
      }

      if (node.children.length > 0) {
        this.collectNodesAndLinks(node.children, allNodes, links);
      }
    }
  }

  /**
   * 获取节点颜色
   */
  private getNodeColor(node: TreeNode): string {
    switch (node.type) {
      case 'heading':
        return 'var(--interactive-accent)';
      case 'list':
        return 'var(--background-secondary)';
      case 'ai-generated':
        return 'var(--color-green)';
      default:
        return 'var(--background-primary)';
    }
  }

  /**
   * 处理节点点击
   */
  private handleNodeClick(node: TreeNode): void {
    this.logger.debug('节点被点击', node);
    this.highlightNode(node);
  }
  
  /**
   * 处理节点编辑
   */
  private handleNodeEdit(node: TreeNode): void {
    this.logger.debug('节点被双击编辑', node);

    // 简化版本：暂时只记录日志，后续可以实现编辑功能
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(node.id, { action: 'edit', content: node.content });
    }
  }

  /**
   * 渲染空状态
   */
  private renderEmptyState(): void {
    if (!this.svg) return;

    const g = this.svg.select('.mindmap-content');
    g.selectAll('*').remove();

    g.append('text')
      .attr('x', this.width / 2)
      .attr('y', this.height / 2)
      .attr('text-anchor', 'middle')
      .style('fill', 'var(--text-muted)')
      .style('font-size', '16px')
      .text('暂无内容，请在Markdown文档中添加标题或列表');
  }

  /**
   * 渲染错误状态
   */
  private renderErrorState(error: any): void {
    if (!this.svg) return;

    const g = this.svg.select('.mindmap-content');
    g.selectAll('*').remove();

    g.append('text')
      .attr('x', this.width / 2)
      .attr('y', this.height / 2)
      .attr('text-anchor', 'middle')
      .style('fill', 'var(--text-error)')
      .style('font-size', '14px')
      .text(`渲染失败: ${error.message || '未知错误'}`);
  }
  
  /**
   * 更新节点内容
   */
  updateNode(nodeId: string, data: Partial<NodeData>): void {
    const node = this.nodeMap.get(nodeId);
    if (!node) {
      this.logger.warn(`未找到节点: ${nodeId}`);
      return;
    }

    // 更新节点数据
    if (data.content !== undefined) node.content = data.content;
    if (data.type !== undefined) node.type = data.type;
    if (data.level !== undefined) node.level = data.level;

    // 通知变更
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, data);
    }
  }

  /**
   * 添加新节点
   */
  addNode(parentId: string, data: NodeData): string {
    const parentNode = this.nodeMap.get(parentId);
    if (!parentNode) {
      this.logger.warn(`未找到父节点: ${parentId}`);
      return '';
    }

    // 创建新节点
    const newNode: TreeNode = {
      id: data.id,
      content: data.content,
      type: data.type,
      level: data.level,
      children: []
    };

    // 添加到父节点
    parentNode.children.push(newNode);

    // 更新节点映射
    this.nodeMap.set(data.id, newNode);

    // 通知变更
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(data.id, { action: 'add', parentId });
    }

    return data.id;
  }

  /**
   * 删除节点
   */
  deleteNode(nodeId: string): void {
    const node = this.nodeMap.get(nodeId);
    if (!node) {
      this.logger.warn(`未找到节点: ${nodeId}`);
      return;
    }

    // 从映射中移除
    this.nodeMap.delete(nodeId);

    // 通知变更
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'delete' });
    }
  }

  /**
   * 移动节点
   */
  moveNode(nodeId: string, targetParentId: string): void {
    this.logger.debug(`移动节点: ${nodeId} -> ${targetParentId}`);

    // 通知变更
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'move', targetParentId });
    }
  }
  
  /**
   * 根据位置获取节点
   */
  getNodeByPosition(pos: { line: number; ch: number }): NodeData | null {
    // 暂时返回null，后续可以实现位置映射
    return null;
  }

  /**
   * 根据节点获取位置
   */
  getPositionByNode(nodeId: string): { line: number; ch: number } | null {
    // 暂时返回null，后续可以实现位置映射
    return null;
  }

  /**
   * 设置节点变更回调
   */
  setNodeChangeCallback(callback: (nodeId: string, changes: any) => void): void {
    this.onNodeChangeCallback = callback;
  }

  /**
   * 高亮节点
   */
  private highlightNode(node: TreeNode): void {
    if (!this.svg) return;

    // 移除之前的高亮
    this.svg.selectAll('.node rect').style('stroke-width', 1);

    // 添加新的高亮
    this.svg.selectAll('.node')
      .filter((d: any) => d.id === node.id)
      .select('rect')
      .style('stroke-width', 3)
      .style('stroke', 'var(--interactive-accent)');
  }

  /**
   * 适应视图
   */
  fit(): void {
    if (!this.svg) return;

    // 简单的居中实现
    const g = this.svg.select('.mindmap-content');
    const bbox = (g.node() as any)?.getBBox();

    if (bbox) {
      const centerX = this.width / 2 - bbox.x - bbox.width / 2;
      const centerY = this.height / 2 - bbox.y - bbox.height / 2;
      g.attr('transform', `translate(${centerX}, ${centerY})`);
    }
  }

  /**
   * 销毁渲染器
   */
  destroy(): void {
    if (this.svg) {
      this.svg.remove();
      this.svg = null;
    }
    this.nodeMap.clear();
  }
}
