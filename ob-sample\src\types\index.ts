/**
 * 核心类型定义
 */

// 插件设置接口
export interface MindMapSyncSettings {
  // AI服务配置
  aiProvider: 'openai' | 'ollama';
  openaiApiKey: string;
  openaiModel: string;
  ollamaUrl: string;
  ollamaModel: string;
  
  // 同步设置
  enableSync: boolean;
  debounceTime: number;
  conflictResolution: 'markdown' | 'mindmap' | 'prompt';
  autoSyncScope: 'current' | 'all';
  
  // 思维导图样式
  layoutDirection: 'horizontal' | 'vertical';
  nodeStylePreset: string;
  enableAiNodeStyle: boolean;
  defaultZoomLevel: number;
  
  // 高级设置
  debugMode: boolean;
}

// 默认设置
export const DEFAULT_SETTINGS: MindMapSyncSettings = {
  aiProvider: 'ollama',
  openaiApiKey: '',
  openaiModel: 'gpt-4',
  ollamaUrl: 'http://localhost:11434',
  ollamaModel: 'llama2',
  
  enableSync: true,
  debounceTime: 300,
  conflictResolution: 'prompt',
  autoSyncScope: 'current',
  
  layoutDirection: 'horizontal',
  nodeStylePreset: 'default',
  enableAiNodeStyle: true,
  defaultZoomLevel: 1.0,
  
  debugMode: false,
};

// 节点数据接口
export interface NodeData {
  id: string;
  content: string;
  level: number;
  type: 'heading' | 'list' | 'code' | 'ai-generated';
  children: NodeData[];
  position?: {
    line: number;
    ch: number;
  };
  metadata?: {
    isAiGenerated?: boolean;
    aiGeneratedAt?: Date;
    originalQuestion?: string;
  };
}

// 文档位置接口
export interface Position {
  line: number;
  ch: number;
}

// 同步事件接口
export interface SyncEvent {
  type: 'markdown-change' | 'mindmap-change';
  source: 'user' | 'system';
  changes: any[];
  timestamp: Date;
}

// AI请求接口
export interface AIRequest {
  question: string;
  context: string;
  selectedText: string;
  position: Position;
}

// AI响应接口
export interface AIResponse {
  answer: string;
  duration: number;
  model: string;
  timestamp: Date;
}

// 错误类型
export interface PluginError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// 事件类型
export type EventType = 
  | 'markdown:change'
  | 'mindmap:node:change'
  | 'sync:start'
  | 'sync:complete'
  | 'sync:error'
  | 'ai:request:start'
  | 'ai:request:complete'
  | 'ai:request:error';

// 事件数据接口
export interface EventData {
  [key: string]: any;
}

// 思维导图API接口
export interface MindMapAPI {
  render(markdown: string): void;
  updateNode(nodeId: string, data: Partial<NodeData>): void;
  addNode(parentId: string, data: NodeData): string;
  deleteNode(nodeId: string): void;
  moveNode(nodeId: string, targetParentId: string): void;
  getNodeByPosition(pos: Position): NodeData | null;
  getPositionByNode(nodeId: string): Position | null;
  setNodeChangeCallback(callback: (nodeId: string, changes: any) => void): void;
  fit(): void;
  destroy(): void;
}

// 同步控制API接口
export interface SyncAPI {
  enableSync(enable: boolean): void;
  isSyncEnabled(): boolean;
  triggerSync(from: 'markdown' | 'mindmap'): Promise<void>;
  pauseSync(): void;
  resumeSync(): void;
  setDebounceTime(ms: number): void;
}

// AI服务API接口
export interface AIAPI {
  setProvider(provider: 'openai' | 'ollama'): void;
  configure(settings: Partial<MindMapSyncSettings>): Promise<boolean>;
  isConfigured(): boolean;
  ask(question: string, context: string): Promise<string>;
  cancelPendingRequest(): void;
}
