/* 思维导图插件样式 */

/* 容器样式 */
.mindmap-container {
    width: 100%;
    height: 100%;
    overflow: auto;
    position: relative;
    background-color: var(--background-primary);
}

/* 节点样式 */
.simple-mindmap-node {
    padding: 10px 15px;
    border: 2px solid var(--background-modifier-border);
    border-radius: 8px;
    background: var(--background-primary);
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
    min-width: 100px;
    text-align: center;
    outline: none;
}

.simple-mindmap-node:hover {
    border-color: var(--text-accent-hover);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.simple-mindmap-node[data-selected="true"] {
    border-color: var(--text-accent);
    color: var(--text-accent);
}

/* 编辑输入框样式 */
.mindmap-node-input,
.simple-mindmap-input {
    font-family: var(--font-text);
    background: var(--background-primary);
    border: 2px solid var(--text-accent);
    border-radius: 4px;
    padding: 4px 8px;
    color: var(--text-normal);
    outline: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 同步状态指示器样式 */
.mindmap-sync-status {
    position: absolute;
    bottom: 10px;
    right: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.mindmap-sync-status.visible {
    opacity: 1;
}

.mindmap-sync-status.success {
    background-color: var(--background-modifier-success);
    color: var(--text-on-accent);
}

.mindmap-sync-status.error {
    background-color: var(--background-modifier-error);
    color: var(--text-on-accent);
}

.mindmap-sync-status.info {
    background-color: var(--background-modifier-border);
    color: var(--text-normal);
}

/* SVG 样式 */
.mindmap-container svg {
    width: 100%;
    height: 100%;
}

/* Markmap 节点样式覆盖 */
.markmap-node {
    cursor: pointer;
}

.markmap-node-text {
    fill: var(--text-normal);
    font-family: var(--font-text);
}

.markmap-node-circle {
    stroke: var(--background-modifier-border);
    stroke-width: 1.5px;
}

.markmap-node-line {
    stroke: var(--background-modifier-border);
    stroke-width: 1.5px;
}

/* 选中节点样式 */
.markmap-node.selected .markmap-node-text {
    fill: var(--text-accent);
    font-weight: bold;
}

.markmap-node.selected .markmap-node-circle {
    stroke: var(--text-accent);
    stroke-width: 2px;
}

/* 子节点容器样式 */
.children-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
    justify-content: center;
}
