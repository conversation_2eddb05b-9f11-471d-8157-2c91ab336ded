# API 参考文档

## 🔌 插件主类 API

### MindMapPlugin

#### 属性
```typescript
class MindMapPlugin extends Plugin {
    private transformer: Transformer;           // Markmap转换器
    private mindmap: Markmap | null;            // 思维导图实例
    private rootNode: MindMapNode | null;       // 根节点数据
    private currentNode: MindMapNode | null;    // 当前节点
    private editingNode: MindMapNode | null;    // 编辑中的节点
    private selectedNode: MindMapNode | null;   // 选中的节点
}
```

#### 核心方法

##### `onload(): Promise<void>`
**描述**：插件加载时的初始化方法
**功能**：
- 初始化 Markmap 转换器
- 注册思维导图视图
- 添加命令和快捷键
- 设置事件监听器

```typescript
async onload() {
    this.transformer = new Transformer();
    this.registerView(MIND_MAP_VIEW_TYPE, (leaf) => new MindMapView(leaf, this));
    this.addCommands();
    this.setupEventListeners();
}
```

##### `toggleMindMapMarkdown(): Promise<void>`
**描述**：切换 Markdown 和思维导图视图
**功能**：
- 检测当前视图类型
- 创建或关闭思维导图视图
- 处理视图间的数据同步

```typescript
async toggleMindMapMarkdown() {
    const activeLeaf = this.app.workspace.getActiveViewOfType(ItemView)?.leaf;
    const currentView = activeLeaf.view;
    
    if (currentView.getViewType() === MIND_MAP_VIEW_TYPE) {
        await this.closeMindMapView(activeLeaf);
    } else if (currentView.getViewType() === 'markdown') {
        await this.createMindMapPreview();
    }
}
```

##### `parseMarkdownToMindMap(content: string, title: string): MindMapNode | null`
**描述**：解析 Markdown 内容为思维导图数据
**参数**：
- `content`: Markdown 文本内容
- `title`: 文档标题
**返回值**：解析后的思维导图根节点或 null

```typescript
parseMarkdownToMindMap(content: string, title: string): MindMapNode | null {
    const lines = content.split('\n');
    let rootNode: MindMapNode | null = null;
    const nodeStack: NodeStackItem[] = [];
    
    // 解析逻辑
    for (const line of lines) {
        // 处理标题和列表项
    }
    
    return rootNode;
}
```

##### `renderMindMap(): Promise<void>`
**描述**：渲染思维导图到视图容器
**功能**：
- 计算容器尺寸
- 创建 SVG 元素
- 初始化 Markmap 实例
- 设置事件监听器

```typescript
async renderMindMap(): Promise<void> {
    const view = this.app.workspace.getActiveViewOfType(MindMapView);
    if (!view || !this.rootNode) return;
    
    const container = view.containerEl.querySelector('.mindmap-container');
    const { width, height } = this.calculateContainerSize(container);
    
    // 创建和配置 SVG
    const svg = d3.select(container).append('svg');
    this.mindmap = Markmap.create(svg.node(), options);
    
    // 渲染数据
    const markdown = this.mindmapNodeToMarkdown(this.rootNode);
    const { root: data } = this.transformer.transform(markdown);
    this.mindmap.setData(data);
}
```

## 🖼️ 视图类 API

### MindMapView

#### 属性
```typescript
class MindMapView extends ItemView {
    plugin: MindMapPlugin;                      // 插件实例引用
    sourceFilePath: string | null;              // 源文件路径
    fileWatcher: any;                           // 文件监听器
}
```

#### 核心方法

##### `getViewType(): string`
**描述**：获取视图类型标识符
**返回值**：视图类型字符串

```typescript
getViewType(): string {
    return MIND_MAP_VIEW_TYPE;
}
```

##### `getDisplayText(): string`
**描述**：获取视图显示名称
**返回值**：显示在标签页的文本

```typescript
getDisplayText(): string {
    return "思维导图";
}
```

##### `onOpen(): Promise<void>`
**描述**：视图打开时的初始化
**功能**：
- 创建容器元素
- 初始化思维导图
- 设置文件监听器

```typescript
async onOpen(): Promise<void> {
    const container = this.containerEl.children[1];
    container.empty();
    
    const mindmapContainer = container.createDiv('mindmap-container');
    // 设置容器样式和初始化
}
```

##### `setupFileWatcher(filePath: string): void`
**描述**：设置文件变更监听器
**参数**：
- `filePath`: 要监听的文件路径

```typescript
setupFileWatcher(filePath: string): void {
    if (this.fileWatcher) {
        this.app.vault.offref(this.fileWatcher);
    }
    
    this.sourceFilePath = filePath;
    this.fileWatcher = this.app.vault.on('modify', async (file) => {
        if (file.path === this.sourceFilePath) {
            await this.updateFromSourceFile(file);
        }
    });
}
```

## 📊 数据结构 API

### MindMapNode

#### 接口定义
```typescript
interface MindMapNode {
    id: string;                    // 节点唯一标识符
    content: string;               // 节点显示内容
    children: MindMapNode[];       // 子节点数组
    parent?: MindMapNode;          // 父节点引用（可选）
    isSelected?: boolean;          // 是否被选中
    isExpanded?: boolean;          // 是否展开子节点
}
```

#### 使用示例
```typescript
const node: MindMapNode = {
    id: 'node_1',
    content: '主题',
    children: [
        {
            id: 'node_2',
            content: '子主题1',
            children: [],
            parent: node,
            isExpanded: true
        }
    ],
    isSelected: true,
    isExpanded: true
};
```

### MindMapViewState

#### 接口定义
```typescript
interface MindMapViewState {
    [key: string]: unknown;
    data?: MindMapNode;           // 思维导图数据
    selectedNodeId?: string;      // 选中节点ID
    sourceFile?: string;          // 源文件路径
}
```

#### 使用示例
```typescript
const viewState: MindMapViewState = {
    data: rootNode,
    selectedNodeId: 'node_1',
    sourceFile: '/path/to/source.md'
};
```

## 🛠️ 工具函数 API

### 节点操作工具

#### `createNode(content: string, parent?: MindMapNode): MindMapNode`
**描述**：创建新的思维导图节点
**参数**：
- `content`: 节点内容
- `parent`: 父节点（可选）
**返回值**：新创建的节点

```typescript
function createNode(content: string, parent?: MindMapNode): MindMapNode {
    return {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        content: content,
        children: [],
        parent: parent,
        isExpanded: true
    };
}
```

#### `findNode(root: MindMapNode, id: string): MindMapNode | null`
**描述**：在思维导图中查找指定ID的节点
**参数**：
- `root`: 根节点
- `id`: 要查找的节点ID
**返回值**：找到的节点或 null

```typescript
function findNode(root: MindMapNode, id: string): MindMapNode | null {
    if (root.id === id) return root;
    
    for (const child of root.children) {
        const found = findNode(child, id);
        if (found) return found;
    }
    
    return null;
}
```

#### `addChildNode(parent: MindMapNode, content: string): MindMapNode`
**描述**：为指定节点添加子节点
**参数**：
- `parent`: 父节点
- `content`: 子节点内容
**返回值**：新创建的子节点

```typescript
function addChildNode(parent: MindMapNode, content: string): MindMapNode {
    const newNode = createNode(content, parent);
    parent.children.push(newNode);
    parent.isExpanded = true;
    return newNode;
}
```

### 文件同步工具

#### `syncToSourceFile(app: App, rootNode: MindMapNode, filePath: string): Promise<boolean>`
**描述**：将思维导图数据同步到源文件
**参数**：
- `app`: Obsidian App 实例
- `rootNode`: 思维导图根节点
- `filePath`: 目标文件路径
**返回值**：同步是否成功

```typescript
async function syncToSourceFile(
    app: App, 
    rootNode: MindMapNode, 
    filePath: string
): Promise<boolean> {
    try {
        const file = app.vault.getAbstractFileByPath(filePath);
        if (!file || !(file instanceof TFile)) return false;
        
        const markdownContent = generateMarkdownExport(rootNode);
        await app.vault.modify(file, markdownContent);
        
        return true;
    } catch (error) {
        console.error('Sync failed:', error);
        return false;
    }
}
```

## 🎨 样式 API

### CSS 类名

#### 容器类
- `.mindmap-container` - 主容器
- `.mindmap-loading` - 加载状态
- `.mindmap-error` - 错误状态

#### 节点类
- `.markmap-node` - 思维导图节点
- `.selected-node` - 选中的节点
- `.editing-node` - 编辑中的节点

#### 输入框类
- `.mindmap-node-input` - 节点编辑输入框

### CSS 变量

#### 颜色变量
```css
:root {
    --mindmap-node-color: var(--text-normal);
    --mindmap-link-color: var(--text-muted);
    --mindmap-selected-color: var(--interactive-accent);
    --mindmap-background: var(--background-primary);
}
```

#### 尺寸变量
```css
:root {
    --mindmap-node-font-size: 14px;
    --mindmap-node-padding: 8px;
    --mindmap-container-padding: 20px;
    --mindmap-input-z-index: 9999;
}
```

## 🔧 配置 API

### 插件配置

#### Markmap 选项
```typescript
interface MarkmapOptions {
    autoFit: boolean;              // 自动适应容器大小
    duration: number;              // 动画持续时间
    maxWidth: number;              // 最大宽度
    initialExpandLevel: number;    // 初始展开层级
    spacingVertical: number;       // 垂直间距
    spacingHorizontal: number;     // 水平间距
    paddingX: number;              // X轴内边距
}
```

#### 容器配置
```typescript
interface ContainerConfig {
    DEFAULT_WIDTH: number;         // 默认宽度
    DEFAULT_HEIGHT: number;        // 默认高度
    MIN_WIDTH: number;             // 最小宽度
    MIN_HEIGHT: number;            // 最小高度
    PADDING: number;               // 内边距
}
```

## 📝 事件 API

### 自定义事件

#### 节点选择事件
```typescript
// 触发节点选择事件
this.trigger('node-selected', { nodeId: 'node_1' });

// 监听节点选择事件
this.on('node-selected', (data) => {
    console.log('Node selected:', data.nodeId);
});
```

#### 数据更新事件
```typescript
// 触发数据更新事件
this.trigger('data-updated', { rootNode: this.rootNode });

// 监听数据更新事件
this.on('data-updated', (data) => {
    this.refreshView();
});
```

## 🐛 错误处理 API

### 错误类型

#### ParseError
```typescript
class ParseError extends Error {
    constructor(message: string, line?: number) {
        super(message);
        this.name = 'ParseError';
        this.line = line;
    }
}
```

#### RenderError
```typescript
class RenderError extends Error {
    constructor(message: string, cause?: Error) {
        super(message);
        this.name = 'RenderError';
        this.cause = cause;
    }
}
```

### 错误处理函数

#### `handleError(error: Error, context: string): void`
**描述**：统一的错误处理函数
**参数**：
- `error`: 错误对象
- `context`: 错误上下文

```typescript
function handleError(error: Error, context: string): void {
    console.error(`[${context}] ${error.message}`, error);
    
    if (error instanceof ParseError) {
        new Notice(`解析错误: ${error.message}`);
    } else if (error instanceof RenderError) {
        new Notice(`渲染错误: ${error.message}`);
    } else {
        new Notice(`未知错误: ${error.message}`);
    }
}
```
