/**
 * vis-network
 * https://visjs.github.io/vis-network/
 *
 * A dynamic, browser-based visualization library.
 *
 * @version 10.0.1
 * @date    2025-07-13T08:15:32.270Z
 *
 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
 *
 * @license
 * vis.js is dual licensed under both
 *
 *   1. The Apache 2.0 License
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *   and
 *
 *   2. The MIT License
 *      http://opensource.org/licenses/MIT
 *
 * vis.js may be distributed under either license.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("vis-data/peer/umd/vis-data.js")):"function"==typeof define&&define.amd?define(["exports","vis-data/peer/umd/vis-data.js"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).vis=t.vis||{},t.vis)}(this,function(t,e){var i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var n,s,r,a,h,d,l,c,u,p,f,g,v,m,y,b,w={};function _(){if(s)return n;s=1;var t=function(t){return t&&t.Math===Math&&t};return n=t("object"==typeof globalThis&&globalThis)||t("object"==typeof window&&window)||t("object"==typeof self&&self)||t("object"==typeof i&&i)||t("object"==typeof n&&n)||function(){return this}()||Function("return this")()}function x(){return a?r:(a=1,r=function(t){try{return!!t()}catch(t){return!0}})}function E(){return d?h:(d=1,h=!x()(function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))}function O(){if(c)return l;c=1;var t=E(),e=Function.prototype,i=e.apply,o=e.call;return l="object"==typeof Reflect&&Reflect.apply||(t?o.bind(i):function(){return o.apply(i,arguments)}),l}function C(){if(p)return u;p=1;var t=E(),e=Function.prototype,i=e.call,o=t&&e.bind.bind(i,i);return u=t?o:function(t){return function(){return i.apply(t,arguments)}},u}function k(){if(g)return f;g=1;var t=C(),e=t({}.toString),i=t("".slice);return f=function(t){return i(e(t),8,-1)},f}function S(){if(m)return v;m=1;var t=k(),e=C();return v=function(i){if("Function"===t(i))return e(i)}}function T(){if(b)return y;b=1;var t="object"==typeof document&&document.all;return y=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(t){return"function"==typeof t}}var M,D,I,P,B={};function z(){return D?M:(D=1,M=!x()(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))}function F(){if(P)return I;P=1;var t=E(),e=Function.prototype.call;return I=t?e.bind(e):function(){return e.apply(e,arguments)},I}var N,A,R,j,L,H,W,V,q,U,Y,X,K,G,Z,Q,$,J,tt,et,it,ot,nt,st,rt,at,ht,dt,lt,ct,ut,pt,ft,gt,vt,mt,yt,bt={};function wt(){if(N)return bt;N=1;var t={}.propertyIsEnumerable,e=Object.getOwnPropertyDescriptor,i=e&&!t.call({1:2},1);return bt.f=i?function(t){var i=e(this,t);return!!i&&i.enumerable}:t,bt}function _t(){return R?A:(R=1,A=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}})}function xt(){if(L)return j;L=1;var t=C(),e=x(),i=k(),o=Object,n=t("".split);return j=e(function(){return!o("z").propertyIsEnumerable(0)})?function(t){return"String"===i(t)?n(t,""):o(t)}:o,j}function Et(){return W||(W=1,H=function(t){return null==t}),H}function Ot(){if(q)return V;q=1;var t=Et(),e=TypeError;return V=function(i){if(t(i))throw new e("Can't call method on "+i);return i},V}function Ct(){if(Y)return U;Y=1;var t=xt(),e=Ot();return U=function(i){return t(e(i))},U}function kt(){if(K)return X;K=1;var t=T();return X=function(e){return"object"==typeof e?null!==e:t(e)},X}function St(){return Z?G:(Z=1,G={})}function Tt(){if($)return Q;$=1;var t=St(),e=_(),i=T(),o=function(t){return i(t)?t:void 0};return Q=function(i,n){return arguments.length<2?o(t[i])||o(e[i]):t[i]&&t[i][n]||e[i]&&e[i][n]},Q}function Mt(){return tt?J:(tt=1,J=C()({}.isPrototypeOf))}function Dt(){if(it)return et;it=1;var t=_().navigator,e=t&&t.userAgent;return et=e?String(e):""}function It(){if(nt)return ot;nt=1;var t,e,i=_(),o=Dt(),n=i.process,s=i.Deno,r=n&&n.versions||s&&s.version,a=r&&r.v8;return a&&(e=(t=a.split("."))[0]>0&&t[0]<4?1:+(t[0]+t[1])),!e&&o&&(!(t=o.match(/Edge\/(\d+)/))||t[1]>=74)&&(t=o.match(/Chrome\/(\d+)/))&&(e=+t[1]),ot=e}function Pt(){if(rt)return st;rt=1;var t=It(),e=x(),i=_().String;return st=!!Object.getOwnPropertySymbols&&!e(function(){var e=Symbol("symbol detection");return!i(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&t&&t<41}),st}function Bt(){return ht?at:(ht=1,at=Pt()&&!Symbol.sham&&"symbol"==typeof Symbol.iterator)}function zt(){if(lt)return dt;lt=1;var t=Tt(),e=T(),i=Mt(),o=Object;return dt=Bt()?function(t){return"symbol"==typeof t}:function(n){var s=t("Symbol");return e(s)&&i(s.prototype,o(n))},dt}function Ft(){if(ut)return ct;ut=1;var t=String;return ct=function(e){try{return t(e)}catch(t){return"Object"}}}function Nt(){if(ft)return pt;ft=1;var t=T(),e=Ft(),i=TypeError;return pt=function(o){if(t(o))return o;throw new i(e(o)+" is not a function")}}function At(){if(vt)return gt;vt=1;var t=Nt(),e=Et();return gt=function(i,o){var n=i[o];return e(n)?void 0:t(n)}}function Rt(){if(yt)return mt;yt=1;var t=F(),e=T(),i=kt(),o=TypeError;return mt=function(n,s){var r,a;if("string"===s&&e(r=n.toString)&&!i(a=t(r,n)))return a;if(e(r=n.valueOf)&&!i(a=t(r,n)))return a;if("string"!==s&&e(r=n.toString)&&!i(a=t(r,n)))return a;throw new o("Can't convert object to primitive value")}}var jt,Lt,Ht,Wt,Vt,qt,Ut,Yt,Xt,Kt,Gt,Zt,Qt,$t,Jt,te,ee,ie,oe,ne,se,re,ae,he,de,le,ce,ue,pe={exports:{}};function fe(){return Lt?jt:(Lt=1,jt=!0)}function ge(){if(Wt)return Ht;Wt=1;var t=_(),e=Object.defineProperty;return Ht=function(i,o){try{e(t,i,{value:o,configurable:!0,writable:!0})}catch(e){t[i]=o}return o}}function ve(){if(Vt)return pe.exports;Vt=1;var t=fe(),e=_(),i=ge(),o="__core-js_shared__",n=pe.exports=e[o]||i(o,{});return(n.versions||(n.versions=[])).push({version:"3.44.0",mode:t?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.44.0/LICENSE",source:"https://github.com/zloirock/core-js"}),pe.exports}function me(){if(Ut)return qt;Ut=1;var t=ve();return qt=function(e,i){return t[e]||(t[e]=i||{})}}function ye(){if(Xt)return Yt;Xt=1;var t=Ot(),e=Object;return Yt=function(i){return e(t(i))}}function be(){if(Gt)return Kt;Gt=1;var t=C(),e=ye(),i=t({}.hasOwnProperty);return Kt=Object.hasOwn||function(t,o){return i(e(t),o)},Kt}function we(){if(Qt)return Zt;Qt=1;var t=C(),e=0,i=Math.random(),o=t(1.1.toString);return Zt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+o(++e+i,36)}}function _e(){if(Jt)return $t;Jt=1;var t=_(),e=me(),i=be(),o=we(),n=Pt(),s=Bt(),r=t.Symbol,a=e("wks"),h=s?r.for||r:r&&r.withoutSetter||o;return $t=function(t){return i(a,t)||(a[t]=n&&i(r,t)?r[t]:h("Symbol."+t)),a[t]}}function xe(){if(ee)return te;ee=1;var t=F(),e=kt(),i=zt(),o=At(),n=Rt(),s=TypeError,r=_e()("toPrimitive");return te=function(a,h){if(!e(a)||i(a))return a;var d,l=o(a,r);if(l){if(void 0===h&&(h="default"),d=t(l,a,h),!e(d)||i(d))return d;throw new s("Can't convert object to primitive value")}return void 0===h&&(h="number"),n(a,h)}}function Ee(){if(oe)return ie;oe=1;var t=xe(),e=zt();return ie=function(i){var o=t(i,"string");return e(o)?o:o+""}}function Oe(){if(se)return ne;se=1;var t=_(),e=kt(),i=t.document,o=e(i)&&e(i.createElement);return ne=function(t){return o?i.createElement(t):{}},ne}function Ce(){if(ae)return re;ae=1;var t=z(),e=x(),i=Oe();return re=!t&&!e(function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a})}function ke(){if(he)return B;he=1;var t=z(),e=F(),i=wt(),o=_t(),n=Ct(),s=Ee(),r=be(),a=Ce(),h=Object.getOwnPropertyDescriptor;return B.f=t?h:function(t,d){if(t=n(t),d=s(d),a)try{return h(t,d)}catch(t){}if(r(t,d))return o(!e(i.f,t,d),t[d])},B}function Se(){if(le)return de;le=1;var t=x(),e=T(),i=/#|\.prototype\./,o=function(i,o){var h=s[n(i)];return h===a||h!==r&&(e(o)?t(o):!!o)},n=o.normalize=function(t){return String(t).replace(i,".").toLowerCase()},s=o.data={},r=o.NATIVE="N",a=o.POLYFILL="P";return de=o}function Te(){if(ue)return ce;ue=1;var t=S(),e=Nt(),i=E(),o=t(t.bind);return ce=function(t,n){return e(t),void 0===n?t:i?o(t,n):function(){return t.apply(n,arguments)}},ce}var Me,De,Ie,Pe,Be,ze,Fe,Ne,Ae,Re,je,Le,He,We,Ve,qe,Ue,Ye,Xe,Ke,Ge,Ze,Qe,$e,Je,ti,ei,ii,oi,ni={};function si(){return De?Me:(De=1,Me=z()&&x()(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype}))}function ri(){if(Pe)return Ie;Pe=1;var t=kt(),e=String,i=TypeError;return Ie=function(o){if(t(o))return o;throw new i(e(o)+" is not an object")}}function ai(){if(Be)return ni;Be=1;var t=z(),e=Ce(),i=si(),o=ri(),n=Ee(),s=TypeError,r=Object.defineProperty,a=Object.getOwnPropertyDescriptor,h="enumerable",d="configurable",l="writable";return ni.f=t?i?function(t,e,i){if(o(t),e=n(e),o(i),"function"==typeof t&&"prototype"===e&&"value"in i&&l in i&&!i[l]){var s=a(t,e);s&&s[l]&&(t[e]=i.value,i={configurable:d in i?i[d]:s[d],enumerable:h in i?i[h]:s[h],writable:!1})}return r(t,e,i)}:r:function(t,i,a){if(o(t),i=n(i),o(a),e)try{return r(t,i,a)}catch(t){}if("get"in a||"set"in a)throw new s("Accessors not supported");return"value"in a&&(t[i]=a.value),t},ni}function hi(){if(Fe)return ze;Fe=1;var t=z(),e=ai(),i=_t();return ze=t?function(t,o,n){return e.f(t,o,i(1,n))}:function(t,e,i){return t[e]=i,t},ze}function di(){if(Ae)return Ne;Ae=1;var t=_(),e=O(),i=S(),o=T(),n=ke().f,s=Se(),r=St(),a=Te(),h=hi(),d=be(),l=function(t){var i=function(o,n,s){if(this instanceof i){switch(arguments.length){case 0:return new t;case 1:return new t(o);case 2:return new t(o,n)}return new t(o,n,s)}return e(t,this,arguments)};return i.prototype=t.prototype,i};return Ne=function(e,c){var u,p,f,g,v,m,y,b,w,_=e.target,x=e.global,E=e.stat,O=e.proto,C=x?t:E?t[_]:t[_]&&t[_].prototype,k=x?r:r[_]||h(r,_,{})[_],S=k.prototype;for(g in c)p=!(u=s(x?g:_+(E?".":"#")+g,e.forced))&&C&&d(C,g),m=k[g],p&&(y=e.dontCallGetSet?(w=n(C,g))&&w.value:C[g]),v=p&&y?y:c[g],(u||O||typeof m!=typeof v)&&(b=e.bind&&p?a(v,t):e.wrap&&p?l(v):O&&o(v)?i(v):v,(e.sham||v&&v.sham||m&&m.sham)&&h(b,"sham",!0),h(k,g,b),O&&(d(r,f=_+"Prototype")||h(r,f,{}),h(r[f],g,v),e.real&&S&&(u||!S[g])&&h(S,g,v)))},Ne}function li(){if(je)return Re;je=1;var t=Math.ceil,e=Math.floor;return Re=Math.trunc||function(i){var o=+i;return(o>0?e:t)(o)}}function ci(){if(He)return Le;He=1;var t=li();return Le=function(e){var i=+e;return i!=i||0===i?0:t(i)},Le}function ui(){if(Ve)return We;Ve=1;var t=ci(),e=Math.max,i=Math.min;return We=function(o,n){var s=t(o);return s<0?e(s+n,0):i(s,n)},We}function pi(){if(Ue)return qe;Ue=1;var t=ci(),e=Math.min;return qe=function(i){var o=t(i);return o>0?e(o,9007199254740991):0}}function fi(){if(Xe)return Ye;Xe=1;var t=pi();return Ye=function(e){return t(e.length)}}function gi(){if(Ge)return Ke;Ge=1;var t=Ct(),e=ui(),i=fi(),o=function(o){return function(n,s,r){var a=t(n),h=i(a);if(0===h)return!o&&-1;var d,l=e(r,h);if(o&&s!=s){for(;h>l;)if((d=a[l++])!=d)return!0}else for(;h>l;l++)if((o||l in a)&&a[l]===s)return o||l||0;return!o&&-1}};return Ke={includes:o(!0),indexOf:o(!1)}}function vi(){return Qe?Ze:(Qe=1,Ze={})}function mi(){if(Je)return $e;Je=1;var t=C(),e=be(),i=Ct(),o=gi().indexOf,n=vi(),s=t([].push);return $e=function(t,r){var a,h=i(t),d=0,l=[];for(a in h)!e(n,a)&&e(h,a)&&s(l,a);for(;r.length>d;)e(h,a=r[d++])&&(~o(l,a)||s(l,a));return l},$e}function yi(){return ei?ti:(ei=1,ti=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"])}function bi(){if(oi)return ii;oi=1;var t=mi(),e=yi();return ii=Object.keys||function(i){return t(i,e)}}var wi,_i,xi,Ei,Oi,Ci,ki,Si,Ti,Mi,Di={};function Ii(){return wi||(wi=1,Di.f=Object.getOwnPropertySymbols),Di}function Pi(){if(xi)return _i;xi=1;var t=z(),e=C(),i=F(),o=x(),n=bi(),s=Ii(),r=wt(),a=ye(),h=xt(),d=Object.assign,l=Object.defineProperty,c=e([].concat);return _i=!d||o(function(){if(t&&1!==d({b:1},d(l({},"a",{enumerable:!0,get:function(){l(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},i={},o=Symbol("assign detection"),s="abcdefghijklmnopqrst";return e[o]=7,s.split("").forEach(function(t){i[t]=t}),7!==d({},e)[o]||n(d({},i)).join("")!==s})?function(e,o){for(var d=a(e),l=arguments.length,u=1,p=s.f,f=r.f;l>u;)for(var g,v=h(arguments[u++]),m=p?c(n(v),p(v)):n(v),y=m.length,b=0;y>b;)g=m[b++],t&&!i(f,v,g)||(d[g]=v[g]);return d}:d,_i}function Bi(){return Ci?Oi:(Ci=1,function(){if(Ei)return w;Ei=1;var t=di(),e=Pi();t({target:"Object",stat:!0,arity:2,forced:Object.assign!==e},{assign:e})}(),Oi=St().Object.assign)}function zi(){return Si?ki:(Si=1,ki=Bi())}var Fi,Ni,Ai,Ri,ji,Li,Hi,Wi,Vi,qi,Ui,Yi,Xi,Ki,Gi,Zi=o(Mi?Ti:(Mi=1,Ti=zi())),Qi={};function $i(){return Ni?Fi:(Ni=1,Fi=C()([].slice))}function Ji(){if(Ri)return Ai;Ri=1;var t=C(),e=Nt(),i=kt(),o=be(),n=$i(),s=E(),r=Function,a=t([].concat),h=t([].join),d={};return Ai=s?r.bind:function(t){var s=e(this),l=s.prototype,c=n(arguments,1),u=function(){var e=a(c,n(arguments));return this instanceof u?function(t,e,i){if(!o(d,e)){for(var n=[],s=0;s<e;s++)n[s]="a["+s+"]";d[e]=r("C,a","return new C("+h(n,",")+")")}return d[e](t,i)}(s,e.length,e):s.apply(t,e)};return i(l)&&(u.prototype=l),u},Ai}function to(){if(Hi)return Li;Hi=1;var t=_(),e=St();return Li=function(i,o){var n=e[i+"Prototype"],s=n&&n[o];if(s)return s;var r=t[i],a=r&&r.prototype;return a&&a[o]}}function eo(){return Vi?Wi:(Vi=1,function(){if(ji)return Qi;ji=1;var t=di(),e=Ji();t({target:"Function",proto:!0,forced:Function.bind!==e},{bind:e})}(),Wi=to()("Function","bind"))}function io(){if(Ui)return qi;Ui=1;var t=Mt(),e=eo(),i=Function.prototype;return qi=function(o){var n=o.bind;return o===i||t(i,o)&&n===i.bind?e:n},qi}function oo(){return Xi?Yi:(Xi=1,Yi=io())}var no=o(Gi?Ki:(Gi=1,Ki=oo()));function so(t,e,i,o){t.beginPath(),t.arc(e,i,o,0,2*Math.PI,!1),t.closePath()}function ro(t,e,i,o,n,s){const r=Math.PI/180;o-2*s<0&&(s=o/2),n-2*s<0&&(s=n/2),t.beginPath(),t.moveTo(e+s,i),t.lineTo(e+o-s,i),t.arc(e+o-s,i+s,s,270*r,360*r,!1),t.lineTo(e+o,i+n-s),t.arc(e+o-s,i+n-s,s,0,90*r,!1),t.lineTo(e+s,i+n),t.arc(e+s,i+n-s,s,90*r,180*r,!1),t.lineTo(e,i+s),t.arc(e+s,i+s,s,180*r,270*r,!1),t.closePath()}function ao(t,e,i,o,n){const s=.5522848,r=o/2*s,a=n/2*s,h=e+o,d=i+n,l=e+o/2,c=i+n/2;t.beginPath(),t.moveTo(e,c),t.bezierCurveTo(e,c-a,l-r,i,l,i),t.bezierCurveTo(l+r,i,h,c-a,h,c),t.bezierCurveTo(h,c+a,l+r,d,l,d),t.bezierCurveTo(l-r,d,e,c+a,e,c),t.closePath()}function ho(t,e,i,o,n){const s=n*(1/3),r=.5522848,a=o/2*r,h=s/2*r,d=e+o,l=i+s,c=e+o/2,u=i+s/2,p=i+(n-s/2),f=i+n;t.beginPath(),t.moveTo(d,u),t.bezierCurveTo(d,u+h,c+a,l,c,l),t.bezierCurveTo(c-a,l,e,u+h,e,u),t.bezierCurveTo(e,u-h,c-a,i,c,i),t.bezierCurveTo(c+a,i,d,u-h,d,u),t.lineTo(d,p),t.bezierCurveTo(d,p+h,c+a,f,c,f),t.bezierCurveTo(c-a,f,e,p+h,e,p),t.lineTo(e,u)}function lo(t,e,i,o,n,s){t.beginPath(),t.moveTo(e,i);const r=s.length,a=o-e,h=n-i,d=h/a;let l=Math.sqrt(a*a+h*h),c=0,u=!0,p=0,f=+s[0];for(;l>=.1;)f=+s[c++%r],f>l&&(f=l),p=Math.sqrt(f*f/(1+d*d)),p=a<0?-p:p,e+=p,i+=d*p,!0===u?t.lineTo(e,i):t.moveTo(e,i),l-=f,u=!u}const co={circle:so,dashedLine:lo,database:ho,diamond:function(t,e,i,o){t.beginPath(),t.lineTo(e,i+o),t.lineTo(e+o,i),t.lineTo(e,i-o),t.lineTo(e-o,i),t.closePath()},ellipse:ao,ellipse_vis:ao,hexagon:function(t,e,i,o){t.beginPath();const n=2*Math.PI/6;t.moveTo(e+o,i);for(let s=1;s<6;s++)t.lineTo(e+o*Math.cos(n*s),i+o*Math.sin(n*s));t.closePath()},roundRect:ro,square:function(t,e,i,o){t.beginPath(),t.rect(e-o,i-o,2*o,2*o),t.closePath()},star:function(t,e,i,o){t.beginPath(),i+=.1*(o*=.82);for(let n=0;n<10;n++){const s=n%2==0?1.3*o:.5*o;t.lineTo(e+s*Math.sin(2*n*Math.PI/10),i-s*Math.cos(2*n*Math.PI/10))}t.closePath()},triangle:function(t,e,i,o){t.beginPath(),i+=.275*(o*=1.15);const n=2*o,s=n/2,r=Math.sqrt(3)/6*n,a=Math.sqrt(n*n-s*s);t.moveTo(e,i-(a-r)),t.lineTo(e+s,i+r),t.lineTo(e-s,i+r),t.lineTo(e,i-(a-r)),t.closePath()},triangleDown:function(t,e,i,o){t.beginPath(),i-=.275*(o*=1.15);const n=2*o,s=n/2,r=Math.sqrt(3)/6*n,a=Math.sqrt(n*n-s*s);t.moveTo(e,i+(a-r)),t.lineTo(e+s,i-r),t.lineTo(e-s,i-r),t.lineTo(e,i+(a-r)),t.closePath()}};var uo,po={exports:{}};var fo,go=(uo||(uo=1,function(t){function e(t){if(t)return function(t){for(var i in e.prototype)t[i]=e.prototype[i];return t}(t)}t.exports=e,e.prototype.on=e.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},e.prototype.once=function(t,e){function i(){this.off(t,i),e.apply(this,arguments)}return i.fn=e,this.on(t,i),this},e.prototype.off=e.prototype.removeListener=e.prototype.removeAllListeners=e.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var i,o=this._callbacks["$"+t];if(!o)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var n=0;n<o.length;n++)if((i=o[n])===e||i.fn===e){o.splice(n,1);break}return 0===o.length&&delete this._callbacks["$"+t],this},e.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),i=this._callbacks["$"+t],o=1;o<arguments.length;o++)e[o-1]=arguments[o];if(i){o=0;for(var n=(i=i.slice(0)).length;o<n;++o)i[o].apply(this,e)}return this},e.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},e.prototype.hasListeners=function(t){return!!this.listeners(t).length}}(po)),po.exports),vo=o(go);
/*! Hammer.JS - v2.0.17-rc - 2019-12-16
	 * http://naver.github.io/egjs
	 *
	 * Forked By Naver egjs
	 * Copyright (c) hammerjs
	 * Licensed under the MIT license */
function mo(){return mo=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o])}return t},mo.apply(this,arguments)}function yo(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}function bo(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}fo="function"!=typeof Object.assign?function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),i=1;i<arguments.length;i++){var o=arguments[i];if(null!=o)for(var n in o)o.hasOwnProperty(n)&&(e[n]=o[n])}return e}:Object.assign;var wo,_o=fo,xo=["","webkit","Moz","MS","ms","o"],Eo="undefined"==typeof document?{style:{}}:document.createElement("div"),Oo=Math.round,Co=Math.abs,ko=Date.now;function So(t,e){for(var i,o,n=e[0].toUpperCase()+e.slice(1),s=0;s<xo.length;){if((o=(i=xo[s])?i+n:e)in t)return o;s++}}wo="undefined"==typeof window?{}:window;var To=So(Eo.style,"touchAction"),Mo=void 0!==To;var Do="compute",Io="auto",Po="manipulation",Bo="none",zo="pan-x",Fo="pan-y",No=function(){if(!Mo)return!1;var t={},e=wo.CSS&&wo.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach(function(i){return t[i]=!e||wo.CSS.supports("touch-action",i)}),t}(),Ao="ontouchstart"in wo,Ro=void 0!==So(wo,"PointerEvent"),jo=Ao&&/mobile|tablet|ip(ad|hone|od)|android/i.test(navigator.userAgent),Lo="touch",Ho="mouse",Wo=16,Vo=24,qo=["x","y"],Uo=["clientX","clientY"];function Yo(t,e,i){var o;if(t)if(t.forEach)t.forEach(e,i);else if(void 0!==t.length)for(o=0;o<t.length;)e.call(i,t[o],o,t),o++;else for(o in t)t.hasOwnProperty(o)&&e.call(i,t[o],o,t)}function Xo(t,e){return"function"==typeof t?t.apply(e&&e[0]||void 0,e):t}function Ko(t,e){return t.indexOf(e)>-1}var Go=function(){function t(t,e){this.manager=t,this.set(e)}var e=t.prototype;return e.set=function(t){t===Do&&(t=this.compute()),Mo&&this.manager.element.style&&No[t]&&(this.manager.element.style[To]=t),this.actions=t.toLowerCase().trim()},e.update=function(){this.set(this.manager.options.touchAction)},e.compute=function(){var t=[];return Yo(this.manager.recognizers,function(e){Xo(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))}),function(t){if(Ko(t,Bo))return Bo;var e=Ko(t,zo),i=Ko(t,Fo);return e&&i?Bo:e||i?e?zo:Fo:Ko(t,Po)?Po:Io}(t.join(" "))},e.preventDefaults=function(t){var e=t.srcEvent,i=t.offsetDirection;if(this.manager.session.prevented)e.preventDefault();else{var o=this.actions,n=Ko(o,Bo)&&!No[Bo],s=Ko(o,Fo)&&!No[Fo],r=Ko(o,zo)&&!No[zo];if(n){var a=1===t.pointers.length,h=t.distance<2,d=t.deltaTime<250;if(a&&h&&d)return}if(!r||!s)return n||s&&6&i||r&&i&Vo?this.preventSrc(e):void 0}},e.preventSrc=function(t){this.manager.session.prevented=!0,t.preventDefault()},t}();function Zo(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1}function Qo(t){var e=t.length;if(1===e)return{x:Oo(t[0].clientX),y:Oo(t[0].clientY)};for(var i=0,o=0,n=0;n<e;)i+=t[n].clientX,o+=t[n].clientY,n++;return{x:Oo(i/e),y:Oo(o/e)}}function $o(t){for(var e=[],i=0;i<t.pointers.length;)e[i]={clientX:Oo(t.pointers[i].clientX),clientY:Oo(t.pointers[i].clientY)},i++;return{timeStamp:ko(),pointers:e,center:Qo(e),deltaX:t.deltaX,deltaY:t.deltaY}}function Jo(t,e,i){i||(i=qo);var o=e[i[0]]-t[i[0]],n=e[i[1]]-t[i[1]];return Math.sqrt(o*o+n*n)}function tn(t,e,i){i||(i=qo);var o=e[i[0]]-t[i[0]],n=e[i[1]]-t[i[1]];return 180*Math.atan2(n,o)/Math.PI}function en(t,e){return t===e?1:Co(t)>=Co(e)?t<0?2:4:e<0?8:Wo}function on(t,e,i){return{x:e/t||0,y:i/t||0}}function nn(t,e){var i=t.session,o=e.pointers,n=o.length;i.firstInput||(i.firstInput=$o(e)),n>1&&!i.firstMultiple?i.firstMultiple=$o(e):1===n&&(i.firstMultiple=!1);var s=i.firstInput,r=i.firstMultiple,a=r?r.center:s.center,h=e.center=Qo(o);e.timeStamp=ko(),e.deltaTime=e.timeStamp-s.timeStamp,e.angle=tn(a,h),e.distance=Jo(a,h),function(t,e){var i=e.center,o=t.offsetDelta||{},n=t.prevDelta||{},s=t.prevInput||{};1!==e.eventType&&4!==s.eventType||(n=t.prevDelta={x:s.deltaX||0,y:s.deltaY||0},o=t.offsetDelta={x:i.x,y:i.y}),e.deltaX=n.x+(i.x-o.x),e.deltaY=n.y+(i.y-o.y)}(i,e),e.offsetDirection=en(e.deltaX,e.deltaY);var d,l,c=on(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=c.x,e.overallVelocityY=c.y,e.overallVelocity=Co(c.x)>Co(c.y)?c.x:c.y,e.scale=r?(d=r.pointers,Jo((l=o)[0],l[1],Uo)/Jo(d[0],d[1],Uo)):1,e.rotation=r?function(t,e){return tn(e[1],e[0],Uo)+tn(t[1],t[0],Uo)}(r.pointers,o):0,e.maxPointers=i.prevInput?e.pointers.length>i.prevInput.maxPointers?e.pointers.length:i.prevInput.maxPointers:e.pointers.length,function(t,e){var i,o,n,s,r=t.lastInterval||e,a=e.timeStamp-r.timeStamp;if(8!==e.eventType&&(a>25||void 0===r.velocity)){var h=e.deltaX-r.deltaX,d=e.deltaY-r.deltaY,l=on(a,h,d);o=l.x,n=l.y,i=Co(l.x)>Co(l.y)?l.x:l.y,s=en(h,d),t.lastInterval=e}else i=r.velocity,o=r.velocityX,n=r.velocityY,s=r.direction;e.velocity=i,e.velocityX=o,e.velocityY=n,e.direction=s}(i,e);var u,p=t.element,f=e.srcEvent;Zo(u=f.composedPath?f.composedPath()[0]:f.path?f.path[0]:f.target,p)&&(p=u),e.target=p}function sn(t,e,i){var o=i.pointers.length,n=i.changedPointers.length,s=1&e&&o-n===0,r=12&e&&o-n===0;i.isFirst=!!s,i.isFinal=!!r,s&&(t.session={}),i.eventType=e,nn(t,i),t.emit("hammer.input",i),t.recognize(i),t.session.prevInput=i}function rn(t){return t.trim().split(/\s+/g)}function an(t,e,i){Yo(rn(e),function(e){t.addEventListener(e,i,!1)})}function hn(t,e,i){Yo(rn(e),function(e){t.removeEventListener(e,i,!1)})}function dn(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||window}var ln=function(){function t(t,e){var i=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){Xo(t.options.enable,[t])&&i.handler(e)},this.init()}var e=t.prototype;return e.handler=function(){},e.init=function(){this.evEl&&an(this.element,this.evEl,this.domHandler),this.evTarget&&an(this.target,this.evTarget,this.domHandler),this.evWin&&an(dn(this.element),this.evWin,this.domHandler)},e.destroy=function(){this.evEl&&hn(this.element,this.evEl,this.domHandler),this.evTarget&&hn(this.target,this.evTarget,this.domHandler),this.evWin&&hn(dn(this.element),this.evWin,this.domHandler)},t}();function cn(t,e,i){if(t.indexOf&&!i)return t.indexOf(e);for(var o=0;o<t.length;){if(i&&t[o][i]==e||!i&&t[o]===e)return o;o++}return-1}var un={pointerdown:1,pointermove:2,pointerup:4,pointercancel:8,pointerout:8},pn={2:Lo,3:"pen",4:Ho,5:"kinect"},fn="pointerdown",gn="pointermove pointerup pointercancel";wo.MSPointerEvent&&!wo.PointerEvent&&(fn="MSPointerDown",gn="MSPointerMove MSPointerUp MSPointerCancel");var vn=function(t){function e(){var i,o=e.prototype;return o.evEl=fn,o.evWin=gn,(i=t.apply(this,arguments)||this).store=i.manager.session.pointerEvents=[],i}return yo(e,t),e.prototype.handler=function(t){var e=this.store,i=!1,o=t.type.toLowerCase().replace("ms",""),n=un[o],s=pn[t.pointerType]||t.pointerType,r=s===Lo,a=cn(e,t.pointerId,"pointerId");1&n&&(0===t.button||r)?a<0&&(e.push(t),a=e.length-1):12&n&&(i=!0),a<0||(e[a]=t,this.callback(this.manager,n,{pointers:e,changedPointers:[t],pointerType:s,srcEvent:t}),i&&e.splice(a,1))},e}(ln);function mn(t){return Array.prototype.slice.call(t,0)}function yn(t,e,i){for(var o=[],n=[],s=0;s<t.length;){var r=e?t[s][e]:t[s];cn(n,r)<0&&o.push(t[s]),n[s]=r,s++}return i&&(o=e?o.sort(function(t,i){return t[e]>i[e]}):o.sort()),o}var bn={touchstart:1,touchmove:2,touchend:4,touchcancel:8},wn=function(t){function e(){var i;return e.prototype.evTarget="touchstart touchmove touchend touchcancel",(i=t.apply(this,arguments)||this).targetIds={},i}return yo(e,t),e.prototype.handler=function(t){var e=bn[t.type],i=_n.call(this,t,e);i&&this.callback(this.manager,e,{pointers:i[0],changedPointers:i[1],pointerType:Lo,srcEvent:t})},e}(ln);function _n(t,e){var i,o,n=mn(t.touches),s=this.targetIds;if(3&e&&1===n.length)return s[n[0].identifier]=!0,[n,n];var r=mn(t.changedTouches),a=[],h=this.target;if(o=n.filter(function(t){return Zo(t.target,h)}),1===e)for(i=0;i<o.length;)s[o[i].identifier]=!0,i++;for(i=0;i<r.length;)s[r[i].identifier]&&a.push(r[i]),12&e&&delete s[r[i].identifier],i++;return a.length?[yn(o.concat(a),"identifier",!0),a]:void 0}var xn={mousedown:1,mousemove:2,mouseup:4},En=function(t){function e(){var i,o=e.prototype;return o.evEl="mousedown",o.evWin="mousemove mouseup",(i=t.apply(this,arguments)||this).pressed=!1,i}return yo(e,t),e.prototype.handler=function(t){var e=xn[t.type];1&e&&0===t.button&&(this.pressed=!0),2&e&&1!==t.which&&(e=4),this.pressed&&(4&e&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:Ho,srcEvent:t}))},e}(ln);function On(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var i={x:e.clientX,y:e.clientY},o=this.lastTouches;this.lastTouches.push(i);setTimeout(function(){var t=o.indexOf(i);t>-1&&o.splice(t,1)},2500)}}function Cn(t,e){1&t?(this.primaryTouch=e.changedPointers[0].identifier,On.call(this,e)):12&t&&On.call(this,e)}function kn(t){for(var e=t.srcEvent.clientX,i=t.srcEvent.clientY,o=0;o<this.lastTouches.length;o++){var n=this.lastTouches[o],s=Math.abs(e-n.x),r=Math.abs(i-n.y);if(s<=25&&r<=25)return!0}return!1}var Sn=function(){return function(t){function e(e,i){var o;return(o=t.call(this,e,i)||this).handler=function(t,e,i){var n=i.pointerType===Lo,s=i.pointerType===Ho;if(!(s&&i.sourceCapabilities&&i.sourceCapabilities.firesTouchEvents)){if(n)Cn.call(bo(bo(o)),e,i);else if(s&&kn.call(bo(bo(o)),i))return;o.callback(t,e,i)}},o.touch=new wn(o.manager,o.handler),o.mouse=new En(o.manager,o.handler),o.primaryTouch=null,o.lastTouches=[],o}return yo(e,t),e.prototype.destroy=function(){this.touch.destroy(),this.mouse.destroy()},e}(ln)}();function Tn(t,e,i){return!!Array.isArray(t)&&(Yo(t,i[e],i),!0)}var Mn=32,Dn=1;function In(t,e){var i=e.manager;return i?i.get(t):t}function Pn(t){return 16&t?"cancel":8&t?"end":4&t?"move":2&t?"start":""}var Bn=function(){function t(t){void 0===t&&(t={}),this.options=mo({enable:!0},t),this.id=Dn++,this.manager=null,this.state=1,this.simultaneous={},this.requireFail=[]}var e=t.prototype;return e.set=function(t){return _o(this.options,t),this.manager&&this.manager.touchAction.update(),this},e.recognizeWith=function(t){if(Tn(t,"recognizeWith",this))return this;var e=this.simultaneous;return e[(t=In(t,this)).id]||(e[t.id]=t,t.recognizeWith(this)),this},e.dropRecognizeWith=function(t){return Tn(t,"dropRecognizeWith",this)||(t=In(t,this),delete this.simultaneous[t.id]),this},e.requireFailure=function(t){if(Tn(t,"requireFailure",this))return this;var e=this.requireFail;return-1===cn(e,t=In(t,this))&&(e.push(t),t.requireFailure(this)),this},e.dropRequireFailure=function(t){if(Tn(t,"dropRequireFailure",this))return this;t=In(t,this);var e=cn(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},e.hasRequireFailures=function(){return this.requireFail.length>0},e.canRecognizeWith=function(t){return!!this.simultaneous[t.id]},e.emit=function(t){var e=this,i=this.state;function o(i){e.manager.emit(i,t)}i<8&&o(e.options.event+Pn(i)),o(e.options.event),t.additionalEvent&&o(t.additionalEvent),i>=8&&o(e.options.event+Pn(i))},e.tryEmit=function(t){if(this.canEmit())return this.emit(t);this.state=Mn},e.canEmit=function(){for(var t=0;t<this.requireFail.length;){if(!(33&this.requireFail[t].state))return!1;t++}return!0},e.recognize=function(t){var e=_o({},t);if(!Xo(this.options.enable,[this,e]))return this.reset(),void(this.state=Mn);56&this.state&&(this.state=1),this.state=this.process(e),30&this.state&&this.tryEmit(e)},e.process=function(t){},e.getTouchAction=function(){},e.reset=function(){},t}(),zn=function(t){function e(e){var i;return void 0===e&&(e={}),(i=t.call(this,mo({event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},e))||this).pTime=!1,i.pCenter=!1,i._timer=null,i._input=null,i.count=0,i}yo(e,t);var i=e.prototype;return i.getTouchAction=function(){return[Po]},i.process=function(t){var e=this,i=this.options,o=t.pointers.length===i.pointers,n=t.distance<i.threshold,s=t.deltaTime<i.time;if(this.reset(),1&t.eventType&&0===this.count)return this.failTimeout();if(n&&s&&o){if(4!==t.eventType)return this.failTimeout();var r=!this.pTime||t.timeStamp-this.pTime<i.interval,a=!this.pCenter||Jo(this.pCenter,t.center)<i.posThreshold;if(this.pTime=t.timeStamp,this.pCenter=t.center,a&&r?this.count+=1:this.count=1,this._input=t,0===this.count%i.taps)return this.hasRequireFailures()?(this._timer=setTimeout(function(){e.state=8,e.tryEmit()},i.interval),2):8}return Mn},i.failTimeout=function(){var t=this;return this._timer=setTimeout(function(){t.state=Mn},this.options.interval),Mn},i.reset=function(){clearTimeout(this._timer)},i.emit=function(){8===this.state&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))},e}(Bn),Fn=function(t){function e(e){return void 0===e&&(e={}),t.call(this,mo({pointers:1},e))||this}yo(e,t);var i=e.prototype;return i.attrTest=function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},i.process=function(t){var e=this.state,i=t.eventType,o=6&e,n=this.attrTest(t);return o&&(8&i||!n)?16|e:o||n?4&i?8|e:2&e?4|e:2:Mn},e}(Bn);function Nn(t){return t===Wo?"down":8===t?"up":2===t?"left":4===t?"right":""}var An=function(t){function e(e){var i;return void 0===e&&(e={}),(i=t.call(this,mo({event:"pan",threshold:10,pointers:1,direction:30},e))||this).pX=null,i.pY=null,i}yo(e,t);var i=e.prototype;return i.getTouchAction=function(){var t=this.options.direction,e=[];return 6&t&&e.push(Fo),t&Vo&&e.push(zo),e},i.directionTest=function(t){var e=this.options,i=!0,o=t.distance,n=t.direction,s=t.deltaX,r=t.deltaY;return n&e.direction||(6&e.direction?(n=0===s?1:s<0?2:4,i=s!==this.pX,o=Math.abs(t.deltaX)):(n=0===r?1:r<0?8:Wo,i=r!==this.pY,o=Math.abs(t.deltaY))),t.direction=n,i&&o>e.threshold&&n&e.direction},i.attrTest=function(t){return Fn.prototype.attrTest.call(this,t)&&(2&this.state||!(2&this.state)&&this.directionTest(t))},i.emit=function(e){this.pX=e.deltaX,this.pY=e.deltaY;var i=Nn(e.direction);i&&(e.additionalEvent=this.options.event+i),t.prototype.emit.call(this,e)},e}(Fn),Rn=function(t){function e(e){return void 0===e&&(e={}),t.call(this,mo({event:"swipe",threshold:10,velocity:.3,direction:30,pointers:1},e))||this}yo(e,t);var i=e.prototype;return i.getTouchAction=function(){return An.prototype.getTouchAction.call(this)},i.attrTest=function(e){var i,o=this.options.direction;return 30&o?i=e.overallVelocity:6&o?i=e.overallVelocityX:o&Vo&&(i=e.overallVelocityY),t.prototype.attrTest.call(this,e)&&o&e.offsetDirection&&e.distance>this.options.threshold&&e.maxPointers===this.options.pointers&&Co(i)>this.options.velocity&&4&e.eventType},i.emit=function(t){var e=Nn(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)},e}(Fn),jn=function(t){function e(e){return void 0===e&&(e={}),t.call(this,mo({event:"pinch",threshold:0,pointers:2},e))||this}yo(e,t);var i=e.prototype;return i.getTouchAction=function(){return[Bo]},i.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.scale-1)>this.options.threshold||2&this.state)},i.emit=function(e){if(1!==e.scale){var i=e.scale<1?"in":"out";e.additionalEvent=this.options.event+i}t.prototype.emit.call(this,e)},e}(Fn),Ln=function(t){function e(e){return void 0===e&&(e={}),t.call(this,mo({event:"rotate",threshold:0,pointers:2},e))||this}yo(e,t);var i=e.prototype;return i.getTouchAction=function(){return[Bo]},i.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.rotation)>this.options.threshold||2&this.state)},e}(Fn),Hn=function(t){function e(e){var i;return void 0===e&&(e={}),(i=t.call(this,mo({event:"press",pointers:1,time:251,threshold:9},e))||this)._timer=null,i._input=null,i}yo(e,t);var i=e.prototype;return i.getTouchAction=function(){return[Io]},i.process=function(t){var e=this,i=this.options,o=t.pointers.length===i.pointers,n=t.distance<i.threshold,s=t.deltaTime>i.time;if(this._input=t,!n||!o||12&t.eventType&&!s)this.reset();else if(1&t.eventType)this.reset(),this._timer=setTimeout(function(){e.state=8,e.tryEmit()},i.time);else if(4&t.eventType)return 8;return Mn},i.reset=function(){clearTimeout(this._timer)},i.emit=function(t){8===this.state&&(t&&4&t.eventType?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=ko(),this.manager.emit(this.options.event,this._input)))},e}(Bn),Wn={domEvents:!1,touchAction:Do,enable:!0,inputTarget:null,inputClass:null,cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}},Vn=[[Ln,{enable:!1}],[jn,{enable:!1},["rotate"]],[Rn,{direction:6}],[An,{direction:6},["swipe"]],[zn],[zn,{event:"doubletap",taps:2},["tap"]],[Hn]];function qn(t,e){var i,o=t.element;o.style&&(Yo(t.options.cssProps,function(n,s){i=So(o.style,s),e?(t.oldCssProps[i]=o.style[i],o.style[i]=n):o.style[i]=t.oldCssProps[i]||""}),e||(t.oldCssProps={}))}var Un=function(){function t(t,e){var i,o=this;this.options=_o({},Wn,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=new((i=this).options.inputClass||(Ro?vn:jo?wn:Ao?Sn:En))(i,sn),this.touchAction=new Go(this,this.options.touchAction),qn(this,!0),Yo(this.options.recognizers,function(t){var e=o.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])},this)}var e=t.prototype;return e.set=function(t){return _o(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},e.stop=function(t){this.session.stopped=t?2:1},e.recognize=function(t){var e=this.session;if(!e.stopped){var i;this.touchAction.preventDefaults(t);var o=this.recognizers,n=e.curRecognizer;(!n||n&&8&n.state)&&(e.curRecognizer=null,n=null);for(var s=0;s<o.length;)i=o[s],2===e.stopped||n&&i!==n&&!i.canRecognizeWith(n)?i.reset():i.recognize(t),!n&&14&i.state&&(e.curRecognizer=i,n=i),s++}},e.get=function(t){if(t instanceof Bn)return t;for(var e=this.recognizers,i=0;i<e.length;i++)if(e[i].options.event===t)return e[i];return null},e.add=function(t){if(Tn(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},e.remove=function(t){if(Tn(t,"remove",this))return this;var e=this.get(t);if(t){var i=this.recognizers,o=cn(i,e);-1!==o&&(i.splice(o,1),this.touchAction.update())}return this},e.on=function(t,e){if(void 0===t||void 0===e)return this;var i=this.handlers;return Yo(rn(t),function(t){i[t]=i[t]||[],i[t].push(e)}),this},e.off=function(t,e){if(void 0===t)return this;var i=this.handlers;return Yo(rn(t),function(t){e?i[t]&&i[t].splice(cn(i[t],e),1):delete i[t]}),this},e.emit=function(t,e){this.options.domEvents&&function(t,e){var i=document.createEvent("Event");i.initEvent(t,!0,!0),i.gesture=e,e.target.dispatchEvent(i)}(t,e);var i=this.handlers[t]&&this.handlers[t].slice();if(i&&i.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var o=0;o<i.length;)i[o](e),o++}},e.destroy=function(){this.element&&qn(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null},t}(),Yn={touchstart:1,touchmove:2,touchend:4,touchcancel:8},Xn=function(t){function e(){var i,o=e.prototype;return o.evTarget="touchstart",o.evWin="touchstart touchmove touchend touchcancel",(i=t.apply(this,arguments)||this).started=!1,i}return yo(e,t),e.prototype.handler=function(t){var e=Yn[t.type];if(1===e&&(this.started=!0),this.started){var i=Kn.call(this,t,e);12&e&&i[0].length-i[1].length===0&&(this.started=!1),this.callback(this.manager,e,{pointers:i[0],changedPointers:i[1],pointerType:Lo,srcEvent:t})}},e}(ln);function Kn(t,e){var i=mn(t.touches),o=mn(t.changedTouches);return 12&e&&(i=yn(i.concat(o),"identifier",!0)),[i,o]}function Gn(t,e,i){var o="DEPRECATED METHOD: "+e+"\n"+i+" AT \n";return function(){var e=new Error("get-stack-trace"),i=e&&e.stack?e.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",n=window.console&&(window.console.warn||window.console.log);return n&&n.call(window.console,o,i),t.apply(this,arguments)}}var Zn=Gn(function(t,e,i){for(var o=Object.keys(e),n=0;n<o.length;)(!i||i&&void 0===t[o[n]])&&(t[o[n]]=e[o[n]]),n++;return t},"extend","Use `assign`."),Qn=Gn(function(t,e){return Zn(t,e,!0)},"merge","Use `assign`.");function $n(t,e,i){var o,n=e.prototype;(o=t.prototype=Object.create(n)).constructor=t,o._super=n,i&&_o(o,i)}function Jn(t,e){return function(){return t.apply(e,arguments)}}var ts=function(){var t=function(t,e){return void 0===e&&(e={}),new Un(t,mo({recognizers:Vn.concat()},e))};return t.VERSION="2.0.17-rc",t.DIRECTION_ALL=30,t.DIRECTION_DOWN=Wo,t.DIRECTION_LEFT=2,t.DIRECTION_RIGHT=4,t.DIRECTION_UP=8,t.DIRECTION_HORIZONTAL=6,t.DIRECTION_VERTICAL=Vo,t.DIRECTION_NONE=1,t.DIRECTION_DOWN=Wo,t.INPUT_START=1,t.INPUT_MOVE=2,t.INPUT_END=4,t.INPUT_CANCEL=8,t.STATE_POSSIBLE=1,t.STATE_BEGAN=2,t.STATE_CHANGED=4,t.STATE_ENDED=8,t.STATE_RECOGNIZED=8,t.STATE_CANCELLED=16,t.STATE_FAILED=Mn,t.Manager=Un,t.Input=ln,t.TouchAction=Go,t.TouchInput=wn,t.MouseInput=En,t.PointerEventInput=vn,t.TouchMouseInput=Sn,t.SingleTouchInput=Xn,t.Recognizer=Bn,t.AttrRecognizer=Fn,t.Tap=zn,t.Pan=An,t.Swipe=Rn,t.Pinch=jn,t.Rotate=Ln,t.Press=Hn,t.on=an,t.off=hn,t.each=Yo,t.merge=Qn,t.extend=Zn,t.bindFn=Jn,t.assign=_o,t.inherit=$n,t.bindFn=Jn,t.prefixed=So,t.toArray=mn,t.inArray=cn,t.uniqueArray=yn,t.splitStr=rn,t.boolOrFn=Xo,t.hasParent=Zo,t.addEventListeners=an,t.removeEventListeners=hn,t.defaults=_o({},Wn,{preset:Vn}),t}();
/**
	 * vis-util
	 * https://github.com/visjs/vis-util
	 *
	 * utilitie collection for visjs
	 *
	 * @version 6.0.0
	 * @date    2025-07-12T18:02:43.836Z
	 *
	 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
	 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
	 *
	 * @license
	 * vis.js is dual licensed under both
	 *
	 *   1. The Apache 2.0 License
	 *      http://www.apache.org/licenses/LICENSE-2.0
	 *
	 *   and
	 *
	 *   2. The MIT License
	 *      http://opensource.org/licenses/MIT
	 *
	 * vis.js may be distributed under either license.
	 */
function es(...t){return function(t){let[e,i,o]=function(...t){const e=function(){let t=**********;return function(e){const i=e.toString();for(let e=0;e<i.length;e++){t+=i.charCodeAt(e);let o=.02519603282416938*t;t=o>>>0,o-=t,o*=t,t=o>>>0,o-=t,t+=***********o}return 2.3283064365386963e-10*(t>>>0)}}();let i=e(" "),o=e(" "),n=e(" ");for(let s=0;s<t.length;s++)i-=e(t[s]),i<0&&(i+=1),o-=e(t[s]),o<0&&(o+=1),n-=e(t[s]),n<0&&(n+=1);return[i,o,n]}(t),n=1;const s=()=>{const t=2091639*e+2.3283064365386963e-10*n;return e=i,i=o,o=t-(n=0|t)};return s.uint32=()=>***********s(),s.fract53=()=>s()+11102230246251565e-32*(2097152*s()|0),s.algorithm="Alea",s.seed=t,s.version="0.9",s}(t.length?t:[Date.now()])}ts.defaults;const is="undefined"!=typeof window?window.Hammer||ts:function(){return function(){const t=()=>{};return{on:t,off:t,destroy:t,emit:t,get:()=>({set:t})}}()};function os(t){this._cleanupQueue=[],this.active=!1,this._dom={container:t,overlay:document.createElement("div")},this._dom.overlay.classList.add("vis-overlay"),this._dom.container.appendChild(this._dom.overlay),this._cleanupQueue.push(()=>{this._dom.overlay.parentNode.removeChild(this._dom.overlay)});const e=is(this._dom.overlay);e.on("tap",this._onTapOverlay.bind(this)),this._cleanupQueue.push(()=>{e.destroy()});["tap","doubletap","press","pinch","pan","panstart","panmove","panend"].forEach(t=>{e.on(t,t=>{t.srcEvent.stopPropagation()})}),document&&document.body&&(this._onClick=e=>{(function(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1})(e.target,t)||this.deactivate()},document.body.addEventListener("click",this._onClick),this._cleanupQueue.push(()=>{document.body.removeEventListener("click",this._onClick)})),this._escListener=t=>{("key"in t?"Escape"===t.key:27===t.keyCode)&&this.deactivate()}}vo(os.prototype),os.current=null,os.prototype.destroy=function(){this.deactivate();for(const t of this._cleanupQueue.splice(0).reverse())t()},os.prototype.activate=function(){os.current&&os.current.deactivate(),os.current=this,this.active=!0,this._dom.overlay.style.display="none",this._dom.container.classList.add("vis-active"),this.emit("change"),this.emit("activate"),document.body.addEventListener("keydown",this._escListener)},os.prototype.deactivate=function(){this.active=!1,this._dom.overlay.style.display="block",this._dom.container.classList.remove("vis-active"),document.body.removeEventListener("keydown",this._escListener),this.emit("change"),this.emit("deactivate")},os.prototype._onTapOverlay=function(t){this.activate(),t.srcEvent.stopPropagation()};const ns=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,ss=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,rs=/^rgb\( *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *\)$/i,as=/^rgba\( *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *([01]|0?\.\d+) *\)$/i;function hs(t){if(t)for(;!0===t.hasChildNodes();){const e=t.firstChild;e&&(hs(e),t.removeChild(e))}}function ds(t){return t instanceof String||"string"==typeof t}function ls(t){return"object"==typeof t&&null!==t}function cs(t,e,i,o){let n=!1;!0===o&&(n=null===e[i]&&void 0!==t[i]),n?delete t[i]:t[i]=e[i]}function us(t,e,i=!1){for(const o in t)if(void 0!==e[o])if(null===e[o]||"object"!=typeof e[o])cs(t,e,o,i);else{const n=t[o],s=e[o];ls(n)&&ls(s)&&us(n,s,i)}}function ps(t,e,i,o=!1){if(Array.isArray(i))throw new TypeError("Arrays are not supported by deepExtend");for(let n=0;n<t.length;n++){const s=t[n];if(Object.prototype.hasOwnProperty.call(i,s))if(i[s]&&i[s].constructor===Object)void 0===e[s]&&(e[s]={}),e[s].constructor===Object?gs(e[s],i[s],!1,o):cs(e,i,s,o);else{if(Array.isArray(i[s]))throw new TypeError("Arrays are not supported by deepExtend");cs(e,i,s,o)}}return e}function fs(t,e,i,o=!1){if(Array.isArray(i))throw new TypeError("Arrays are not supported by deepExtend");for(const n in i)if(Object.prototype.hasOwnProperty.call(i,n)&&!t.includes(n))if(i[n]&&i[n].constructor===Object)void 0===e[n]&&(e[n]={}),e[n].constructor===Object?gs(e[n],i[n]):cs(e,i,n,o);else if(Array.isArray(i[n])){e[n]=[];for(let t=0;t<i[n].length;t++)e[n].push(i[n][t])}else cs(e,i,n,o);return e}function gs(t,e,i=!1,o=!1){for(const n in e)(Object.prototype.hasOwnProperty.call(e,n)||!0===i)&&("object"==typeof e[n]&&null!==e[n]&&Object.getPrototypeOf(e[n])===Object.prototype?void 0===t[n]?t[n]=gs({},e[n],i):"object"==typeof t[n]&&null!==t[n]&&Object.getPrototypeOf(t[n])===Object.prototype?gs(t[n],e[n],i):cs(t,e,n,o):Array.isArray(e[n])?t[n]=e[n].slice():cs(t,e,n,o));return t}function vs(t,e){return[...t,e]}function ms(t){return t.slice()}function ys(t){return t.getBoundingClientRect().top}function bs(t,e){if(Array.isArray(t)){const i=t.length;for(let o=0;o<i;o++)e(t[o],o,t)}else for(const i in t)Object.prototype.hasOwnProperty.call(t,i)&&e(t[i],i,t)}function ws(t){let e;switch(t.length){case 3:case 4:return e=ss.exec(t),e?{r:parseInt(e[1]+e[1],16),g:parseInt(e[2]+e[2],16),b:parseInt(e[3]+e[3],16)}:null;case 6:case 7:return e=ns.exec(t),e?{r:parseInt(e[1],16),g:parseInt(e[2],16),b:parseInt(e[3],16)}:null;default:return null}}function _s(t,e){if(t.includes("rgba"))return t;if(t.includes("rgb")){const i=t.substr(t.indexOf("(")+1).replace(")","").split(",");return"rgba("+i[0]+","+i[1]+","+i[2]+","+e+")"}{const i=ws(t);return null==i?t:"rgba("+i.r+","+i.g+","+i.b+","+e+")"}}function xs(t,e,i){return"#"+((1<<24)+(t<<16)+(e<<8)+i).toString(16).slice(1)}function Es(t,e){if(ds(t)){let e=t;if(Ts(e)){const t=e.substr(4).substr(0,e.length-5).split(",").map(function(t){return parseInt(t)});e=xs(t[0],t[1],t[2])}if(!0===Ss(e)){const t=function(t){const e=ws(t);if(!e)throw new TypeError(`'${t}' is not a valid color.`);return Os(e.r,e.g,e.b)}(e),i={h:t.h,s:.8*t.s,v:Math.min(1,1.02*t.v)},o={h:t.h,s:Math.min(1,1.25*t.s),v:.8*t.v},n=ks(o.h,o.s,o.v),s=ks(i.h,i.s,i.v);return{background:e,border:n,highlight:{background:s,border:n},hover:{background:s,border:n}}}return{background:e,border:e,highlight:{background:e,border:e},hover:{background:e,border:e}}}return{background:t.background||void 0,border:t.border||void 0,highlight:ds(t.highlight)?{border:t.highlight,background:t.highlight}:{background:t.highlight&&t.highlight.background||void 0,border:t.highlight&&t.highlight.border||void 0},hover:ds(t.hover)?{border:t.hover,background:t.hover}:{border:t.hover&&t.hover.border||void 0,background:t.hover&&t.hover.background||void 0}}}function Os(t,e,i){t/=255,e/=255,i/=255;const o=Math.min(t,Math.min(e,i)),n=Math.max(t,Math.max(e,i));if(o===n)return{h:0,s:0,v:o};return{h:60*((t===o?3:i===o?1:5)-(t===o?e-i:i===o?t-e:i-t)/(n-o))/360,s:(n-o)/n,v:n}}function Cs(t,e,i){let o,n,s;const r=Math.floor(6*t),a=6*t-r,h=i*(1-e),d=i*(1-a*e),l=i*(1-(1-a)*e);switch(r%6){case 0:o=i,n=l,s=h;break;case 1:o=d,n=i,s=h;break;case 2:o=h,n=i,s=l;break;case 3:o=h,n=d,s=i;break;case 4:o=l,n=h,s=i;break;case 5:o=i,n=h,s=d}return{r:Math.floor(255*o),g:Math.floor(255*n),b:Math.floor(255*s)}}function ks(t,e,i){const o=Cs(t,e,i);return xs(o.r,o.g,o.b)}function Ss(t){return/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(t)}function Ts(t){return rs.test(t)}function Ms(t){if(null===t||"object"!=typeof t)return null;if(t instanceof Element)return t;const e=Object.create(t);for(const i in t)Object.prototype.hasOwnProperty.call(t,i)&&"object"==typeof t[i]&&(e[i]=Ms(t[i]));return e}function Ds(t,e,i,o={}){const n=function(t){return null!=t},s=function(t){return null!==t&&"object"==typeof t};if(!s(t))throw new Error("Parameter mergeTarget must be an object");if(!s(e))throw new Error("Parameter options must be an object");if(!n(i))throw new Error("Parameter option must have a value");if(!s(o))throw new Error("Parameter globalOptions must be an object");const r=e[i],a=s(o)&&!function(t){for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e))return!1;return!0}(o)?o[i]:void 0,h=a?a.enabled:void 0;if(void 0===r)return;if("boolean"==typeof r)return s(t[i])||(t[i]={}),void(t[i].enabled=r);if(null===r&&!s(t[i])){if(!n(a))return;t[i]=Object.create(a)}if(!s(r))return;let d=!0;void 0!==r.enabled?d=r.enabled:void 0!==h&&(d=a.enabled),function(t,e,i){s(t[i])||(t[i]={});const o=e[i],n=t[i];for(const t in o)Object.prototype.hasOwnProperty.call(o,t)&&(n[t]=o[t])}(t,e,i),t[i].enabled=d}const Is={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>t*(2-t),easeInOutQuad:t=>t<.5?2*t*t:(4-2*t)*t-1,easeInCubic:t=>t*t*t,easeOutCubic:t=>--t*t*t+1,easeInOutCubic:t=>t<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1,easeInQuart:t=>t*t*t*t,easeOutQuart:t=>1- --t*t*t*t,easeInOutQuart:t=>t<.5?8*t*t*t*t:1-8*--t*t*t*t,easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>1+--t*t*t*t*t,easeInOutQuint:t=>t<.5?16*t*t*t*t*t:1+16*--t*t*t*t*t};function Ps(t,e){let i;Array.isArray(e)||(e=[e]);for(const o of t)if(o){i=o[e[0]];for(let t=1;t<e.length;t++)i&&(i=i[e[t]]);if(void 0!==i)break}return i}const Bs={black:"#000000",navy:"#000080",darkblue:"#00008B",mediumblue:"#0000CD",blue:"#0000FF",darkgreen:"#006400",green:"#008000",teal:"#008080",darkcyan:"#008B8B",deepskyblue:"#00BFFF",darkturquoise:"#00CED1",mediumspringgreen:"#00FA9A",lime:"#00FF00",springgreen:"#00FF7F",aqua:"#00FFFF",cyan:"#00FFFF",midnightblue:"#191970",dodgerblue:"#1E90FF",lightseagreen:"#20B2AA",forestgreen:"#228B22",seagreen:"#2E8B57",darkslategray:"#2F4F4F",limegreen:"#32CD32",mediumseagreen:"#3CB371",turquoise:"#40E0D0",royalblue:"#4169E1",steelblue:"#4682B4",darkslateblue:"#483D8B",mediumturquoise:"#48D1CC",indigo:"#4B0082",darkolivegreen:"#556B2F",cadetblue:"#5F9EA0",cornflowerblue:"#6495ED",mediumaquamarine:"#66CDAA",dimgray:"#696969",slateblue:"#6A5ACD",olivedrab:"#6B8E23",slategray:"#708090",lightslategray:"#778899",mediumslateblue:"#7B68EE",lawngreen:"#7CFC00",chartreuse:"#7FFF00",aquamarine:"#7FFFD4",maroon:"#800000",purple:"#800080",olive:"#808000",gray:"#808080",skyblue:"#87CEEB",lightskyblue:"#87CEFA",blueviolet:"#8A2BE2",darkred:"#8B0000",darkmagenta:"#8B008B",saddlebrown:"#8B4513",darkseagreen:"#8FBC8F",lightgreen:"#90EE90",mediumpurple:"#9370D8",darkviolet:"#9400D3",palegreen:"#98FB98",darkorchid:"#9932CC",yellowgreen:"#9ACD32",sienna:"#A0522D",brown:"#A52A2A",darkgray:"#A9A9A9",lightblue:"#ADD8E6",greenyellow:"#ADFF2F",paleturquoise:"#AFEEEE",lightsteelblue:"#B0C4DE",powderblue:"#B0E0E6",firebrick:"#B22222",darkgoldenrod:"#B8860B",mediumorchid:"#BA55D3",rosybrown:"#BC8F8F",darkkhaki:"#BDB76B",silver:"#C0C0C0",mediumvioletred:"#C71585",indianred:"#CD5C5C",peru:"#CD853F",chocolate:"#D2691E",tan:"#D2B48C",lightgrey:"#D3D3D3",palevioletred:"#D87093",thistle:"#D8BFD8",orchid:"#DA70D6",goldenrod:"#DAA520",crimson:"#DC143C",gainsboro:"#DCDCDC",plum:"#DDA0DD",burlywood:"#DEB887",lightcyan:"#E0FFFF",lavender:"#E6E6FA",darksalmon:"#E9967A",violet:"#EE82EE",palegoldenrod:"#EEE8AA",lightcoral:"#F08080",khaki:"#F0E68C",aliceblue:"#F0F8FF",honeydew:"#F0FFF0",azure:"#F0FFFF",sandybrown:"#F4A460",wheat:"#F5DEB3",beige:"#F5F5DC",whitesmoke:"#F5F5F5",mintcream:"#F5FFFA",ghostwhite:"#F8F8FF",salmon:"#FA8072",antiquewhite:"#FAEBD7",linen:"#FAF0E6",lightgoldenrodyellow:"#FAFAD2",oldlace:"#FDF5E6",red:"#FF0000",fuchsia:"#FF00FF",magenta:"#FF00FF",deeppink:"#FF1493",orangered:"#FF4500",tomato:"#FF6347",hotpink:"#FF69B4",coral:"#FF7F50",darkorange:"#FF8C00",lightsalmon:"#FFA07A",orange:"#FFA500",lightpink:"#FFB6C1",pink:"#FFC0CB",gold:"#FFD700",peachpuff:"#FFDAB9",navajowhite:"#FFDEAD",moccasin:"#FFE4B5",bisque:"#FFE4C4",mistyrose:"#FFE4E1",blanchedalmond:"#FFEBCD",papayawhip:"#FFEFD5",lavenderblush:"#FFF0F5",seashell:"#FFF5EE",cornsilk:"#FFF8DC",lemonchiffon:"#FFFACD",floralwhite:"#FFFAF0",snow:"#FFFAFA",yellow:"#FFFF00",lightyellow:"#FFFFE0",ivory:"#FFFFF0",white:"#FFFFFF"};let zs=class{constructor(t=1){this.pixelRatio=t,this.generated=!1,this.centerCoordinates={x:144.5,y:144.5},this.r=289*.49,this.color={r:255,g:255,b:255,a:1},this.hueCircle=void 0,this.initialColor={r:255,g:255,b:255,a:1},this.previousColor=void 0,this.applied=!1,this.updateCallback=()=>{},this.closeCallback=()=>{},this._create()}insertTo(t){void 0!==this.hammer&&(this.hammer.destroy(),this.hammer=void 0),this.container=t,this.container.appendChild(this.frame),this._bindHammer(),this._setSize()}setUpdateCallback(t){if("function"!=typeof t)throw new Error("Function attempted to set as colorPicker update callback is not a function.");this.updateCallback=t}setCloseCallback(t){if("function"!=typeof t)throw new Error("Function attempted to set as colorPicker closing callback is not a function.");this.closeCallback=t}_isColorString(t){if("string"==typeof t)return Bs[t]}setColor(t,e=!0){if("none"===t)return;let i;const o=this._isColorString(t);if(void 0!==o&&(t=o),!0===ds(t)){if(!0===Ts(t)){const e=t.substr(4).substr(0,t.length-5).split(",");i={r:e[0],g:e[1],b:e[2],a:1}}else if(!0===function(t){return as.test(t)}(t)){const e=t.substr(5).substr(0,t.length-6).split(",");i={r:e[0],g:e[1],b:e[2],a:e[3]}}else if(!0===Ss(t)){const e=ws(t);i={r:e.r,g:e.g,b:e.b,a:1}}}else if(t instanceof Object&&void 0!==t.r&&void 0!==t.g&&void 0!==t.b){const e=void 0!==t.a?t.a:"1.0";i={r:t.r,g:t.g,b:t.b,a:e}}if(void 0===i)throw new Error("Unknown color passed to the colorPicker. Supported are strings: rgb, hex, rgba. Object: rgb ({r:r,g:g,b:b,[a:a]}). Supplied: "+JSON.stringify(t));this._setColor(i,e)}show(){void 0!==this.closeCallback&&(this.closeCallback(),this.closeCallback=void 0),this.applied=!1,this.frame.style.display="block",this._generateHueCircle()}_hide(t=!0){!0===t&&(this.previousColor=Object.assign({},this.color)),!0===this.applied&&this.updateCallback(this.initialColor),this.frame.style.display="none",setTimeout(()=>{void 0!==this.closeCallback&&(this.closeCallback(),this.closeCallback=void 0)},0)}_save(){this.updateCallback(this.color),this.applied=!1,this._hide()}_apply(){this.applied=!0,this.updateCallback(this.color),this._updatePicker(this.color)}_loadLast(){void 0!==this.previousColor?this.setColor(this.previousColor,!1):alert("There is no last color to load...")}_setColor(t,e=!0){!0===e&&(this.initialColor=Object.assign({},t)),this.color=t;const i=Os(t.r,t.g,t.b),o=2*Math.PI,n=this.r*i.s,s=this.centerCoordinates.x+n*Math.sin(o*i.h),r=this.centerCoordinates.y+n*Math.cos(o*i.h);this.colorPickerSelector.style.left=s-.5*this.colorPickerSelector.clientWidth+"px",this.colorPickerSelector.style.top=r-.5*this.colorPickerSelector.clientHeight+"px",this._updatePicker(t)}_setOpacity(t){this.color.a=t/100,this._updatePicker(this.color)}_setBrightness(t){const e=Os(this.color.r,this.color.g,this.color.b);e.v=t/100;const i=Cs(e.h,e.s,e.v);i.a=this.color.a,this.color=i,this._updatePicker()}_updatePicker(t=this.color){const e=Os(t.r,t.g,t.b),i=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(i.webkitBackingStorePixelRatio||i.mozBackingStorePixelRatio||i.msBackingStorePixelRatio||i.oBackingStorePixelRatio||i.backingStorePixelRatio||1)),i.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);const o=this.colorPickerCanvas.clientWidth,n=this.colorPickerCanvas.clientHeight;i.clearRect(0,0,o,n),i.putImageData(this.hueCircle,0,0),i.fillStyle="rgba(0,0,0,"+(1-e.v)+")",i.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),i.fill(),this.brightnessRange.value=100*e.v,this.opacityRange.value=100*t.a,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}_setSize(){this.colorPickerCanvas.style.width="100%",this.colorPickerCanvas.style.height="100%",this.colorPickerCanvas.width=289*this.pixelRatio,this.colorPickerCanvas.height=289*this.pixelRatio}_create(){if(this.frame=document.createElement("div"),this.frame.className="vis-color-picker",this.colorPickerDiv=document.createElement("div"),this.colorPickerSelector=document.createElement("div"),this.colorPickerSelector.className="vis-selector",this.colorPickerDiv.appendChild(this.colorPickerSelector),this.colorPickerCanvas=document.createElement("canvas"),this.colorPickerDiv.appendChild(this.colorPickerCanvas),this.colorPickerCanvas.getContext){const t=this.colorPickerCanvas.getContext("2d");this.pixelRatio=(window.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1),this.colorPickerCanvas.getContext("2d").setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0)}else{const t=document.createElement("DIV");t.style.color="red",t.style.fontWeight="bold",t.style.padding="10px",t.innerText="Error: your browser does not support HTML canvas",this.colorPickerCanvas.appendChild(t)}this.colorPickerDiv.className="vis-color",this.opacityDiv=document.createElement("div"),this.opacityDiv.className="vis-opacity",this.brightnessDiv=document.createElement("div"),this.brightnessDiv.className="vis-brightness",this.arrowDiv=document.createElement("div"),this.arrowDiv.className="vis-arrow",this.opacityRange=document.createElement("input");try{this.opacityRange.type="range",this.opacityRange.min="0",this.opacityRange.max="100"}catch(t){}this.opacityRange.value="100",this.opacityRange.className="vis-range",this.brightnessRange=document.createElement("input");try{this.brightnessRange.type="range",this.brightnessRange.min="0",this.brightnessRange.max="100"}catch(t){}this.brightnessRange.value="100",this.brightnessRange.className="vis-range",this.opacityDiv.appendChild(this.opacityRange),this.brightnessDiv.appendChild(this.brightnessRange);const t=this;this.opacityRange.onchange=function(){t._setOpacity(this.value)},this.opacityRange.oninput=function(){t._setOpacity(this.value)},this.brightnessRange.onchange=function(){t._setBrightness(this.value)},this.brightnessRange.oninput=function(){t._setBrightness(this.value)},this.brightnessLabel=document.createElement("div"),this.brightnessLabel.className="vis-label vis-brightness",this.brightnessLabel.innerText="brightness:",this.opacityLabel=document.createElement("div"),this.opacityLabel.className="vis-label vis-opacity",this.opacityLabel.innerText="opacity:",this.newColorDiv=document.createElement("div"),this.newColorDiv.className="vis-new-color",this.newColorDiv.innerText="new",this.initialColorDiv=document.createElement("div"),this.initialColorDiv.className="vis-initial-color",this.initialColorDiv.innerText="initial",this.cancelButton=document.createElement("div"),this.cancelButton.className="vis-button vis-cancel",this.cancelButton.innerText="cancel",this.cancelButton.onclick=this._hide.bind(this,!1),this.applyButton=document.createElement("div"),this.applyButton.className="vis-button vis-apply",this.applyButton.innerText="apply",this.applyButton.onclick=this._apply.bind(this),this.saveButton=document.createElement("div"),this.saveButton.className="vis-button vis-save",this.saveButton.innerText="save",this.saveButton.onclick=this._save.bind(this),this.loadButton=document.createElement("div"),this.loadButton.className="vis-button vis-load",this.loadButton.innerText="load last",this.loadButton.onclick=this._loadLast.bind(this),this.frame.appendChild(this.colorPickerDiv),this.frame.appendChild(this.arrowDiv),this.frame.appendChild(this.brightnessLabel),this.frame.appendChild(this.brightnessDiv),this.frame.appendChild(this.opacityLabel),this.frame.appendChild(this.opacityDiv),this.frame.appendChild(this.newColorDiv),this.frame.appendChild(this.initialColorDiv),this.frame.appendChild(this.cancelButton),this.frame.appendChild(this.applyButton),this.frame.appendChild(this.saveButton),this.frame.appendChild(this.loadButton)}_bindHammer(){this.drag={},this.pinch={},this.hammer=new is(this.colorPickerCanvas),this.hammer.get("pinch").set({enable:!0}),this.hammer.on("hammer.input",t=>{t.isFirst&&this._moveSelector(t)}),this.hammer.on("tap",t=>{this._moveSelector(t)}),this.hammer.on("panstart",t=>{this._moveSelector(t)}),this.hammer.on("panmove",t=>{this._moveSelector(t)}),this.hammer.on("panend",t=>{this._moveSelector(t)})}_generateHueCircle(){if(!1===this.generated){const t=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1)),t.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);const e=this.colorPickerCanvas.clientWidth,i=this.colorPickerCanvas.clientHeight;let o,n,s,r;t.clearRect(0,0,e,i),this.centerCoordinates={x:.5*e,y:.5*i},this.r=.49*e;const a=2*Math.PI/360,h=1/360,d=1/this.r;let l;for(s=0;s<360;s++)for(r=0;r<this.r;r++)o=this.centerCoordinates.x+r*Math.sin(a*s),n=this.centerCoordinates.y+r*Math.cos(a*s),l=Cs(s*h,r*d,1),t.fillStyle="rgb("+l.r+","+l.g+","+l.b+")",t.fillRect(o-.5,n-.5,2,2);t.strokeStyle="rgba(0,0,0,1)",t.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),t.stroke(),this.hueCircle=t.getImageData(0,0,e,i)}this.generated=!0}_moveSelector(t){const e=this.colorPickerDiv.getBoundingClientRect(),i=t.center.x-e.left,o=t.center.y-e.top,n=.5*this.colorPickerDiv.clientHeight,s=.5*this.colorPickerDiv.clientWidth,r=i-s,a=o-n,h=Math.atan2(r,a),d=.98*Math.min(Math.sqrt(r*r+a*a),s),l=Math.cos(h)*d+n,c=Math.sin(h)*d+s;this.colorPickerSelector.style.top=l-.5*this.colorPickerSelector.clientHeight+"px",this.colorPickerSelector.style.left=c-.5*this.colorPickerSelector.clientWidth+"px";let u=h/(2*Math.PI);u=u<0?u+1:u;const p=d/this.r,f=Os(this.color.r,this.color.g,this.color.b);f.h=u,f.s=p;const g=Cs(f.h,f.s,f.v);g.a=this.color.a,this.color=g,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}};function Fs(...t){if(t.length<1)throw new TypeError("Invalid arguments.");if(1===t.length)return document.createTextNode(t[0]);{const e=document.createElement(t[0]);return e.appendChild(Fs(...t.slice(1))),e}}let Ns,As=!1;const Rs="background: #FFeeee; color: #dd0000";const js=os,Ls=class{constructor(t,e,i,o=1,n=()=>!1){this.parent=t,this.changedOptions=[],this.container=e,this.allowCreation=!1,this.hideOption=n,this.options={},this.initialized=!1,this.popupCounter=0,this.defaultOptions={enabled:!1,filter:!0,container:void 0,showButton:!0},Object.assign(this.options,this.defaultOptions),this.configureOptions=i,this.moduleOptions={},this.domElements=[],this.popupDiv={},this.popupLimit=5,this.popupHistory={},this.colorPicker=new zs(o),this.wrapper=void 0}setOptions(t){if(void 0!==t){this.popupHistory={},this._removePopup();let e=!0;if("string"==typeof t)this.options.filter=t;else if(Array.isArray(t))this.options.filter=t.join();else if("object"==typeof t){if(null==t)throw new TypeError("options cannot be null");void 0!==t.container&&(this.options.container=t.container),void 0!==t.filter&&(this.options.filter=t.filter),void 0!==t.showButton&&(this.options.showButton=t.showButton),void 0!==t.enabled&&(e=t.enabled)}else"boolean"==typeof t?(this.options.filter=!0,e=t):"function"==typeof t&&(this.options.filter=t,e=!0);!1===this.options.filter&&(e=!1),this.options.enabled=e}this._clean()}setModuleOptions(t){this.moduleOptions=t,!0===this.options.enabled&&(this._clean(),void 0!==this.options.container&&(this.container=this.options.container),this._create())}_create(){this._clean(),this.changedOptions=[];const t=this.options.filter;let e=0,i=!1;for(const o in this.configureOptions)Object.prototype.hasOwnProperty.call(this.configureOptions,o)&&(this.allowCreation=!1,i=!1,"function"==typeof t?(i=t(o,[]),i=i||this._handleObject(this.configureOptions[o],[o],!0)):!0!==t&&-1===t.indexOf(o)||(i=!0),!1!==i&&(this.allowCreation=!0,e>0&&this._makeItem([]),this._makeHeader(o),this._handleObject(this.configureOptions[o],[o])),e++);this._makeButton(),this._push()}_push(){this.wrapper=document.createElement("div"),this.wrapper.className="vis-configuration-wrapper",this.container.appendChild(this.wrapper);for(let t=0;t<this.domElements.length;t++)this.wrapper.appendChild(this.domElements[t]);this._showPopupIfNeeded()}_clean(){for(let t=0;t<this.domElements.length;t++)this.wrapper.removeChild(this.domElements[t]);void 0!==this.wrapper&&(this.container.removeChild(this.wrapper),this.wrapper=void 0),this.domElements=[],this._removePopup()}_getValue(t){let e=this.moduleOptions;for(let i=0;i<t.length;i++){if(void 0===e[t[i]]){e=void 0;break}e=e[t[i]]}return e}_makeItem(t,...e){if(!0===this.allowCreation){const i=document.createElement("div");return i.className="vis-configuration vis-config-item vis-config-s"+t.length,e.forEach(t=>{i.appendChild(t)}),this.domElements.push(i),this.domElements.length}return 0}_makeHeader(t){const e=document.createElement("div");e.className="vis-configuration vis-config-header",e.innerText=t,this._makeItem([],e)}_makeLabel(t,e,i=!1){const o=document.createElement("div");if(o.className="vis-configuration vis-config-label vis-config-s"+e.length,!0===i){for(;o.firstChild;)o.removeChild(o.firstChild);o.appendChild(Fs("i","b",t))}else o.innerText=t+":";return o}_makeDropdown(t,e,i){const o=document.createElement("select");o.className="vis-configuration vis-config-select";let n=0;void 0!==e&&-1!==t.indexOf(e)&&(n=t.indexOf(e));for(let e=0;e<t.length;e++){const i=document.createElement("option");i.value=t[e],e===n&&(i.selected="selected"),i.innerText=t[e],o.appendChild(i)}const s=this;o.onchange=function(){s._update(this.value,i)};const r=this._makeLabel(i[i.length-1],i);this._makeItem(i,r,o)}_makeRange(t,e,i){const o=t[0],n=t[1],s=t[2],r=t[3],a=document.createElement("input");a.className="vis-configuration vis-config-range";try{a.type="range",a.min=n,a.max=s}catch(t){}a.step=r;let h="",d=0;if(void 0!==e){const t=1.2;e<0&&e*t<n?(a.min=Math.ceil(e*t),d=a.min,h="range increased"):e/t<n&&(a.min=Math.ceil(e/t),d=a.min,h="range increased"),e*t>s&&1!==s&&(a.max=Math.ceil(e*t),d=a.max,h="range increased"),a.value=e}else a.value=o;const l=document.createElement("input");l.className="vis-configuration vis-config-rangeinput",l.value=a.value;const c=this;a.onchange=function(){l.value=this.value,c._update(Number(this.value),i)},a.oninput=function(){l.value=this.value};const u=this._makeLabel(i[i.length-1],i),p=this._makeItem(i,u,a,l);""!==h&&this.popupHistory[p]!==d&&(this.popupHistory[p]=d,this._setupPopup(h,p))}_makeButton(){if(!0===this.options.showButton){const t=document.createElement("div");t.className="vis-configuration vis-config-button",t.innerText="generate options",t.onclick=()=>{this._printOptions()},t.onmouseover=()=>{t.className="vis-configuration vis-config-button hover"},t.onmouseout=()=>{t.className="vis-configuration vis-config-button"},this.optionsContainer=document.createElement("div"),this.optionsContainer.className="vis-configuration vis-config-option-container",this.domElements.push(this.optionsContainer),this.domElements.push(t)}}_setupPopup(t,e){if(!0===this.initialized&&!0===this.allowCreation&&this.popupCounter<this.popupLimit){const i=document.createElement("div");i.id="vis-configuration-popup",i.className="vis-configuration-popup",i.innerText=t,i.onclick=()=>{this._removePopup()},this.popupCounter+=1,this.popupDiv={html:i,index:e}}}_removePopup(){void 0!==this.popupDiv.html&&(this.popupDiv.html.parentNode.removeChild(this.popupDiv.html),clearTimeout(this.popupDiv.hideTimeout),clearTimeout(this.popupDiv.deleteTimeout),this.popupDiv={})}_showPopupIfNeeded(){if(void 0!==this.popupDiv.html){const t=this.domElements[this.popupDiv.index].getBoundingClientRect();this.popupDiv.html.style.left=t.left+"px",this.popupDiv.html.style.top=t.top-30+"px",document.body.appendChild(this.popupDiv.html),this.popupDiv.hideTimeout=setTimeout(()=>{this.popupDiv.html.style.opacity=0},1500),this.popupDiv.deleteTimeout=setTimeout(()=>{this._removePopup()},1800)}}_makeCheckbox(t,e,i){const o=document.createElement("input");o.type="checkbox",o.className="vis-configuration vis-config-checkbox",o.checked=t,void 0!==e&&(o.checked=e,e!==t&&("object"==typeof t?e!==t.enabled&&this.changedOptions.push({path:i,value:e}):this.changedOptions.push({path:i,value:e})));const n=this;o.onchange=function(){n._update(this.checked,i)};const s=this._makeLabel(i[i.length-1],i);this._makeItem(i,s,o)}_makeTextInput(t,e,i){const o=document.createElement("input");o.type="text",o.className="vis-configuration vis-config-text",o.value=e,e!==t&&this.changedOptions.push({path:i,value:e});const n=this;o.onchange=function(){n._update(this.value,i)};const s=this._makeLabel(i[i.length-1],i);this._makeItem(i,s,o)}_makeColorField(t,e,i){const o=t[1],n=document.createElement("div");"none"!==(e=void 0===e?o:e)?(n.className="vis-configuration vis-config-colorBlock",n.style.backgroundColor=e):n.className="vis-configuration vis-config-colorBlock none",e=void 0===e?o:e,n.onclick=()=>{this._showColorPicker(e,n,i)};const s=this._makeLabel(i[i.length-1],i);this._makeItem(i,s,n)}_showColorPicker(t,e,i){e.onclick=function(){},this.colorPicker.insertTo(e),this.colorPicker.show(),this.colorPicker.setColor(t),this.colorPicker.setUpdateCallback(t=>{const o="rgba("+t.r+","+t.g+","+t.b+","+t.a+")";e.style.backgroundColor=o,this._update(o,i)}),this.colorPicker.setCloseCallback(()=>{e.onclick=()=>{this._showColorPicker(t,e,i)}})}_handleObject(t,e=[],i=!1){let o=!1;const n=this.options.filter;let s=!1;for(const r in t)if(Object.prototype.hasOwnProperty.call(t,r)){o=!0;const a=t[r],h=vs(e,r);if("function"==typeof n&&(o=n(r,e),!1===o&&!Array.isArray(a)&&"string"!=typeof a&&"boolean"!=typeof a&&a instanceof Object&&(this.allowCreation=!1,o=this._handleObject(a,h,!0),this.allowCreation=!1===i)),!1!==o){s=!0;const t=this._getValue(h);if(Array.isArray(a))this._handleArray(a,t,h);else if("string"==typeof a)this._makeTextInput(a,t,h);else if("boolean"==typeof a)this._makeCheckbox(a,t,h);else if(a instanceof Object){if(!this.hideOption(e,r,this.moduleOptions))if(void 0!==a.enabled){const t=vs(h,"enabled"),e=this._getValue(t);if(!0===e){const t=this._makeLabel(r,h,!0);this._makeItem(h,t),s=this._handleObject(a,h)||s}else this._makeCheckbox(a,e,h)}else{const t=this._makeLabel(r,h,!0);this._makeItem(h,t),s=this._handleObject(a,h)||s}}else console.error("dont know how to handle",a,r,h)}}return s}_handleArray(t,e,i){"string"==typeof t[0]&&"color"===t[0]?(this._makeColorField(t,e,i),t[1]!==e&&this.changedOptions.push({path:i,value:e})):"string"==typeof t[0]?(this._makeDropdown(t,e,i),t[0]!==e&&this.changedOptions.push({path:i,value:e})):"number"==typeof t[0]&&(this._makeRange(t,e,i),t[0]!==e&&this.changedOptions.push({path:i,value:Number(e)}))}_update(t,e){const i=this._constructOptions(t,e);this.parent.body&&this.parent.body.emitter&&this.parent.body.emitter.emit&&this.parent.body.emitter.emit("configChange",i),this.initialized=!0,this.parent.setOptions(i)}_constructOptions(t,e,i={}){let o=i;t="false"!==(t="true"===t||t)&&t;for(let i=0;i<e.length;i++)"global"!==e[i]&&(void 0===o[e[i]]&&(o[e[i]]={}),i!==e.length-1?o=o[e[i]]:o[e[i]]=t);return i}_printOptions(){const t=this.getOptions();for(;this.optionsContainer.firstChild;)this.optionsContainer.removeChild(this.optionsContainer.firstChild);this.optionsContainer.appendChild(Fs("pre","const options = "+JSON.stringify(t,null,2)))}getOptions(){const t={};for(let e=0;e<this.changedOptions.length;e++)this._constructOptions(this.changedOptions[e].value,this.changedOptions[e].path,t);return t}},Hs=is,Ws=class{constructor(t,e){this.container=t,this.overflowMethod=e||"cap",this.x=0,this.y=0,this.padding=5,this.hidden=!1,this.frame=document.createElement("div"),this.frame.className="vis-tooltip",this.container.appendChild(this.frame)}setPosition(t,e){this.x=parseInt(t),this.y=parseInt(e)}setText(t){if(t instanceof Element){for(;this.frame.firstChild;)this.frame.removeChild(this.frame.firstChild);this.frame.appendChild(t)}else this.frame.innerText=t}show(t){if(void 0===t&&(t=!0),!0===t){const t=this.frame.clientHeight,e=this.frame.clientWidth,i=this.frame.parentNode.clientHeight,o=this.frame.parentNode.clientWidth;let n=0,s=0;if("flip"==this.overflowMethod){let i=!1,r=!0;this.y-t<this.padding&&(r=!1),this.x+e>o-this.padding&&(i=!0),n=i?this.x-e:this.x,s=r?this.y-t:this.y}else s=this.y-t,s+t+this.padding>i&&(s=i-t-this.padding),s<this.padding&&(s=this.padding),n=this.x,n+e+this.padding>o&&(n=o-e-this.padding),n<this.padding&&(n=this.padding);this.frame.style.left=n+"px",this.frame.style.top=s+"px",this.frame.style.visibility="visible",this.hidden=!1}else this.hide()}hide(){this.hidden=!0,this.frame.style.left="0",this.frame.style.top="0",this.frame.style.visibility="hidden"}destroy(){this.frame.parentNode.removeChild(this.frame)}},Vs=Rs,qs=class t{static validate(e,i,o){As=!1,Ns=i;let n=i;return void 0!==o&&(n=i[o]),t.parse(e,n,[]),As}static parse(e,i,o){for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.check(n,e,i,o)}static check(e,i,o,n){if(void 0===o[e]&&void 0===o.__any__)return void t.getSuggestion(e,o,n);let s=e,r=!0;void 0===o[e]&&void 0!==o.__any__&&(s="__any__",r="object"===t.getType(i[e]));let a=o[s];r&&void 0!==a.__type__&&(a=a.__type__),t.checkFields(e,i,o,s,a,n)}static checkFields(e,i,o,n,s,r){const a=function(i){console.error("%c"+i+t.printLocation(r,e),Rs)},h=t.getType(i[e]),d=s[h];void 0!==d?"array"===t.getType(d)&&-1===d.indexOf(i[e])?(a('Invalid option detected in "'+e+'". Allowed values are:'+t.print(d)+' not "'+i[e]+'". '),As=!0):"object"===h&&"__any__"!==n&&(r=vs(r,e),t.parse(i[e],o[n],r)):void 0===s.any&&(a('Invalid type received for "'+e+'". Expected: '+t.print(Object.keys(s))+". Received ["+h+'] "'+i[e]+'"'),As=!0)}static getType(t){const e=typeof t;return"object"===e?null===t?"null":t instanceof Boolean?"boolean":t instanceof Number?"number":t instanceof String?"string":Array.isArray(t)?"array":t instanceof Date?"date":void 0!==t.nodeType?"dom":!0===t._isAMomentObject?"moment":"object":"number"===e?"number":"boolean"===e?"boolean":"string"===e?"string":void 0===e?"undefined":e}static getSuggestion(e,i,o){const n=t.findInOptions(e,i,o,!1),s=t.findInOptions(e,Ns,[],!0);let r;r=void 0!==n.indexMatch?" in "+t.printLocation(n.path,e,"")+'Perhaps it was incomplete? Did you mean: "'+n.indexMatch+'"?\n\n':s.distance<=4&&n.distance>s.distance?" in "+t.printLocation(n.path,e,"")+"Perhaps it was misplaced? Matching option found at: "+t.printLocation(s.path,s.closestMatch,""):n.distance<=8?'. Did you mean "'+n.closestMatch+'"?'+t.printLocation(n.path,e):". Did you mean one of these: "+t.print(Object.keys(i))+t.printLocation(o,e),console.error('%cUnknown option detected: "'+e+'"'+r,Rs),As=!0}static findInOptions(e,i,o,n=!1){let s=1e9,r="",a=[];const h=e.toLowerCase();let d;for(const l in i){let c;if(void 0!==i[l].__type__&&!0===n){const n=t.findInOptions(e,i[l],vs(o,l));s>n.distance&&(r=n.closestMatch,a=n.path,s=n.distance,d=n.indexMatch)}else-1!==l.toLowerCase().indexOf(h)&&(d=l),c=t.levenshteinDistance(e,l),s>c&&(r=l,a=ms(o),s=c)}return{closestMatch:r,path:a,distance:s,indexMatch:d}}static printLocation(t,e,i="Problem value found at: \n"){let o="\n\n"+i+"options = {\n";for(let e=0;e<t.length;e++){for(let t=0;t<e+1;t++)o+="  ";o+=t[e]+": {\n"}for(let e=0;e<t.length+1;e++)o+="  ";o+=e+"\n";for(let e=0;e<t.length+1;e++){for(let i=0;i<t.length-e;i++)o+="  ";o+="}\n"}return o+"\n\n"}static print(t){return JSON.stringify(t).replace(/(")|(\[)|(\])|(,"__type__")/g,"").replace(/(,)/g,", ")}static levenshteinDistance(t,e){if(0===t.length)return e.length;if(0===e.length)return t.length;const i=[];let o,n;for(o=0;o<=e.length;o++)i[o]=[o];for(n=0;n<=t.length;n++)i[0][n]=n;for(o=1;o<=e.length;o++)for(n=1;n<=t.length;n++)e.charAt(o-1)==t.charAt(n-1)?i[o][n]=i[o-1][n-1]:i[o][n]=Math.min(i[o-1][n-1]+1,Math.min(i[o][n-1]+1,i[o-1][n]+1));return i[e.length][t.length]}};var Us,Ys,Xs,Ks,Gs,Zs,Qs,$s,Js,tr,er,ir,or,nr,sr={};function rr(){if(Us)return sr;Us=1;var t=z(),e=si(),i=ai(),o=ri(),n=Ct(),s=bi();return sr.f=t&&!e?Object.defineProperties:function(t,e){o(t);for(var r,a=n(e),h=s(e),d=h.length,l=0;d>l;)i.f(t,r=h[l++],a[r]);return t},sr}function ar(){return Xs?Ys:(Xs=1,Ys=Tt()("document","documentElement"))}function hr(){if(Gs)return Ks;Gs=1;var t=me(),e=we(),i=t("keys");return Ks=function(t){return i[t]||(i[t]=e(t))}}function dr(){if(Qs)return Zs;Qs=1;var t,e=ri(),i=rr(),o=yi(),n=vi(),s=ar(),r=Oe(),a="prototype",h="script",d=hr()("IE_PROTO"),l=function(){},c=function(t){return"<"+h+">"+t+"</"+h+">"},u=function(t){t.write(c("")),t.close();var e=t.parentWindow.Object;return t=null,e},p=function(){try{t=new ActiveXObject("htmlfile")}catch(t){}var e,i,n;p="undefined"!=typeof document?document.domain&&t?u(t):(i=r("iframe"),n="java"+h+":",i.style.display="none",s.appendChild(i),i.src=String(n),(e=i.contentWindow.document).open(),e.write(c("document.F=Object")),e.close(),e.F):u(t);for(var d=o.length;d--;)delete p[a][o[d]];return p()};return n[d]=!0,Zs=Object.create||function(t,o){var n;return null!==t?(l[a]=e(t),n=new l,l[a]=null,n[d]=t):n=p(),void 0===o?n:i.f(n,o)}}function lr(){if(tr)return Js;tr=1,$s||($s=1,di()({target:"Object",stat:!0,sham:!z()},{create:dr()}));var t=St().Object;return Js=function(e,i){return t.create(e,i)}}function cr(){return ir?er:(ir=1,er=lr())}var ur,pr,fr,gr,vr,mr,yr,br,wr,_r,xr,Er=o(nr?or:(nr=1,or=cr())),Or={};function Cr(){if(pr)return ur;pr=1;var t=x();return ur=function(e,i){var o=[][e];return!!o&&t(function(){o.call(null,i||function(){return 1},1)})}}function kr(){return vr?gr:(vr=1,function(){if(fr)return Or;fr=1;var t=di(),e=S(),i=gi().indexOf,o=Cr(),n=e([].indexOf),s=!!n&&1/n([1],1,-0)<0;t({target:"Array",proto:!0,forced:s||!o("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return s?n(this,t,e)||0:i(this,t,e)}})}(),gr=to()("Array","indexOf"))}function Sr(){if(yr)return mr;yr=1;var t=Mt(),e=kr(),i=Array.prototype;return mr=function(o){var n=o.indexOf;return o===i||t(i,o)&&n===i.indexOf?e:n},mr}function Tr(){return wr?br:(wr=1,br=Sr())}var Mr,Dr,Ir,Pr,Br,zr=o(xr?_r:(xr=1,_r=Tr())),Fr={};function Nr(){return Dr?Mr:(Dr=1,Mr=function(){})}function Ar(){return Br?Pr:(Br=1,function(){if(Ir)return Fr;Ir=1;var t=di(),e=gi().includes,i=x(),o=Nr();t({target:"Array",proto:!0,forced:i(function(){return!Array(1).includes()})},{includes:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0)}}),o("includes")}(),Pr=to()("Array","includes"))}var Rr,jr,Lr,Hr,Wr,Vr,qr,Ur,Yr,Xr,Kr,Gr,Zr,Qr,$r,Jr,ta,ea,ia,oa,na,sa={};function ra(){if(jr)return Rr;jr=1;var t=kt(),e=k(),i=_e()("match");return Rr=function(o){var n;return t(o)&&(void 0!==(n=o[i])?!!n:"RegExp"===e(o))},Rr}function aa(){if(Hr)return Lr;Hr=1;var t=ra(),e=TypeError;return Lr=function(i){if(t(i))throw new e("The method doesn't accept regular expressions");return i},Lr}function ha(){if(Vr)return Wr;Vr=1;var t={};return t[_e()("toStringTag")]="z",Wr="[object z]"===String(t)}function da(){if(Ur)return qr;Ur=1;var t=ha(),e=T(),i=k(),o=_e()("toStringTag"),n=Object,s="Arguments"===i(function(){return arguments}());return qr=t?i:function(t){var r,a,h;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(a=function(t,e){try{return t[e]}catch(t){}}(r=n(t),o))?a:s?i(r):"Object"===(h=i(r))&&e(r.callee)?"Arguments":h},qr}function la(){if(Xr)return Yr;Xr=1;var t=da(),e=String;return Yr=function(i){if("Symbol"===t(i))throw new TypeError("Cannot convert a Symbol value to a string");return e(i)}}function ca(){if(Gr)return Kr;Gr=1;var t=_e()("match");return Kr=function(e){var i=/./;try{"/./"[e](i)}catch(o){try{return i[t]=!1,"/./"[e](i)}catch(t){}}return!1}}function ua(){return $r?Qr:($r=1,function(){if(Zr)return sa;Zr=1;var t=di(),e=C(),i=aa(),o=Ot(),n=la(),s=ca(),r=e("".indexOf);t({target:"String",proto:!0,forced:!s("includes")},{includes:function(t){return!!~r(n(o(this)),n(i(t)),arguments.length>1?arguments[1]:void 0)}})}(),Qr=to()("String","includes"))}function pa(){if(ta)return Jr;ta=1;var t=Mt(),e=Ar(),i=ua(),o=Array.prototype,n=String.prototype;return Jr=function(s){var r=s.includes;return s===o||t(o,s)&&r===o.includes?e:"string"==typeof s||s===n||t(n,s)&&r===n.includes?i:r},Jr}function fa(){return ia?ea:(ia=1,ea=pa())}var ga,va,ma,ya,ba,wa,_a,xa,Ea,Oa,Ca,ka,Sa,Ta,Ma,Da,Ia,Pa,Ba,za,Fa,Na,Aa,Ra,ja,La,Ha,Wa,Va,qa=o(na?oa:(na=1,oa=fa())),Ua={};function Ya(){if(va)return ga;va=1;var t=k();return ga=Array.isArray||function(e){return"Array"===t(e)}}function Xa(){if(ya)return ma;ya=1;var t=z(),e=Ya(),i=TypeError,o=Object.getOwnPropertyDescriptor,n=t&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();return ma=n?function(t,n){if(e(t)&&!o(t,"length").writable)throw new i("Cannot set read only .length");return t.length=n}:function(t,e){return t.length=e}}function Ka(){if(wa)return ba;wa=1;var t=TypeError;return ba=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e},ba}function Ga(){if(xa)return _a;xa=1;var t=C(),e=T(),i=ve(),o=t(Function.toString);return e(i.inspectSource)||(i.inspectSource=function(t){return o(t)}),_a=i.inspectSource}function Za(){if(Oa)return Ea;Oa=1;var t=C(),e=x(),i=T(),o=da(),n=Tt(),s=Ga(),r=function(){},a=n("Reflect","construct"),h=/^\s*(?:class|function)\b/,d=t(h.exec),l=!h.test(r),c=function(t){if(!i(t))return!1;try{return a(r,[],t),!0}catch(t){return!1}},u=function(t){if(!i(t))return!1;switch(o(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return l||!!d(h,s(t))}catch(t){return!0}};return u.sham=!0,Ea=!a||e(function(){var t;return c(c.call)||!c(Object)||!c(function(){t=!0})||t})?u:c}function Qa(){if(ka)return Ca;ka=1;var t=Ya(),e=Za(),i=kt(),o=_e()("species"),n=Array;return Ca=function(s){var r;return t(s)&&(r=s.constructor,(e(r)&&(r===n||t(r.prototype))||i(r)&&null===(r=r[o]))&&(r=void 0)),void 0===r?n:r}}function $a(){if(Ta)return Sa;Ta=1;var t=Qa();return Sa=function(e,i){return new(t(e))(0===i?0:i)}}function Ja(){if(Da)return Ma;Da=1;var t=z(),e=ai(),i=_t();return Ma=function(o,n,s){t?e.f(o,n,i(0,s)):o[n]=s},Ma}function th(){if(Pa)return Ia;Pa=1;var t=Ft(),e=TypeError;return Ia=function(i,o){if(!delete i[o])throw new e("Cannot delete property "+t(o)+" of "+t(i))}}function eh(){if(za)return Ba;za=1;var t=x(),e=_e(),i=It(),o=e("species");return Ba=function(e){return i>=51||!t(function(){var t=[];return(t.constructor={})[o]=function(){return{foo:1}},1!==t[e](Boolean).foo})},Ba}function ih(){return Aa?Na:(Aa=1,function(){if(Fa)return Ua;Fa=1;var t=di(),e=ye(),i=ui(),o=ci(),n=fi(),s=Xa(),r=Ka(),a=$a(),h=Ja(),d=th(),l=eh()("splice"),c=Math.max,u=Math.min;t({target:"Array",proto:!0,forced:!l},{splice:function(t,l){var p,f,g,v,m,y,b=e(this),w=n(b),_=i(t,w),x=arguments.length;for(0===x?p=f=0:1===x?(p=0,f=w-_):(p=x-2,f=u(c(o(l),0),w-_)),r(w+p-f),g=a(b,f),v=0;v<f;v++)(m=_+v)in b&&h(g,v,b[m]);if(g.length=f,p<f){for(v=_;v<w-f;v++)y=v+p,(m=v+f)in b?b[y]=b[m]:d(b,y);for(v=w;v>w-f+p;v--)d(b,v-1)}else if(p>f)for(v=w-f;v>_;v--)y=v+p-1,(m=v+f-1)in b?b[y]=b[m]:d(b,y);for(v=0;v<p;v++)b[v+_]=arguments[v+2];return s(b,w-f+p),g}})}(),Na=to()("Array","splice"))}function oh(){if(ja)return Ra;ja=1;var t=Mt(),e=ih(),i=Array.prototype;return Ra=function(o){var n=o.splice;return o===i||t(i,o)&&n===i.splice?e:n},Ra}function nh(){return Ha?La:(Ha=1,La=oh())}var sh,rh,ah,hh,dh,lh,ch,uh=o(Va?Wa:(Va=1,Wa=nh()));function ph(){return ah?rh:(ah=1,sh||(sh=1,di()({target:"Array",stat:!0},{isArray:Ya()})),rh=St().Array.isArray)}function fh(){return dh?hh:(dh=1,hh=ph())}var gh,vh,mh,yh,bh,wh,_h,xh,Eh,Oh,Ch,kh,Sh,Th=o(ch?lh:(ch=1,lh=fh())),Mh={};function Dh(){if(vh)return gh;vh=1;var t=Te(),e=C(),i=xt(),o=ye(),n=fi(),s=$a(),r=e([].push),a=function(e){var a=1===e,h=2===e,d=3===e,l=4===e,c=6===e,u=7===e,p=5===e||c;return function(f,g,v,m){for(var y,b,w=o(f),_=i(w),x=n(_),E=t(g,v),O=0,C=m||s,k=a?C(f,x):h||u?C(f,0):void 0;x>O;O++)if((p||O in _)&&(b=E(y=_[O],O,w),e))if(a)k[O]=b;else if(b)switch(e){case 3:return!0;case 5:return y;case 6:return O;case 2:r(k,y)}else switch(e){case 4:return!1;case 7:r(k,y)}return c?-1:d||l?l:k}};return gh={forEach:a(0),map:a(1),filter:a(2),some:a(3),every:a(4),find:a(5),findIndex:a(6),filterReject:a(7)}}function Ih(){if(yh)return mh;yh=1;var t=Dh().forEach,e=Cr()("forEach");return mh=e?[].forEach:function(e){return t(this,e,arguments.length>1?arguments[1]:void 0)},mh}function Ph(){return _h?wh:(_h=1,function(){if(bh)return Mh;bh=1;var t=di(),e=Ih();t({target:"Array",proto:!0,forced:[].forEach!==e},{forEach:e})}(),wh=to()("Array","forEach"))}function Bh(){return Eh?xh:(Eh=1,xh=Ph())}function zh(){if(Ch)return Oh;Ch=1;var t=da(),e=be(),i=Mt(),o=Bh(),n=Array.prototype,s={DOMTokenList:!0,NodeList:!0};return Oh=function(r){var a=r.forEach;return r===n||i(n,r)&&a===n.forEach||e(s,t(r))?o:a},Oh}var Fh=o(Sh?kh:(Sh=1,kh=zh()));function Nh(t){return Hh=t,function(){var t={};Wh=0,void(Vh=Hh.charAt(0)),td(),"strict"===qh&&(t.strict=!0,td());"graph"!==qh&&"digraph"!==qh||(t.type=qh,td());Uh===jh.IDENTIFIER&&(t.id=qh,td());if("{"!=qh)throw rd("Angle bracket { expected");if(td(),ed(t),"}"!=qh)throw rd("Angle bracket } expected");if(td(),""!==qh)throw rd("End of file expected");return td(),delete t.node,delete t.edge,delete t.graph,t}()}var Ah={fontsize:"font.size",fontcolor:"font.color",labelfontcolor:"font.color",fontname:"font.face",color:["color.border","color.background"],fillcolor:"color.background",tooltip:"title",labeltooltip:"title"},Rh=Er(Ah);Rh.color="color.color",Rh.style="dashes";var jh={NULL:0,DELIMITER:1,IDENTIFIER:2,UNKNOWN:3},Lh={"{":!0,"}":!0,"[":!0,"]":!0,";":!0,"=":!0,",":!0,"->":!0,"--":!0},Hh="",Wh=0,Vh="",qh="",Uh=jh.NULL;function Yh(){Wh++,Vh=Hh.charAt(Wh)}function Xh(){return Hh.charAt(Wh+1)}function Kh(t){var e=t.charCodeAt(0);return e<47?35===e||46===e:e<59?e>47:e<91?e>64:e<96?95===e:e<123&&e>96}function Gh(t,e){if(t||(t={}),e)for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function Zh(t,e,i){for(var o=e.split("."),n=t;o.length;){var s=o.shift();o.length?(n[s]||(n[s]={}),n=n[s]):n[s]=i}}function Qh(t,e){for(var i,o,n=null,s=[t],r=t;r.parent;)s.push(r.parent),r=r.parent;if(r.nodes)for(i=0,o=r.nodes.length;i<o;i++)if(e.id===r.nodes[i].id){n=r.nodes[i];break}for(n||(n={id:e.id},t.node&&(n.attr=Gh(n.attr,t.node))),i=s.length-1;i>=0;i--){var a,h=s[i];h.nodes||(h.nodes=[]),-1===zr(a=h.nodes).call(a,n)&&h.nodes.push(n)}e.attr&&(n.attr=Gh(n.attr,e.attr))}function $h(t,e){if(t.edges||(t.edges=[]),t.edges.push(e),t.edge){var i=Gh({},t.edge);e.attr=Gh(i,e.attr)}}function Jh(t,e,i,o,n){var s={from:e,to:i,type:o};return t.edge&&(s.attr=Gh({},t.edge)),s.attr=Gh(s.attr||{},n),null!=n&&n.hasOwnProperty("arrows")&&null!=n.arrows&&(s.arrows={to:{enabled:!0,type:n.arrows.type}},n.arrows=null),s}function td(){for(Uh=jh.NULL,qh="";" "===Vh||"\t"===Vh||"\n"===Vh||"\r"===Vh;)Yh();do{var t=!1;if("#"===Vh){for(var e=Wh-1;" "===Hh.charAt(e)||"\t"===Hh.charAt(e);)e--;if("\n"===Hh.charAt(e)||""===Hh.charAt(e)){for(;""!=Vh&&"\n"!=Vh;)Yh();t=!0}}if("/"===Vh&&"/"===Xh()){for(;""!=Vh&&"\n"!=Vh;)Yh();t=!0}if("/"===Vh&&"*"===Xh()){for(;""!=Vh;){if("*"===Vh&&"/"===Xh()){Yh(),Yh();break}Yh()}t=!0}for(;" "===Vh||"\t"===Vh||"\n"===Vh||"\r"===Vh;)Yh()}while(t);if(""!==Vh){var i=Vh+Xh();if(Lh[i])return Uh=jh.DELIMITER,qh=i,Yh(),void Yh();if(Lh[Vh])return Uh=jh.DELIMITER,qh=Vh,void Yh();if(Kh(Vh)||"-"===Vh){for(qh+=Vh,Yh();Kh(Vh);)qh+=Vh,Yh();return"false"===qh?qh=!1:"true"===qh?qh=!0:isNaN(Number(qh))||(qh=Number(qh)),void(Uh=jh.IDENTIFIER)}if('"'===Vh){for(Yh();""!=Vh&&('"'!=Vh||'"'===Vh&&'"'===Xh());)'"'===Vh?(qh+=Vh,Yh()):"\\"===Vh&&"n"===Xh()?(qh+="\n",Yh()):qh+=Vh,Yh();if('"'!=Vh)throw rd('End of string " expected');return Yh(),void(Uh=jh.IDENTIFIER)}for(Uh=jh.UNKNOWN;""!=Vh;)qh+=Vh,Yh();throw new SyntaxError('Syntax error in part "'+ad(qh,30)+'"')}Uh=jh.DELIMITER}function ed(t){for(;""!==qh&&"}"!=qh;)id(t),";"===qh&&td()}function id(t){var e=od(t);if(e)nd(t,e);else{var i=function(t){if("node"===qh)return td(),t.node=sd(),"node";if("edge"===qh)return td(),t.edge=sd(),"edge";if("graph"===qh)return td(),t.graph=sd(),"graph";return null}(t);if(!i){if(Uh!=jh.IDENTIFIER)throw rd("Identifier expected");var o=qh;if(td(),"="===qh){if(td(),Uh!=jh.IDENTIFIER)throw rd("Identifier expected");t[o]=qh,td()}else!function(t,e){var i={id:e},o=sd();o&&(i.attr=o);Qh(t,i),nd(t,e)}(t,o)}}}function od(t){var e=null;if("subgraph"===qh&&((e={}).type="subgraph",td(),Uh===jh.IDENTIFIER&&(e.id=qh,td())),"{"===qh){if(td(),e||(e={}),e.parent=t,e.node=t.node,e.edge=t.edge,e.graph=t.graph,ed(e),"}"!=qh)throw rd("Angle bracket } expected");td(),delete e.node,delete e.edge,delete e.graph,delete e.parent,t.subgraphs||(t.subgraphs=[]),t.subgraphs.push(e)}return e}function nd(t,e){for(;"->"===qh||"--"===qh;){var i,o=qh;td();var n=od(t);if(n)i=n;else{if(Uh!=jh.IDENTIFIER)throw rd("Identifier or subgraph expected");Qh(t,{id:i=qh}),td()}$h(t,Jh(t,e,i,o,sd())),e=i}}function sd(){for(var t,e,i=null,o={dashed:!0,solid:!1,dotted:[1,5]},n={dot:"circle",box:"box",crow:"crow",curve:"curve",icurve:"inv_curve",normal:"triangle",inv:"inv_triangle",diamond:"diamond",tee:"bar",vee:"vee"},s=new Array,r=new Array;"["===qh;){for(td(),i={};""!==qh&&"]"!=qh;){if(Uh!=jh.IDENTIFIER)throw rd("Attribute name expected");var a=qh;if(td(),"="!=qh)throw rd("Equal sign = expected");if(td(),Uh!=jh.IDENTIFIER)throw rd("Attribute value expected");var h=qh;"style"===a&&(h=o[h]),"arrowhead"===a&&(a="arrows",h={to:{enabled:!0,type:n[h]}}),"arrowtail"===a&&(a="arrows",h={from:{enabled:!0,type:n[h]}}),s.push({attr:i,name:a,value:h}),r.push(a),td(),","==qh&&td()}if("]"!=qh)throw rd("Bracket ] expected");td()}if(qa(r).call(r,"dir")){var d={arrows:{}};for(t=0;t<s.length;t++)if("arrows"===s[t].name)if(null!=s[t].value.to)d.arrows.to=t;else{if(null==s[t].value.from)throw rd("Invalid value of arrows");d.arrows.from=t}else"dir"===s[t].name&&(d.dir=t);var l,c,u=s[d.dir].value;if(!qa(r).call(r,"arrows"))if("both"===u)s.push({attr:s[d.dir].attr,name:"arrows",value:{to:{enabled:!0}}}),d.arrows.to=s.length-1,s.push({attr:s[d.dir].attr,name:"arrows",value:{from:{enabled:!0}}}),d.arrows.from=s.length-1;else if("forward"===u)s.push({attr:s[d.dir].attr,name:"arrows",value:{to:{enabled:!0}}}),d.arrows.to=s.length-1;else if("back"===u)s.push({attr:s[d.dir].attr,name:"arrows",value:{from:{enabled:!0}}}),d.arrows.from=s.length-1;else{if("none"!==u)throw rd('Invalid dir type "'+u+'"');s.push({attr:s[d.dir].attr,name:"arrows",value:""}),d.arrows.to=s.length-1}if("both"===u)d.arrows.to&&d.arrows.from?(c=s[d.arrows.to].value.to.type,l=s[d.arrows.from].value.from.type,s[d.arrows.to]={attr:s[d.arrows.to].attr,name:s[d.arrows.to].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}},uh(s).call(s,d.arrows.from,1)):d.arrows.to?(c=s[d.arrows.to].value.to.type,l="arrow",s[d.arrows.to]={attr:s[d.arrows.to].attr,name:s[d.arrows.to].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}):d.arrows.from&&(c="arrow",l=s[d.arrows.from].value.from.type,s[d.arrows.from]={attr:s[d.arrows.from].attr,name:s[d.arrows.from].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}});else if("back"===u)d.arrows.to&&d.arrows.from?(c="",l=s[d.arrows.from].value.from.type,s[d.arrows.from]={attr:s[d.arrows.from].attr,name:s[d.arrows.from].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}):d.arrows.to?(c="",l="arrow",d.arrows.from=d.arrows.to,s[d.arrows.from]={attr:s[d.arrows.from].attr,name:s[d.arrows.from].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}):d.arrows.from&&(c="",l=s[d.arrows.from].value.from.type,s[d.arrows.to]={attr:s[d.arrows.from].attr,name:s[d.arrows.from].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}),s[d.arrows.from]={attr:s[d.arrows.from].attr,name:s[d.arrows.from].name,value:{from:{enabled:!0,type:s[d.arrows.from].value.from.type}}};else if("none"===u){var p;s[p=d.arrows.to?d.arrows.to:d.arrows.from]={attr:s[p].attr,name:s[p].name,value:""}}else{if("forward"!==u)throw rd('Invalid dir type "'+u+'"');d.arrows.to&&d.arrows.from||d.arrows.to?(c=s[d.arrows.to].value.to.type,l="",s[d.arrows.to]={attr:s[d.arrows.to].attr,name:s[d.arrows.to].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}):d.arrows.from&&(c="arrow",l="",d.arrows.to=d.arrows.from,s[d.arrows.to]={attr:s[d.arrows.to].attr,name:s[d.arrows.to].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}),s[d.arrows.to]={attr:s[d.arrows.to].attr,name:s[d.arrows.to].name,value:{to:{enabled:!0,type:s[d.arrows.to].value.to.type}}}}uh(s).call(s,d.dir,1)}if(qa(r).call(r,"penwidth")){var f=[];for(e=s.length,t=0;t<e;t++)"width"!==s[t].name&&("penwidth"===s[t].name&&(s[t].name="width"),f.push(s[t]));s=f}for(e=s.length,t=0;t<e;t++)Zh(s[t].attr,s[t].name,s[t].value);return i}function rd(t){return new SyntaxError(t+', got "'+ad(qh,30)+'" (char '+Wh+")")}function ad(t,e){return t.length<=e?t:t.substr(0,27)+"..."}function hd(t,e,i){for(var o=e.split("."),n=o.pop(),s=t,r=0;r<o.length;r++){var a=o[r];a in s||(s[a]={}),s=s[a]}return s[n]=i,t}function dd(t,e){var i={};for(var o in t)if(t.hasOwnProperty(o)){var n=e[o];Th(n)?Fh(n).call(n,function(e){hd(i,e,t[o])}):hd(i,"string"==typeof n?n:o,t[o])}return i}function ld(t){var e,i=Nh(t),o={nodes:[],edges:[],options:{}};i.nodes&&Fh(e=i.nodes).call(e,function(t){var e={id:t.id,label:String(t.label||t.id)};Gh(e,dd(t.attr,Ah)),e.image&&(e.shape="image"),o.nodes.push(e)});if(i.edges){var n,s=function(t){var e={from:t.from,to:t.to};return Gh(e,dd(t.attr,Rh)),null==e.arrows&&"->"===t.type&&(e.arrows="to"),e};Fh(n=i.edges).call(n,function(t){var e,i,n,r,a,h,d;(e=t.from instanceof Object?t.from.nodes:{id:t.from},i=t.to instanceof Object?t.to.nodes:{id:t.to},t.from instanceof Object&&t.from.edges)&&Fh(n=t.from.edges).call(n,function(t){var e=s(t);o.edges.push(e)});(a=i,h=function(e,i){var n=Jh(o,e.id,i.id,t.type,t.attr),r=s(n);o.edges.push(r)},Th(r=e)?Fh(r).call(r,function(t){Th(a)?Fh(a).call(a,function(e){h(t,e)}):h(t,a)}):Th(a)?Fh(a).call(a,function(t){h(r,t)}):h(r,a),t.to instanceof Object&&t.to.edges)&&Fh(d=t.to.edges).call(d,function(t){var e=s(t);o.edges.push(e)})})}return i.attr&&(o.options=i.attr),o}var cd,ud,pd,fd,gd,vd,md,yd,bd,wd=Object.freeze({__proto__:null,DOTToGraph:ld,parseDOT:Nh}),_d={};function xd(){return pd?ud:(pd=1,function(){if(cd)return _d;cd=1;var t=di(),e=Dh().map;t({target:"Array",proto:!0,forced:!eh()("map")},{map:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0)}})}(),ud=to()("Array","map"))}function Ed(){if(gd)return fd;gd=1;var t=Mt(),e=xd(),i=Array.prototype;return fd=function(o){var n=o.map;return o===i||t(i,o)&&n===i.map?e:n},fd}function Od(){return md?vd:(md=1,vd=Ed())}var Cd=o(bd?yd:(bd=1,yd=Od()));function kd(t,e){var i;const o={edges:{inheritColor:!1},nodes:{fixed:!1,parseColor:!1}};null!=e&&(null!=e.fixed&&(o.nodes.fixed=e.fixed),null!=e.parseColor&&(o.nodes.parseColor=e.parseColor),null!=e.inheritColor&&(o.edges.inheritColor=e.inheritColor));const n=t.edges,s=Cd(n).call(n,t=>{const e={from:t.source,id:t.id,to:t.target};return null!=t.attributes&&(e.attributes=t.attributes),null!=t.label&&(e.label=t.label),null!=t.attributes&&null!=t.attributes.title&&(e.title=t.attributes.title),"Directed"===t.type&&(e.arrows="to"),t.color&&!1===o.edges.inheritColor&&(e.color=t.color),e});return{nodes:Cd(i=t.nodes).call(i,t=>{const e={id:t.id,fixed:o.nodes.fixed&&null!=t.x&&null!=t.y};return null!=t.attributes&&(e.attributes=t.attributes),null!=t.label&&(e.label=t.label),null!=t.size&&(e.size=t.size),null!=t.attributes&&null!=t.attributes.title&&(e.title=t.attributes.title),null!=t.title&&(e.title=t.title),null!=t.x&&(e.x=t.x),null!=t.y&&(e.y=t.y),null!=t.color&&(!0===o.nodes.parseColor?e.color=t.color:e.color={background:t.color,border:t.color,highlight:{background:t.color,border:t.color},hover:{background:t.color,border:t.color}}),e}),edges:s}}var Sd=Object.freeze({__proto__:null,parseGephi:kd});var Td,Md,Dd,Id,Pd,Bd,zd,Fd,Nd,Ad=Object.freeze({__proto__:null,cn:{addDescription:"单击空白处放置新节点。",addEdge:"添加连接线",addNode:"添加节点",back:"返回",close:"關閉",createEdgeError:"无法将连接线连接到群集。",del:"删除选定",deleteClusterError:"无法删除群集。",edgeDescription:"单击某个节点并将该连接线拖动到另一个节点以连接它们。",edit:"编辑",editClusterError:"无法编辑群集。",editEdge:"编辑连接线",editEdgeDescription:"单击控制节点并将它们拖到节点上连接。",editNode:"编辑节点"},cs:{addDescription:"Kluknutím do prázdného prostoru můžete přidat nový vrchol.",addEdge:"Přidat hranu",addNode:"Přidat vrchol",back:"Zpět",close:"Zavřít",createEdgeError:"Nelze připojit hranu ke shluku.",del:"Smazat výběr",deleteClusterError:"Nelze mazat shluky.",edgeDescription:"Přetažením z jednoho vrcholu do druhého můžete spojit tyto vrcholy novou hranou.",edit:"Upravit",editClusterError:"Nelze upravovat shluky.",editEdge:"Upravit hranu",editEdgeDescription:"Přetažením kontrolního vrcholu hrany ji můžete připojit k jinému vrcholu.",editNode:"Upravit vrchol"},de:{addDescription:"Klicke auf eine freie Stelle, um einen neuen Knoten zu plazieren.",addEdge:"Kante hinzufügen",addNode:"Knoten hinzufügen",back:"Zurück",close:"Schließen",createEdgeError:"Es ist nicht möglich, Kanten mit Clustern zu verbinden.",del:"Lösche Auswahl",deleteClusterError:"Cluster können nicht gelöscht werden.",edgeDescription:"Klicke auf einen Knoten und ziehe die Kante zu einem anderen Knoten, um diese zu verbinden.",edit:"Editieren",editClusterError:"Cluster können nicht editiert werden.",editEdge:"Kante editieren",editEdgeDescription:"Klicke auf die Verbindungspunkte und ziehe diese auf einen Knoten, um sie zu verbinden.",editNode:"Knoten editieren"},en:{addDescription:"Click in an empty space to place a new node.",addEdge:"Add Edge",addNode:"Add Node",back:"Back",close:"Close",createEdgeError:"Cannot link edges to a cluster.",del:"Delete selected",deleteClusterError:"Clusters cannot be deleted.",edgeDescription:"Click on a node and drag the edge to another node to connect them.",edit:"Edit",editClusterError:"Clusters cannot be edited.",editEdge:"Edit Edge",editEdgeDescription:"Click on the control points and drag them to a node to connect to it.",editNode:"Edit Node"},es:{addDescription:"Haga clic en un lugar vacío para colocar un nuevo nodo.",addEdge:"Añadir arista",addNode:"Añadir nodo",back:"Atrás",close:"Cerrar",createEdgeError:"No se puede conectar una arista a un grupo.",del:"Eliminar selección",deleteClusterError:"No es posible eliminar grupos.",edgeDescription:"Haga clic en un nodo y arrastre la arista hacia otro nodo para conectarlos.",edit:"Editar",editClusterError:"No es posible editar grupos.",editEdge:"Editar arista",editEdgeDescription:"Haga clic en un punto de control y arrastrelo a un nodo para conectarlo.",editNode:"Editar nodo"},fr:{addDescription:"Cliquez dans un endroit vide pour placer un nœud.",addEdge:"Ajouter un lien",addNode:"Ajouter un nœud",back:"Retour",close:"Fermer",createEdgeError:"Impossible de créer un lien vers un cluster.",del:"Effacer la sélection",deleteClusterError:"Les clusters ne peuvent pas être effacés.",edgeDescription:"Cliquez sur un nœud et glissez le lien vers un autre nœud pour les connecter.",edit:"Éditer",editClusterError:"Les clusters ne peuvent pas être édités.",editEdge:"Éditer le lien",editEdgeDescription:"Cliquez sur les points de contrôle et glissez-les pour connecter un nœud.",editNode:"Éditer le nœud"},it:{addDescription:"Clicca per aggiungere un nuovo nodo",addEdge:"Aggiungi un vertice",addNode:"Aggiungi un nodo",back:"Indietro",close:"Chiudere",createEdgeError:"Non si possono collegare vertici ad un cluster",del:"Cancella la selezione",deleteClusterError:"I cluster non possono essere cancellati",edgeDescription:"Clicca su un nodo e trascinalo ad un altro nodo per connetterli.",edit:"Modifica",editClusterError:"I clusters non possono essere modificati.",editEdge:"Modifica il vertice",editEdgeDescription:"Clicca sui Punti di controllo e trascinali ad un nodo per connetterli.",editNode:"Modifica il nodo"},nl:{addDescription:"Klik op een leeg gebied om een nieuwe node te maken.",addEdge:"Link toevoegen",addNode:"Node toevoegen",back:"Terug",close:"Sluiten",createEdgeError:"Kan geen link maken naar een cluster.",del:"Selectie verwijderen",deleteClusterError:"Clusters kunnen niet worden verwijderd.",edgeDescription:"Klik op een node en sleep de link naar een andere node om ze te verbinden.",edit:"Wijzigen",editClusterError:"Clusters kunnen niet worden aangepast.",editEdge:"Link wijzigen",editEdgeDescription:"Klik op de verbindingspunten en sleep ze naar een node om daarmee te verbinden.",editNode:"Node wijzigen"},pt:{addDescription:"Clique em um espaço em branco para adicionar um novo nó",addEdge:"Adicionar aresta",addNode:"Adicionar nó",back:"Voltar",close:"Fechar",createEdgeError:"Não foi possível linkar arestas a um cluster.",del:"Remover selecionado",deleteClusterError:"Clusters não puderam ser removidos.",edgeDescription:"Clique em um nó e arraste a aresta até outro nó para conectá-los",edit:"Editar",editClusterError:"Clusters não puderam ser editados.",editEdge:"Editar aresta",editEdgeDescription:"Clique nos pontos de controle e os arraste para um nó para conectá-los",editNode:"Editar nó"},ru:{addDescription:"Кликните в свободное место, чтобы добавить новый узел.",addEdge:"Добавить ребро",addNode:"Добавить узел",back:"Назад",close:"Закрывать",createEdgeError:"Невозможно соединить ребра в кластер.",del:"Удалить выбранное",deleteClusterError:"Кластеры не могут быть удалены",edgeDescription:"Кликните на узел и протяните ребро к другому узлу, чтобы соединить их.",edit:"Редактировать",editClusterError:"Кластеры недоступны для редактирования.",editEdge:"Редактировать ребро",editEdgeDescription:"Кликните на контрольные точки и перетащите их в узел, чтобы подключиться к нему.",editNode:"Редактировать узел"},uk:{addDescription:"Kлікніть на вільне місце, щоб додати новий вузол.",addEdge:"Додати край",addNode:"Додати вузол",back:"Назад",close:"Закрити",createEdgeError:"Не можливо об'єднати краї в групу.",del:"Видалити обране",deleteClusterError:"Групи не можуть бути видалені.",edgeDescription:"Клікніть на вузол і перетягніть край до іншого вузла, щоб їх з'єднати.",edit:"Редагувати",editClusterError:"Групи недоступні для редагування.",editEdge:"Редагувати край",editEdgeDescription:"Клікніть на контрольні точки і перетягніть їх у вузол, щоб підключитися до нього.",editNode:"Редагувати вузол"}}),Rd={};function jd(){if(Td)return Rd;Td=1;var t=di(),e=x(),i=Ya(),o=kt(),n=ye(),s=fi(),r=Ka(),a=Ja(),h=$a(),d=eh(),l=_e(),c=It(),u=l("isConcatSpreadable"),p=c>=51||!e(function(){var t=[];return t[u]=!1,t.concat()[0]!==t}),f=function(t){if(!o(t))return!1;var e=t[u];return void 0!==e?!!e:i(t)};return t({target:"Array",proto:!0,arity:1,forced:!p||!d("concat")},{concat:function(t){var e,i,o,d,l,c=n(this),u=h(c,0),p=0;for(e=-1,o=arguments.length;e<o;e++)if(f(l=-1===e?c:arguments[e]))for(d=s(l),r(p+d),i=0;i<d;i++,p++)i in l&&a(u,p,l[i]);else r(p+1),a(u,p++,l);return u.length=p,u}}),Rd}function Ld(){return Dd?Md:(Dd=1,jd(),Md=to()("Array","concat"))}function Hd(){if(Pd)return Id;Pd=1;var t=Mt(),e=Ld(),i=Array.prototype;return Id=function(o){var n=o.concat;return o===i||t(i,o)&&n===i.concat?e:n},Id}function Wd(){return zd?Bd:(zd=1,Bd=Hd())}var Vd,qd,Ud,Yd,Xd,Kd,Gd,Zd,Qd,$d,Jd,tl,el,il,ol,nl,sl,rl,al,hl,dl,ll,cl,ul,pl,fl,gl,vl,ml,yl,bl,wl,_l,xl,El,Ol,Cl=o(Nd?Fd:(Nd=1,Fd=Wd()));class kl{constructor(){this.NUM_ITERATIONS=4,this.image=new Image,this.canvas=document.createElement("canvas")}init(){if(this.initialized())return;this.src=this.image.src;const t=this.image.width,e=this.image.height;this.width=t,this.height=e;const i=Math.floor(e/2),o=Math.floor(e/4),n=Math.floor(e/8),s=Math.floor(e/16),r=Math.floor(t/2),a=Math.floor(t/4),h=Math.floor(t/8),d=Math.floor(t/16);this.canvas.width=3*a,this.canvas.height=i,this.coordinates=[[0,0,r,i],[r,0,a,o],[r,o,h,n],[5*h,o,d,s]],this._fillMipMap()}initialized(){return void 0!==this.coordinates}_fillMipMap(){const t=this.canvas.getContext("2d"),e=this.coordinates[0];t.drawImage(this.image,e[0],e[1],e[2],e[3]);for(let e=1;e<this.NUM_ITERATIONS;e++){const i=this.coordinates[e-1],o=this.coordinates[e];t.drawImage(this.canvas,i[0],i[1],i[2],i[3],o[0],o[1],o[2],o[3])}}drawImageAtPosition(t,e,i,o,n,s){if(this.initialized())if(e>2){e*=.5;let r=0;for(;e>2&&r<this.NUM_ITERATIONS;)e*=.5,r+=1;r>=this.NUM_ITERATIONS&&(r=this.NUM_ITERATIONS-1);const a=this.coordinates[r];t.drawImage(this.canvas,a[0],a[1],a[2],a[3],i,o,n,s)}else t.drawImage(this.image,i,o,n,s)}}class Sl{constructor(t){this.images={},this.imageBroken={},this.callback=t}_tryloadBrokenUrl(t,e,i){void 0!==t&&void 0!==i&&(void 0!==e?(i.image.onerror=()=>{console.error("Could not load brokenImage:",e)},i.image.src=e):console.warn("No broken url image defined"))}_redrawWithImage(t){this.callback&&this.callback(t)}load(t,e){const i=this.images[t];if(i)return i;const o=new kl;return this.images[t]=o,o.image.onload=()=>{this._fixImageCoordinates(o.image),o.init(),this._redrawWithImage(o)},o.image.onerror=()=>{console.error("Could not load image:",t),this._tryloadBrokenUrl(t,e,o)},o.image.src=t,o}_fixImageCoordinates(t){0===t.width&&(document.body.appendChild(t),t.width=t.offsetWidth,t.height=t.offsetHeight,document.body.removeChild(t))}}function Tl(){return qd?Vd:(qd=1,Vd={})}function Ml(){if(Yd)return Ud;Yd=1;var t=_(),e=T(),i=t.WeakMap;return Ud=e(i)&&/native code/.test(String(i))}function Dl(){if(Kd)return Xd;Kd=1;var t,e,i,o=Ml(),n=_(),s=kt(),r=hi(),a=be(),h=ve(),d=hr(),l=vi(),c="Object already initialized",u=n.TypeError,p=n.WeakMap;if(o||h.state){var f=h.state||(h.state=new p);f.get=f.get,f.has=f.has,f.set=f.set,t=function(t,e){if(f.has(t))throw new u(c);return e.facade=t,f.set(t,e),e},e=function(t){return f.get(t)||{}},i=function(t){return f.has(t)}}else{var g=d("state");l[g]=!0,t=function(t,e){if(a(t,g))throw new u(c);return e.facade=t,r(t,g,e),e},e=function(t){return a(t,g)?t[g]:{}},i=function(t){return a(t,g)}}return Xd={set:t,get:e,has:i,enforce:function(o){return i(o)?e(o):t(o,{})},getterFor:function(t){return function(i){var o;if(!s(i)||(o=e(i)).type!==t)throw new u("Incompatible receiver, "+t+" required");return o}}},Xd}function Il(){if(Zd)return Gd;Zd=1;var t=z(),e=be(),i=Function.prototype,o=t&&Object.getOwnPropertyDescriptor,n=e(i,"name"),s=n&&"something"===function(){}.name,r=n&&(!t||t&&o(i,"name").configurable);return Gd={EXISTS:n,PROPER:s,CONFIGURABLE:r}}function Pl(){return $d?Qd:($d=1,Qd=!x()(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))}function Bl(){if(tl)return Jd;tl=1;var t=be(),e=T(),i=ye(),o=hr(),n=Pl(),s=o("IE_PROTO"),r=Object,a=r.prototype;return Jd=n?r.getPrototypeOf:function(o){var n=i(o);if(t(n,s))return n[s];var h=n.constructor;return e(h)&&n instanceof h?h.prototype:n instanceof r?a:null},Jd}function zl(){if(il)return el;il=1;var t=hi();return el=function(e,i,o,n){return n&&n.enumerable?e[i]=o:t(e,i,o),e},el}function Fl(){if(nl)return ol;nl=1;var t,e,i,o=x(),n=T(),s=kt(),r=dr(),a=Bl(),h=zl(),d=_e(),l=fe(),c=d("iterator"),u=!1;return[].keys&&("next"in(i=[].keys())?(e=a(a(i)))!==Object.prototype&&(t=e):u=!0),!s(t)||o(function(){var e={};return t[c].call(e)!==e})?t={}:l&&(t=r(t)),n(t[c])||h(t,c,function(){return this}),ol={IteratorPrototype:t,BUGGY_SAFARI_ITERATORS:u}}function Nl(){if(rl)return sl;rl=1;var t=ha(),e=da();return sl=t?{}.toString:function(){return"[object "+e(this)+"]"}}function Al(){if(hl)return al;hl=1;var t=ha(),e=ai().f,i=hi(),o=be(),n=Nl(),s=_e()("toStringTag");return al=function(r,a,h,d){var l=h?r:r&&r.prototype;l&&(o(l,s)||e(l,s,{configurable:!0,value:a}),d&&!t&&i(l,"toString",n))},al}function Rl(){if(ll)return dl;ll=1;var t=Fl().IteratorPrototype,e=dr(),i=_t(),o=Al(),n=Tl(),s=function(){return this};return dl=function(r,a,h,d){var l=a+" Iterator";return r.prototype=e(t,{next:i(+!d,h)}),o(r,l,!1,!0),n[l]=s,r},dl}function jl(){if(ul)return cl;ul=1;var t=C(),e=Nt();return cl=function(i,o,n){try{return t(e(Object.getOwnPropertyDescriptor(i,o)[n]))}catch(t){}},cl}function Ll(){if(fl)return pl;fl=1;var t=kt();return pl=function(e){return t(e)||null===e}}function Hl(){if(vl)return gl;vl=1;var t=Ll(),e=String,i=TypeError;return gl=function(o){if(t(o))return o;throw new i("Can't set "+e(o)+" as a prototype")}}function Wl(){if(yl)return ml;yl=1;var t=jl(),e=kt(),i=Ot(),o=Hl();return ml=Object.setPrototypeOf||("__proto__"in{}?function(){var n,s=!1,r={};try{(n=t(Object.prototype,"__proto__","set"))(r,[]),s=r instanceof Array}catch(t){}return function(t,r){return i(t),o(r),e(t)?(s?n(t,r):t.__proto__=r,t):t}}():void 0)}function Vl(){if(wl)return bl;wl=1;var t=di(),e=F(),i=fe(),o=Il(),n=T(),s=Rl(),r=Bl(),a=Wl(),h=Al(),d=hi(),l=zl(),c=_e(),u=Tl(),p=Fl(),f=o.PROPER,g=o.CONFIGURABLE,v=p.IteratorPrototype,m=p.BUGGY_SAFARI_ITERATORS,y=c("iterator"),b="keys",w="values",_="entries",x=function(){return this};return bl=function(o,c,p,E,O,C,k){s(p,c,E);var S,T,M,D=function(t){if(t===O&&F)return F;if(!m&&t&&t in B)return B[t];switch(t){case b:case w:case _:return function(){return new p(this,t)}}return function(){return new p(this)}},I=c+" Iterator",P=!1,B=o.prototype,z=B[y]||B["@@iterator"]||O&&B[O],F=!m&&z||D(O),N="Array"===c&&B.entries||z;if(N&&(S=r(N.call(new o)))!==Object.prototype&&S.next&&(i||r(S)===v||(a?a(S,v):n(S[y])||l(S,y,x)),h(S,I,!0,!0),i&&(u[I]=x)),f&&O===w&&z&&z.name!==w&&(!i&&g?d(B,"name",w):(P=!0,F=function(){return e(z,this)})),O)if(T={values:D(w),keys:C?F:D(b),entries:D(_)},k)for(M in T)(m||P||!(M in B))&&l(B,M,T[M]);else t({target:c,proto:!0,forced:m||P},T);return i&&!k||B[y]===F||l(B,y,F,{name:O}),u[c]=F,T},bl}function ql(){return xl?_l:(xl=1,_l=function(t,e){return{value:t,done:e}})}function Ul(){if(Ol)return El;Ol=1;var t=Ct(),e=Nr(),i=Tl(),o=Dl(),n=ai().f,s=Vl(),r=ql(),a=fe(),h=z(),d="Array Iterator",l=o.set,c=o.getterFor(d);El=s(Array,"Array",function(e,i){l(this,{type:d,target:t(e),index:0,kind:i})},function(){var t=c(this),e=t.target,i=t.index++;if(!e||i>=e.length)return t.target=null,r(void 0,!0);switch(t.kind){case"keys":return r(i,!1);case"values":return r(e[i],!1)}return r([i,e[i]],!1)},"values");var u=i.Arguments=i.Array;if(e("keys"),e("values"),e("entries"),!a&&h&&"values"!==u.name)try{n(u,"name",{value:"values"})}catch(t){}return El}var Yl,Xl={},Kl={exports:{}},Gl={};function Zl(){if(Yl)return Gl;Yl=1;var t=mi(),e=yi().concat("length","prototype");return Gl.f=Object.getOwnPropertyNames||function(i){return t(i,e)},Gl}var Ql,$l,Jl,tc,ec,ic,oc,nc,sc,rc,ac,hc,dc,lc,cc,uc,pc,fc,gc,vc,mc,yc,bc,wc,_c,xc,Ec,Oc,Cc,kc,Sc,Tc,Mc={};function Dc(){if(Ql)return Mc;Ql=1;var t=k(),e=Ct(),i=Zl().f,o=$i(),n="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];return Mc.f=function(s){return n&&"Window"===t(s)?function(t){try{return i(t)}catch(t){return o(n)}}(s):i(e(s))},Mc}function Ic(){return Jl?$l:(Jl=1,$l=x()(function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}}))}function Pc(){if(ec)return tc;ec=1;var t=x(),e=kt(),i=k(),o=Ic(),n=Object.isExtensible,s=t(function(){});return tc=s||o?function(t){return!!e(t)&&((!o||"ArrayBuffer"!==i(t))&&(!n||n(t)))}:n,tc}function Bc(){return oc?ic:(oc=1,ic=!x()(function(){return Object.isExtensible(Object.preventExtensions({}))}))}function zc(){if(nc)return Kl.exports;nc=1;var t=di(),e=C(),i=vi(),o=kt(),n=be(),s=ai().f,r=Zl(),a=Dc(),h=Pc(),d=we(),l=Bc(),c=!1,u=d("meta"),p=0,f=function(t){s(t,u,{value:{objectID:"O"+p++,weakData:{}}})},g=Kl.exports={enable:function(){g.enable=function(){},c=!0;var i=r.f,o=e([].splice),n={};n[u]=1,i(n).length&&(r.f=function(t){for(var e=i(t),n=0,s=e.length;n<s;n++)if(e[n]===u){o(e,n,1);break}return e},t({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:a.f}))},fastKey:function(t,e){if(!o(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!n(t,u)){if(!h(t))return"F";if(!e)return"E";f(t)}return t[u].objectID},getWeakData:function(t,e){if(!n(t,u)){if(!h(t))return!0;if(!e)return!1;f(t)}return t[u].weakData},onFreeze:function(t){return l&&c&&h(t)&&!n(t,u)&&f(t),t}};return i[u]=!0,Kl.exports}function Fc(){if(rc)return sc;rc=1;var t=_e(),e=Tl(),i=t("iterator"),o=Array.prototype;return sc=function(t){return void 0!==t&&(e.Array===t||o[i]===t)},sc}function Nc(){if(hc)return ac;hc=1;var t=da(),e=At(),i=Et(),o=Tl(),n=_e()("iterator");return ac=function(s){if(!i(s))return e(s,n)||e(s,"@@iterator")||o[t(s)]},ac}function Ac(){if(lc)return dc;lc=1;var t=F(),e=Nt(),i=ri(),o=Ft(),n=Nc(),s=TypeError;return dc=function(r,a){var h=arguments.length<2?n(r):a;if(e(h))return i(t(h,r));throw new s(o(r)+" is not iterable")},dc}function Rc(){if(uc)return cc;uc=1;var t=F(),e=ri(),i=At();return cc=function(o,n,s){var r,a;e(o);try{if(!(r=i(o,"return"))){if("throw"===n)throw s;return s}r=t(r,o)}catch(t){a=!0,r=t}if("throw"===n)throw s;if(a)throw r;return e(r),s},cc}function jc(){if(fc)return pc;fc=1;var t=Te(),e=F(),i=ri(),o=Ft(),n=Fc(),s=fi(),r=Mt(),a=Ac(),h=Nc(),d=Rc(),l=TypeError,c=function(t,e){this.stopped=t,this.result=e},u=c.prototype;return pc=function(p,f,g){var v,m,y,b,w,_,x,E=g&&g.that,O=!(!g||!g.AS_ENTRIES),C=!(!g||!g.IS_RECORD),k=!(!g||!g.IS_ITERATOR),S=!(!g||!g.INTERRUPTED),T=t(f,E),M=function(t){return v&&d(v,"normal"),new c(!0,t)},D=function(t){return O?(i(t),S?T(t[0],t[1],M):T(t[0],t[1])):S?T(t,M):T(t)};if(C)v=p.iterator;else if(k)v=p;else{if(!(m=h(p)))throw new l(o(p)+" is not iterable");if(n(m)){for(y=0,b=s(p);b>y;y++)if((w=D(p[y]))&&r(u,w))return w;return new c(!1)}v=a(p,m)}for(_=C?p.next:v.next;!(x=e(_,v)).done;){try{w=D(x.value)}catch(t){d(v,"throw",t)}if("object"==typeof w&&w&&r(u,w))return w}return new c(!1)},pc}function Lc(){if(vc)return gc;vc=1;var t=Mt(),e=TypeError;return gc=function(i,o){if(t(o,i))return i;throw new e("Incorrect invocation")},gc}function Hc(){if(yc)return mc;yc=1;var t=di(),e=_(),i=zc(),o=x(),n=hi(),s=jc(),r=Lc(),a=T(),h=kt(),d=Et(),l=Al(),c=ai().f,u=Dh().forEach,p=z(),f=Dl(),g=f.set,v=f.getterFor;return mc=function(f,m,y){var b,w=-1!==f.indexOf("Map"),_=-1!==f.indexOf("Weak"),x=w?"set":"add",E=e[f],O=E&&E.prototype,C={};if(p&&a(E)&&(_||O.forEach&&!o(function(){(new E).entries().next()}))){var k=(b=m(function(t,e){g(r(t,k),{type:f,collection:new E}),d(e)||s(e,t[x],{that:t,AS_ENTRIES:w})})).prototype,S=v(f);u(["add","clear","delete","forEach","get","has","set","keys","values","entries"],function(t){var e="add"===t||"set"===t;!(t in O)||_&&"clear"===t||n(k,t,function(i,o){var n=S(this).collection;if(!e&&_&&!h(i))return"get"===t&&void 0;var s=n[t](0===i?0:i,o);return e?this:s})}),_||c(k,"size",{configurable:!0,get:function(){return S(this).collection.size}})}else b=y.getConstructor(m,f,w,x),i.enable();return l(b,f,!1,!0),C[f]=b,t({global:!0,forced:!0},C),_||y.setStrong(b,f,w),b}}function Wc(){if(wc)return bc;wc=1;var t=ai();return bc=function(e,i,o){return t.f(e,i,o)}}function Vc(){if(xc)return _c;xc=1;var t=zl();return _c=function(e,i,o){for(var n in i)o&&o.unsafe&&e[n]?e[n]=i[n]:t(e,n,i[n],o);return e},_c}function qc(){if(Oc)return Ec;Oc=1;var t=Tt(),e=Wc(),i=_e(),o=z(),n=i("species");return Ec=function(i){var s=t(i);o&&s&&!s[n]&&e(s,n,{configurable:!0,get:function(){return this}})}}function Uc(){if(kc)return Cc;kc=1;var t=dr(),e=Wc(),i=Vc(),o=Te(),n=Lc(),s=Et(),r=jc(),a=Vl(),h=ql(),d=qc(),l=z(),c=zc().fastKey,u=Dl(),p=u.set,f=u.getterFor;return Cc={getConstructor:function(a,h,d,u){var g=a(function(e,i){n(e,v),p(e,{type:h,index:t(null),first:null,last:null,size:0}),l||(e.size=0),s(i)||r(i,e[u],{that:e,AS_ENTRIES:d})}),v=g.prototype,m=f(h),y=function(t,e,i){var o,n,s=m(t),r=b(t,e);return r?r.value=i:(s.last=r={index:n=c(e,!0),key:e,value:i,previous:o=s.last,next:null,removed:!1},s.first||(s.first=r),o&&(o.next=r),l?s.size++:t.size++,"F"!==n&&(s.index[n]=r)),t},b=function(t,e){var i,o=m(t),n=c(e);if("F"!==n)return o.index[n];for(i=o.first;i;i=i.next)if(i.key===e)return i};return i(v,{clear:function(){for(var e=m(this),i=e.first;i;)i.removed=!0,i.previous&&(i.previous=i.previous.next=null),i=i.next;e.first=e.last=null,e.index=t(null),l?e.size=0:this.size=0},delete:function(t){var e=this,i=m(e),o=b(e,t);if(o){var n=o.next,s=o.previous;delete i.index[o.index],o.removed=!0,s&&(s.next=n),n&&(n.previous=s),i.first===o&&(i.first=n),i.last===o&&(i.last=s),l?i.size--:e.size--}return!!o},forEach:function(t){for(var e,i=m(this),n=o(t,arguments.length>1?arguments[1]:void 0);e=e?e.next:i.first;)for(n(e.value,e.key,this);e&&e.removed;)e=e.previous},has:function(t){return!!b(this,t)}}),i(v,d?{get:function(t){var e=b(this,t);return e&&e.value},set:function(t,e){return y(this,0===t?0:t,e)}}:{add:function(t){return y(this,t=0===t?0:t,t)}}),l&&e(v,"size",{configurable:!0,get:function(){return m(this).size}}),g},setStrong:function(t,e,i){var o=e+" Iterator",n=f(e),s=f(o);a(t,e,function(t,e){p(this,{type:o,target:t,state:n(t),kind:e,last:null})},function(){for(var t=s(this),e=t.kind,i=t.last;i&&i.removed;)i=i.previous;return t.target&&(t.last=i=i?i.next:t.state.first)?h("keys"===e?i.key:"values"===e?i.value:[i.key,i.value],!1):(t.target=null,h(void 0,!0))},i?"entries":"values",!i,!0),d(e)}},Cc}function Yc(){return Tc||(Tc=1,Sc||(Sc=1,Hc()("Map",function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},Uc()))),Xl}var Xc,Kc,Gc,Zc,Qc,$c={};function Jc(){return Kc||(Kc=1,Xc=function(t,e){return 1===e?function(e,i){return e[t](i)}:function(e,i,o){return e[t](i,o)}}),Xc}function tu(){if(Zc)return Gc;Zc=1;var t=Tt(),e=Jc(),i=t("Map");return Gc={Map:i,set:e("set",2),get:e("get",1),has:e("has",1),remove:e("delete",1),proto:i.prototype}}var eu,iu,ou,nu,su,ru={};function au(){if(iu)return eu;iu=1;var t=C(),e=ci(),i=la(),o=Ot(),n=t("".charAt),s=t("".charCodeAt),r=t("".slice),a=function(t){return function(a,h){var d,l,c=i(o(a)),u=e(h),p=c.length;return u<0||u>=p?t?"":void 0:(d=s(c,u))<55296||d>56319||u+1===p||(l=s(c,u+1))<56320||l>57343?t?n(c,u):d:t?r(c,u,u+2):l-56320+(d-55296<<10)+65536}};return eu={codeAt:a(!1),charAt:a(!0)}}function hu(){if(ou)return ru;ou=1;var t=au().charAt,e=la(),i=Dl(),o=Vl(),n=ql(),s="String Iterator",r=i.set,a=i.getterFor(s);return o(String,"String",function(t){r(this,{type:s,string:e(t),index:0})},function(){var e,i=a(this),o=i.string,s=i.index;return s>=o.length?n(void 0,!0):(e=t(o,s),i.index+=e.length,n(e,!1))}),ru}function du(){return su?nu:(su=1,Ul(),Yc(),function(){if(Qc)return $c;Qc=1;var t=di(),e=C(),i=Nt(),o=Ot(),n=jc(),s=tu(),r=fe(),a=x(),h=s.Map,d=s.has,l=s.get,c=s.set,u=e([].push),p=r||a(function(){return 1!==h.groupBy("ab",function(t){return t}).get("a").length});t({target:"Map",stat:!0,forced:r||p},{groupBy:function(t,e){o(t),i(e);var s=new h,r=0;return n(t,function(t){var i=e(t,r++);d(s,i)?u(l(s,i),t):c(s,i,[t])}),s}})}(),hu(),nu=St().Map)}var lu,cu,uu,pu,fu,gu,vu,mu={};function yu(){return cu?lu:(cu=1,lu={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0})}function bu(){if(uu)return mu;uu=1,Ul();var t=yu(),e=_(),i=Al(),o=Tl();for(var n in t)i(e[n],n),o[n]=o.Array;return mu}function wu(){if(fu)return pu;fu=1;var t=du();return bu(),pu=t}var _u=o(vu?gu:(vu=1,gu=wu()));class xu{constructor(){this.clear(),this._defaultIndex=0,this._groupIndex=0,this._defaultGroups=[{border:"#2B7CE9",background:"#97C2FC",highlight:{border:"#2B7CE9",background:"#D2E5FF"},hover:{border:"#2B7CE9",background:"#D2E5FF"}},{border:"#FFA500",background:"#FFFF00",highlight:{border:"#FFA500",background:"#FFFFA3"},hover:{border:"#FFA500",background:"#FFFFA3"}},{border:"#FA0A10",background:"#FB7E81",highlight:{border:"#FA0A10",background:"#FFAFB1"},hover:{border:"#FA0A10",background:"#FFAFB1"}},{border:"#41A906",background:"#7BE141",highlight:{border:"#41A906",background:"#A1EC76"},hover:{border:"#41A906",background:"#A1EC76"}},{border:"#E129F0",background:"#EB7DF4",highlight:{border:"#E129F0",background:"#F0B3F5"},hover:{border:"#E129F0",background:"#F0B3F5"}},{border:"#7C29F0",background:"#AD85E4",highlight:{border:"#7C29F0",background:"#D3BDF0"},hover:{border:"#7C29F0",background:"#D3BDF0"}},{border:"#C37F00",background:"#FFA807",highlight:{border:"#C37F00",background:"#FFCA66"},hover:{border:"#C37F00",background:"#FFCA66"}},{border:"#4220FB",background:"#6E6EFD",highlight:{border:"#4220FB",background:"#9B9BFD"},hover:{border:"#4220FB",background:"#9B9BFD"}},{border:"#FD5A77",background:"#FFC0CB",highlight:{border:"#FD5A77",background:"#FFD1D9"},hover:{border:"#FD5A77",background:"#FFD1D9"}},{border:"#4AD63A",background:"#C2FABC",highlight:{border:"#4AD63A",background:"#E6FFE3"},hover:{border:"#4AD63A",background:"#E6FFE3"}},{border:"#990000",background:"#EE0000",highlight:{border:"#BB0000",background:"#FF3333"},hover:{border:"#BB0000",background:"#FF3333"}},{border:"#FF6000",background:"#FF6000",highlight:{border:"#FF6000",background:"#FF6000"},hover:{border:"#FF6000",background:"#FF6000"}},{border:"#97C2FC",background:"#2B7CE9",highlight:{border:"#D2E5FF",background:"#2B7CE9"},hover:{border:"#D2E5FF",background:"#2B7CE9"}},{border:"#399605",background:"#255C03",highlight:{border:"#399605",background:"#255C03"},hover:{border:"#399605",background:"#255C03"}},{border:"#B70054",background:"#FF007E",highlight:{border:"#B70054",background:"#FF007E"},hover:{border:"#B70054",background:"#FF007E"}},{border:"#AD85E4",background:"#7C29F0",highlight:{border:"#D3BDF0",background:"#7C29F0"},hover:{border:"#D3BDF0",background:"#7C29F0"}},{border:"#4557FA",background:"#000EA1",highlight:{border:"#6E6EFD",background:"#000EA1"},hover:{border:"#6E6EFD",background:"#000EA1"}},{border:"#FFC0CB",background:"#FD5A77",highlight:{border:"#FFD1D9",background:"#FD5A77"},hover:{border:"#FFD1D9",background:"#FD5A77"}},{border:"#C2FABC",background:"#74D66A",highlight:{border:"#E6FFE3",background:"#74D66A"},hover:{border:"#E6FFE3",background:"#74D66A"}},{border:"#EE0000",background:"#990000",highlight:{border:"#FF3333",background:"#BB0000"},hover:{border:"#FF3333",background:"#BB0000"}}],this.options={},this.defaultOptions={useDefaultGroups:!0},Zi(this.options,this.defaultOptions)}setOptions(t){const e=["useDefaultGroups"];if(void 0!==t)for(const i in t)if(Object.prototype.hasOwnProperty.call(t,i)&&-1===zr(e).call(e,i)){const e=t[i];this.add(i,e)}}clear(){this._groups=new _u,this._groupNames=[]}get(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this._groups.get(t);if(void 0===i&&e)if(!1===this.options.useDefaultGroups&&this._groupNames.length>0){const e=this._groupIndex%this._groupNames.length;++this._groupIndex,i={},i.color=this._groups.get(this._groupNames[e]),this._groups.set(t,i)}else{const e=this._defaultIndex%this._defaultGroups.length;this._defaultIndex++,i={},i.color=this._defaultGroups[e],this._groups.set(t,i)}return i}add(t,e){return this._groups.has(t)||this._groupNames.push(t),this._groups.set(t,e),e}}var Eu,Ou,Cu,ku,Su,Tu,Mu;function Du(){return Cu?Ou:(Cu=1,Eu||(Eu=1,di()({target:"Number",stat:!0},{isNaN:function(t){return t!=t}})),Ou=St().Number.isNaN)}function Iu(){return Su?ku:(Su=1,ku=Du())}var Pu,Bu,zu,Fu,Nu,Au,Ru,ju,Lu,Hu=o(Mu?Tu:(Mu=1,Tu=Iu()));function Wu(){if(Bu)return Pu;Bu=1;var t=_().isFinite;return Pu=Number.isFinite||function(e){return"number"==typeof e&&t(e)},Pu}function Vu(){return Nu?Fu:(Nu=1,zu||(zu=1,di()({target:"Number",stat:!0},{isFinite:Wu()})),Fu=St().Number.isFinite)}function qu(){return Ru?Au:(Ru=1,Au=Vu())}var Uu,Yu,Xu,Ku,Gu,Zu,Qu,$u=o(Lu?ju:(Lu=1,ju=qu())),Ju={};function tp(){return Xu?Yu:(Xu=1,function(){if(Uu)return Ju;Uu=1;var t=di(),e=ye(),i=bi();t({target:"Object",stat:!0,forced:x()(function(){i(1)})},{keys:function(t){return i(e(t))}})}(),Yu=St().Object.keys)}function ep(){return Gu?Ku:(Gu=1,Ku=tp())}var ip,op,np,sp,rp,ap,hp,dp,lp,cp=o(Qu?Zu:(Qu=1,Zu=ep())),up={};function pp(){return np?op:(np=1,function(){if(ip)return up;ip=1;var t=di(),e=Dh().some;t({target:"Array",proto:!0,forced:!Cr()("some")},{some:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0)}})}(),op=to()("Array","some"))}function fp(){if(rp)return sp;rp=1;var t=Mt(),e=pp(),i=Array.prototype;return sp=function(o){var n=o.some;return o===i||t(i,o)&&n===i.some?e:n},sp}function gp(){return hp?ap:(hp=1,ap=fp())}var vp,mp,yp,bp,wp,_p,xp,Ep=o(lp?dp:(lp=1,dp=gp())),Op={},Cp={};function kp(){if(mp)return vp;mp=1;var t=_(),e=Dt(),i=k(),o=function(t){return e.slice(0,t.length)===t};return vp=o("Bun/")?"BUN":o("Cloudflare-Workers")?"CLOUDFLARE":o("Deno/")?"DENO":o("Node.js/")?"NODE":t.Bun&&"string"==typeof Bun.version?"BUN":t.Deno&&"object"==typeof Deno.version?"DENO":"process"===i(t.process)?"NODE":t.window&&t.document?"BROWSER":"REST"}function Sp(){if(bp)return yp;bp=1;var t=TypeError;return yp=function(e,i){if(e<i)throw new t("Not enough arguments");return e}}function Tp(){if(_p)return wp;_p=1;var t,e=_(),i=O(),o=T(),n=kp(),s=Dt(),r=$i(),a=Sp(),h=e.Function,d=/MSIE .\./.test(s)||"BUN"===n&&((t=e.Bun.version.split(".")).length<3||"0"===t[0]&&(t[1]<3||"3"===t[1]&&"0"===t[2]));return wp=function(t,e){var n=e?2:1;return d?function(s,d){var l=a(arguments.length,1)>n,c=o(s)?s:h(s),u=l?r(arguments,n):[],p=l?function(){i(c,this,u)}:c;return e?t(p,d):t(p)}:t},wp}var Mp,Dp,Ip,Pp,Bp,zp,Fp={};function Np(){return Dp||(Dp=1,function(){if(xp)return Cp;xp=1;var t=di(),e=_(),i=Tp()(e.setInterval,!0);t({global:!0,bind:!0,forced:e.setInterval!==i},{setInterval:i})}(),function(){if(Mp)return Fp;Mp=1;var t=di(),e=_(),i=Tp()(e.setTimeout,!0);t({global:!0,bind:!0,forced:e.setTimeout!==i},{setTimeout:i})}()),Op}function Ap(){return Pp?Ip:(Pp=1,Np(),Ip=St().setTimeout)}var Rp,jp,Lp,Hp,Wp,Vp,qp,Up,Yp,Xp,Kp=o(zp?Bp:(zp=1,Bp=Ap())),Gp={exports:{}},Zp={};function Qp(){if(Rp)return Zp;Rp=1;var t=di(),e=z(),i=ai().f;return t({target:"Object",stat:!0,forced:Object.defineProperty!==i,sham:!e},{defineProperty:i}),Zp}function $p(){if(jp)return Gp.exports;jp=1,Qp();var t=St().Object,e=Gp.exports=function(e,i,o){return t.defineProperty(e,i,o)};return t.defineProperty.sham&&(e.sham=!0),Gp.exports}function Jp(){return Hp?Lp:(Hp=1,Lp=$p())}function tf(){return Vp?Wp:(Vp=1,Wp=Jp())}function ef(){return Up?qp:(Up=1,qp=tf())}function of(){return Xp?Yp:(Xp=1,Yp=ef())}var nf,sf,rf,af,hf,df,lf=o(of()),cf={},uf={},pf={};function ff(){if(nf)return pf;nf=1;var t=_e();return pf.f=t,pf}function gf(){if(rf)return sf;rf=1;var t=St(),e=be(),i=ff(),o=ai().f;return sf=function(n){var s=t.Symbol||(t.Symbol={});e(s,n)||o(s,n,{value:i.f(n)})}}function vf(){if(hf)return af;hf=1;var t=F(),e=Tt(),i=_e(),o=zl();return af=function(){var n=e("Symbol"),s=n&&n.prototype,r=s&&s.valueOf,a=i("toPrimitive");s&&!s[a]&&o(s,a,function(e){return t(r,this)},{arity:1})}}var mf,yf,bf,wf={};function _f(){return yf?mf:(yf=1,mf=Pt()&&!!Symbol.for&&!!Symbol.keyFor)}var xf,Ef={};var Of,Cf,kf,Sf={};function Tf(){if(Cf)return Of;Cf=1;var t=C(),e=Ya(),i=T(),o=k(),n=la(),s=t([].push);return Of=function(t){if(i(t))return t;if(e(t)){for(var r=t.length,a=[],h=0;h<r;h++){var d=t[h];"string"==typeof d?s(a,d):"number"!=typeof d&&"Number"!==o(d)&&"String"!==o(d)||s(a,n(d))}var l=a.length,c=!0;return function(t,i){if(c)return c=!1,i;if(e(this))return i;for(var o=0;o<l;o++)if(a[o]===t)return i}}},Of}function Mf(){if(kf)return Sf;kf=1;var t=di(),e=Tt(),i=O(),o=F(),n=C(),s=x(),r=T(),a=zt(),h=$i(),d=Tf(),l=Pt(),c=String,u=e("JSON","stringify"),p=n(/./.exec),f=n("".charAt),g=n("".charCodeAt),v=n("".replace),m=n(1.1.toString),y=/[\uD800-\uDFFF]/g,b=/^[\uD800-\uDBFF]$/,w=/^[\uDC00-\uDFFF]$/,_=!l||s(function(){var t=e("Symbol")("stringify detection");return"[null]"!==u([t])||"{}"!==u({a:t})||"{}"!==u(Object(t))}),E=s(function(){return'"\\udf06\\ud834"'!==u("\udf06\ud834")||'"\\udead"'!==u("\udead")}),k=function(t,e){var n=h(arguments),s=d(e);if(r(s)||void 0!==t&&!a(t))return n[1]=function(t,e){if(r(s)&&(e=o(s,this,c(t),e)),!a(e))return e},i(u,null,n)},S=function(t,e,i){var o=f(i,e-1),n=f(i,e+1);return p(b,t)&&!p(w,n)||p(w,t)&&!p(b,o)?"\\u"+m(g(t,0),16):t};return u&&t({target:"JSON",stat:!0,arity:3,forced:_||E},{stringify:function(t,e,o){var n=h(arguments),s=i(_?k:u,null,n);return E&&"string"==typeof s?v(s,y,S):s}}),Sf}var Df,If,Pf={};function Bf(){return If||(If=1,function(){if(df)return uf;df=1;var t=di(),e=_(),i=F(),o=C(),n=fe(),s=z(),r=Pt(),a=x(),h=be(),d=Mt(),l=ri(),c=Ct(),u=Ee(),p=la(),f=_t(),g=dr(),v=bi(),m=Zl(),y=Dc(),b=Ii(),w=ke(),E=ai(),O=rr(),k=wt(),S=zl(),T=Wc(),M=me(),D=hr(),I=vi(),P=we(),B=_e(),N=ff(),A=gf(),R=vf(),j=Al(),L=Dl(),H=Dh().forEach,W=D("hidden"),V="Symbol",q="prototype",U=L.set,Y=L.getterFor(V),X=Object[q],K=e.Symbol,G=K&&K[q],Z=e.RangeError,Q=e.TypeError,$=e.QObject,J=w.f,tt=E.f,et=y.f,it=k.f,ot=o([].push),nt=M("symbols"),st=M("op-symbols"),rt=M("wks"),at=!$||!$[q]||!$[q].findChild,ht=function(t,e,i){var o=J(X,e);o&&delete X[e],tt(t,e,i),o&&t!==X&&tt(X,e,o)},dt=s&&a(function(){return 7!==g(tt({},"a",{get:function(){return tt(this,"a",{value:7}).a}})).a})?ht:tt,lt=function(t,e){var i=nt[t]=g(G);return U(i,{type:V,tag:t,description:e}),s||(i.description=e),i},ct=function(t,e,i){t===X&&ct(st,e,i),l(t);var o=u(e);return l(i),h(nt,o)?(i.enumerable?(h(t,W)&&t[W][o]&&(t[W][o]=!1),i=g(i,{enumerable:f(0,!1)})):(h(t,W)||tt(t,W,f(1,g(null))),t[W][o]=!0),dt(t,o,i)):tt(t,o,i)},ut=function(t,e){l(t);var o=c(e),n=v(o).concat(vt(o));return H(n,function(e){s&&!i(pt,o,e)||ct(t,e,o[e])}),t},pt=function(t){var e=u(t),o=i(it,this,e);return!(this===X&&h(nt,e)&&!h(st,e))&&(!(o||!h(this,e)||!h(nt,e)||h(this,W)&&this[W][e])||o)},ft=function(t,e){var i=c(t),o=u(e);if(i!==X||!h(nt,o)||h(st,o)){var n=J(i,o);return!n||!h(nt,o)||h(i,W)&&i[W][o]||(n.enumerable=!0),n}},gt=function(t){var e=et(c(t)),i=[];return H(e,function(t){h(nt,t)||h(I,t)||ot(i,t)}),i},vt=function(t){var e=t===X,i=et(e?st:c(t)),o=[];return H(i,function(t){!h(nt,t)||e&&!h(X,t)||ot(o,nt[t])}),o};r||(K=function(){if(d(G,this))throw new Q("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?p(arguments[0]):void 0,o=P(t),n=function(t){var s=void 0===this?e:this;s===X&&i(n,st,t),h(s,W)&&h(s[W],o)&&(s[W][o]=!1);var r=f(1,t);try{dt(s,o,r)}catch(t){if(!(t instanceof Z))throw t;ht(s,o,r)}};return s&&at&&dt(X,o,{configurable:!0,set:n}),lt(o,t)},S(G=K[q],"toString",function(){return Y(this).tag}),S(K,"withoutSetter",function(t){return lt(P(t),t)}),k.f=pt,E.f=ct,O.f=ut,w.f=ft,m.f=y.f=gt,b.f=vt,N.f=function(t){return lt(B(t),t)},s&&(T(G,"description",{configurable:!0,get:function(){return Y(this).description}}),n||S(X,"propertyIsEnumerable",pt,{unsafe:!0}))),t({global:!0,constructor:!0,wrap:!0,forced:!r,sham:!r},{Symbol:K}),H(v(rt),function(t){A(t)}),t({target:V,stat:!0,forced:!r},{useSetter:function(){at=!0},useSimple:function(){at=!1}}),t({target:"Object",stat:!0,forced:!r,sham:!s},{create:function(t,e){return void 0===e?g(t):ut(g(t),e)},defineProperty:ct,defineProperties:ut,getOwnPropertyDescriptor:ft}),t({target:"Object",stat:!0,forced:!r},{getOwnPropertyNames:gt}),R(),j(K,V),I[W]=!0}(),function(){if(bf)return wf;bf=1;var t=di(),e=Tt(),i=be(),o=la(),n=me(),s=_f(),r=n("string-to-symbol-registry"),a=n("symbol-to-string-registry");t({target:"Symbol",stat:!0,forced:!s},{for:function(t){var n=o(t);if(i(r,n))return r[n];var s=e("Symbol")(n);return r[n]=s,a[s]=n,s}})}(),function(){if(xf)return Ef;xf=1;var t=di(),e=be(),i=zt(),o=Ft(),n=me(),s=_f(),r=n("symbol-to-string-registry");t({target:"Symbol",stat:!0,forced:!s},{keyFor:function(t){if(!i(t))throw new TypeError(o(t)+" is not a symbol");if(e(r,t))return r[t]}})}(),Mf(),function(){if(Df)return Pf;Df=1;var t=di(),e=Pt(),i=x(),o=Ii(),n=ye();t({target:"Object",stat:!0,forced:!e||i(function(){o.f(1)})},{getOwnPropertySymbols:function(t){var e=o.f;return e?e(n(t)):[]}})}()),cf}var zf,Ff={};function Nf(){return zf||(zf=1,gf()("asyncDispose")),Ff}var Af;var Rf,jf={};function Lf(){return Rf||(Rf=1,gf()("dispose")),jf}var Hf;var Wf;var Vf,qf={};function Uf(){return Vf||(Vf=1,gf()("iterator")),qf}var Yf;var Xf;var Kf;var Gf;var Zf;var Qf;var $f,Jf={};function tg(){if($f)return Jf;$f=1;var t=gf(),e=vf();return t("toPrimitive"),e(),Jf}var eg,ig={};var og;var ng,sg,rg,ag,hg,dg={};function lg(){return rg?sg:(rg=1,jd(),Bf(),Nf(),Af||(Af=1,gf()("asyncIterator")),Lf(),Hf||(Hf=1,gf()("hasInstance")),Wf||(Wf=1,gf()("isConcatSpreadable")),Uf(),Yf||(Yf=1,gf()("match")),Xf||(Xf=1,gf()("matchAll")),Kf||(Kf=1,gf()("replace")),Gf||(Gf=1,gf()("search")),Zf||(Zf=1,gf()("species")),Qf||(Qf=1,gf()("split")),tg(),function(){if(eg)return ig;eg=1;var t=Tt(),e=gf(),i=Al();e("toStringTag"),i(t("Symbol"),"Symbol")}(),og||(og=1,gf()("unscopables")),function(){if(ng)return dg;ng=1;var t=_();Al()(t.JSON,"JSON",!0)}(),sg=St().Symbol)}function cg(){if(hg)return ag;hg=1;var t=lg();return bu(),ag=t}var ug,pg={};var fg;var gg;var vg,mg,yg;function bg(){if(yg)return mg;yg=1;var t=cg();return function(){if(ug)return pg;ug=1;var t=_e(),e=ai().f,i=t("metadata"),o=Function.prototype;void 0===o[i]&&e(o,i,{value:null})}(),fg||(fg=1,Nf()),gg||(gg=1,Lf()),vg||(vg=1,gf()("metadata")),mg=t}var wg,_g,xg;function Eg(){if(_g)return wg;_g=1;var t=Tt(),e=C(),i=t("Symbol"),o=i.keyFor,n=e(i.prototype.valueOf);return wg=i.isRegisteredSymbol||function(t){try{return void 0!==o(n(t))}catch(t){return!1}}}var Og,Cg,kg;function Sg(){if(Cg)return Og;Cg=1;for(var t=me(),e=Tt(),i=C(),o=zt(),n=_e(),s=e("Symbol"),r=s.isWellKnownSymbol,a=e("Object","getOwnPropertyNames"),h=i(s.prototype.valueOf),d=t("wks"),l=0,c=a(s),u=c.length;l<u;l++)try{var p=c[l];o(s[p])&&n(p)}catch(t){}return Og=function(t){if(r&&r(t))return!0;try{for(var e=h(t),i=0,o=a(d),n=o.length;i<n;i++)if(d[o[i]]==e)return!0}catch(t){}return!1},Og}var Tg;var Mg;var Dg;var Ig;var Pg;var Bg;var zg;var Fg,Ng,Ag,Rg,jg;function Lg(){if(Ag)return Ng;Ag=1;var t=bg();return xg||(xg=1,di()({target:"Symbol",stat:!0},{isRegisteredSymbol:Eg()})),kg||(kg=1,di()({target:"Symbol",stat:!0,forced:!0},{isWellKnownSymbol:Sg()})),Tg||(Tg=1,gf()("customMatcher")),Mg||(Mg=1,gf()("observable")),Dg||(Dg=1,di()({target:"Symbol",stat:!0,name:"isRegisteredSymbol"},{isRegistered:Eg()})),Ig||(Ig=1,di()({target:"Symbol",stat:!0,name:"isWellKnownSymbol",forced:!0},{isWellKnown:Sg()})),Pg||(Pg=1,gf()("matcher")),Bg||(Bg=1,gf()("metadataKey")),zg||(zg=1,gf()("patternMatch")),Fg||(Fg=1,gf()("replaceAll")),Ng=t}function Hg(){return jg?Rg:(jg=1,Rg=Lg())}var Wg,Vg,qg,Ug,Yg,Xg,Kg,Gg,Zg,Qg,$g=o(Hg());function Jg(){return Vg?Wg:(Vg=1,Ul(),hu(),Uf(),Wg=ff().f("iterator"))}function tv(){if(Ug)return qg;Ug=1;var t=Jg();return bu(),qg=t}function ev(){return Xg?Yg:(Xg=1,Yg=tv())}function iv(){return Gg?Kg:(Gg=1,Kg=ev())}function ov(){return Qg?Zg:(Qg=1,Zg=iv())}var nv,sv,rv,av,hv,dv,lv,cv,uv,pv,fv=o(ov());function gv(t){return gv="function"==typeof $g&&"symbol"==typeof fv?function(t){return typeof t}:function(t){return t&&"function"==typeof $g&&t.constructor===$g&&t!==$g.prototype?"symbol":typeof t},gv(t)}function vv(){return sv?nv:(sv=1,tg(),nv=ff().f("toPrimitive"))}function mv(){return av?rv:(av=1,rv=vv())}function yv(){return dv?hv:(dv=1,hv=mv())}function bv(){return cv?lv:(cv=1,lv=yv())}function wv(){return pv?uv:(pv=1,uv=bv())}var _v=o(wv());function xv(t){var e=function(t,e){if("object"!=gv(t)||!t)return t;var i=t[_v];if(void 0!==i){var o=i.call(t,e);if("object"!=gv(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==gv(e)?e:e+""}function Ev(t,e,i){return(e=xv(e))in t?lf(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var Ov,Cv,kv,Sv,Tv,Mv,Dv,Iv,Pv,Bv,zv,Fv,Nv,Av={};function Rv(){return Cv?Ov:(Cv=1,Ov="\t\n\v\f\r                　\u2028\u2029\ufeff")}function jv(){if(Sv)return kv;Sv=1;var t=C(),e=Ot(),i=la(),o=Rv(),n=t("".replace),s=RegExp("^["+o+"]+"),r=RegExp("(^|[^"+o+"])["+o+"]+$"),a=function(t){return function(o){var a=i(e(o));return 1&t&&(a=n(a,s,"")),2&t&&(a=n(a,r,"$1")),a}};return kv={start:a(1),end:a(2),trim:a(3)}}function Lv(){if(Mv)return Tv;Mv=1;var t=_(),e=x(),i=C(),o=la(),n=jv().trim,s=Rv(),r=t.parseInt,a=t.Symbol,h=a&&a.iterator,d=/^[+-]?0x/i,l=i(d.exec),c=8!==r(s+"08")||22!==r(s+"0x16")||h&&!e(function(){r(Object(h))});return Tv=c?function(t,e){var i=n(o(t));return r(i,e>>>0||(l(d,i)?16:10))}:r,Tv}function Hv(){return Pv?Iv:(Pv=1,function(){if(Dv)return Av;Dv=1;var t=di(),e=Lv();t({global:!0,forced:parseInt!==e},{parseInt:e})}(),Iv=St().parseInt)}function Wv(){return zv?Bv:(zv=1,Bv=Hv())}var Vv,qv,Uv,Yv,Xv,Kv,Gv,Zv,Qv,$v=o(Nv?Fv:(Nv=1,Fv=Wv())),Jv={};function tm(){if(qv)return Vv;qv=1;var t=_(),e=x(),i=C(),o=la(),n=jv().trim,s=Rv(),r=i("".charAt),a=t.parseFloat,h=t.Symbol,d=h&&h.iterator,l=1/a(s+"-0")!=-1/0||d&&!e(function(){a(Object(d))});return Vv=l?function(t){var e=n(o(t)),i=a(e);return 0===i&&"-"===r(e,0)?-0:i}:a,Vv}function em(){return Xv?Yv:(Xv=1,function(){if(Uv)return Jv;Uv=1;var t=di(),e=tm();t({global:!0,forced:parseFloat!==e},{parseFloat:e})}(),Yv=St().parseFloat)}function im(){return Gv?Kv:(Gv=1,Kv=em())}var om,nm,sm,rm,am,hm,dm,lm,cm,um=o(Qv?Zv:(Qv=1,Zv=im())),pm={};function fm(){return sm?nm:(sm=1,function(){if(om)return pm;om=1;var t=di(),e=Dh().filter;t({target:"Array",proto:!0,forced:!eh()("filter")},{filter:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0)}})}(),nm=to()("Array","filter"))}function gm(){if(am)return rm;am=1;var t=Mt(),e=fm(),i=Array.prototype;return rm=function(o){var n=o.filter;return o===i||t(i,o)&&n===i.filter?e:n},rm}function vm(){return dm?hm:(dm=1,hm=gm())}var mm,ym,bm,wm,_m,xm,Em,Om=o(cm?lm:(cm=1,lm=vm())),Cm={};function km(){if(mm)return Cm;mm=1;var t=di(),e=x(),i=Dc().f;return t({target:"Object",stat:!0,forced:e(function(){return!Object.getOwnPropertyNames(1)})},{getOwnPropertyNames:i}),Cm}function Sm(){if(bm)return ym;bm=1,km();var t=St().Object;return ym=function(e){return t.getOwnPropertyNames(e)},ym}function Tm(){return _m?wm:(_m=1,wm=Sm())}var Mm,Dm,Im,Pm,Bm,zm,Fm=o(Em?xm:(Em=1,xm=Tm()));function Nm(){return Dm?Mm:(Dm=1,Bf(),Mm=St().Object.getOwnPropertySymbols)}function Am(){return Pm?Im:(Pm=1,Im=Nm())}var Rm,jm,Lm,Hm,Wm,Vm,qm=o(zm?Bm:(zm=1,Bm=Am())),Um={exports:{}},Ym={};function Xm(){if(jm)return Um.exports;jm=1,function(){if(Rm)return Ym;Rm=1;var t=di(),e=x(),i=Ct(),o=ke().f,n=z();t({target:"Object",stat:!0,forced:!n||e(function(){o(1)}),sham:!n},{getOwnPropertyDescriptor:function(t,e){return o(i(t),e)}})}();var t=St().Object,e=Um.exports=function(e,i){return t.getOwnPropertyDescriptor(e,i)};return t.getOwnPropertyDescriptor.sham&&(e.sham=!0),Um.exports}function Km(){return Hm?Lm:(Hm=1,Lm=Xm())}var Gm,Zm,Qm,$m,Jm,ty,ey,iy,oy,ny=o(Vm?Wm:(Vm=1,Wm=Km())),sy={};function ry(){if(Zm)return Gm;Zm=1;var t=Tt(),e=C(),i=Zl(),o=Ii(),n=ri(),s=e([].concat);return Gm=t("Reflect","ownKeys")||function(t){var e=i.f(n(t)),r=o.f;return r?s(e,r(t)):e},Gm}function ay(){return Jm?$m:(Jm=1,function(){if(Qm)return sy;Qm=1;var t=di(),e=z(),i=ry(),o=Ct(),n=ke(),s=Ja();t({target:"Object",stat:!0,sham:!e},{getOwnPropertyDescriptors:function(t){for(var e,r,a=o(t),h=n.f,d=i(a),l={},c=0;d.length>c;)void 0!==(r=h(a,e=d[c++]))&&s(l,e,r);return l}})}(),$m=St().Object.getOwnPropertyDescriptors)}function hy(){return ey?ty:(ey=1,ty=ay())}var dy,ly,cy,uy,py,fy,gy=o(oy?iy:(oy=1,iy=hy())),vy={exports:{}},my={};function yy(){if(dy)return my;dy=1;var t=di(),e=z(),i=rr().f;return t({target:"Object",stat:!0,forced:Object.defineProperties!==i,sham:!e},{defineProperties:i}),my}function by(){if(ly)return vy.exports;ly=1,yy();var t=St().Object,e=vy.exports=function(e,i){return t.defineProperties(e,i)};return t.defineProperties.sham&&(e.sham=!0),vy.exports}function wy(){return uy?cy:(uy=1,cy=by())}var _y,xy,Ey=o(fy?py:(fy=1,py=wy()));var Oy=o(xy?_y:(xy=1,_y=Jp()));function Cy(t,e){const i=["node","edge","label"];let o=!0;const n=Ps(e,"chosen");if("boolean"==typeof n)o=n;else if("object"==typeof n){if(-1===zr(i).call(i,t))throw new Error("choosify: subOption '"+t+"' should be one of '"+i.join("', '")+"'");const n=Ps(e,["chosen",t]);"boolean"!=typeof n&&"function"!=typeof n||(o=n)}return o}function ky(t,e,i){if(t.width<=0||t.height<=0)return!1;if(void 0!==i){const t={x:e.x-i.x,y:e.y-i.y};if(0!==i.angle){const o=-i.angle;e={x:Math.cos(o)*t.x-Math.sin(o)*t.y,y:Math.sin(o)*t.x+Math.cos(o)*t.y}}else e=t}const o=t.x+t.width,n=t.y+t.width;return t.left<e.x&&o>e.x&&t.top<e.y&&n>e.y}function Sy(t){return"string"==typeof t&&""!==t}function Ty(t,e,i,o){let n=o.x,s=o.y;if("function"==typeof o.distanceToBorder){const i=o.distanceToBorder(t,e),r=Math.sin(e)*i,a=Math.cos(e)*i;a===i?(n+=i,s=o.y):r===i?(n=o.x,s-=i):(n+=a,s-=r)}else o.shape.width>o.shape.height?(n=o.x+.5*o.shape.width,s=o.y-i):(n=o.x+i,s=o.y-.5*o.shape.height);return{x:n,y:s}}var My,Dy,Iy,Py,By,zy,Fy,Ny,Ay,Ry={};function jy(){return Iy?Dy:(Iy=1,function(){if(My)return Ry;My=1;var t=di(),e=Ya(),i=Za(),o=kt(),n=ui(),s=fi(),r=Ct(),a=Ja(),h=_e(),d=eh(),l=$i(),c=d("slice"),u=h("species"),p=Array,f=Math.max;t({target:"Array",proto:!0,forced:!c},{slice:function(t,h){var d,c,g,v=r(this),m=s(v),y=n(t,m),b=n(void 0===h?m:h,m);if(e(v)&&(d=v.constructor,(i(d)&&(d===p||e(d.prototype))||o(d)&&null===(d=d[u]))&&(d=void 0),d===p||void 0===d))return l(v,y,b);for(c=new(void 0===d?p:d)(f(b-y,0)),g=0;y<b;y++,g++)y in v&&a(c,g,v[y]);return c.length=g,c}})}(),Dy=to()("Array","slice"))}function Ly(){if(By)return Py;By=1;var t=Mt(),e=jy(),i=Array.prototype;return Py=function(o){var n=o.slice;return o===i||t(i,o)&&n===i.slice?e:n},Py}function Hy(){return Fy?zy:(Fy=1,zy=Ly())}var Wy,Vy,qy,Uy,Yy,Xy,Ky,Gy,Zy=o(Ay?Ny:(Ay=1,Ny=Hy()));function Qy(){return Vy?Wy:(Vy=1,Ul(),Wy=to()("Array","values"))}function $y(){return Uy?qy:(Uy=1,qy=Qy())}function Jy(){if(Xy)return Yy;Xy=1,bu();var t=da(),e=be(),i=Mt(),o=$y(),n=Array.prototype,s={DOMTokenList:!0,NodeList:!0};return Yy=function(r){var a=r.values;return r===n||i(n,r)&&a===n.values||e(s,t(r))?o:a},Yy}var tb=o(Gy?Ky:(Gy=1,Ky=Jy()));class eb{constructor(t){this.measureText=t,this.current=0,this.width=0,this.height=0,this.lines=[]}_add(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"normal";void 0===this.lines[t]&&(this.lines[t]={width:0,height:0,blocks:[]});let o=e;void 0!==e&&""!==e||(o=" ");const n=this.measureText(o,i),s=Zi({},tb(n));s.text=e,s.width=n.width,s.mod=i,void 0!==e&&""!==e||(s.width=0),this.lines[t].blocks.push(s),this.lines[t].width+=s.width}curWidth(){const t=this.lines[this.current];return void 0===t?0:t.width}append(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"normal";this._add(this.current,t,e)}newLine(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"normal";this._add(this.current,t,e),this.current++}determineLineHeights(){for(let t=0;t<this.lines.length;t++){const e=this.lines[t];let i=0;if(void 0!==e.blocks)for(let t=0;t<e.blocks.length;t++){const o=e.blocks[t];i<o.height&&(i=o.height)}e.height=i}}determineLabelSize(){let t=0,e=0;for(let i=0;i<this.lines.length;i++){const o=this.lines[i];o.width>t&&(t=o.width),e+=o.height}this.width=t,this.height=e}removeEmptyBlocks(){const t=[];for(let e=0;e<this.lines.length;e++){const i=this.lines[e];if(0===i.blocks.length)continue;if(e===this.lines.length-1&&0===i.width)continue;const o={};let n;Zi(o,i),o.blocks=[];const s=[];for(let t=0;t<i.blocks.length;t++){const e=i.blocks[t];0!==e.width?s.push(e):void 0===n&&(n=e)}0===s.length&&void 0!==n&&s.push(n),o.blocks=s,t.push(o)}return t}finalize(){this.determineLineHeights(),this.determineLabelSize();const t=this.removeEmptyBlocks();return{width:this.width,height:this.height,lines:t}}}const ib={"<b>":/<b>/,"<i>":/<i>/,"<code>":/<code>/,"</b>":/<\/b>/,"</i>":/<\/i>/,"</code>":/<\/code>/,"*":/\*/,_:/_/,"`":/`/,afterBold:/[^*]/,afterItal:/[^_]/,afterMono:/[^`]/};class ob{constructor(t){this.text=t,this.bold=!1,this.ital=!1,this.mono=!1,this.spacing=!1,this.position=0,this.buffer="",this.modStack=[],this.blocks=[]}mod(){return 0===this.modStack.length?"normal":this.modStack[0]}modName(){return 0===this.modStack.length?"normal":"mono"===this.modStack[0]?"mono":this.bold&&this.ital?"boldital":this.bold?"bold":this.ital?"ital":void 0}emitBlock(){this.spacing&&(this.add(" "),this.spacing=!1),this.buffer.length>0&&(this.blocks.push({text:this.buffer,mod:this.modName()}),this.buffer="")}add(t){" "===t&&(this.spacing=!0),this.spacing&&(this.buffer+=" ",this.spacing=!1)," "!=t&&(this.buffer+=t)}parseWS(t){return!!/[ \t]/.test(t)&&(this.mono?this.add(t):this.spacing=!0,!0)}setTag(t){this.emitBlock(),this[t]=!0,this.modStack.unshift(t)}unsetTag(t){this.emitBlock(),this[t]=!1,this.modStack.shift()}parseStartTag(t,e){return!(this.mono||this[t]||!this.match(e))&&(this.setTag(t),!0)}match(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const[i,o]=this.prepareRegExp(t),n=i.test(this.text.substr(this.position,o));return n&&e&&(this.position+=o-1),n}parseEndTag(t,e,i){let o=this.mod()===t;return o="mono"===t?o&&this.mono:o&&!this.mono,!(!o||!this.match(e))&&(void 0!==i?(this.position===this.text.length-1||this.match(i,!1))&&this.unsetTag(t):this.unsetTag(t),!0)}replace(t,e){return!!this.match(t)&&(this.add(e),this.position+=length-1,!0)}prepareRegExp(t){let e,i;if(t instanceof RegExp)i=t,e=1;else{const o=ib[t];i=void 0!==o?o:new RegExp(t),e=t.length}return[i,e]}}class nb{constructor(t,e,i,o){this.ctx=t,this.parent=e,this.selected=i,this.hover=o;this.lines=new eb((e,n)=>{if(void 0===e)return 0;const s=this.parent.getFormattingValues(t,i,o,n);let r=0;if(""!==e){r=this.ctx.measureText(e).width}return{width:r,values:s}})}process(t){if(!Sy(t))return this.lines.finalize();const e=this.parent.fontOptions;t=(t=t.replace(/\r\n/g,"\n")).replace(/\r/g,"\n");const i=String(t).split("\n"),o=i.length;if(e.multi)for(let t=0;t<o;t++){const o=this.splitBlocks(i[t],e.multi);if(void 0!==o)if(0!==o.length){if(e.maxWdt>0)for(let t=0;t<o.length;t++){const e=o[t].mod,i=o[t].text;this.splitStringIntoLines(i,e,!0)}else for(let t=0;t<o.length;t++){const e=o[t].mod,i=o[t].text;this.lines.append(i,e)}this.lines.newLine()}else this.lines.newLine("")}else if(e.maxWdt>0)for(let t=0;t<o;t++)this.splitStringIntoLines(i[t]);else for(let t=0;t<o;t++)this.lines.newLine(i[t]);return this.lines.finalize()}decodeMarkupSystem(t){let e="none";return"markdown"===t||"md"===t?e="markdown":!0!==t&&"html"!==t||(e="html"),e}splitHtmlBlocks(t){const e=new ob(t),i=t=>{if(/&/.test(t)){return e.replace(e.text,"&lt;","<")||e.replace(e.text,"&amp;","&")||e.add("&"),!0}return!1};for(;e.position<e.text.length;){const t=e.text.charAt(e.position);e.parseWS(t)||/</.test(t)&&(e.parseStartTag("bold","<b>")||e.parseStartTag("ital","<i>")||e.parseStartTag("mono","<code>")||e.parseEndTag("bold","</b>")||e.parseEndTag("ital","</i>")||e.parseEndTag("mono","</code>"))||i(t)||e.add(t),e.position++}return e.emitBlock(),e.blocks}splitMarkdownBlocks(t){const e=new ob(t);let i=!0;const o=t=>!!/\\/.test(t)&&(e.position<this.text.length+1&&(e.position++,t=this.text.charAt(e.position),/ \t/.test(t)?e.spacing=!0:(e.add(t),i=!1)),!0);for(;e.position<e.text.length;){const t=e.text.charAt(e.position);e.parseWS(t)||o(t)||(i||e.spacing)&&(e.parseStartTag("bold","*")||e.parseStartTag("ital","_")||e.parseStartTag("mono","`"))||e.parseEndTag("bold","*","afterBold")||e.parseEndTag("ital","_","afterItal")||e.parseEndTag("mono","`","afterMono")||(e.add(t),i=!1),e.position++}return e.emitBlock(),e.blocks}splitBlocks(t,e){const i=this.decodeMarkupSystem(e);return"none"===i?[{text:t,mod:"normal"}]:"markdown"===i?this.splitMarkdownBlocks(t):"html"===i?this.splitHtmlBlocks(t):void 0}overMaxWidth(t){const e=this.ctx.measureText(t).width;return this.lines.curWidth()+e>this.parent.fontOptions.maxWdt}getLongestFit(t){let e="",i=0;for(;i<t.length;){const o=e+(""===e?"":" ")+t[i];if(this.overMaxWidth(o))break;e=o,i++}return i}getLongestFitWord(t){let e=0;for(;e<t.length&&!this.overMaxWidth(Zy(t).call(t,0,e));)e++;return e}splitStringIntoLines(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"normal",i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.parent.getFormattingValues(this.ctx,this.selected,this.hover,e);let o=(t=(t=t.replace(/^( +)/g,"$1\r")).replace(/([^\r][^ ]*)( +)/g,"$1\r$2\r")).split("\r");for(;o.length>0;){let t=this.getLongestFit(o);if(0===t){const t=o[0],i=this.getLongestFitWord(t);this.lines.newLine(Zy(t).call(t,0,i),e),o[0]=Zy(t).call(t,i)}else{let n=t;" "===o[t-1]?t--:" "===o[n]&&n++;const s=Zy(o).call(o,0,t).join("");t==o.length&&i?this.lines.append(s,e):this.lines.newLine(s,e),o=Zy(o).call(o,n)}}}}const sb=["bold","ital","boldital","mono"];class rb{constructor(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.body=t,this.pointToSelf=!1,this.baseSize=void 0,this.fontOptions={},this.setOptions(e),this.size={top:0,left:0,width:0,height:0,yLine:0},this.isEdgeLabel=i}setOptions(t){if(this.elementOptions=t,this.initFontOptions(t.font),Sy(t.label)?this.labelDirty=!0:t.label=void 0,void 0!==t.font&&null!==t.font)if("string"==typeof t.font)this.baseSize=this.fontOptions.size;else if("object"==typeof t.font){const e=t.font.size;void 0!==e&&(this.baseSize=e)}}initFontOptions(t){bs(sb,t=>{this.fontOptions[t]={}}),rb.parseFontString(this.fontOptions,t)?this.fontOptions.vadjust=0:bs(t,(t,e)=>{null!=t&&"object"!=typeof t&&(this.fontOptions[e]=t)})}static parseFontString(t,e){if(!e||"string"!=typeof e)return!1;const i=e.split(" ");return t.size=+i[0].replace("px",""),t.face=i[1],t.color=i[2],!0}constrain(t){const e={constrainWidth:!1,maxWdt:-1,minWdt:-1,constrainHeight:!1,minHgt:-1,valign:"middle"},i=Ps(t,"widthConstraint");if("number"==typeof i)e.maxWdt=Number(i),e.minWdt=Number(i);else if("object"==typeof i){const i=Ps(t,["widthConstraint","maximum"]);"number"==typeof i&&(e.maxWdt=Number(i));const o=Ps(t,["widthConstraint","minimum"]);"number"==typeof o&&(e.minWdt=Number(o))}const o=Ps(t,"heightConstraint");if("number"==typeof o)e.minHgt=Number(o);else if("object"==typeof o){const i=Ps(t,["heightConstraint","minimum"]);"number"==typeof i&&(e.minHgt=Number(i));const o=Ps(t,["heightConstraint","valign"]);"string"==typeof o&&("top"!==o&&"bottom"!==o||(e.valign=o))}return e}update(t,e){this.setOptions(t,!0),this.propagateFonts(e),gs(this.fontOptions,this.constrain(e)),this.fontOptions.chooser=Cy("label",e)}adjustSizes(t){const e=t?t.right+t.left:0;this.fontOptions.constrainWidth&&(this.fontOptions.maxWdt-=e,this.fontOptions.minWdt-=e);const i=t?t.top+t.bottom:0;this.fontOptions.constrainHeight&&(this.fontOptions.minHgt-=i)}addFontOptionsToPile(t,e){for(let i=0;i<e.length;++i)this.addFontToPile(t,e[i])}addFontToPile(t,e){if(void 0===e)return;if(void 0===e.font||null===e.font)return;const i=e.font;t.push(i)}getBasicOptions(t){const e={};for(let i=0;i<t.length;++i){let o=t[i];const n={};rb.parseFontString(n,o)&&(o=n),bs(o,(t,i)=>{void 0!==t&&(Object.prototype.hasOwnProperty.call(e,i)||(-1!==zr(sb).call(sb,i)?e[i]={}:e[i]=t))})}return e}getFontOption(t,e,i){let o;for(let n=0;n<t.length;++n){const s=t[n];if(Object.prototype.hasOwnProperty.call(s,e)){if(o=s[e],null==o)continue;const t={};if(rb.parseFontString(t,o)&&(o=t),Object.prototype.hasOwnProperty.call(o,i))return o[i]}}if(Object.prototype.hasOwnProperty.call(this.fontOptions,i))return this.fontOptions[i];throw new Error("Did not find value for multi-font for property: '"+i+"'")}getFontOptions(t,e){const i={},o=["color","size","face","mod","vadjust"];for(let n=0;n<o.length;++n){const s=o[n];i[s]=this.getFontOption(t,e,s)}return i}propagateFonts(t){const e=[];this.addFontOptionsToPile(e,t),this.fontOptions=this.getBasicOptions(e);for(let t=0;t<sb.length;++t){const i=sb[t],o=this.fontOptions[i];bs(this.getFontOptions(e,i),(t,e)=>{o[e]=t}),o.size=Number(o.size),o.vadjust=Number(o.vadjust)}}draw(t,e,i,o,n){let s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"middle";if(void 0===this.elementOptions.label)return;let r=this.fontOptions.size*this.body.view.scale;this.elementOptions.label&&r<this.elementOptions.scaling.label.drawThreshold-1||(r>=this.elementOptions.scaling.label.maxVisible&&(r=Number(this.elementOptions.scaling.label.maxVisible)/this.body.view.scale),this.calculateLabelSize(t,o,n,e,i,s),this._drawBackground(t),this._drawText(t,e,this.size.yLine,s,r))}_drawBackground(t){if(void 0!==this.fontOptions.background&&"none"!==this.fontOptions.background){t.fillStyle=this.fontOptions.background;const e=this.getSize();t.fillRect(e.left,e.top,e.width,e.height)}}_drawText(t,e,i){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"middle",n=arguments.length>4?arguments[4]:void 0;[e,i]=this._setAlignment(t,e,i,o),t.textAlign="left",e-=this.size.width/2,this.fontOptions.valign&&this.size.height>this.size.labelHeight&&("top"===this.fontOptions.valign&&(i-=(this.size.height-this.size.labelHeight)/2),"bottom"===this.fontOptions.valign&&(i+=(this.size.height-this.size.labelHeight)/2));for(let o=0;o<this.lineCount;o++){const s=this.lines[o];if(s&&s.blocks){let o=0;this.isEdgeLabel||"center"===this.fontOptions.align?o+=(this.size.width-s.width)/2:"right"===this.fontOptions.align&&(o+=this.size.width-s.width);for(let r=0;r<s.blocks.length;r++){const a=s.blocks[r];t.font=a.font;const[h,d]=this._getColor(a.color,n,a.strokeColor);a.strokeWidth>0&&(t.lineWidth=a.strokeWidth,t.strokeStyle=d,t.lineJoin="round"),t.fillStyle=h,a.strokeWidth>0&&t.strokeText(a.text,e+o,i+a.vadjust),t.fillText(a.text,e+o,i+a.vadjust),o+=a.width}i+=s.height}}}_setAlignment(t,e,i,o){if(this.isEdgeLabel&&"horizontal"!==this.fontOptions.align&&!1===this.pointToSelf){e=0,i=0;const o=2;"top"===this.fontOptions.align?(t.textBaseline="alphabetic",i-=2*o):"bottom"===this.fontOptions.align?(t.textBaseline="hanging",i+=2*o):t.textBaseline="middle"}else t.textBaseline=o;return[e,i]}_getColor(t,e,i){let o=t||"#000000",n=i||"#ffffff";if(e<=this.elementOptions.scaling.label.drawThreshold){const t=Math.max(0,Math.min(1,1-(this.elementOptions.scaling.label.drawThreshold-e)));o=_s(o,t),n=_s(n,t)}return[o,n]}getTextSize(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this._processLabel(t,e,i),{width:this.size.width,height:this.size.height,lineCount:this.lineCount}}getSize(){let t=this.size.left,e=this.size.top-1;if(this.isEdgeLabel){const i=.5*-this.size.width;switch(this.fontOptions.align){case"middle":t=i,e=.5*-this.size.height;break;case"top":t=i,e=-(this.size.height+2);break;case"bottom":t=i,e=2}}return{left:t,top:e,width:this.size.width,height:this.size.height}}calculateLabelSize(t,e,i){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"middle";this._processLabel(t,e,i),this.size.left=o-.5*this.size.width,this.size.top=n-.5*this.size.height,this.size.yLine=n+.5*(1-this.lineCount)*this.fontOptions.size,"hanging"===s&&(this.size.top+=.5*this.fontOptions.size,this.size.top+=4,this.size.yLine+=4)}getFormattingValues(t,e,i,o){const n=function(t,e,i){return"normal"===e?"mod"===i?"":t[i]:void 0!==t[e][i]?t[e][i]:t[i]},s={color:n(this.fontOptions,o,"color"),size:n(this.fontOptions,o,"size"),face:n(this.fontOptions,o,"face"),mod:n(this.fontOptions,o,"mod"),vadjust:n(this.fontOptions,o,"vadjust"),strokeWidth:this.fontOptions.strokeWidth,strokeColor:this.fontOptions.strokeColor};(e||i)&&("normal"===o&&!0===this.fontOptions.chooser&&this.elementOptions.labelHighlightBold?s.mod="bold":"function"==typeof this.fontOptions.chooser&&this.fontOptions.chooser(s,this.elementOptions.id,e,i));let r="";return void 0!==s.mod&&""!==s.mod&&(r+=s.mod+" "),r+=s.size+"px "+s.face,t.font=r.replace(/"/g,""),s.font=t.font,s.height=s.size,s}differentState(t,e){return t!==this.selectedState||e!==this.hoverState}_processLabelText(t,e,i,o){return new nb(t,this,e,i).process(o)}_processLabel(t,e,i){if(!1===this.labelDirty&&!this.differentState(e,i))return;const o=this._processLabelText(t,e,i,this.elementOptions.label);this.fontOptions.minWdt>0&&o.width<this.fontOptions.minWdt&&(o.width=this.fontOptions.minWdt),this.size.labelHeight=o.height,this.fontOptions.minHgt>0&&o.height<this.fontOptions.minHgt&&(o.height=this.fontOptions.minHgt),this.lines=o.lines,this.lineCount=o.lines.length,this.size.width=o.width,this.size.height=o.height,this.selectedState=e,this.hoverState=i,this.labelDirty=!1}visible(){if(0===this.size.width||0===this.size.height||void 0===this.elementOptions.label)return!1;return!(this.fontOptions.size*this.body.view.scale<this.elementOptions.scaling.label.drawThreshold-1)}}var ab,hb,db,lb,cb,ub,pb,fb,gb,vb,mb,yb={};function bb(){if(hb)return ab;hb=1;var t=ye(),e=ui(),i=fi();return ab=function(o){for(var n=t(this),s=i(n),r=arguments.length,a=e(r>1?arguments[1]:void 0,s),h=r>2?arguments[2]:void 0,d=void 0===h?s:e(h,s);d>a;)n[a++]=o;return n},ab}function wb(){return cb?lb:(cb=1,function(){if(db)return yb;db=1;var t=di(),e=bb(),i=Nr();t({target:"Array",proto:!0},{fill:e}),i("fill")}(),lb=to()("Array","fill"))}function _b(){if(pb)return ub;pb=1;var t=Mt(),e=wb(),i=Array.prototype;return ub=function(o){var n=o.fill;return o===i||t(i,o)&&n===i.fill?e:n},ub}function xb(){return gb?fb:(gb=1,fb=_b())}var Eb=o(mb?vb:(mb=1,vb=xb()));class Ob{constructor(t,e,i){this.body=e,this.labelModule=i,this.setOptions(t),this.top=void 0,this.left=void 0,this.height=void 0,this.width=void 0,this.radius=void 0,this.margin=void 0,this.refreshNeeded=!0,this.boundingBox={top:0,left:0,right:0,bottom:0}}setOptions(t){this.options=t}_setMargins(t){this.margin={},this.options.margin&&("object"==typeof this.options.margin?(this.margin.top=this.options.margin.top,this.margin.right=this.options.margin.right,this.margin.bottom=this.options.margin.bottom,this.margin.left=this.options.margin.left):(this.margin.top=this.options.margin,this.margin.right=this.options.margin,this.margin.bottom=this.options.margin,this.margin.left=this.options.margin)),t.adjustSizes(this.margin)}_distanceToBorder(t,e){const i=this.options.borderWidth;return t&&this.resize(t),Math.min(Math.abs(this.width/2/Math.cos(e)),Math.abs(this.height/2/Math.sin(e)))+i}enableShadow(t,e){e.shadow&&(t.shadowColor=e.shadowColor,t.shadowBlur=e.shadowSize,t.shadowOffsetX=e.shadowX,t.shadowOffsetY=e.shadowY)}disableShadow(t,e){e.shadow&&(t.shadowColor="rgba(0,0,0,0)",t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0)}enableBorderDashes(t,e){if(!1!==e.borderDashes)if(void 0!==t.setLineDash){let i=e.borderDashes;!0===i&&(i=[5,15]),t.setLineDash(i)}else console.warn("setLineDash is not supported in this browser. The dashed borders cannot be used."),this.options.shapeProperties.borderDashes=!1,e.borderDashes=!1}disableBorderDashes(t,e){!1!==e.borderDashes&&(void 0!==t.setLineDash?t.setLineDash([0]):(console.warn("setLineDash is not supported in this browser. The dashed borders cannot be used."),this.options.shapeProperties.borderDashes=!1,e.borderDashes=!1))}needsRefresh(t,e){return!0===this.refreshNeeded?(this.refreshNeeded=!1,!0):void 0===this.width||this.labelModule.differentState(t,e)}initContextForDraw(t,e){const i=e.borderWidth/this.body.view.scale;t.lineWidth=Math.min(this.width,i),t.strokeStyle=e.borderColor,t.fillStyle=e.color}performStroke(t,e){const i=e.borderWidth/this.body.view.scale;t.save(),i>0&&(this.enableBorderDashes(t,e),t.stroke(),this.disableBorderDashes(t,e)),t.restore()}performFill(t,e){t.save(),t.fillStyle=e.color,this.enableShadow(t,e),Eb(t).call(t),this.disableShadow(t,e),t.restore(),this.performStroke(t,e)}_addBoundingBoxMargin(t){this.boundingBox.left-=t,this.boundingBox.top-=t,this.boundingBox.bottom+=t,this.boundingBox.right+=t}_updateBoundingBox(t,e,i,o,n){void 0!==i&&this.resize(i,o,n),this.left=t-this.width/2,this.top=e-this.height/2,this.boundingBox.left=this.left,this.boundingBox.top=this.top,this.boundingBox.bottom=this.top+this.height,this.boundingBox.right=this.left+this.width}updateBoundingBox(t,e,i,o,n){this._updateBoundingBox(t,e,i,o,n)}getDimensionsFromLabel(t,e,i){this.textSize=this.labelModule.getTextSize(t,e,i);let o=this.textSize.width,n=this.textSize.height;return 0===o&&(o=14,n=14),{width:o,height:n}}}let Cb=class extends Ob{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover;if(this.needsRefresh(e,i)){const o=this.getDimensionsFromLabel(t,e,i);this.width=o.width+this.margin.right+this.margin.left,this.height=o.height+this.margin.top+this.margin.bottom,this.radius=this.width/2}}draw(t,e,i,o,n,s){this.resize(t,o,n),this.left=e-this.width/2,this.top=i-this.height/2,this.initContextForDraw(t,s),ro(t,this.left,this.top,this.width,this.height,s.borderRadius),this.performFill(t,s),this.updateBoundingBox(e,i,t,o,n),this.labelModule.draw(t,this.left+this.textSize.width/2+this.margin.left,this.top+this.textSize.height/2+this.margin.top,o,n)}updateBoundingBox(t,e,i,o,n){this._updateBoundingBox(t,e,i,o,n);const s=this.options.shapeProperties.borderRadius;this._addBoundingBoxMargin(s)}distanceToBorder(t,e){t&&this.resize(t);const i=this.options.borderWidth;return Math.min(Math.abs(this.width/2/Math.cos(e)),Math.abs(this.height/2/Math.sin(e)))+i}};class kb extends Ob{constructor(t,e,i){super(t,e,i),this.labelOffset=0,this.selected=!1}setOptions(t,e,i){this.options=t,void 0===e&&void 0===i||this.setImages(e,i)}setImages(t,e){e&&this.selected?(this.imageObj=e,this.imageObjAlt=t):(this.imageObj=t,this.imageObjAlt=e)}switchImages(t){const e=t&&!this.selected||!t&&this.selected;if(this.selected=t,void 0!==this.imageObjAlt&&e){const t=this.imageObj;this.imageObj=this.imageObjAlt,this.imageObjAlt=t}}_getImagePadding(){const t={top:0,right:0,bottom:0,left:0};if(this.options.imagePadding){const e=this.options.imagePadding;"object"==typeof e?(t.top=e.top,t.right=e.right,t.bottom=e.bottom,t.left=e.left):(t.top=e,t.right=e,t.bottom=e,t.left=e)}return t}_resizeImage(){let t,e;if(!1===this.options.shapeProperties.useImageSize){let i=1,o=1;this.imageObj.width&&this.imageObj.height&&(this.imageObj.width>this.imageObj.height?i=this.imageObj.width/this.imageObj.height:o=this.imageObj.height/this.imageObj.width),t=2*this.options.size*i,e=2*this.options.size*o}else{const i=this._getImagePadding();t=this.imageObj.width+i.left+i.right,e=this.imageObj.height+i.top+i.bottom}this.width=t,this.height=e,this.radius=.5*this.width}_drawRawCircle(t,e,i,o){this.initContextForDraw(t,o),so(t,e,i,o.size),this.performFill(t,o)}_drawImageAtPosition(t,e){if(0!=this.imageObj.width){t.globalAlpha=void 0!==e.opacity?e.opacity:1,this.enableShadow(t,e);let i=1;!0===this.options.shapeProperties.interpolation&&(i=this.imageObj.width/this.width/this.body.view.scale);const o=this._getImagePadding(),n=this.left+o.left,s=this.top+o.top,r=this.width-o.left-o.right,a=this.height-o.top-o.bottom;this.imageObj.drawImageAtPosition(t,i,n,s,r,a),this.disableShadow(t,e)}}_drawImageLabel(t,e,i,o,n){let s=0;if(void 0!==this.height){s=.5*this.height;const e=this.labelModule.getTextSize(t,o,n);e.lineCount>=1&&(s+=e.height/2)}const r=i+s;this.options.label&&(this.labelOffset=s),this.labelModule.draw(t,e,r,o,n,"hanging")}}let Sb=class extends kb{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover;if(this.needsRefresh(e,i)){const o=this.getDimensionsFromLabel(t,e,i),n=Math.max(o.width+this.margin.right+this.margin.left,o.height+this.margin.top+this.margin.bottom);this.options.size=n/2,this.width=n,this.height=n,this.radius=this.width/2}}draw(t,e,i,o,n,s){this.resize(t,o,n),this.left=e-this.width/2,this.top=i-this.height/2,this._drawRawCircle(t,e,i,s),this.updateBoundingBox(e,i),this.labelModule.draw(t,this.left+this.textSize.width/2+this.margin.left,i,o,n)}updateBoundingBox(t,e){this.boundingBox.top=e-this.options.size,this.boundingBox.left=t-this.options.size,this.boundingBox.right=t+this.options.size,this.boundingBox.bottom=e+this.options.size}distanceToBorder(t){return t&&this.resize(t),.5*this.width}};class Tb extends kb{constructor(t,e,i,o,n){super(t,e,i),this.setImages(o,n)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover;if(void 0===this.imageObj.src||void 0===this.imageObj.width||void 0===this.imageObj.height){const t=2*this.options.size;return this.width=t,this.height=t,void(this.radius=.5*this.width)}this.needsRefresh(e,i)&&this._resizeImage()}draw(t,e,i,o,n,s){this.switchImages(o),this.resize();let r=e,a=i;"top-left"===this.options.shapeProperties.coordinateOrigin?(this.left=e,this.top=i,r+=this.width/2,a+=this.height/2):(this.left=e-this.width/2,this.top=i-this.height/2),this._drawRawCircle(t,r,a,s),t.save(),t.clip(),this._drawImageAtPosition(t,s),t.restore(),this._drawImageLabel(t,r,a,o,n),this.updateBoundingBox(e,i)}updateBoundingBox(t,e){"top-left"===this.options.shapeProperties.coordinateOrigin?(this.boundingBox.top=e,this.boundingBox.left=t,this.boundingBox.right=t+2*this.options.size,this.boundingBox.bottom=e+2*this.options.size):(this.boundingBox.top=e-this.options.size,this.boundingBox.left=t-this.options.size,this.boundingBox.right=t+this.options.size,this.boundingBox.bottom=e+this.options.size),this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelOffset)}distanceToBorder(t){return t&&this.resize(t),.5*this.width}}class Mb extends Ob{constructor(t,e,i){super(t,e,i)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{size:this.options.size};if(this.needsRefresh(e,i)){var n,s;this.labelModule.getTextSize(t,e,i);const r=2*o.size;this.width=null!==(n=this.customSizeWidth)&&void 0!==n?n:r,this.height=null!==(s=this.customSizeHeight)&&void 0!==s?s:r,this.radius=.5*this.width}}_drawShape(t,e,i,o,n,s,r,a){var h;return this.resize(t,s,r,a),this.left=o-this.width/2,this.top=n-this.height/2,this.initContextForDraw(t,a),(h=e,Object.prototype.hasOwnProperty.call(co,h)?co[h]:function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),o=1;o<e;o++)i[o-1]=arguments[o];CanvasRenderingContext2D.prototype[h].call(t,i)})(t,o,n,a.size),this.performFill(t,a),void 0!==this.options.icon&&void 0!==this.options.icon.code&&(t.font=(s?"bold ":"")+this.height/2+"px "+(this.options.icon.face||"FontAwesome"),t.fillStyle=this.options.icon.color||"black",t.textAlign="center",t.textBaseline="middle",t.fillText(this.options.icon.code,o,n)),{drawExternalLabel:()=>{if(void 0!==this.options.label){this.labelModule.calculateLabelSize(t,s,r,o,n,"hanging");const e=n+.5*this.height+.5*this.labelModule.size.height;this.labelModule.draw(t,o,e,s,r,"hanging")}this.updateBoundingBox(o,n)}}}updateBoundingBox(t,e){this.boundingBox.top=e-this.options.size,this.boundingBox.left=t-this.options.size,this.boundingBox.right=t+this.options.size,this.boundingBox.bottom=e+this.options.size,void 0!==this.options.label&&this.labelModule.size.width>0&&(this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelModule.size.height))}}function Db(t,e){var i=cp(t);if(qm){var o=qm(t);e&&(o=Om(o).call(o,function(e){return ny(t,e).enumerable})),i.push.apply(i,o)}return i}function Ib(t){for(var e=1;e<arguments.length;e++){var i,o,n=null!=arguments[e]?arguments[e]:{};e%2?Fh(i=Db(Object(n),!0)).call(i,function(e){Ev(t,e,n[e])}):gy?Ey(t,gy(n)):Fh(o=Db(Object(n))).call(o,function(e){Oy(t,e,ny(n,e))})}return t}class Pb extends Mb{constructor(t,e,i,o){super(t,e,i,o),this.ctxRenderer=o}draw(t,e,i,o,n,s){this.resize(t,o,n,s),this.left=e-this.width/2,this.top=i-this.height/2,t.save();const r=this.ctxRenderer({ctx:t,id:this.options.id,x:e,y:i,state:{selected:o,hover:n},style:Ib({},s),label:this.options.label});if(null!=r.drawNode&&r.drawNode(),t.restore(),r.drawExternalLabel){const e=r.drawExternalLabel;r.drawExternalLabel=()=>{t.save(),e(),t.restore()}}return r.nodeDimensions&&(this.customSizeWidth=r.nodeDimensions.width,this.customSizeHeight=r.nodeDimensions.height),r}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class Bb extends Ob{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t,e,i){if(this.needsRefresh(e,i)){const o=this.getDimensionsFromLabel(t,e,i).width+this.margin.right+this.margin.left;this.width=o,this.height=o,this.radius=this.width/2}}draw(t,e,i,o,n,s){this.resize(t,o,n),this.left=e-this.width/2,this.top=i-this.height/2,this.initContextForDraw(t,s),ho(t,e-this.width/2,i-this.height/2,this.width,this.height),this.performFill(t,s),this.updateBoundingBox(e,i,t,o,n),this.labelModule.draw(t,this.left+this.textSize.width/2+this.margin.left,this.top+this.textSize.height/2+this.margin.top,o,n)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}let zb=class extends Mb{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,n,s){return this._drawShape(t,"diamond",4,e,i,o,n,s)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}};class Fb extends Mb{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,n,s){return this._drawShape(t,"circle",2,e,i,o,n,s)}distanceToBorder(t){return t&&this.resize(t),this.options.size}}class Nb extends Ob{constructor(t,e,i){super(t,e,i)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover;if(this.needsRefresh(e,i)){const o=this.getDimensionsFromLabel(t,e,i);this.height=2*o.height,this.width=o.width+o.height,this.radius=.5*this.width}}draw(t,e,i,o,n,s){this.resize(t,o,n),this.left=e-.5*this.width,this.top=i-.5*this.height,this.initContextForDraw(t,s),ao(t,this.left,this.top,this.width,this.height),this.performFill(t,s),this.updateBoundingBox(e,i,t,o,n),this.labelModule.draw(t,e,i,o,n)}distanceToBorder(t,e){t&&this.resize(t);const i=.5*this.width,o=.5*this.height,n=Math.sin(e)*i,s=Math.cos(e)*o;return i*o/Math.sqrt(n*n+s*s)}}class Ab extends Ob{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t,e,i){this.needsRefresh(e,i)&&(this.iconSize={width:Number(this.options.icon.size),height:Number(this.options.icon.size)},this.width=this.iconSize.width+this.margin.right+this.margin.left,this.height=this.iconSize.height+this.margin.top+this.margin.bottom,this.radius=.5*this.width)}draw(t,e,i,o,n,s){return this.resize(t,o,n),this.options.icon.size=this.options.icon.size||50,this.left=e-this.width/2,this.top=i-this.height/2,this._icon(t,e,i,o,n,s),{drawExternalLabel:()=>{if(void 0!==this.options.label){const e=5;this.labelModule.draw(t,this.left+this.iconSize.width/2+this.margin.left,i+this.height/2+e,o)}this.updateBoundingBox(e,i)}}}updateBoundingBox(t,e){if(this.boundingBox.top=e-.5*this.options.icon.size,this.boundingBox.left=t-.5*this.options.icon.size,this.boundingBox.right=t+.5*this.options.icon.size,this.boundingBox.bottom=e+.5*this.options.icon.size,void 0!==this.options.label&&this.labelModule.size.width>0){const t=5;this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelModule.size.height+t)}}_icon(t,e,i,o,n,s){const r=Number(this.options.icon.size);void 0!==this.options.icon.code?(t.font=[null!=this.options.icon.weight?this.options.icon.weight:o?"bold":"",(null!=this.options.icon.weight&&o?5:0)+r+"px",this.options.icon.face].join(" "),t.fillStyle=this.options.icon.color||"black",t.textAlign="center",t.textBaseline="middle",this.enableShadow(t,s),t.fillText(this.options.icon.code,e,i),this.disableShadow(t,s)):console.error("When using the icon shape, you need to define the code in the icon options object. This can be done per node or globally.")}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}let Rb=class extends kb{constructor(t,e,i,o,n){super(t,e,i),this.setImages(o,n)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover;if(void 0===this.imageObj.src||void 0===this.imageObj.width||void 0===this.imageObj.height){const t=2*this.options.size;return this.width=t,void(this.height=t)}this.needsRefresh(e,i)&&this._resizeImage()}draw(t,e,i,o,n,s){t.save(),this.switchImages(o),this.resize();let r=e,a=i;if("top-left"===this.options.shapeProperties.coordinateOrigin?(this.left=e,this.top=i,r+=this.width/2,a+=this.height/2):(this.left=e-this.width/2,this.top=i-this.height/2),!0===this.options.shapeProperties.useBorderWithImage){const e=this.options.borderWidth,i=this.options.borderWidthSelected||2*this.options.borderWidth,r=(o?i:e)/this.body.view.scale;t.lineWidth=Math.min(this.width,r),t.beginPath();let a=o?this.options.color.highlight.border:n?this.options.color.hover.border:this.options.color.border,h=o?this.options.color.highlight.background:n?this.options.color.hover.background:this.options.color.background;void 0!==s.opacity&&(a=_s(a,s.opacity),h=_s(h,s.opacity)),t.strokeStyle=a,t.fillStyle=h,t.rect(this.left-.5*t.lineWidth,this.top-.5*t.lineWidth,this.width+t.lineWidth,this.height+t.lineWidth),Eb(t).call(t),this.performStroke(t,s),t.closePath()}this._drawImageAtPosition(t,s),this._drawImageLabel(t,r,a,o,n),this.updateBoundingBox(e,i),t.restore()}updateBoundingBox(t,e){this.resize(),"top-left"===this.options.shapeProperties.coordinateOrigin?(this.left=t,this.top=e):(this.left=t-this.width/2,this.top=e-this.height/2),this.boundingBox.left=this.left,this.boundingBox.top=this.top,this.boundingBox.bottom=this.top+this.height,this.boundingBox.right=this.left+this.width,void 0!==this.options.label&&this.labelModule.size.width>0&&(this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelOffset))}distanceToBorder(t,e){return this._distanceToBorder(t,e)}};class jb extends Mb{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,n,s){return this._drawShape(t,"square",2,e,i,o,n,s)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class Lb extends Mb{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,n,s){return this._drawShape(t,"hexagon",4,e,i,o,n,s)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class Hb extends Mb{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,n,s){return this._drawShape(t,"star",4,e,i,o,n,s)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class Wb extends Ob{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t,e,i){this.needsRefresh(e,i)&&(this.textSize=this.labelModule.getTextSize(t,e,i),this.width=this.textSize.width+this.margin.right+this.margin.left,this.height=this.textSize.height+this.margin.top+this.margin.bottom,this.radius=.5*this.width)}draw(t,e,i,o,n,s){this.resize(t,o,n),this.left=e-this.width/2,this.top=i-this.height/2,this.enableShadow(t,s),this.labelModule.draw(t,this.left+this.textSize.width/2+this.margin.left,this.top+this.textSize.height/2+this.margin.top,o,n),this.disableShadow(t,s),this.updateBoundingBox(e,i,t,o,n)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}let Vb=class extends Mb{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,n,s){return this._drawShape(t,"triangle",3,e,i,o,n,s)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}};class qb extends Mb{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,n,s){return this._drawShape(t,"triangleDown",3,e,i,o,n,s)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}function Ub(t,e){var i=cp(t);if(qm){var o=qm(t);e&&(o=Om(o).call(o,function(e){return ny(t,e).enumerable})),i.push.apply(i,o)}return i}function Yb(t){for(var e=1;e<arguments.length;e++){var i,o,n=null!=arguments[e]?arguments[e]:{};e%2?Fh(i=Ub(Object(n),!0)).call(i,function(e){Ev(t,e,n[e])}):gy?Ey(t,gy(n)):Fh(o=Ub(Object(n))).call(o,function(e){Oy(t,e,ny(n,e))})}return t}class Xb{constructor(t,e,i,o,n,s){this.options=Ms(n),this.globalOptions=n,this.defaultOptions=s,this.body=e,this.edges=[],this.id=void 0,this.imagelist=i,this.grouplist=o,this.x=void 0,this.y=void 0,this.baseSize=this.options.size,this.baseFontSize=this.options.font.size,this.predefinedPosition=!1,this.selected=!1,this.hover=!1,this.labelModule=new rb(this.body,this.options,!1),this.setOptions(t)}attachEdge(t){var e;-1===zr(e=this.edges).call(e,t)&&this.edges.push(t)}detachEdge(t){var e;const i=zr(e=this.edges).call(e,t);var o;-1!=i&&uh(o=this.edges).call(o,i,1)}setOptions(t){const e=this.options.shape;if(!t)return;if(void 0!==t.color&&(this._localColor=t.color),void 0!==t.id&&(this.id=t.id),void 0===this.id)throw new Error("Node must have an id");Xb.checkMass(t,this.id),void 0!==t.x&&(null===t.x?(this.x=void 0,this.predefinedPosition=!1):(this.x=$v(t.x),this.predefinedPosition=!0)),void 0!==t.y&&(null===t.y?(this.y=void 0,this.predefinedPosition=!1):(this.y=$v(t.y),this.predefinedPosition=!0)),void 0!==t.size&&(this.baseSize=t.size),void 0!==t.value&&(t.value=um(t.value)),Xb.parseOptions(this.options,t,!0,this.globalOptions,this.grouplist);const i=[t,this.options,this.defaultOptions];return this.chooser=Cy("node",i),this._load_images(),this.updateLabelModule(t),void 0!==t.opacity&&Xb.checkOpacity(t.opacity)&&(this.options.opacity=t.opacity),this.updateShape(e),void 0!==t.hidden||void 0!==t.physics}_load_images(){if(("circularImage"===this.options.shape||"image"===this.options.shape)&&void 0===this.options.image)throw new Error("Option image must be defined for node type '"+this.options.shape+"'");if(void 0!==this.options.image){if(void 0===this.imagelist)throw new Error("Internal Error: No images provided");if("string"==typeof this.options.image)this.imageObj=this.imagelist.load(this.options.image,this.options.brokenImage,this.id);else{if(void 0===this.options.image.unselected)throw new Error("No unselected image provided");this.imageObj=this.imagelist.load(this.options.image.unselected,this.options.brokenImage,this.id),void 0!==this.options.image.selected?this.imageObjAlt=this.imagelist.load(this.options.image.selected,this.options.brokenImage,this.id):this.imageObjAlt=void 0}}}static checkOpacity(t){return 0<=t&&t<=1}static checkCoordinateOrigin(t){return void 0===t||"center"===t||"top-left"===t}static updateGroupOptions(t,e,i){var o;if(void 0===i)return;const n=t.group;if(void 0!==e&&void 0!==e.group&&n!==e.group)throw new Error("updateGroupOptions: group values in options don't match.");if(!("number"==typeof n||"string"==typeof n&&""!=n))return;const s=i.get(n);void 0!==s.opacity&&void 0===e.opacity&&(Xb.checkOpacity(s.opacity)||(console.error("Invalid option for node opacity. Value must be between 0 and 1, found: "+s.opacity),s.opacity=void 0));const r=Om(o=Fm(e)).call(o,t=>null!=e[t]);r.push("font"),fs(r,t,s),t.color=Es(t.color)}static parseOptions(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=arguments.length>4?arguments[4]:void 0;if(fs(["color","fixed","shadow"],t,e,i),Xb.checkMass(e),void 0!==t.opacity&&(Xb.checkOpacity(t.opacity)||(console.error("Invalid option for node opacity. Value must be between 0 and 1, found: "+t.opacity),t.opacity=void 0)),void 0!==e.opacity&&(Xb.checkOpacity(e.opacity)||(console.error("Invalid option for node opacity. Value must be between 0 and 1, found: "+e.opacity),e.opacity=void 0)),e.shapeProperties&&!Xb.checkCoordinateOrigin(e.shapeProperties.coordinateOrigin)&&console.error("Invalid option for node coordinateOrigin, found: "+e.shapeProperties.coordinateOrigin),Ds(t,e,"shadow",o),void 0!==e.color&&null!==e.color){const i=Es(e.color);us(t.color,i)}else!0===i&&null===e.color&&(t.color=Ms(o.color));void 0!==e.fixed&&null!==e.fixed&&("boolean"==typeof e.fixed?(t.fixed.x=e.fixed,t.fixed.y=e.fixed):(void 0!==e.fixed.x&&"boolean"==typeof e.fixed.x&&(t.fixed.x=e.fixed.x),void 0!==e.fixed.y&&"boolean"==typeof e.fixed.y&&(t.fixed.y=e.fixed.y))),!0===i&&null===e.font&&(t.font=Ms(o.font)),Xb.updateGroupOptions(t,e,n),void 0!==e.scaling&&Ds(t.scaling,e.scaling,"label",o.scaling)}getFormattingValues(){const t={color:this.options.color.background,opacity:this.options.opacity,borderWidth:this.options.borderWidth,borderColor:this.options.color.border,size:this.options.size,borderDashes:this.options.shapeProperties.borderDashes,borderRadius:this.options.shapeProperties.borderRadius,shadow:this.options.shadow.enabled,shadowColor:this.options.shadow.color,shadowSize:this.options.shadow.size,shadowX:this.options.shadow.x,shadowY:this.options.shadow.y};if(this.selected||this.hover?!0===this.chooser?this.selected?(null!=this.options.borderWidthSelected?t.borderWidth=this.options.borderWidthSelected:t.borderWidth*=2,t.color=this.options.color.highlight.background,t.borderColor=this.options.color.highlight.border,t.shadow=this.options.shadow.enabled):this.hover&&(t.color=this.options.color.hover.background,t.borderColor=this.options.color.hover.border,t.shadow=this.options.shadow.enabled):"function"==typeof this.chooser&&(this.chooser(t,this.options.id,this.selected,this.hover),!1===t.shadow&&(t.shadowColor===this.options.shadow.color&&t.shadowSize===this.options.shadow.size&&t.shadowX===this.options.shadow.x&&t.shadowY===this.options.shadow.y||(t.shadow=!0))):t.shadow=this.options.shadow.enabled,void 0!==this.options.opacity){const e=this.options.opacity;t.borderColor=_s(t.borderColor,e),t.color=_s(t.color,e),t.shadowColor=_s(t.shadowColor,e)}return t}updateLabelModule(t){void 0!==this.options.label&&null!==this.options.label||(this.options.label=""),Xb.updateGroupOptions(this.options,Yb(Yb({},t),{},{color:t&&t.color||this._localColor||void 0}),this.grouplist);const e=this.grouplist.get(this.options.group,!1),i=[t,this.options,e,this.globalOptions,this.defaultOptions];this.labelModule.update(this.options,i),void 0!==this.labelModule.baseSize&&(this.baseFontSize=this.labelModule.baseSize)}updateShape(t){if(t===this.options.shape&&this.shape)this.shape.setOptions(this.options,this.imageObj,this.imageObjAlt);else switch(this.options.shape){case"box":this.shape=new Cb(this.options,this.body,this.labelModule);break;case"circle":this.shape=new Sb(this.options,this.body,this.labelModule);break;case"circularImage":this.shape=new Tb(this.options,this.body,this.labelModule,this.imageObj,this.imageObjAlt);break;case"custom":this.shape=new Pb(this.options,this.body,this.labelModule,this.options.ctxRenderer);break;case"database":this.shape=new Bb(this.options,this.body,this.labelModule);break;case"diamond":this.shape=new zb(this.options,this.body,this.labelModule);break;case"dot":this.shape=new Fb(this.options,this.body,this.labelModule);break;case"ellipse":default:this.shape=new Nb(this.options,this.body,this.labelModule);break;case"icon":this.shape=new Ab(this.options,this.body,this.labelModule);break;case"image":this.shape=new Rb(this.options,this.body,this.labelModule,this.imageObj,this.imageObjAlt);break;case"square":this.shape=new jb(this.options,this.body,this.labelModule);break;case"hexagon":this.shape=new Lb(this.options,this.body,this.labelModule);break;case"star":this.shape=new Hb(this.options,this.body,this.labelModule);break;case"text":this.shape=new Wb(this.options,this.body,this.labelModule);break;case"triangle":this.shape=new Vb(this.options,this.body,this.labelModule);break;case"triangleDown":this.shape=new qb(this.options,this.body,this.labelModule)}this.needsRefresh()}select(){this.selected=!0,this.needsRefresh()}unselect(){this.selected=!1,this.needsRefresh()}needsRefresh(){this.shape.refreshNeeded=!0}getTitle(){return this.options.title}distanceToBorder(t,e){return this.shape.distanceToBorder(t,e)}isFixed(){return this.options.fixed.x&&this.options.fixed.y}isSelected(){return this.selected}getValue(){return this.options.value}getLabelSize(){return this.labelModule.size()}setValueRange(t,e,i){if(void 0!==this.options.value){const o=this.options.scaling.customScalingFunction(t,e,i,this.options.value),n=this.options.scaling.max-this.options.scaling.min;if(!0===this.options.scaling.label.enabled){const t=this.options.scaling.label.max-this.options.scaling.label.min;this.options.font.size=this.options.scaling.label.min+o*t}this.options.size=this.options.scaling.min+o*n}else this.options.size=this.baseSize,this.options.font.size=this.baseFontSize;this.updateLabelModule()}draw(t){const e=this.getFormattingValues();return this.shape.draw(t,this.x,this.y,this.selected,this.hover,e)||{}}updateBoundingBox(t){this.shape.updateBoundingBox(this.x,this.y,t)}resize(t){const e=this.getFormattingValues();this.shape.resize(t,this.selected,this.hover,e)}getItemsOnPoint(t){const e=[];return this.labelModule.visible()&&ky(this.labelModule.getSize(),t)&&e.push({nodeId:this.id,labelId:0}),ky(this.shape.boundingBox,t)&&e.push({nodeId:this.id}),e}isOverlappingWith(t){return this.shape.left<t.right&&this.shape.left+this.shape.width>t.left&&this.shape.top<t.bottom&&this.shape.top+this.shape.height>t.top}isBoundingBoxOverlappingWith(t){return this.shape.boundingBox.left<t.right&&this.shape.boundingBox.right>t.left&&this.shape.boundingBox.top<t.bottom&&this.shape.boundingBox.bottom>t.top}static checkMass(t,e){if(void 0!==t.mass&&t.mass<=0){let i="";void 0!==e&&(i=" in node id: "+e),console.error("%cNegative or zero mass disallowed"+i+", setting mass to 1.",Vs),t.mass=1}}}class Kb{constructor(t,e,i,o){var n;if(this.body=t,this.images=e,this.groups=i,this.layoutEngine=o,this.body.functions.createNode=no(n=this.create).call(n,this),this.nodesListeners={add:(t,e)=>{this.add(e.items)},update:(t,e)=>{this.update(e.items,e.data,e.oldData)},remove:(t,e)=>{this.remove(e.items)}},this.defaultOptions={borderWidth:1,borderWidthSelected:void 0,brokenImage:void 0,color:{border:"#2B7CE9",background:"#97C2FC",highlight:{border:"#2B7CE9",background:"#D2E5FF"},hover:{border:"#2B7CE9",background:"#D2E5FF"}},opacity:void 0,fixed:{x:!1,y:!1},font:{color:"#343434",size:14,face:"arial",background:"none",strokeWidth:0,strokeColor:"#ffffff",align:"center",vadjust:0,multi:!1,bold:{mod:"bold"},boldital:{mod:"bold italic"},ital:{mod:"italic"},mono:{mod:"",size:15,face:"monospace",vadjust:2}},group:void 0,hidden:!1,icon:{face:"FontAwesome",code:void 0,size:50,color:"#2B7CE9"},image:void 0,imagePadding:{top:0,right:0,bottom:0,left:0},label:void 0,labelHighlightBold:!0,level:void 0,margin:{top:5,right:5,bottom:5,left:5},mass:1,physics:!0,scaling:{min:10,max:30,label:{enabled:!1,min:14,max:30,maxVisible:30,drawThreshold:5},customScalingFunction:function(t,e,i,o){if(e===t)return.5;{const i=1/(e-t);return Math.max(0,(o-t)*i)}}},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:10,x:5,y:5},shape:"ellipse",shapeProperties:{borderDashes:!1,borderRadius:6,interpolation:!0,useImageSize:!1,useBorderWithImage:!1,coordinateOrigin:"center"},size:25,title:void 0,value:void 0,x:void 0,y:void 0},this.defaultOptions.mass<=0)throw"Internal error: mass in defaultOptions of NodesHandler may not be zero or negative";this.options=Ms(this.defaultOptions),this.bindEventListeners()}bindEventListeners(){var t,e;this.body.emitter.on("refreshNodes",no(t=this.refresh).call(t,this)),this.body.emitter.on("refresh",no(e=this.refresh).call(e,this)),this.body.emitter.on("destroy",()=>{bs(this.nodesListeners,(t,e)=>{this.body.data.nodes&&this.body.data.nodes.off(e,t)}),delete this.body.functions.createNode,delete this.nodesListeners.add,delete this.nodesListeners.update,delete this.nodesListeners.remove,delete this.nodesListeners})}setOptions(t){if(void 0!==t){if(Xb.parseOptions(this.options,t),void 0!==t.opacity&&(Hu(t.opacity)||!$u(t.opacity)||t.opacity<0||t.opacity>1?console.error("Invalid option for node opacity. Value must be between 0 and 1, found: "+t.opacity):this.options.opacity=t.opacity),void 0!==t.shape)for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&this.body.nodes[t].updateShape();if(void 0!==t.font||void 0!==t.widthConstraint||void 0!==t.heightConstraint)for(const t of cp(this.body.nodes))this.body.nodes[t].updateLabelModule(),this.body.nodes[t].needsRefresh();if(void 0!==t.size)for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&this.body.nodes[t].needsRefresh();void 0===t.hidden&&void 0===t.physics||this.body.emitter.emit("_dataChanged")}}setData(t){let i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const o=this.body.data.nodes;if(e.isDataViewLike("id",t))this.body.data.nodes=t;else if(Th(t))this.body.data.nodes=new e.DataSet,this.body.data.nodes.add(t);else{if(t)throw new TypeError("Array or DataSet expected");this.body.data.nodes=new e.DataSet}if(o&&bs(this.nodesListeners,function(t,e){o.off(e,t)}),this.body.nodes={},this.body.data.nodes){const t=this;bs(this.nodesListeners,function(e,i){t.body.data.nodes.on(i,e)});const e=this.body.data.nodes.getIds();this.add(e,!0)}!1===i&&this.body.emitter.emit("_dataChanged")}add(t){let e,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const o=[];for(let i=0;i<t.length;i++){e=t[i];const n=this.body.data.nodes.get(e),s=this.create(n);o.push(s),this.body.nodes[e]=s}this.layoutEngine.positionInitially(o),!1===i&&this.body.emitter.emit("_dataChanged")}update(t,e,i){const o=this.body.nodes;let n=!1;for(let i=0;i<t.length;i++){const s=t[i];let r=o[s];const a=e[i];void 0!==r?r.setOptions(a)&&(n=!0):(n=!0,r=this.create(a),o[s]=r)}n||void 0===i||(n=Ep(e).call(e,function(t,e){const o=i[e];return o&&o.level!==t.level})),!0===n?this.body.emitter.emit("_dataChanged"):this.body.emitter.emit("_dataUpdated")}remove(t){const e=this.body.nodes;for(let i=0;i<t.length;i++){delete e[t[i]]}this.body.emitter.emit("_dataChanged")}create(t){return new(arguments.length>1&&void 0!==arguments[1]?arguments[1]:Xb)(t,this.body,this.images,this.groups,this.options,this.defaultOptions)}refresh(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];bs(this.body.nodes,(e,i)=>{const o=this.body.data.nodes.get(i);void 0!==o&&(!0===t&&e.setOptions({x:null,y:null}),e.setOptions({fixed:!1}),e.setOptions(o))})}getPositions(t){const e={};if(void 0!==t){if(!0===Th(t)){for(let i=0;i<t.length;i++)if(void 0!==this.body.nodes[t[i]]){const o=this.body.nodes[t[i]];e[t[i]]={x:Math.round(o.x),y:Math.round(o.y)}}}else if(void 0!==this.body.nodes[t]){const i=this.body.nodes[t];e[t]={x:Math.round(i.x),y:Math.round(i.y)}}}else for(let t=0;t<this.body.nodeIndices.length;t++){const i=this.body.nodes[this.body.nodeIndices[t]];e[this.body.nodeIndices[t]]={x:Math.round(i.x),y:Math.round(i.y)}}return e}getPosition(t){if(null==t)throw new TypeError("No id was specified for getPosition method.");if(null==this.body.nodes[t])throw new ReferenceError("NodeId provided for getPosition does not exist. Provided: ".concat(t));return{x:Math.round(this.body.nodes[t].x),y:Math.round(this.body.nodes[t].y)}}storePositions(){const t=[],e=this.body.data.nodes.getDataSet();for(const i of e.get()){const e=i.id,o=this.body.nodes[e],n=Math.round(o.x),s=Math.round(o.y);i.x===n&&i.y===s||t.push({id:e,x:n,y:s})}e.update(t)}getBoundingBox(t){if(void 0!==this.body.nodes[t])return this.body.nodes[t].shape.boundingBox}getConnectedNodes(t,e){const i=[];if(void 0!==this.body.nodes[t]){const o=this.body.nodes[t],n={};for(let t=0;t<o.edges.length;t++){const s=o.edges[t];"to"!==e&&s.toId==o.id?void 0===n[s.fromId]&&(i.push(s.fromId),n[s.fromId]=!0):"from"!==e&&s.fromId==o.id&&void 0===n[s.toId]&&(i.push(s.toId),n[s.toId]=!0)}}return i}getConnectedEdges(t){const e=[];if(void 0!==this.body.nodes[t]){const i=this.body.nodes[t];for(let t=0;t<i.edges.length;t++)e.push(i.edges[t].id)}else console.error("NodeId provided for getConnectedEdges does not exist. Provided: ",t);return e}moveNode(t,e,i){void 0!==this.body.nodes[t]?(this.body.nodes[t].x=Number(e),this.body.nodes[t].y=Number(i),Kp(()=>{this.body.emitter.emit("startSimulation")},0)):console.error("Node id supplied to moveNode does not exist. Provided: ",t)}}var Gb,Zb,Qb,$b,Jb,tw,ew,iw,ow,nw,sw,rw,aw,hw={};function dw(){if(Zb)return Gb;Zb=1;var t=ci(),e=la(),i=Ot(),o=RangeError;return Gb=function(n){var s=e(i(this)),r="",a=t(n);if(a<0||a===1/0)throw new o("Wrong number of repetitions");for(;a>0;(a>>>=1)&&(s+=s))1&a&&(r+=s);return r}}function lw(){if($b)return Qb;$b=1;var t=C(),e=pi(),i=la(),o=dw(),n=Ot(),s=t(o),r=t("".slice),a=Math.ceil,h=function(t){return function(o,h,d){var l,c,u=i(n(o)),p=e(h),f=u.length,g=void 0===d?" ":i(d);return p<=f||""===g?u:((c=s(g,a((l=p-f)/g.length))).length>l&&(c=r(c,0,l)),t?u+c:c+u)}};return Qb={start:h(!1),end:h(!0)}}function cw(){if(tw)return Jb;tw=1;var t=C(),e=x(),i=lw().start,o=RangeError,n=isFinite,s=Math.abs,r=Date.prototype,a=r.toISOString,h=t(r.getTime),d=t(r.getUTCDate),l=t(r.getUTCFullYear),c=t(r.getUTCHours),u=t(r.getUTCMilliseconds),p=t(r.getUTCMinutes),f=t(r.getUTCMonth),g=t(r.getUTCSeconds);return Jb=e(function(){return"0385-07-25T07:06:39.999Z"!==a.call(new Date(-50000000000001))})||!e(function(){a.call(new Date(NaN))})?function(){if(!n(h(this)))throw new o("Invalid time value");var t=this,e=l(t),r=u(t),a=e<0?"-":e>9999?"+":"";return a+i(s(e),a?6:4,0)+"-"+i(f(t)+1,2,0)+"-"+i(d(t),2,0)+"T"+i(c(t),2,0)+":"+i(p(t),2,0)+":"+i(g(t),2,0)+"."+i(r,3,0)+"Z"}:a}function uw(){if(ow)return iw;ow=1,function(){if(ew)return hw;ew=1;var t=di(),e=F(),i=ye(),o=xe(),n=cw(),s=k();t({target:"Date",proto:!0,forced:x()(function(){return null!==new Date(NaN).toJSON()||1!==e(Date.prototype.toJSON,{toISOString:function(){return 1}})})},{toJSON:function(t){var r=i(this),a=o(r,"number");return"number"!=typeof a||isFinite(a)?"toISOString"in r||"Date"!==s(r)?r.toISOString():e(n,r):null}})}(),Mf();var t=St(),e=O();return t.JSON||(t.JSON={stringify:JSON.stringify}),iw=function(i,o,n){return e(t.JSON.stringify,null,arguments)},iw}function pw(){return sw?nw:(sw=1,nw=uw())}var fw,gw,vw,mw,yw,bw,ww,_w=o(aw?rw:(aw=1,rw=pw())),xw={};function Ew(){return vw?gw:(vw=1,function(){if(fw)return xw;fw=1;var t=di(),e=Math.hypot,i=Math.abs,o=Math.sqrt;t({target:"Math",stat:!0,arity:2,forced:!!e&&e(1/0,NaN)!==1/0},{hypot:function(t,e){for(var n,s,r=0,a=0,h=arguments.length,d=0;a<h;)d<(n=i(arguments[a++]))?(r=r*(s=d/n)*s+1,d=n):r+=n>0?(s=n/d)*s:n;return d===1/0?1/0:d*o(r)}})}(),gw=St().Math.hypot)}function Ow(){return yw?mw:(yw=1,mw=Ew())}var Cw=o(ww?bw:(ww=1,bw=Ow()));class kw{static transform(t,e){Th(t)||(t=[t]);const i=e.point.x,o=e.point.y,n=e.angle,s=e.length;for(let e=0;e<t.length;++e){const r=t[e],a=r.x*Math.cos(n)-r.y*Math.sin(n),h=r.x*Math.sin(n)+r.y*Math.cos(n);r.x=i+s*a,r.y=o+s*h}}static drawPath(t,e){t.beginPath(),t.moveTo(e[0].x,e[0].y);for(let i=1;i<e.length;++i)t.lineTo(e[i].x,e[i].y);t.closePath()}}let Sw=class extends kw{static draw(t,e){if(e.image){t.save(),t.translate(e.point.x,e.point.y),t.rotate(Math.PI/2+e.angle);const i=null!=e.imageWidth?e.imageWidth:e.image.width,o=null!=e.imageHeight?e.imageHeight:e.image.height;e.image.drawImageAtPosition(t,1,-i/2,0,i,o),t.restore()}return!1}};class Tw extends kw{static draw(t,e){const i=[{x:0,y:0},{x:-1,y:.3},{x:-.9,y:0},{x:-1,y:-.3}];return kw.transform(i,e),kw.drawPath(t,i),!0}}class Mw{static draw(t,e){const i=[{x:-1,y:0},{x:0,y:.3},{x:-.4,y:0},{x:0,y:-.3}];return kw.transform(i,e),kw.drawPath(t,i),!0}}class Dw{static draw(t,e){const i={x:-.4,y:0};kw.transform(i,e),t.strokeStyle=t.fillStyle,t.fillStyle="rgba(0, 0, 0, 0)";const o=Math.PI,n=e.angle-o/2,s=e.angle+o/2;return t.beginPath(),t.arc(i.x,i.y,.4*e.length,n,s,!1),t.stroke(),!0}}class Iw{static draw(t,e){const i={x:-.3,y:0};kw.transform(i,e),t.strokeStyle=t.fillStyle,t.fillStyle="rgba(0, 0, 0, 0)";const o=Math.PI,n=e.angle+o/2,s=e.angle+3*o/2;return t.beginPath(),t.arc(i.x,i.y,.4*e.length,n,s,!1),t.stroke(),!0}}class Pw{static draw(t,e){const i=[{x:.02,y:0},{x:-1,y:.3},{x:-1,y:-.3}];return kw.transform(i,e),kw.drawPath(t,i),!0}}class Bw{static draw(t,e){const i=[{x:0,y:.3},{x:0,y:-.3},{x:-1,y:0}];return kw.transform(i,e),kw.drawPath(t,i),!0}}class zw{static draw(t,e){const i={x:-.4,y:0};return kw.transform(i,e),so(t,i.x,i.y,.4*e.length),!0}}class Fw{static draw(t,e){const i=[{x:0,y:.5},{x:0,y:-.5},{x:-.15,y:-.5},{x:-.15,y:.5}];return kw.transform(i,e),kw.drawPath(t,i),!0}}class Nw{static draw(t,e){const i=[{x:0,y:.3},{x:0,y:-.3},{x:-.6,y:-.3},{x:-.6,y:.3}];return kw.transform(i,e),kw.drawPath(t,i),!0}}class Aw{static draw(t,e){const i=[{x:0,y:0},{x:-.5,y:-.3},{x:-1,y:0},{x:-.5,y:.3}];return kw.transform(i,e),kw.drawPath(t,i),!0}}class Rw{static draw(t,e){const i=[{x:-1,y:.3},{x:-.5,y:0},{x:-1,y:-.3},{x:0,y:0}];return kw.transform(i,e),kw.drawPath(t,i),!0}}class jw{static draw(t,e){let i;switch(e.type&&(i=e.type.toLowerCase()),i){case"image":return Sw.draw(t,e);case"circle":return zw.draw(t,e);case"box":return Nw.draw(t,e);case"crow":return Mw.draw(t,e);case"curve":return Dw.draw(t,e);case"diamond":return Aw.draw(t,e);case"inv_curve":return Iw.draw(t,e);case"triangle":return Pw.draw(t,e);case"inv_triangle":return Bw.draw(t,e);case"bar":return Fw.draw(t,e);case"vee":return Rw.draw(t,e);default:return Tw.draw(t,e)}}}function Lw(t,e){var i=cp(t);if(qm){var o=qm(t);e&&(o=Om(o).call(o,function(e){return ny(t,e).enumerable})),i.push.apply(i,o)}return i}function Hw(t){for(var e=1;e<arguments.length;e++){var i,o,n=null!=arguments[e]?arguments[e]:{};e%2?Fh(i=Lw(Object(n),!0)).call(i,function(e){Ev(t,e,n[e])}):gy?Ey(t,gy(n)):Fh(o=Lw(Object(n))).call(o,function(e){Oy(t,e,ny(n,e))})}return t}class Ww{constructor(t,e,i){Ev(this,"color",{}),Ev(this,"colorDirty",!0),Ev(this,"hoverWidth",1.5),Ev(this,"selectionWidth",2),this._body=e,this._labelModule=i,this.setOptions(t),this.fromPoint=this.from,this.toPoint=this.to}connect(){this.from=this._body.nodes[this.options.from],this.to=this._body.nodes[this.options.to]}cleanup(){return!1}setOptions(t){this.options=t,this.from=this._body.nodes[this.options.from],this.to=this._body.nodes[this.options.to],this.id=this.options.id}drawLine(t,e,i,o){let n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.getViaNode();t.strokeStyle=this.getColor(t,e),t.lineWidth=e.width,!1!==e.dashes?this._drawDashedLine(t,e,n):this._drawLine(t,e,n)}_drawLine(t,e,i,o,n){if(this.from!=this.to)this._line(t,e,i,o,n);else{const[i,o,n]=this._getCircleData(t);this._circle(t,e,i,o,n)}}_drawDashedLine(t,e,i,o,n){t.lineCap="round";const s=Th(e.dashes)?e.dashes:[5,5];if(void 0!==t.setLineDash){if(t.save(),t.setLineDash(s),t.lineDashOffset=0,this.from!=this.to)this._line(t,e,i);else{const[i,o,n]=this._getCircleData(t);this._circle(t,e,i,o,n)}t.setLineDash([0]),t.lineDashOffset=0,t.restore()}else{if(this.from!=this.to)lo(t,this.from.x,this.from.y,this.to.x,this.to.y,s);else{const[i,o,n]=this._getCircleData(t);this._circle(t,e,i,o,n)}this.enableShadow(t,e),t.stroke(),this.disableShadow(t,e)}}findBorderPosition(t,e,i){return this.from!=this.to?this._findBorderPosition(t,e,i):this._findBorderPositionCircle(t,e,i)}findBorderPositions(t){if(this.from!=this.to)return{from:this._findBorderPosition(this.from,t),to:this._findBorderPosition(this.to,t)};{var e;const[i,o]=Zy(e=this._getCircleData(t)).call(e,0,2);return{from:this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:.25,high:.6,direction:-1}),to:this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:.6,high:.8,direction:1})}}}_getCircleData(t){const e=this.options.selfReference.size;void 0!==t&&void 0===this.from.shape.width&&this.from.shape.resize(t);const i=Ty(t,this.options.selfReference.angle,e,this.from);return[i.x,i.y,e]}_pointOnCircle(t,e,i,o){const n=2*o*Math.PI;return{x:t+i*Math.cos(n),y:e-i*Math.sin(n)}}_findBorderPositionCircle(t,e,i){const o=i.x,n=i.y;let s=i.low,r=i.high;const a=i.direction,h=this.options.selfReference.size;let d,l=.5*(s+r),c=0;!0===this.options.arrowStrikethrough&&(-1===a?c=this.options.endPointOffset.from:1===a&&(c=this.options.endPointOffset.to));let u=0;do{l=.5*(s+r),d=this._pointOnCircle(o,n,h,l);const i=Math.atan2(t.y-d.y,t.x-d.x),p=t.distanceToBorder(e,i)+c-Math.sqrt(Math.pow(d.x-t.x,2)+Math.pow(d.y-t.y,2));if(Math.abs(p)<.05)break;p>0?a>0?s=l:r=l:a>0?r=l:s=l,++u}while(s<=r&&u<10);return Hw(Hw({},d),{},{t:l})}getLineWidth(t,e){return!0===t?Math.max(this.selectionWidth,.3/this._body.view.scale):!0===e?Math.max(this.hoverWidth,.3/this._body.view.scale):Math.max(this.options.width,.3/this._body.view.scale)}getColor(t,e){if(!1!==e.inheritsColor){if("both"===e.inheritsColor&&this.from.id!==this.to.id){const i=t.createLinearGradient(this.from.x,this.from.y,this.to.x,this.to.y);let o=this.from.options.color.highlight.border,n=this.to.options.color.highlight.border;return!1===this.from.selected&&!1===this.to.selected?(o=_s(this.from.options.color.border,e.opacity),n=_s(this.to.options.color.border,e.opacity)):!0===this.from.selected&&!1===this.to.selected?n=this.to.options.color.border:!1===this.from.selected&&!0===this.to.selected&&(o=this.from.options.color.border),i.addColorStop(0,o),i.addColorStop(1,n),i}return"to"===e.inheritsColor?_s(this.to.options.color.border,e.opacity):_s(this.from.options.color.border,e.opacity)}return _s(e.color,e.opacity)}_circle(t,e,i,o,n){this.enableShadow(t,e);let s=0,r=2*Math.PI;if(!this.options.selfReference.renderBehindTheNode){const e=this.options.selfReference.angle,n=this.options.selfReference.angle+Math.PI,a=this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:e,high:n,direction:-1}),h=this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:e,high:n,direction:1});s=Math.atan2(a.y-o,a.x-i),r=Math.atan2(h.y-o,h.x-i)}t.beginPath(),t.arc(i,o,n,s,r,!1),t.stroke(),this.disableShadow(t,e)}getDistanceToEdge(t,e,i,o,n,s){if(this.from!=this.to)return this._getDistanceToEdge(t,e,i,o,n,s);{const[t,e,i]=this._getCircleData(void 0),o=t-n,r=e-s;return Math.abs(Math.sqrt(o*o+r*r)-i)}}_getDistanceToLine(t,e,i,o,n,s){const r=i-t,a=o-e;let h=((n-t)*r+(s-e)*a)/(r*r+a*a);h>1?h=1:h<0&&(h=0);const d=t+h*r-n,l=e+h*a-s;return Math.sqrt(d*d+l*l)}getArrowData(t,e,i,o,n,s){let r,a,h,d,l,c,u;const p=s.width;"from"===e?(h=this.from,d=this.to,l=s.fromArrowScale<0,c=Math.abs(s.fromArrowScale),u=s.fromArrowType):"to"===e?(h=this.to,d=this.from,l=s.toArrowScale<0,c=Math.abs(s.toArrowScale),u=s.toArrowType):(h=this.to,d=this.from,l=s.middleArrowScale<0,c=Math.abs(s.middleArrowScale),u=s.middleArrowType);const f=15*c+3*p;if(h!=d){const o=f/Cw(h.x-d.x,h.y-d.y);if("middle"!==e)if(!0===this.options.smooth.enabled){const n=this._findBorderPosition(h,t,{via:i}),s=this.getPoint(n.t+o*("from"===e?1:-1),i);r=Math.atan2(n.y-s.y,n.x-s.x),a=n}else r=Math.atan2(h.y-d.y,h.x-d.x),a=this._findBorderPosition(h,t);else{const t=(l?-o:o)/2,e=this.getPoint(.5+t,i),n=this.getPoint(.5-t,i);r=Math.atan2(e.y-n.y,e.x-n.x),a=this.getPoint(.5,i)}}else{const[i,o,n]=this._getCircleData(t);if("from"===e){const e=this.options.selfReference.angle,n=this.options.selfReference.angle+Math.PI,s=this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:e,high:n,direction:-1});r=-2*s.t*Math.PI+1.5*Math.PI+.1*Math.PI,a=s}else if("to"===e){const e=this.options.selfReference.angle,n=this.options.selfReference.angle+Math.PI,s=this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:e,high:n,direction:1});r=-2*s.t*Math.PI+1.5*Math.PI-1.1*Math.PI,a=s}else{const t=this.options.selfReference.angle/(2*Math.PI);a=this._pointOnCircle(i,o,n,t),r=-2*t*Math.PI+1.5*Math.PI+.1*Math.PI}}return{point:a,core:{x:a.x-.9*f*Math.cos(r),y:a.y-.9*f*Math.sin(r)},angle:r,length:f,type:u}}drawArrowHead(t,e,i,o,n){t.strokeStyle=this.getColor(t,e),t.fillStyle=t.strokeStyle,t.lineWidth=e.width;jw.draw(t,n)&&(this.enableShadow(t,e),Eb(t).call(t),this.disableShadow(t,e))}enableShadow(t,e){!0===e.shadow&&(t.shadowColor=e.shadowColor,t.shadowBlur=e.shadowSize,t.shadowOffsetX=e.shadowX,t.shadowOffsetY=e.shadowY)}disableShadow(t,e){!0===e.shadow&&(t.shadowColor="rgba(0,0,0,0)",t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0)}drawBackground(t,e){if(!1!==e.background){const i={strokeStyle:t.strokeStyle,lineWidth:t.lineWidth,dashes:t.dashes};t.strokeStyle=e.backgroundColor,t.lineWidth=e.backgroundSize,this.setStrokeDashed(t,e.backgroundDashes),t.stroke(),t.strokeStyle=i.strokeStyle,t.lineWidth=i.lineWidth,t.dashes=i.dashes,this.setStrokeDashed(t,e.dashes)}}setStrokeDashed(t,e){if(!1!==e)if(void 0!==t.setLineDash){const i=Th(e)?e:[5,5];t.setLineDash(i)}else console.warn("setLineDash is not supported in this browser. The dashed stroke cannot be used.");else void 0!==t.setLineDash?t.setLineDash([]):console.warn("setLineDash is not supported in this browser. The dashed stroke cannot be used.")}}function Vw(t,e){var i=cp(t);if(qm){var o=qm(t);e&&(o=Om(o).call(o,function(e){return ny(t,e).enumerable})),i.push.apply(i,o)}return i}function qw(t){for(var e=1;e<arguments.length;e++){var i,o,n=null!=arguments[e]?arguments[e]:{};e%2?Fh(i=Vw(Object(n),!0)).call(i,function(e){Ev(t,e,n[e])}):gy?Ey(t,gy(n)):Fh(o=Vw(Object(n))).call(o,function(e){Oy(t,e,ny(n,e))})}return t}class Uw extends Ww{constructor(t,e,i){super(t,e,i)}_findBorderPositionBezier(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this._getViaCoordinates();let o,n,s=!1,r=1,a=0,h=this.to,d=this.options.endPointOffset?this.options.endPointOffset.to:0;t.id===this.from.id&&(h=this.from,s=!0,d=this.options.endPointOffset?this.options.endPointOffset.from:0),!1===this.options.arrowStrikethrough&&(d=0);let l=0;do{n=.5*(a+r),o=this.getPoint(n,i);const t=Math.atan2(h.y-o.y,h.x-o.x),c=h.distanceToBorder(e,t)+d-Math.sqrt(Math.pow(o.x-h.x,2)+Math.pow(o.y-h.y,2));if(Math.abs(c)<.2)break;c<0?!1===s?a=n:r=n:!1===s?r=n:a=n,++l}while(a<=r&&l<10);return qw(qw({},o),{},{t:n})}_getDistanceToBezierEdge(t,e,i,o,n,s,r){let a,h,d,l,c,u=1e9,p=t,f=e;for(h=1;h<10;h++)d=.1*h,l=Math.pow(1-d,2)*t+2*d*(1-d)*r.x+Math.pow(d,2)*i,c=Math.pow(1-d,2)*e+2*d*(1-d)*r.y+Math.pow(d,2)*o,h>0&&(a=this._getDistanceToLine(p,f,l,c,n,s),u=a<u?a:u),p=l,f=c;return u}_bezierCurve(t,e,i,o){t.beginPath(),t.moveTo(this.fromPoint.x,this.fromPoint.y),null!=i&&null!=i.x?null!=o&&null!=o.x?t.bezierCurveTo(i.x,i.y,o.x,o.y,this.toPoint.x,this.toPoint.y):t.quadraticCurveTo(i.x,i.y,this.toPoint.x,this.toPoint.y):t.lineTo(this.toPoint.x,this.toPoint.y),this.drawBackground(t,e),this.enableShadow(t,e),t.stroke(),this.disableShadow(t,e)}getViaNode(){return this._getViaCoordinates()}}class Yw extends Uw{constructor(t,e,i){super(t,e,i),Ev(this,"via",this.via),this._boundFunction=()=>{this.positionBezierNode()},this._body.emitter.on("_repositionBezierNodes",this._boundFunction)}setOptions(t){super.setOptions(t);let e=!1;this.options.physics!==t.physics&&(e=!0),this.options=t,this.id=this.options.id,this.from=this._body.nodes[this.options.from],this.to=this._body.nodes[this.options.to],this.setupSupportNode(),this.connect(),!0===e&&(this.via.setOptions({physics:this.options.physics}),this.positionBezierNode())}connect(){this.from=this._body.nodes[this.options.from],this.to=this._body.nodes[this.options.to],void 0===this.from||void 0===this.to||!1===this.options.physics||this.from.id===this.to.id?this.via.setOptions({physics:!1}):this.via.setOptions({physics:!0})}cleanup(){return this._body.emitter.off("_repositionBezierNodes",this._boundFunction),void 0!==this.via&&(delete this._body.nodes[this.via.id],this.via=void 0,!0)}setupSupportNode(){if(void 0===this.via){const t="edgeId:"+this.id,e=this._body.functions.createNode({id:t,shape:"circle",physics:!0,hidden:!0});this._body.nodes[t]=e,this.via=e,this.via.parentEdgeId=this.id,this.positionBezierNode()}}positionBezierNode(){void 0!==this.via&&void 0!==this.from&&void 0!==this.to?(this.via.x=.5*(this.from.x+this.to.x),this.via.y=.5*(this.from.y+this.to.y)):void 0!==this.via&&(this.via.x=0,this.via.y=0)}_line(t,e,i){this._bezierCurve(t,e,i)}_getViaCoordinates(){return this.via}getViaNode(){return this.via}getPoint(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.via;if(this.from===this.to){const[e,i,o]=this._getCircleData(),n=2*Math.PI*(1-t);return{x:e+o*Math.sin(n),y:i+o-o*(1-Math.cos(n))}}return{x:Math.pow(1-t,2)*this.fromPoint.x+2*t*(1-t)*e.x+Math.pow(t,2)*this.toPoint.x,y:Math.pow(1-t,2)*this.fromPoint.y+2*t*(1-t)*e.y+Math.pow(t,2)*this.toPoint.y}}_findBorderPosition(t,e){return this._findBorderPositionBezier(t,e,this.via)}_getDistanceToEdge(t,e,i,o,n,s){return this._getDistanceToBezierEdge(t,e,i,o,n,s,this.via)}}class Xw extends Uw{constructor(t,e,i){super(t,e,i)}_line(t,e,i){this._bezierCurve(t,e,i)}getViaNode(){return this._getViaCoordinates()}_getViaCoordinates(){const t=this.options.smooth.roundness,e=this.options.smooth.type;let i=Math.abs(this.from.x-this.to.x),o=Math.abs(this.from.y-this.to.y);if("discrete"===e||"diagonalCross"===e){let n,s;n=s=i<=o?t*o:t*i,this.from.x>this.to.x&&(n=-n),this.from.y>=this.to.y&&(s=-s);let r=this.from.x+n,a=this.from.y+s;return"discrete"===e&&(i<=o?r=i<t*o?this.from.x:r:a=o<t*i?this.from.y:a),{x:r,y:a}}if("straightCross"===e){let e=(1-t)*i,n=(1-t)*o;return i<=o?(e=0,this.from.y<this.to.y&&(n=-n)):(this.from.x<this.to.x&&(e=-e),n=0),{x:this.to.x+e,y:this.to.y+n}}if("horizontal"===e){let e=(1-t)*i;return this.from.x<this.to.x&&(e=-e),{x:this.to.x+e,y:this.from.y}}if("vertical"===e){let e=(1-t)*o;return this.from.y<this.to.y&&(e=-e),{x:this.from.x,y:this.to.y+e}}if("curvedCW"===e){i=this.to.x-this.from.x,o=this.from.y-this.to.y;const e=Math.sqrt(i*i+o*o),n=Math.PI,s=(Math.atan2(o,i)+(.5*t+.5)*n)%(2*n);return{x:this.from.x+(.5*t+.5)*e*Math.sin(s),y:this.from.y+(.5*t+.5)*e*Math.cos(s)}}if("curvedCCW"===e){i=this.to.x-this.from.x,o=this.from.y-this.to.y;const e=Math.sqrt(i*i+o*o),n=Math.PI,s=(Math.atan2(o,i)+(.5*-t+.5)*n)%(2*n);return{x:this.from.x+(.5*t+.5)*e*Math.sin(s),y:this.from.y+(.5*t+.5)*e*Math.cos(s)}}{let e,n;e=n=i<=o?t*o:t*i,this.from.x>this.to.x&&(e=-e),this.from.y>=this.to.y&&(n=-n);let s=this.from.x+e,r=this.from.y+n;return i<=o?s=this.from.x<=this.to.x?this.to.x<s?this.to.x:s:this.to.x>s?this.to.x:s:r=this.from.y>=this.to.y?this.to.y>r?this.to.y:r:this.to.y<r?this.to.y:r,{x:s,y:r}}}_findBorderPosition(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this._findBorderPositionBezier(t,e,i.via)}_getDistanceToEdge(t,e,i,o,n,s){let r=arguments.length>6&&void 0!==arguments[6]?arguments[6]:this._getViaCoordinates();return this._getDistanceToBezierEdge(t,e,i,o,n,s,r)}getPoint(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._getViaCoordinates();const i=t;return{x:Math.pow(1-i,2)*this.fromPoint.x+2*i*(1-i)*e.x+Math.pow(i,2)*this.toPoint.x,y:Math.pow(1-i,2)*this.fromPoint.y+2*i*(1-i)*e.y+Math.pow(i,2)*this.toPoint.y}}}class Kw extends Uw{constructor(t,e,i){super(t,e,i)}_getDistanceToBezierEdge2(t,e,i,o,n,s,r,a){let h=1e9,d=t,l=e;const c=[0,0,0,0];for(let u=1;u<10;u++){const p=.1*u;c[0]=Math.pow(1-p,3),c[1]=3*p*Math.pow(1-p,2),c[2]=3*Math.pow(p,2)*(1-p),c[3]=Math.pow(p,3);const f=c[0]*t+c[1]*r.x+c[2]*a.x+c[3]*i,g=c[0]*e+c[1]*r.y+c[2]*a.y+c[3]*o;if(u>0){const t=this._getDistanceToLine(d,l,f,g,n,s);h=t<h?t:h}d=f,l=g}return h}}class Gw extends Kw{constructor(t,e,i){super(t,e,i)}_line(t,e,i){const o=i[0],n=i[1];this._bezierCurve(t,e,o,n)}_getViaCoordinates(){const t=this.from.x-this.to.x,e=this.from.y-this.to.y;let i,o,n,s;const r=this.options.smooth.roundness;return(Math.abs(t)>Math.abs(e)||!0===this.options.smooth.forceDirection||"horizontal"===this.options.smooth.forceDirection)&&"vertical"!==this.options.smooth.forceDirection?(o=this.from.y,s=this.to.y,i=this.from.x-r*t,n=this.to.x+r*t):(o=this.from.y-r*e,s=this.to.y+r*e,i=this.from.x,n=this.to.x),[{x:i,y:o},{x:n,y:s}]}getViaNode(){return this._getViaCoordinates()}_findBorderPosition(t,e){return this._findBorderPositionBezier(t,e)}_getDistanceToEdge(t,e,i,o,n,s){let[r,a]=arguments.length>6&&void 0!==arguments[6]?arguments[6]:this._getViaCoordinates();return this._getDistanceToBezierEdge2(t,e,i,o,n,s,r,a)}getPoint(t){let[e,i]=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._getViaCoordinates();const o=t,n=[Math.pow(1-o,3),3*o*Math.pow(1-o,2),3*Math.pow(o,2)*(1-o),Math.pow(o,3)];return{x:n[0]*this.fromPoint.x+n[1]*e.x+n[2]*i.x+n[3]*this.toPoint.x,y:n[0]*this.fromPoint.y+n[1]*e.y+n[2]*i.y+n[3]*this.toPoint.y}}}class Zw extends Ww{constructor(t,e,i){super(t,e,i)}_line(t,e){t.beginPath(),t.moveTo(this.fromPoint.x,this.fromPoint.y),t.lineTo(this.toPoint.x,this.toPoint.y),this.enableShadow(t,e),t.stroke(),this.disableShadow(t,e)}getViaNode(){}getPoint(t){return{x:(1-t)*this.fromPoint.x+t*this.toPoint.x,y:(1-t)*this.fromPoint.y+t*this.toPoint.y}}_findBorderPosition(t,e){let i=this.to,o=this.from;t.id===this.from.id&&(i=this.from,o=this.to);const n=Math.atan2(i.y-o.y,i.x-o.x),s=i.x-o.x,r=i.y-o.y,a=Math.sqrt(s*s+r*r),h=(a-t.distanceToBorder(e,n))/a;return{x:(1-h)*o.x+h*i.x,y:(1-h)*o.y+h*i.y,t:0}}_getDistanceToEdge(t,e,i,o,n,s){return this._getDistanceToLine(t,e,i,o,n,s)}}class Qw{constructor(t,e,i,o,n){if(void 0===e)throw new Error("No body provided");this.options=Ms(o),this.globalOptions=o,this.defaultOptions=n,this.body=e,this.imagelist=i,this.id=void 0,this.fromId=void 0,this.toId=void 0,this.selected=!1,this.hover=!1,this.labelDirty=!0,this.baseWidth=this.options.width,this.baseFontSize=this.options.font.size,this.from=void 0,this.to=void 0,this.edgeType=void 0,this.connected=!1,this.labelModule=new rb(this.body,this.options,!0),this.setOptions(t)}setOptions(t){if(!t)return;let e=void 0!==t.physics&&this.options.physics!==t.physics||void 0!==t.hidden&&(this.options.hidden||!1)!==(t.hidden||!1)||void 0!==t.from&&this.options.from!==t.from||void 0!==t.to&&this.options.to!==t.to;Qw.parseOptions(this.options,t,!0,this.globalOptions),void 0!==t.id&&(this.id=t.id),void 0!==t.from&&(this.fromId=t.from),void 0!==t.to&&(this.toId=t.to),void 0!==t.title&&(this.title=t.title),void 0!==t.value&&(t.value=um(t.value));const i=[t,this.options,this.defaultOptions];return this.chooser=Cy("edge",i),this.updateLabelModule(t),e=this.updateEdgeType()||e,this._setInteractionWidths(),this.connect(),e}static parseOptions(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(ps(["endPointOffset","arrowStrikethrough","id","from","hidden","hoverWidth","labelHighlightBold","length","line","opacity","physics","scaling","selectionWidth","selfReferenceSize","selfReference","to","title","value","width","font","chosen","widthConstraint"],t,e,i),void 0!==e.endPointOffset&&void 0!==e.endPointOffset.from&&($u(e.endPointOffset.from)?t.endPointOffset.from=e.endPointOffset.from:(t.endPointOffset.from=void 0!==o.endPointOffset.from?o.endPointOffset.from:0,console.error("endPointOffset.from is not a valid number"))),void 0!==e.endPointOffset&&void 0!==e.endPointOffset.to&&($u(e.endPointOffset.to)?t.endPointOffset.to=e.endPointOffset.to:(t.endPointOffset.to=void 0!==o.endPointOffset.to?o.endPointOffset.to:0,console.error("endPointOffset.to is not a valid number"))),Sy(e.label)?t.label=e.label:Sy(t.label)||(t.label=void 0),Ds(t,e,"smooth",o),Ds(t,e,"shadow",o),Ds(t,e,"background",o),void 0!==e.dashes&&null!==e.dashes?t.dashes=e.dashes:!0===i&&null===e.dashes&&(t.dashes=Er(o.dashes)),void 0!==e.scaling&&null!==e.scaling?(void 0!==e.scaling.min&&(t.scaling.min=e.scaling.min),void 0!==e.scaling.max&&(t.scaling.max=e.scaling.max),Ds(t.scaling,e.scaling,"label",o.scaling)):!0===i&&null===e.scaling&&(t.scaling=Er(o.scaling)),void 0!==e.arrows&&null!==e.arrows)if("string"==typeof e.arrows){const i=e.arrows.toLowerCase();t.arrows.to.enabled=-1!=zr(i).call(i,"to"),t.arrows.middle.enabled=-1!=zr(i).call(i,"middle"),t.arrows.from.enabled=-1!=zr(i).call(i,"from")}else{if("object"!=typeof e.arrows)throw new Error("The arrow newOptions can only be an object or a string. Refer to the documentation. You used:"+_w(e.arrows));Ds(t.arrows,e.arrows,"to",o.arrows),Ds(t.arrows,e.arrows,"middle",o.arrows),Ds(t.arrows,e.arrows,"from",o.arrows)}else!0===i&&null===e.arrows&&(t.arrows=Er(o.arrows));if(void 0!==e.color&&null!==e.color){const s=ds(e.color)?{color:e.color,highlight:e.color,hover:e.color,inherit:!1,opacity:1}:e.color,r=t.color;if(n)gs(r,o.color,!1,i);else for(const t in r)Object.prototype.hasOwnProperty.call(r,t)&&delete r[t];if(ds(r))r.color=r,r.highlight=r,r.hover=r,r.inherit=!1,void 0===s.opacity&&(r.opacity=1);else{let t=!1;void 0!==s.color&&(r.color=s.color,t=!0),void 0!==s.highlight&&(r.highlight=s.highlight,t=!0),void 0!==s.hover&&(r.hover=s.hover,t=!0),void 0!==s.inherit&&(r.inherit=s.inherit),void 0!==s.opacity&&(r.opacity=Math.min(1,Math.max(0,s.opacity))),!0===t?r.inherit=!1:void 0===r.inherit&&(r.inherit="from")}}else!0===i&&null===e.color&&(t.color=Ms(o.color));!0===i&&null===e.font&&(t.font=Ms(o.font)),Object.prototype.hasOwnProperty.call(e,"selfReferenceSize")&&(console.warn("The selfReferenceSize property has been deprecated. Please use selfReference property instead. The selfReference can be set like thise selfReference:{size:30, angle:Math.PI / 4}"),t.selfReference.size=e.selfReferenceSize)}getFormattingValues(){const t=!0===this.options.arrows.to||!0===this.options.arrows.to.enabled,e=!0===this.options.arrows.from||!0===this.options.arrows.from.enabled,i=!0===this.options.arrows.middle||!0===this.options.arrows.middle.enabled,o=this.options.color.inherit,n={toArrow:t,toArrowScale:this.options.arrows.to.scaleFactor,toArrowType:this.options.arrows.to.type,toArrowSrc:this.options.arrows.to.src,toArrowImageWidth:this.options.arrows.to.imageWidth,toArrowImageHeight:this.options.arrows.to.imageHeight,middleArrow:i,middleArrowScale:this.options.arrows.middle.scaleFactor,middleArrowType:this.options.arrows.middle.type,middleArrowSrc:this.options.arrows.middle.src,middleArrowImageWidth:this.options.arrows.middle.imageWidth,middleArrowImageHeight:this.options.arrows.middle.imageHeight,fromArrow:e,fromArrowScale:this.options.arrows.from.scaleFactor,fromArrowType:this.options.arrows.from.type,fromArrowSrc:this.options.arrows.from.src,fromArrowImageWidth:this.options.arrows.from.imageWidth,fromArrowImageHeight:this.options.arrows.from.imageHeight,arrowStrikethrough:this.options.arrowStrikethrough,color:o?void 0:this.options.color.color,inheritsColor:o,opacity:this.options.color.opacity,hidden:this.options.hidden,length:this.options.length,shadow:this.options.shadow.enabled,shadowColor:this.options.shadow.color,shadowSize:this.options.shadow.size,shadowX:this.options.shadow.x,shadowY:this.options.shadow.y,dashes:this.options.dashes,width:this.options.width,background:this.options.background.enabled,backgroundColor:this.options.background.color,backgroundSize:this.options.background.size,backgroundDashes:this.options.background.dashes};if(this.selected||this.hover)if(!0===this.chooser){if(this.selected){const t=this.options.selectionWidth;"function"==typeof t?n.width=t(n.width):"number"==typeof t&&(n.width+=t),n.width=Math.max(n.width,.3/this.body.view.scale),n.color=this.options.color.highlight,n.shadow=this.options.shadow.enabled}else if(this.hover){const t=this.options.hoverWidth;"function"==typeof t?n.width=t(n.width):"number"==typeof t&&(n.width+=t),n.width=Math.max(n.width,.3/this.body.view.scale),n.color=this.options.color.hover,n.shadow=this.options.shadow.enabled}}else"function"==typeof this.chooser&&(this.chooser(n,this.options.id,this.selected,this.hover),void 0!==n.color&&(n.inheritsColor=!1),!1===n.shadow&&(n.shadowColor===this.options.shadow.color&&n.shadowSize===this.options.shadow.size&&n.shadowX===this.options.shadow.x&&n.shadowY===this.options.shadow.y||(n.shadow=!0)));else n.shadow=this.options.shadow.enabled,n.width=Math.max(n.width,.3/this.body.view.scale);return n}updateLabelModule(t){const e=[t,this.options,this.globalOptions,this.defaultOptions];this.labelModule.update(this.options,e),void 0!==this.labelModule.baseSize&&(this.baseFontSize=this.labelModule.baseSize)}updateEdgeType(){const t=this.options.smooth;let e=!1,i=!0;return void 0!==this.edgeType&&((this.edgeType instanceof Yw&&!0===t.enabled&&"dynamic"===t.type||this.edgeType instanceof Gw&&!0===t.enabled&&"cubicBezier"===t.type||this.edgeType instanceof Xw&&!0===t.enabled&&"dynamic"!==t.type&&"cubicBezier"!==t.type||this.edgeType instanceof Zw&&!1===t.type.enabled)&&(i=!1),!0===i&&(e=this.cleanup())),!0===i?!0===t.enabled?"dynamic"===t.type?(e=!0,this.edgeType=new Yw(this.options,this.body,this.labelModule)):"cubicBezier"===t.type?this.edgeType=new Gw(this.options,this.body,this.labelModule):this.edgeType=new Xw(this.options,this.body,this.labelModule):this.edgeType=new Zw(this.options,this.body,this.labelModule):this.edgeType.setOptions(this.options),e}connect(){this.disconnect(),this.from=this.body.nodes[this.fromId]||void 0,this.to=this.body.nodes[this.toId]||void 0,this.connected=void 0!==this.from&&void 0!==this.to,!0===this.connected?(this.from.attachEdge(this),this.to.attachEdge(this)):(this.from&&this.from.detachEdge(this),this.to&&this.to.detachEdge(this)),this.edgeType.connect()}disconnect(){this.from&&(this.from.detachEdge(this),this.from=void 0),this.to&&(this.to.detachEdge(this),this.to=void 0),this.connected=!1}getTitle(){return this.title}isSelected(){return this.selected}getValue(){return this.options.value}setValueRange(t,e,i){if(void 0!==this.options.value){const o=this.options.scaling.customScalingFunction(t,e,i,this.options.value),n=this.options.scaling.max-this.options.scaling.min;if(!0===this.options.scaling.label.enabled){const t=this.options.scaling.label.max-this.options.scaling.label.min;this.options.font.size=this.options.scaling.label.min+o*t}this.options.width=this.options.scaling.min+o*n}else this.options.width=this.baseWidth,this.options.font.size=this.baseFontSize;this._setInteractionWidths(),this.updateLabelModule()}_setInteractionWidths(){"function"==typeof this.options.hoverWidth?this.edgeType.hoverWidth=this.options.hoverWidth(this.options.width):this.edgeType.hoverWidth=this.options.hoverWidth+this.options.width,"function"==typeof this.options.selectionWidth?this.edgeType.selectionWidth=this.options.selectionWidth(this.options.width):this.edgeType.selectionWidth=this.options.selectionWidth+this.options.width}draw(t){const e=this.getFormattingValues();if(e.hidden)return;const i=this.edgeType.getViaNode();this.edgeType.drawLine(t,e,this.selected,this.hover,i),this.drawLabel(t,i)}drawArrows(t){const e=this.getFormattingValues();if(e.hidden)return;const i=this.edgeType.getViaNode(),o={};this.edgeType.fromPoint=this.edgeType.from,this.edgeType.toPoint=this.edgeType.to,e.fromArrow&&(o.from=this.edgeType.getArrowData(t,"from",i,this.selected,this.hover,e),!1===e.arrowStrikethrough&&(this.edgeType.fromPoint=o.from.core),e.fromArrowSrc&&(o.from.image=this.imagelist.load(e.fromArrowSrc)),e.fromArrowImageWidth&&(o.from.imageWidth=e.fromArrowImageWidth),e.fromArrowImageHeight&&(o.from.imageHeight=e.fromArrowImageHeight)),e.toArrow&&(o.to=this.edgeType.getArrowData(t,"to",i,this.selected,this.hover,e),!1===e.arrowStrikethrough&&(this.edgeType.toPoint=o.to.core),e.toArrowSrc&&(o.to.image=this.imagelist.load(e.toArrowSrc)),e.toArrowImageWidth&&(o.to.imageWidth=e.toArrowImageWidth),e.toArrowImageHeight&&(o.to.imageHeight=e.toArrowImageHeight)),e.middleArrow&&(o.middle=this.edgeType.getArrowData(t,"middle",i,this.selected,this.hover,e),e.middleArrowSrc&&(o.middle.image=this.imagelist.load(e.middleArrowSrc)),e.middleArrowImageWidth&&(o.middle.imageWidth=e.middleArrowImageWidth),e.middleArrowImageHeight&&(o.middle.imageHeight=e.middleArrowImageHeight)),e.fromArrow&&this.edgeType.drawArrowHead(t,e,this.selected,this.hover,o.from),e.middleArrow&&this.edgeType.drawArrowHead(t,e,this.selected,this.hover,o.middle),e.toArrow&&this.edgeType.drawArrowHead(t,e,this.selected,this.hover,o.to)}drawLabel(t,e){if(void 0!==this.options.label){const i=this.from,o=this.to;let n;if(this.labelModule.differentState(this.selected,this.hover)&&this.labelModule.getTextSize(t,this.selected,this.hover),i.id!=o.id){this.labelModule.pointToSelf=!1,n=this.edgeType.getPoint(.5,e),t.save();const i=this._getRotation(t);0!=i.angle&&(t.translate(i.x,i.y),t.rotate(i.angle)),this.labelModule.draw(t,n.x,n.y,this.selected,this.hover),t.restore()}else{this.labelModule.pointToSelf=!0;const e=Ty(t,this.options.selfReference.angle,this.options.selfReference.size,i);n=this._pointOnCircle(e.x,e.y,this.options.selfReference.size,this.options.selfReference.angle),this.labelModule.draw(t,n.x,n.y,this.selected,this.hover)}}}getItemsOnPoint(t){const e=[];if(this.labelModule.visible()){const i=this._getRotation();ky(this.labelModule.getSize(),t,i)&&e.push({edgeId:this.id,labelId:0})}const i={left:t.x,top:t.y};return this.isOverlappingWith(i)&&e.push({edgeId:this.id}),e}isOverlappingWith(t){if(this.connected){const e=10,i=this.from.x,o=this.from.y,n=this.to.x,s=this.to.y,r=t.left,a=t.top;return this.edgeType.getDistanceToEdge(i,o,n,s,r,a)<e}return!1}_getRotation(t){const e=this.edgeType.getViaNode(),i=this.edgeType.getPoint(.5,e);void 0!==t&&this.labelModule.calculateLabelSize(t,this.selected,this.hover,i.x,i.y);const o={x:i.x,y:this.labelModule.size.yLine,angle:0};if(!this.labelModule.visible())return o;if("horizontal"===this.options.font.align)return o;const n=this.from.y-this.to.y,s=this.from.x-this.to.x;let r=Math.atan2(n,s);return(r<-1&&s<0||r>0&&s<0)&&(r+=Math.PI),o.angle=r,o}_pointOnCircle(t,e,i,o){return{x:t+i*Math.cos(o),y:e-i*Math.sin(o)}}select(){this.selected=!0}unselect(){this.selected=!1}cleanup(){return this.edgeType.cleanup()}remove(){this.cleanup(),this.disconnect(),delete this.body.edges[this.id]}endPointsValid(){return void 0!==this.body.nodes[this.fromId]&&void 0!==this.body.nodes[this.toId]}}class $w{constructor(t,e,i){var o;this.body=t,this.images=e,this.groups=i,this.body.functions.createEdge=no(o=this.create).call(o,this),this.edgesListeners={add:(t,e)=>{this.add(e.items)},update:(t,e)=>{this.update(e.items)},remove:(t,e)=>{this.remove(e.items)}},this.options={},this.defaultOptions={arrows:{to:{enabled:!1,scaleFactor:1,type:"arrow"},middle:{enabled:!1,scaleFactor:1,type:"arrow"},from:{enabled:!1,scaleFactor:1,type:"arrow"}},endPointOffset:{from:0,to:0},arrowStrikethrough:!0,color:{color:"#848484",highlight:"#848484",hover:"#848484",inherit:"from",opacity:1},dashes:!1,font:{color:"#343434",size:14,face:"arial",background:"none",strokeWidth:2,strokeColor:"#ffffff",align:"horizontal",multi:!1,vadjust:0,bold:{mod:"bold"},boldital:{mod:"bold italic"},ital:{mod:"italic"},mono:{mod:"",size:15,face:"courier new",vadjust:2}},hidden:!1,hoverWidth:1.5,label:void 0,labelHighlightBold:!0,length:void 0,physics:!0,scaling:{min:1,max:15,label:{enabled:!0,min:14,max:30,maxVisible:30,drawThreshold:5},customScalingFunction:function(t,e,i,o){if(e===t)return.5;{const i=1/(e-t);return Math.max(0,(o-t)*i)}}},selectionWidth:1.5,selfReference:{size:20,angle:Math.PI/4,renderBehindTheNode:!0},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:10,x:5,y:5},background:{enabled:!1,color:"rgba(111,111,111,1)",size:10,dashes:!1},smooth:{enabled:!0,type:"dynamic",forceDirection:"none",roundness:.5},title:void 0,width:1,value:void 0},gs(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){var t,e,i=this;this.body.emitter.on("_forceDisableDynamicCurves",function(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];"dynamic"===t&&(t="continuous");let o=!1;for(const e in i.body.edges)if(Object.prototype.hasOwnProperty.call(i.body.edges,e)){const n=i.body.edges[e],s=i.body.data.edges.get(e);if(null!=s){const e=s.smooth;void 0!==e&&!0===e.enabled&&"dynamic"===e.type&&(void 0===t?n.setOptions({smooth:!1}):n.setOptions({smooth:{type:t}}),o=!0)}}!0===e&&!0===o&&i.body.emitter.emit("_dataChanged")}),this.body.emitter.on("_dataUpdated",()=>{this.reconnectEdges()}),this.body.emitter.on("refreshEdges",no(t=this.refresh).call(t,this)),this.body.emitter.on("refresh",no(e=this.refresh).call(e,this)),this.body.emitter.on("destroy",()=>{bs(this.edgesListeners,(t,e)=>{this.body.data.edges&&this.body.data.edges.off(e,t)}),delete this.body.functions.createEdge,delete this.edgesListeners.add,delete this.edgesListeners.update,delete this.edgesListeners.remove,delete this.edgesListeners})}setOptions(t){if(void 0!==t){Qw.parseOptions(this.options,t,!0,this.defaultOptions,!0);let e=!1;if(void 0!==t.smooth)for(const t in this.body.edges)Object.prototype.hasOwnProperty.call(this.body.edges,t)&&(e=this.body.edges[t].updateEdgeType()||e);if(void 0!==t.font)for(const t in this.body.edges)Object.prototype.hasOwnProperty.call(this.body.edges,t)&&this.body.edges[t].updateLabelModule();void 0===t.hidden&&void 0===t.physics&&!0!==e||this.body.emitter.emit("_dataChanged")}}setData(t){let i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const o=this.body.data.edges;if(e.isDataViewLike("id",t))this.body.data.edges=t;else if(Th(t))this.body.data.edges=new e.DataSet,this.body.data.edges.add(t);else{if(t)throw new TypeError("Array or DataSet expected");this.body.data.edges=new e.DataSet}if(o&&bs(this.edgesListeners,(t,e)=>{o.off(e,t)}),this.body.edges={},this.body.data.edges){bs(this.edgesListeners,(t,e)=>{this.body.data.edges.on(e,t)});const t=this.body.data.edges.getIds();this.add(t,!0)}this.body.emitter.emit("_adjustEdgesForHierarchicalLayout"),!1===i&&this.body.emitter.emit("_dataChanged")}add(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const i=this.body.edges,o=this.body.data.edges;for(let e=0;e<t.length;e++){const n=t[e],s=i[n];s&&s.disconnect();const r=o.get(n,{showInternalIds:!0});i[n]=this.create(r)}this.body.emitter.emit("_adjustEdgesForHierarchicalLayout"),!1===e&&this.body.emitter.emit("_dataChanged")}update(t){const e=this.body.edges,i=this.body.data.edges;let o=!1;for(let n=0;n<t.length;n++){const s=t[n],r=i.get(s),a=e[s];void 0!==a?(a.disconnect(),o=a.setOptions(r)||o,a.connect()):(this.body.edges[s]=this.create(r),o=!0)}!0===o?(this.body.emitter.emit("_adjustEdgesForHierarchicalLayout"),this.body.emitter.emit("_dataChanged")):this.body.emitter.emit("_dataUpdated")}remove(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(0===t.length)return;const i=this.body.edges;bs(t,t=>{const e=i[t];void 0!==e&&e.remove()}),e&&this.body.emitter.emit("_dataChanged")}refresh(){bs(this.body.edges,(t,e)=>{const i=this.body.data.edges.get(e);void 0!==i&&t.setOptions(i)})}create(t){return new Qw(t,this.body,this.images,this.options,this.defaultOptions)}reconnectEdges(){let t;const e=this.body.nodes,i=this.body.edges;for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(e[t].edges=[]);for(t in i)if(Object.prototype.hasOwnProperty.call(i,t)){const e=i[t];e.from=null,e.to=null,e.connect()}}getConnectedNodes(t){const e=[];if(void 0!==this.body.edges[t]){const i=this.body.edges[t];void 0!==i.fromId&&e.push(i.fromId),void 0!==i.toId&&e.push(i.toId)}return e}_updateState(){this._addMissingEdges(),this._removeInvalidEdges()}_removeInvalidEdges(){const t=[];bs(this.body.edges,(e,i)=>{const o=this.body.nodes[e.toId],n=this.body.nodes[e.fromId];void 0!==o&&!0===o.isCluster||void 0!==n&&!0===n.isCluster||void 0!==o&&void 0!==n||t.push(i)}),this.remove(t,!1)}_addMissingEdges(){const t=this.body.data.edges;if(null==t)return;const e=this.body.edges,i=[];Fh(t).call(t,(t,o)=>{void 0===e[o]&&i.push(o)}),this.add(i,!0)}}var Jw,t_,e_,i_,o_,n_,s_,r_={};function a_(){return e_?t_:(e_=1,function(){if(Jw)return r_;Jw=1;var t=di(),e=Date,i=C()(e.prototype.getTime);t({target:"Date",stat:!0},{now:function(){return i(new e)}})}(),t_=St().Date.now)}function h_(){return o_?i_:(o_=1,i_=a_())}var d_=o(s_?n_:(s_=1,n_=h_()));class l_{constructor(t,e,i){this.body=t,this.physicsBody=e,this.barnesHutTree,this.setOptions(i),this._rng=es("BARNES HUT SOLVER")}setOptions(t){this.options=t,this.thetaInversed=1/this.options.theta,this.overlapAvoidanceFactor=1-Math.max(0,Math.min(1,this.options.avoidOverlap))}solve(){if(0!==this.options.gravitationalConstant&&this.physicsBody.physicsNodeIndices.length>0){let t;const e=this.body.nodes,i=this.physicsBody.physicsNodeIndices,o=i.length,n=this._formBarnesHutTree(e,i);this.barnesHutTree=n;for(let s=0;s<o;s++)t=e[i[s]],t.options.mass>0&&this._getForceContributions(n.root,t)}}_getForceContributions(t,e){this._getForceContribution(t.children.NW,e),this._getForceContribution(t.children.NE,e),this._getForceContribution(t.children.SW,e),this._getForceContribution(t.children.SE,e)}_getForceContribution(t,e){if(t.childrenCount>0){const i=t.centerOfMass.x-e.x,o=t.centerOfMass.y-e.y,n=Math.sqrt(i*i+o*o);n*t.calcSize>this.thetaInversed?this._calculateForces(n,i,o,e,t):4===t.childrenCount?this._getForceContributions(t,e):t.children.data.id!=e.id&&this._calculateForces(n,i,o,e,t)}}_calculateForces(t,e,i,o,n){0===t&&(e=t=.1),this.overlapAvoidanceFactor<1&&o.shape.radius&&(t=Math.max(.1+this.overlapAvoidanceFactor*o.shape.radius,t-o.shape.radius));const s=this.options.gravitationalConstant*n.mass*o.options.mass/Math.pow(t,3),r=e*s,a=i*s;this.physicsBody.forces[o.id].x+=r,this.physicsBody.forces[o.id].y+=a}_formBarnesHutTree(t,e){let i;const o=e.length;let n=t[e[0]].x,s=t[e[0]].y,r=t[e[0]].x,a=t[e[0]].y;for(let i=1;i<o;i++){const o=t[e[i]],h=o.x,d=o.y;o.options.mass>0&&(h<n&&(n=h),h>r&&(r=h),d<s&&(s=d),d>a&&(a=d))}const h=Math.abs(r-n)-Math.abs(a-s);h>0?(s-=.5*h,a+=.5*h):(n+=.5*h,r-=.5*h);const d=Math.max(1e-5,Math.abs(r-n)),l=.5*d,c=.5*(n+r),u=.5*(s+a),p={root:{centerOfMass:{x:0,y:0},mass:0,range:{minX:c-l,maxX:c+l,minY:u-l,maxY:u+l},size:d,calcSize:1/d,children:{data:null},maxWidth:0,level:0,childrenCount:4}};this._splitBranch(p.root);for(let n=0;n<o;n++)i=t[e[n]],i.options.mass>0&&this._placeInTree(p.root,i);return p}_updateBranchMass(t,e){const i=t.centerOfMass,o=t.mass+e.options.mass,n=1/o;i.x=i.x*t.mass+e.x*e.options.mass,i.x*=n,i.y=i.y*t.mass+e.y*e.options.mass,i.y*=n,t.mass=o;const s=Math.max(Math.max(e.height,e.radius),e.width);t.maxWidth=t.maxWidth<s?s:t.maxWidth}_placeInTree(t,e,i){1==i&&void 0!==i||this._updateBranchMass(t,e);const o=t.children.NW.range;let n;n=o.maxX>e.x?o.maxY>e.y?"NW":"SW":o.maxY>e.y?"NE":"SE",this._placeInRegion(t,e,n)}_placeInRegion(t,e,i){const o=t.children[i];switch(o.childrenCount){case 0:o.children.data=e,o.childrenCount=1,this._updateBranchMass(o,e);break;case 1:o.children.data.x===e.x&&o.children.data.y===e.y?(e.x+=this._rng(),e.y+=this._rng()):(this._splitBranch(o),this._placeInTree(o,e));break;case 4:this._placeInTree(o,e)}}_splitBranch(t){let e=null;1===t.childrenCount&&(e=t.children.data,t.mass=0,t.centerOfMass.x=0,t.centerOfMass.y=0),t.childrenCount=4,t.children.data=null,this._insertRegion(t,"NW"),this._insertRegion(t,"NE"),this._insertRegion(t,"SW"),this._insertRegion(t,"SE"),null!=e&&this._placeInTree(t,e)}_insertRegion(t,e){let i,o,n,s;const r=.5*t.size;switch(e){case"NW":i=t.range.minX,o=t.range.minX+r,n=t.range.minY,s=t.range.minY+r;break;case"NE":i=t.range.minX+r,o=t.range.maxX,n=t.range.minY,s=t.range.minY+r;break;case"SW":i=t.range.minX,o=t.range.minX+r,n=t.range.minY+r,s=t.range.maxY;break;case"SE":i=t.range.minX+r,o=t.range.maxX,n=t.range.minY+r,s=t.range.maxY}t.children[e]={centerOfMass:{x:0,y:0},mass:0,range:{minX:i,maxX:o,minY:n,maxY:s},size:.5*t.size,calcSize:2*t.calcSize,children:{data:null},maxWidth:0,level:t.level+1,childrenCount:0}}_debug(t,e){void 0!==this.barnesHutTree&&(t.lineWidth=1,this._drawBranch(this.barnesHutTree.root,t,e))}_drawBranch(t,e,i){void 0===i&&(i="#FF0000"),4===t.childrenCount&&(this._drawBranch(t.children.NW,e),this._drawBranch(t.children.NE,e),this._drawBranch(t.children.SE,e),this._drawBranch(t.children.SW,e)),e.strokeStyle=i,e.beginPath(),e.moveTo(t.range.minX,t.range.minY),e.lineTo(t.range.maxX,t.range.minY),e.stroke(),e.beginPath(),e.moveTo(t.range.maxX,t.range.minY),e.lineTo(t.range.maxX,t.range.maxY),e.stroke(),e.beginPath(),e.moveTo(t.range.maxX,t.range.maxY),e.lineTo(t.range.minX,t.range.maxY),e.stroke(),e.beginPath(),e.moveTo(t.range.minX,t.range.maxY),e.lineTo(t.range.minX,t.range.minY),e.stroke()}}class c_{constructor(t,e,i){this._rng=es("REPULSION SOLVER"),this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t}solve(){let t,e,i,o,n,s,r,a;const h=this.body.nodes,d=this.physicsBody.physicsNodeIndices,l=this.physicsBody.forces,c=this.options.nodeDistance,u=-2/3/c,p=4/3;for(let f=0;f<d.length-1;f++){r=h[d[f]];for(let g=f+1;g<d.length;g++)a=h[d[g]],t=a.x-r.x,e=a.y-r.y,i=Math.sqrt(t*t+e*e),0===i&&(i=.1*this._rng(),t=i),i<2*c&&(s=i<.5*c?1:u*i+p,s/=i,o=t*s,n=e*s,l[r.id].x-=o,l[r.id].y-=n,l[a.id].x+=o,l[a.id].y+=n)}}}class u_{constructor(t,e,i){this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t,this.overlapAvoidanceFactor=Math.max(0,Math.min(1,this.options.avoidOverlap||0))}solve(){const t=this.body.nodes,e=this.physicsBody.physicsNodeIndices,i=this.physicsBody.forces,o=this.options.nodeDistance;for(let n=0;n<e.length-1;n++){const s=t[e[n]];for(let r=n+1;r<e.length;r++){const n=t[e[r]];if(s.level===n.level){const t=o+this.overlapAvoidanceFactor*((s.shape.radius||0)/2+(n.shape.radius||0)/2),e=n.x-s.x,r=n.y-s.y,a=Math.sqrt(e*e+r*r),h=.05;let d;d=a<t?-Math.pow(h*a,2)+Math.pow(h*t,2):0,0!==a&&(d/=a);const l=e*d,c=r*d;i[s.id].x-=l,i[s.id].y-=c,i[n.id].x+=l,i[n.id].y+=c}}}}}class p_{constructor(t,e,i){this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t}solve(){let t,e;const i=this.physicsBody.physicsEdgeIndices,o=this.body.edges;let n,s,r;for(let a=0;a<i.length;a++)e=o[i[a]],!0===e.connected&&e.toId!==e.fromId&&void 0!==this.body.nodes[e.toId]&&void 0!==this.body.nodes[e.fromId]&&(void 0!==e.edgeType.via?(t=void 0===e.options.length?this.options.springLength:e.options.length,n=e.to,s=e.edgeType.via,r=e.from,this._calculateSpringForce(n,s,.5*t),this._calculateSpringForce(s,r,.5*t)):(t=void 0===e.options.length?1.5*this.options.springLength:e.options.length,this._calculateSpringForce(e.from,e.to,t)))}_calculateSpringForce(t,e,i){const o=t.x-e.x,n=t.y-e.y,s=Math.max(Math.sqrt(o*o+n*n),.01),r=this.options.springConstant*(i-s)/s,a=o*r,h=n*r;void 0!==this.physicsBody.forces[t.id]&&(this.physicsBody.forces[t.id].x+=a,this.physicsBody.forces[t.id].y+=h),void 0!==this.physicsBody.forces[e.id]&&(this.physicsBody.forces[e.id].x-=a,this.physicsBody.forces[e.id].y-=h)}}class f_{constructor(t,e,i){this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t}solve(){let t,e,i,o,n,s,r,a;const h=this.body.edges,d=.5,l=this.physicsBody.physicsEdgeIndices,c=this.physicsBody.physicsNodeIndices,u=this.physicsBody.forces;for(let t=0;t<c.length;t++){const e=c[t];u[e].springFx=0,u[e].springFy=0}for(let c=0;c<l.length;c++)e=h[l[c]],!0===e.connected&&(t=void 0===e.options.length?this.options.springLength:e.options.length,i=e.from.x-e.to.x,o=e.from.y-e.to.y,a=Math.sqrt(i*i+o*o),a=0===a?.01:a,r=this.options.springConstant*(t-a)/a,n=i*r,s=o*r,e.to.level!=e.from.level?(void 0!==u[e.toId]&&(u[e.toId].springFx-=n,u[e.toId].springFy-=s),void 0!==u[e.fromId]&&(u[e.fromId].springFx+=n,u[e.fromId].springFy+=s)):(void 0!==u[e.toId]&&(u[e.toId].x-=d*n,u[e.toId].y-=d*s),void 0!==u[e.fromId]&&(u[e.fromId].x+=d*n,u[e.fromId].y+=d*s)));let p,f;r=1;for(let t=0;t<c.length;t++){const e=c[t];p=Math.min(r,Math.max(-r,u[e].springFx)),f=Math.min(r,Math.max(-r,u[e].springFy)),u[e].x+=p,u[e].y+=f}let g=0,v=0;for(let t=0;t<c.length;t++){const e=c[t];g+=u[e].x,v+=u[e].y}const m=g/c.length,y=v/c.length;for(let t=0;t<c.length;t++){const e=c[t];u[e].x-=m,u[e].y-=y}}}class g_{constructor(t,e,i){this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t}solve(){let t,e,i,o;const n=this.body.nodes,s=this.physicsBody.physicsNodeIndices,r=this.physicsBody.forces;for(let a=0;a<s.length;a++){o=n[s[a]],t=-o.x,e=-o.y,i=Math.sqrt(t*t+e*e),this._calculateForces(i,t,e,r,o)}}_calculateForces(t,e,i,o,n){const s=0===t?0:this.options.centralGravity/t;o[n.id].x=e*s,o[n.id].y=i*s}}class v_ extends l_{constructor(t,e,i){super(t,e,i),this._rng=es("FORCE ATLAS 2 BASED REPULSION SOLVER")}_calculateForces(t,e,i,o,n){0===t&&(e=t=.1*this._rng()),this.overlapAvoidanceFactor<1&&o.shape.radius&&(t=Math.max(.1+this.overlapAvoidanceFactor*o.shape.radius,t-o.shape.radius));const s=o.edges.length+1,r=this.options.gravitationalConstant*n.mass*o.options.mass*s/Math.pow(t,2),a=e*r,h=i*r;this.physicsBody.forces[o.id].x+=a,this.physicsBody.forces[o.id].y+=h}}class m_ extends g_{constructor(t,e,i){super(t,e,i)}_calculateForces(t,e,i,o,n){if(t>0){const t=n.edges.length+1,s=this.options.centralGravity*t*n.options.mass;o[n.id].x=e*s,o[n.id].y=i*s}}}class y_{constructor(t){this.body=t,this.physicsBody={physicsNodeIndices:[],physicsEdgeIndices:[],forces:{},velocities:{}},this.physicsEnabled=!0,this.simulationInterval=1e3/60,this.requiresTimeout=!0,this.previousStates={},this.referenceState={},this.freezeCache={},this.renderTimer=void 0,this.adaptiveTimestep=!1,this.adaptiveTimestepEnabled=!1,this.adaptiveCounter=0,this.adaptiveInterval=3,this.stabilized=!1,this.startedStabilization=!1,this.stabilizationIterations=0,this.ready=!1,this.options={},this.defaultOptions={enabled:!0,barnesHut:{theta:.5,gravitationalConstant:-2e3,centralGravity:.3,springLength:95,springConstant:.04,damping:.09,avoidOverlap:0},forceAtlas2Based:{theta:.5,gravitationalConstant:-50,centralGravity:.01,springConstant:.08,springLength:100,damping:.4,avoidOverlap:0},repulsion:{centralGravity:.2,springLength:200,springConstant:.05,nodeDistance:100,damping:.09,avoidOverlap:0},hierarchicalRepulsion:{centralGravity:0,springLength:100,springConstant:.01,nodeDistance:120,damping:.09},maxVelocity:50,minVelocity:.75,solver:"barnesHut",stabilization:{enabled:!0,iterations:1e3,updateInterval:50,onlyDynamicEdges:!1,fit:!0},timestep:.5,adaptiveTimestep:!0,wind:{x:0,y:0}},Zi(this.options,this.defaultOptions),this.timestep=.5,this.layoutFailed=!1,this.bindEventListeners()}bindEventListeners(){this.body.emitter.on("initPhysics",()=>{this.initPhysics()}),this.body.emitter.on("_layoutFailed",()=>{this.layoutFailed=!0}),this.body.emitter.on("resetPhysics",()=>{this.stopSimulation(),this.ready=!1}),this.body.emitter.on("disablePhysics",()=>{this.physicsEnabled=!1,this.stopSimulation()}),this.body.emitter.on("restorePhysics",()=>{this.setOptions(this.options),!0===this.ready&&this.startSimulation()}),this.body.emitter.on("startSimulation",()=>{!0===this.ready&&this.startSimulation()}),this.body.emitter.on("stopSimulation",()=>{this.stopSimulation()}),this.body.emitter.on("destroy",()=>{this.stopSimulation(!1),this.body.emitter.off()}),this.body.emitter.on("_dataChanged",()=>{this.updatePhysicsData()})}setOptions(t){if(void 0!==t)if(!1===t)this.options.enabled=!1,this.physicsEnabled=!1,this.stopSimulation();else if(!0===t)this.options.enabled=!0,this.physicsEnabled=!0,this.startSimulation();else{this.physicsEnabled=!0,fs(["stabilization"],this.options,t),Ds(this.options,t,"stabilization"),void 0===t.enabled&&(this.options.enabled=!0),!1===this.options.enabled&&(this.physicsEnabled=!1,this.stopSimulation());const e=this.options.wind;e&&(("number"!=typeof e.x||Hu(e.x))&&(e.x=0),("number"!=typeof e.y||Hu(e.y))&&(e.y=0)),this.timestep=this.options.timestep}this.init()}init(){let t;"forceAtlas2Based"===this.options.solver?(t=this.options.forceAtlas2Based,this.nodesSolver=new v_(this.body,this.physicsBody,t),this.edgesSolver=new p_(this.body,this.physicsBody,t),this.gravitySolver=new m_(this.body,this.physicsBody,t)):"repulsion"===this.options.solver?(t=this.options.repulsion,this.nodesSolver=new c_(this.body,this.physicsBody,t),this.edgesSolver=new p_(this.body,this.physicsBody,t),this.gravitySolver=new g_(this.body,this.physicsBody,t)):"hierarchicalRepulsion"===this.options.solver?(t=this.options.hierarchicalRepulsion,this.nodesSolver=new u_(this.body,this.physicsBody,t),this.edgesSolver=new f_(this.body,this.physicsBody,t),this.gravitySolver=new g_(this.body,this.physicsBody,t)):(t=this.options.barnesHut,this.nodesSolver=new l_(this.body,this.physicsBody,t),this.edgesSolver=new p_(this.body,this.physicsBody,t),this.gravitySolver=new g_(this.body,this.physicsBody,t)),this.modelOptions=t}initPhysics(){!0===this.physicsEnabled&&!0===this.options.enabled?!0===this.options.stabilization.enabled?this.stabilize():(this.stabilized=!1,this.ready=!0,this.body.emitter.emit("fit",{},this.layoutFailed),this.startSimulation()):(this.ready=!0,this.body.emitter.emit("fit"))}startSimulation(){var t;!0===this.physicsEnabled&&!0===this.options.enabled?(this.stabilized=!1,this.adaptiveTimestep=!1,this.body.emitter.emit("_resizeNodes"),void 0===this.viewFunction&&(this.viewFunction=no(t=this.simulationStep).call(t,this),this.body.emitter.on("initRedraw",this.viewFunction),this.body.emitter.emit("_startRendering"))):this.body.emitter.emit("_redraw")}stopSimulation(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.stabilized=!0,!0===t&&this._emitStabilized(),void 0!==this.viewFunction&&(this.body.emitter.off("initRedraw",this.viewFunction),this.viewFunction=void 0,!0===t&&this.body.emitter.emit("_stopRendering"))}simulationStep(){const t=d_();this.physicsTick();(d_()-t<.4*this.simulationInterval||!0===this.runDoubleSpeed)&&!1===this.stabilized&&(this.physicsTick(),this.runDoubleSpeed=!0),!0===this.stabilized&&this.stopSimulation()}_emitStabilized(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.stabilizationIterations;(this.stabilizationIterations>1||!0===this.startedStabilization)&&Kp(()=>{this.body.emitter.emit("stabilized",{iterations:t}),this.startedStabilization=!1,this.stabilizationIterations=0},0)}physicsStep(){this.gravitySolver.solve(),this.nodesSolver.solve(),this.edgesSolver.solve(),this.moveNodes()}adjustTimeStep(){!0===this._evaluateStepQuality()?this.timestep=1.2*this.timestep:this.timestep/1.2<this.options.timestep?this.timestep=this.options.timestep:(this.adaptiveCounter=-1,this.timestep=Math.max(this.options.timestep,this.timestep/1.2))}physicsTick(){if(this._startStabilizing(),!0!==this.stabilized){if(!0===this.adaptiveTimestep&&!0===this.adaptiveTimestepEnabled){this.adaptiveCounter%this.adaptiveInterval===0?(this.timestep=2*this.timestep,this.physicsStep(),this.revert(),this.timestep=.5*this.timestep,this.physicsStep(),this.physicsStep(),this.adjustTimeStep()):this.physicsStep(),this.adaptiveCounter+=1}else this.timestep=this.options.timestep,this.physicsStep();!0===this.stabilized&&this.revert(),this.stabilizationIterations++}}updatePhysicsData(){this.physicsBody.forces={},this.physicsBody.physicsNodeIndices=[],this.physicsBody.physicsEdgeIndices=[];const t=this.body.nodes,e=this.body.edges;for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&!0===t[e].options.physics&&this.physicsBody.physicsNodeIndices.push(t[e].id);for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&!0===e[t].options.physics&&this.physicsBody.physicsEdgeIndices.push(e[t].id);for(let t=0;t<this.physicsBody.physicsNodeIndices.length;t++){const e=this.physicsBody.physicsNodeIndices[t];this.physicsBody.forces[e]={x:0,y:0},void 0===this.physicsBody.velocities[e]&&(this.physicsBody.velocities[e]={x:0,y:0})}for(const e in this.physicsBody.velocities)void 0===t[e]&&delete this.physicsBody.velocities[e]}revert(){const t=cp(this.previousStates),e=this.body.nodes,i=this.physicsBody.velocities;this.referenceState={};for(let o=0;o<t.length;o++){const n=t[o];void 0!==e[n]?!0===e[n].options.physics&&(this.referenceState[n]={positions:{x:e[n].x,y:e[n].y}},i[n].x=this.previousStates[n].vx,i[n].y=this.previousStates[n].vy,e[n].x=this.previousStates[n].x,e[n].y=this.previousStates[n].y):delete this.previousStates[n]}}_evaluateStepQuality(){let t,e,i;const o=this.body.nodes,n=this.referenceState;for(const s in this.referenceState)if(Object.prototype.hasOwnProperty.call(this.referenceState,s)&&void 0!==o[s]&&(t=o[s].x-n[s].positions.x,e=o[s].y-n[s].positions.y,i=Math.sqrt(Math.pow(t,2)+Math.pow(e,2)),i>.3))return!1;return!0}moveNodes(){const t=this.physicsBody.physicsNodeIndices;let e=0,i=0;for(let o=0;o<t.length;o++){const n=t[o],s=this._performStep(n);e=Math.max(e,s),i+=s}this.adaptiveTimestepEnabled=i/t.length<5,this.stabilized=e<this.options.minVelocity}calculateComponentVelocity(t,e,i){t+=(e-this.modelOptions.damping*t)/i*this.timestep;const o=this.options.maxVelocity||1e9;return Math.abs(t)>o&&(t=t>0?o:-o),t}_performStep(t){const e=this.body.nodes[t],i=this.physicsBody.forces[t];this.options.wind&&(i.x+=this.options.wind.x,i.y+=this.options.wind.y);const o=this.physicsBody.velocities[t];this.previousStates[t]={x:e.x,y:e.y,vx:o.x,vy:o.y},!1===e.options.fixed.x?(o.x=this.calculateComponentVelocity(o.x,i.x,e.options.mass),e.x+=o.x*this.timestep):(i.x=0,o.x=0),!1===e.options.fixed.y?(o.y=this.calculateComponentVelocity(o.y,i.y,e.options.mass),e.y+=o.y*this.timestep):(i.y=0,o.y=0);return Math.sqrt(Math.pow(o.x,2)+Math.pow(o.y,2))}_freezeNodes(){const t=this.body.nodes;for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)&&t[e].x&&t[e].y){const i=t[e].options.fixed;this.freezeCache[e]={x:i.x,y:i.y},i.x=!0,i.y=!0}}_restoreFrozenNodes(){const t=this.body.nodes;for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&void 0!==this.freezeCache[e]&&(t[e].options.fixed.x=this.freezeCache[e].x,t[e].options.fixed.y=this.freezeCache[e].y);this.freezeCache={}}stabilize(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.stabilization.iterations;"number"!=typeof t&&(t=this.options.stabilization.iterations,console.error("The stabilize method needs a numeric amount of iterations. Switching to default: ",t)),0!==this.physicsBody.physicsNodeIndices.length?(this.adaptiveTimestep=this.options.adaptiveTimestep,this.body.emitter.emit("_resizeNodes"),this.stopSimulation(),this.stabilized=!1,this.body.emitter.emit("_blockRedraw"),this.targetIterations=t,!0===this.options.stabilization.onlyDynamicEdges&&this._freezeNodes(),this.stabilizationIterations=0,Kp(()=>this._stabilizationBatch(),0)):this.ready=!0}_startStabilizing(){return!0!==this.startedStabilization&&(this.body.emitter.emit("startStabilizing"),this.startedStabilization=!0,!0)}_stabilizationBatch(){const t=()=>!1===this.stabilized&&this.stabilizationIterations<this.targetIterations,e=()=>{this.body.emitter.emit("stabilizationProgress",{iterations:this.stabilizationIterations,total:this.targetIterations})};this._startStabilizing()&&e();let i=0;for(;t()&&i<this.options.stabilization.updateInterval;)this.physicsTick(),i++;var o;(e(),t())?Kp(no(o=this._stabilizationBatch).call(o,this),0):this._finalizeStabilization()}_finalizeStabilization(){this.body.emitter.emit("_allowRedraw"),!0===this.options.stabilization.fit&&this.body.emitter.emit("fit"),!0===this.options.stabilization.onlyDynamicEdges&&this._restoreFrozenNodes(),this.body.emitter.emit("stabilizationIterationsDone"),this.body.emitter.emit("_requestRedraw"),!0===this.stabilized?this._emitStabilized():this.startSimulation(),this.ready=!0}_drawForces(t){for(let e=0;e<this.physicsBody.physicsNodeIndices.length;e++){const i=this.physicsBody.physicsNodeIndices[e],o=this.body.nodes[i],n=this.physicsBody.forces[i],s=20,r=.03,a=Math.sqrt(Math.pow(n.x,2)+Math.pow(n.x,2)),h=Math.min(Math.max(5,a),15),d=3*h,l=ks((180-180*Math.min(1,Math.max(0,r*a)))/360,1,1),c={x:o.x+s*n.x,y:o.y+s*n.y};t.lineWidth=h,t.strokeStyle=l,t.beginPath(),t.moveTo(o.x,o.y),t.lineTo(c.x,c.y),t.stroke();const u=Math.atan2(n.y,n.x);t.fillStyle=l,jw.draw(t,{type:"arrow",point:c,angle:u,length:d}),Eb(t).call(t)}}}var b_,w_,__,x_,E_,O_,C_,k_,S_,T_={};function M_(){return __?w_:(__=1,function(){if(b_)return T_;b_=1;var t=di(),e=C(),i=Ya(),o=e([].reverse),n=[1,2];t({target:"Array",proto:!0,forced:String(n)===String(n.reverse())},{reverse:function(){return i(this)&&(this.length=this.length),o(this)}})}(),w_=to()("Array","reverse"))}function D_(){if(E_)return x_;E_=1;var t=Mt(),e=M_(),i=Array.prototype;return x_=function(o){var n=o.reverse;return o===i||t(i,o)&&n===i.reverse?e:n},x_}function I_(){return C_?O_:(C_=1,O_=D_())}var P_=o(S_?k_:(S_=1,k_=I_()));const B_=[];for(let t=0;t<256;++t)B_.push((t+256).toString(16).slice(1));let z_;const F_=new Uint8Array(16);var N_,A_,R_,j_,L_={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function H_(t,e,i){if(L_.randomUUID&&!t)return L_.randomUUID();const o=(t=t||{}).random??t.rng?.()??function(){if(!z_){if("undefined"==typeof crypto||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");z_=crypto.getRandomValues.bind(crypto)}return z_(F_)}();if(o.length<16)throw new Error("Random bytes length must be >= 16");return o[6]=15&o[6]|64,o[8]=63&o[8]|128,function(t,e=0){return(B_[t[e+0]]+B_[t[e+1]]+B_[t[e+2]]+B_[t[e+3]]+"-"+B_[t[e+4]]+B_[t[e+5]]+"-"+B_[t[e+6]]+B_[t[e+7]]+"-"+B_[t[e+8]]+B_[t[e+9]]+"-"+B_[t[e+10]]+B_[t[e+11]]+B_[t[e+12]]+B_[t[e+13]]+B_[t[e+14]]+B_[t[e+15]]).toLowerCase()}(o)}class W_{constructor(){}static getRange(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o=1e9,n=-1e9,s=1e9,r=-1e9;if(i.length>0)for(let a=0;a<i.length;a++)e=t[i[a]],s>e.shape.boundingBox.left&&(s=e.shape.boundingBox.left),r<e.shape.boundingBox.right&&(r=e.shape.boundingBox.right),o>e.shape.boundingBox.top&&(o=e.shape.boundingBox.top),n<e.shape.boundingBox.bottom&&(n=e.shape.boundingBox.bottom);return 1e9===s&&-1e9===r&&1e9===o&&-1e9===n&&(o=0,n=0,s=0,r=0),{minX:s,maxX:r,minY:o,maxY:n}}static getRangeCore(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o=1e9,n=-1e9,s=1e9,r=-1e9;if(i.length>0)for(let a=0;a<i.length;a++)e=t[i[a]],s>e.x&&(s=e.x),r<e.x&&(r=e.x),o>e.y&&(o=e.y),n<e.y&&(n=e.y);return 1e9===s&&-1e9===r&&1e9===o&&-1e9===n&&(o=0,n=0,s=0,r=0),{minX:s,maxX:r,minY:o,maxY:n}}static findCenter(t){return{x:.5*(t.maxX+t.minX),y:.5*(t.maxY+t.minY)}}static cloneOptions(t,e){const i={};return void 0===e||"node"===e?(gs(i,t.options,!0),i.x=t.x,i.y=t.y,i.amountOfConnections=t.edges.length):gs(i,t.options,!0),i}}class V_ extends Xb{constructor(t,e,i,o,n,s){super(t,e,i,o,n,s),this.isCluster=!0,this.containedNodes={},this.containedEdges={}}_openChildCluster(t){const e=this.body.nodes[t];if(void 0===this.containedNodes[t])throw new Error("node with id: "+t+" not in current cluster");if(!e.isCluster)throw new Error("node with id: "+t+" is not a cluster");delete this.containedNodes[t],bs(e.edges,t=>{delete this.containedEdges[t.id]}),bs(e.containedNodes,(t,e)=>{this.containedNodes[e]=t}),e.containedNodes={},bs(e.containedEdges,(t,e)=>{this.containedEdges[e]=t}),e.containedEdges={},bs(e.edges,t=>{bs(this.edges,e=>{var i,o;const n=zr(i=e.clusteringEdgeReplacingIds).call(i,t.id);-1!==n&&(bs(t.clusteringEdgeReplacingIds,t=>{e.clusteringEdgeReplacingIds.push(t),this.body.edges[t].edgeReplacedById=e.id}),uh(o=e.clusteringEdgeReplacingIds).call(o,n,1))})}),e.edges=[]}}class q_{constructor(t){this.body=t,this.clusteredNodes={},this.clusteredEdges={},this.options={},this.defaultOptions={},Zi(this.options,this.defaultOptions),this.body.emitter.on("_resetData",()=>{this.clusteredNodes={},this.clusteredEdges={}})}clusterByHubsize(t,e){void 0===t?t=this._getHubSize():"object"==typeof t&&(e=this._checkOptions(t),t=this._getHubSize());const i=[];for(let e=0;e<this.body.nodeIndices.length;e++){const o=this.body.nodes[this.body.nodeIndices[e]];o.edges.length>=t&&i.push(o.id)}for(let t=0;t<i.length;t++)this.clusterByConnection(i[t],e,!0);this.body.emitter.emit("_dataChanged")}cluster(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(void 0===t.joinCondition)throw new Error("Cannot call clusterByNodeData without a joinCondition function in the options.");t=this._checkOptions(t);const i={},o={};bs(this.body.nodes,(e,n)=>{e.options&&!0===t.joinCondition(e.options)&&(i[n]=e,bs(e.edges,t=>{void 0===this.clusteredEdges[t.id]&&(o[t.id]=t)}))}),this._cluster(i,o,t,e)}clusterByEdgeCount(t,e){let i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];e=this._checkOptions(e);const o=[],n={};let s,r,a;for(let i=0;i<this.body.nodeIndices.length;i++){const h={},d={},l=this.body.nodeIndices[i],c=this.body.nodes[l];if(void 0===n[l]){a=0,r=[];for(let t=0;t<c.edges.length;t++)s=c.edges[t],void 0===this.clusteredEdges[s.id]&&(s.toId!==s.fromId&&a++,r.push(s));if(a===t){const t=function(t){if(void 0===e.joinCondition||null===e.joinCondition)return!0;const i=W_.cloneOptions(t);return e.joinCondition(i)};let i=!0;for(let e=0;e<r.length;e++){s=r[e];const o=this._getConnectedId(s,l);if(!t(c)){i=!1;break}d[s.id]=s,h[l]=c,h[o]=this.body.nodes[o],n[l]=!0}if(cp(h).length>0&&cp(d).length>0&&!0===i){const t=function(){for(let t=0;t<o.length;++t)for(const e in h)if(void 0!==o[t].nodes[e])return o[t]}();if(void 0!==t){for(const e in h)void 0===t.nodes[e]&&(t.nodes[e]=h[e]);for(const e in d)void 0===t.edges[e]&&(t.edges[e]=d[e])}else o.push({nodes:h,edges:d})}}}}for(let t=0;t<o.length;t++)this._cluster(o[t].nodes,o[t].edges,e,!1);!0===i&&this.body.emitter.emit("_dataChanged")}clusterOutliers(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.clusterByEdgeCount(1,t,e)}clusterBridges(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.clusterByEdgeCount(2,t,e)}clusterByConnection(t,e){var i;let o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(void 0===t)throw new Error("No nodeId supplied to clusterByConnection!");if(void 0===this.body.nodes[t])throw new Error("The nodeId given to clusterByConnection does not exist!");const n=this.body.nodes[t];void 0===(e=this._checkOptions(e,n)).clusterNodeProperties.x&&(e.clusterNodeProperties.x=n.x),void 0===e.clusterNodeProperties.y&&(e.clusterNodeProperties.y=n.y),void 0===e.clusterNodeProperties.fixed&&(e.clusterNodeProperties.fixed={},e.clusterNodeProperties.fixed.x=n.options.fixed.x,e.clusterNodeProperties.fixed.y=n.options.fixed.y);const s={},r={},a=n.id,h=W_.cloneOptions(n);s[a]=n;for(let t=0;t<n.edges.length;t++){const i=n.edges[t];if(void 0===this.clusteredEdges[i.id]){const t=this._getConnectedId(i,a);if(void 0===this.clusteredNodes[t])if(t!==a)if(void 0===e.joinCondition)r[i.id]=i,s[t]=this.body.nodes[t];else{const o=W_.cloneOptions(this.body.nodes[t]);!0===e.joinCondition(h,o)&&(r[i.id]=i,s[t]=this.body.nodes[t])}else r[i.id]=i}}const d=Cd(i=cp(s)).call(i,function(t){return s[t].id});for(const t in s){if(!Object.prototype.hasOwnProperty.call(s,t))continue;const e=s[t];for(let t=0;t<e.edges.length;t++){const i=e.edges[t];zr(d).call(d,this._getConnectedId(i,e.id))>-1&&(r[i.id]=i)}}this._cluster(s,r,e,o)}_createClusterEdges(t,e,i,o){let n,s,r,a,h,d;const l=cp(t),c=[];for(let o=0;o<l.length;o++){s=l[o],r=t[s];for(let o=0;o<r.edges.length;o++)n=r.edges[o],void 0===this.clusteredEdges[n.id]&&(n.toId==n.fromId?e[n.id]=n:n.toId==s?(a=i.id,h=n.fromId,d=h):(a=n.toId,h=i.id,d=a),void 0===t[d]&&c.push({edge:n,fromId:h,toId:a}))}const u=[],p=function(t){for(let e=0;e<u.length;e++){const i=u[e],o=t.fromId===i.fromId&&t.toId===i.toId,n=t.fromId===i.toId&&t.toId===i.fromId;if(o||n)return i}return null};for(let t=0;t<c.length;t++){const e=c[t],i=e.edge;let n=p(e);null===n?(n=this._createClusteredEdge(e.fromId,e.toId,i,o),u.push(n)):n.clusteringEdgeReplacingIds.push(i.id),this.body.edges[i.id].edgeReplacedById=n.id,this._backupEdgeOptions(i),i.setOptions({physics:!1})}}_checkOptions(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return void 0===t.clusterEdgeProperties&&(t.clusterEdgeProperties={}),void 0===t.clusterNodeProperties&&(t.clusterNodeProperties={}),t}_cluster(t,e,i){let o=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];const n=[];for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&void 0!==this.clusteredNodes[e]&&n.push(e);for(let e=0;e<n.length;++e)delete t[n[e]];if(0==cp(t).length)return;if(1==cp(t).length&&1!=i.clusterNodeProperties.allowSingleNodeCluster)return;let s=gs({},i.clusterNodeProperties);if(void 0!==i.processProperties){const o=[];for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)){const i=W_.cloneOptions(t[e]);o.push(i)}const n=[];for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&"clusterEdge:"!==t.substr(0,12)){const i=W_.cloneOptions(e[t],"edge");n.push(i)}if(s=i.processProperties(s,o,n),!s)throw new Error("The processProperties function does not return properties!")}void 0===s.id&&(s.id="cluster:"+H_());const r=s.id;let a;void 0===s.label&&(s.label="cluster"),void 0===s.x&&(a=this._getClusterPosition(t),s.x=a.x),void 0===s.y&&(void 0===a&&(a=this._getClusterPosition(t)),s.y=a.y),s.id=r;const h=this.body.functions.createNode(s,V_);h.containedNodes=t,h.containedEdges=e,h.clusterEdgeProperties=i.clusterEdgeProperties,this.body.nodes[s.id]=h,this._clusterEdges(t,e,s,i.clusterEdgeProperties),s.id=void 0,!0===o&&this.body.emitter.emit("_dataChanged")}_backupEdgeOptions(t){void 0===this.clusteredEdges[t.id]&&(this.clusteredEdges[t.id]={physics:t.options.physics})}_restoreEdge(t){const e=this.clusteredEdges[t.id];void 0!==e&&(t.setOptions({physics:e.physics}),delete this.clusteredEdges[t.id])}isCluster(t){return void 0!==this.body.nodes[t]?!0===this.body.nodes[t].isCluster:(console.error("Node does not exist."),!1)}_getClusterPosition(t){const e=cp(t);let i,o=t[e[0]].x,n=t[e[0]].x,s=t[e[0]].y,r=t[e[0]].y;for(let a=1;a<e.length;a++)i=t[e[a]],o=i.x<o?i.x:o,n=i.x>n?i.x:n,s=i.y<s?i.y:s,r=i.y>r?i.y:r;return{x:.5*(o+n),y:.5*(s+r)}}openCluster(t,e){let i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(void 0===t)throw new Error("No clusterNodeId supplied to openCluster.");const o=this.body.nodes[t];if(void 0===o)throw new Error("The clusterNodeId supplied to openCluster does not exist.");if(!0!==o.isCluster||void 0===o.containedNodes||void 0===o.containedEdges)throw new Error("The node:"+t+" is not a valid cluster.");const n=this.findNode(t),s=zr(n).call(n,t)-1;if(s>=0){const e=n[s];return this.body.nodes[e]._openChildCluster(t),delete this.body.nodes[t],void(!0===i&&this.body.emitter.emit("_dataChanged"))}const r=o.containedNodes,a=o.containedEdges;if(void 0!==e&&void 0!==e.releaseFunction&&"function"==typeof e.releaseFunction){const t={},i={x:o.x,y:o.y};for(const e in r)if(Object.prototype.hasOwnProperty.call(r,e)){const i=this.body.nodes[e];t[e]={x:i.x,y:i.y}}const n=e.releaseFunction(i,t);for(const t in r)if(Object.prototype.hasOwnProperty.call(r,t)){const e=this.body.nodes[t];void 0!==n[t]&&(e.x=void 0===n[t].x?o.x:n[t].x,e.y=void 0===n[t].y?o.y:n[t].y)}}else bs(r,function(t){!1===t.options.fixed.x&&(t.x=o.x),!1===t.options.fixed.y&&(t.y=o.y)});for(const t in r)if(Object.prototype.hasOwnProperty.call(r,t)){const e=this.body.nodes[t];e.vx=o.vx,e.vy=o.vy,e.setOptions({physics:!0}),delete this.clusteredNodes[t]}const h=[];for(let t=0;t<o.edges.length;t++)h.push(o.edges[t]);for(let e=0;e<h.length;e++){const i=h[e],o=this._getConnectedId(i,t),n=this.clusteredNodes[o];for(let t=0;t<i.clusteringEdgeReplacingIds.length;t++){const e=i.clusteringEdgeReplacingIds[t],s=this.body.edges[e];if(void 0!==s)if(void 0!==n){const t=this.body.nodes[n.clusterId];t.containedEdges[s.id]=s,delete a[s.id];let e=s.fromId,i=s.toId;s.toId==o?i=n.clusterId:e=n.clusterId,this._createClusteredEdge(e,i,s,t.clusterEdgeProperties,{hidden:!1,physics:!0})}else this._restoreEdge(s)}i.remove()}for(const t in a)Object.prototype.hasOwnProperty.call(a,t)&&this._restoreEdge(a[t]);delete this.body.nodes[t],!0===i&&this.body.emitter.emit("_dataChanged")}getNodesInCluster(t){const e=[];if(!0===this.isCluster(t)){const i=this.body.nodes[t].containedNodes;for(const t in i)Object.prototype.hasOwnProperty.call(i,t)&&e.push(this.body.nodes[t].id)}return e}findNode(t){const e=[];let i,o=0;for(;void 0!==this.clusteredNodes[t]&&o<100;){if(i=this.body.nodes[t],void 0===i)return[];e.push(i.id),t=this.clusteredNodes[t].clusterId,o++}return i=this.body.nodes[t],void 0===i?[]:(e.push(i.id),P_(e).call(e),e)}updateClusteredNode(t,e){if(void 0===t)throw new Error("No clusteredNodeId supplied to updateClusteredNode.");if(void 0===e)throw new Error("No newOptions supplied to updateClusteredNode.");if(void 0===this.body.nodes[t])throw new Error("The clusteredNodeId supplied to updateClusteredNode does not exist.");this.body.nodes[t].setOptions(e),this.body.emitter.emit("_dataChanged")}updateEdge(t,e){if(void 0===t)throw new Error("No startEdgeId supplied to updateEdge.");if(void 0===e)throw new Error("No newOptions supplied to updateEdge.");if(void 0===this.body.edges[t])throw new Error("The startEdgeId supplied to updateEdge does not exist.");const i=this.getClusteredEdges(t);for(let t=0;t<i.length;t++){this.body.edges[i[t]].setOptions(e)}this.body.emitter.emit("_dataChanged")}getClusteredEdges(t){const e=[];let i=0;for(;void 0!==t&&void 0!==this.body.edges[t]&&i<100;)e.push(this.body.edges[t].id),t=this.body.edges[t].edgeReplacedById,i++;return P_(e).call(e),e}getBaseEdge(t){return this.getBaseEdges(t)[0]}getBaseEdges(t){const e=[t],i=[],o=[];let n=0;for(;e.length>0&&n<100;){const t=e.pop();if(void 0===t)continue;const s=this.body.edges[t];if(void 0===s)continue;n++;const r=s.clusteringEdgeReplacingIds;if(void 0===r)o.push(t);else for(let t=0;t<r.length;++t){const o=r[t];-1===zr(e).call(e,r)&&-1===zr(i).call(i,r)&&e.push(o)}i.push(t)}return o}_getConnectedId(t,e){return t.toId!=e?t.toId:(t.fromId,t.fromId)}_getHubSize(){let t=0,e=0,i=0,o=0;for(let n=0;n<this.body.nodeIndices.length;n++){const s=this.body.nodes[this.body.nodeIndices[n]];s.edges.length>o&&(o=s.edges.length),t+=s.edges.length,e+=Math.pow(s.edges.length,2),i+=1}t/=i,e/=i;const n=e-Math.pow(t,2),s=Math.sqrt(n);let r=Math.floor(t+2*s);return r>o&&(r=o),r}_createClusteredEdge(t,e,i,o,n){const s=W_.cloneOptions(i,"edge");gs(s,o),s.from=t,s.to=e,s.id="clusterEdge:"+H_(),void 0!==n&&gs(s,n);const r=this.body.functions.createEdge(s);return r.clusteringEdgeReplacingIds=[i.id],r.connect(),this.body.edges[r.id]=r,r}_clusterEdges(t,e,i,o){if(e instanceof Qw){const t=e,i={};i[t.id]=t,e=i}if(t instanceof Xb){const e=t,i={};i[e.id]=e,t=i}if(null==i)throw new Error("_clusterEdges: parameter clusterNode required");void 0===o&&(o=i.clusterEdgeProperties),this._createClusterEdges(t,e,i,o);for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&void 0!==this.body.edges[t]){const e=this.body.edges[t];this._backupEdgeOptions(e),e.setOptions({physics:!1})}for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(this.clusteredNodes[e]={clusterId:i.id,node:this.body.nodes[e]},this.body.nodes[e].setOptions({physics:!1}))}_getClusterNodeForNode(t){if(void 0===t)return;const e=this.clusteredNodes[t];if(void 0===e)return;const i=e.clusterId;return void 0!==i?this.body.nodes[i]:void 0}_filter(t,e){const i=[];return bs(t,t=>{e(t)&&i.push(t)}),i}_updateState(){let t;const e=[],i={},o=t=>{bs(this.body.nodes,e=>{!0===e.isCluster&&t(e)})};for(t in this.clusteredNodes){if(!Object.prototype.hasOwnProperty.call(this.clusteredNodes,t))continue;void 0===this.body.nodes[t]&&e.push(t)}o(function(t){for(let i=0;i<e.length;i++)delete t.containedNodes[e[i]]});for(let t=0;t<e.length;t++)delete this.clusteredNodes[e[t]];bs(this.clusteredEdges,t=>{const e=this.body.edges[t];void 0!==e&&e.endPointsValid()||(i[t]=t)}),o(function(t){bs(t.containedEdges,(t,e)=>{t.endPointsValid()||i[e]||(i[e]=e)})}),bs(this.body.edges,(t,e)=>{let o=!0;const n=t.clusteringEdgeReplacingIds;if(void 0!==n){let t=0;bs(n,e=>{const i=this.body.edges[e];void 0!==i&&i.endPointsValid()&&(t+=1)}),o=t>0}t.endPointsValid()&&o||(i[e]=e)}),o(t=>{bs(i,e=>{delete t.containedEdges[e],bs(t.edges,(o,n)=>{o.id!==e?o.clusteringEdgeReplacingIds=this._filter(o.clusteringEdgeReplacingIds,function(t){return!i[t]}):t.edges[n]=null}),t.edges=this._filter(t.edges,function(t){return null!==t})})}),bs(i,t=>{delete this.clusteredEdges[t]}),bs(i,t=>{delete this.body.edges[t]});bs(cp(this.body.edges),t=>{const e=this.body.edges[t],i=this._isClusteredNode(e.fromId)||this._isClusteredNode(e.toId);if(i!==this._isClusteredEdge(e.id))if(i){const t=this._getClusterNodeForNode(e.fromId);void 0!==t&&this._clusterEdges(this.body.nodes[e.fromId],e,t);const i=this._getClusterNodeForNode(e.toId);void 0!==i&&this._clusterEdges(this.body.nodes[e.toId],e,i)}else delete this._clusterEdges[t],this._restoreEdge(e)});let n=!1,s=!0;for(;s;){const t=[];o(function(e){const i=cp(e.containedNodes).length,o=!0===e.options.allowSingleNodeCluster;(o&&i<1||!o&&i<2)&&t.push(e.id)});for(let e=0;e<t.length;++e)this.openCluster(t[e],{},!1);s=t.length>0,n=n||s}n&&this._updateState()}_isClusteredNode(t){return void 0!==this.clusteredNodes[t]}_isClusteredEdge(t){return void 0!==this.clusteredEdges[t]}}class U_{constructor(t,e){this.body=t,this.canvas=e,this.redrawRequested=!1,this.requestAnimationFrameRequestId=void 0,this.renderingActive=!1,this.renderRequests=0,this.allowRedraw=!0,this.dragging=!1,this.zooming=!1,this.options={},this.defaultOptions={hideEdgesOnDrag:!1,hideEdgesOnZoom:!1,hideNodesOnDrag:!1},Zi(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){var t;this.body.emitter.on("dragStart",()=>{this.dragging=!0}),this.body.emitter.on("dragEnd",()=>{this.dragging=!1}),this.body.emitter.on("zoom",()=>{this.zooming=!0,window.clearTimeout(this.zoomTimeoutId),this.zoomTimeoutId=Kp(()=>{var t;this.zooming=!1,no(t=this._requestRedraw).call(t,this)()},250)}),this.body.emitter.on("_resizeNodes",()=>{this._resizeNodes()}),this.body.emitter.on("_redraw",()=>{!1===this.renderingActive&&this._redraw()}),this.body.emitter.on("_blockRedraw",()=>{this.allowRedraw=!1}),this.body.emitter.on("_allowRedraw",()=>{this.allowRedraw=!0,this.redrawRequested=!1}),this.body.emitter.on("_requestRedraw",no(t=this._requestRedraw).call(t,this)),this.body.emitter.on("_startRendering",()=>{this.renderRequests+=1,this.renderingActive=!0,this._startRendering()}),this.body.emitter.on("_stopRendering",()=>{this.renderRequests-=1,this.renderingActive=this.renderRequests>0,this.requestAnimationFrameRequestId=void 0}),this.body.emitter.on("destroy",()=>{this.renderRequests=0,this.allowRedraw=!1,this.renderingActive=!1,window.cancelAnimationFrame(this.requestAnimationFrameRequestId),this.body.emitter.off()})}setOptions(t){if(void 0!==t){ps(["hideEdgesOnDrag","hideEdgesOnZoom","hideNodesOnDrag"],this.options,t)}}_startRendering(){var t;!0===this.renderingActive&&(void 0===this.requestAnimationFrameRequestId&&(this.requestAnimationFrameRequestId=window.requestAnimationFrame(no(t=this._renderStep).call(t,this),this.simulationInterval)))}_renderStep(){!0===this.renderingActive&&(this.requestAnimationFrameRequestId=void 0,this._startRendering(),this._redraw())}redraw(){this.body.emitter.emit("setSize"),this._redraw()}_requestRedraw(){!0!==this.redrawRequested&&!1===this.renderingActive&&!0===this.allowRedraw&&(this.redrawRequested=!0,window.requestAnimationFrame(()=>{this._redraw(!1)}))}_redraw(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!0===this.allowRedraw){this.body.emitter.emit("initRedraw"),this.redrawRequested=!1;const e={drawExternalLabels:null};0!==this.canvas.frame.canvas.width&&0!==this.canvas.frame.canvas.height||this.canvas.setSize(),this.canvas.setTransform();const i=this.canvas.getContext(),o=this.canvas.frame.canvas.clientWidth,n=this.canvas.frame.canvas.clientHeight;if(i.clearRect(0,0,o,n),0===this.canvas.frame.clientWidth)return;if(i.save(),i.translate(this.body.view.translation.x,this.body.view.translation.y),i.scale(this.body.view.scale,this.body.view.scale),i.beginPath(),this.body.emitter.emit("beforeDrawing",i),i.closePath(),!1===t&&(!1===this.dragging||!0===this.dragging&&!1===this.options.hideEdgesOnDrag)&&(!1===this.zooming||!0===this.zooming&&!1===this.options.hideEdgesOnZoom)&&this._drawEdges(i),!1===this.dragging||!0===this.dragging&&!1===this.options.hideNodesOnDrag){const{drawExternalLabels:o}=this._drawNodes(i,t);e.drawExternalLabels=o}!1===t&&(!1===this.dragging||!0===this.dragging&&!1===this.options.hideEdgesOnDrag)&&(!1===this.zooming||!0===this.zooming&&!1===this.options.hideEdgesOnZoom)&&this._drawArrows(i),null!=e.drawExternalLabels&&e.drawExternalLabels(),!1===t&&this._drawSelectionBox(i),i.beginPath(),this.body.emitter.emit("afterDrawing",i),i.closePath(),i.restore(),!0===t&&i.clearRect(0,0,o,n)}}_resizeNodes(){this.canvas.setTransform();const t=this.canvas.getContext();t.save(),t.translate(this.body.view.translation.x,this.body.view.translation.y),t.scale(this.body.view.scale,this.body.view.scale);const e=this.body.nodes;let i;for(const o in e)Object.prototype.hasOwnProperty.call(e,o)&&(i=e[o],i.resize(t),i.updateBoundingBox(t,i.selected));t.restore()}_drawNodes(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const i=this.body.nodes,o=this.body.nodeIndices;let n;const s=[],r=[],a=this.canvas.DOMtoCanvas({x:-20,y:-20}),h=this.canvas.DOMtoCanvas({x:this.canvas.frame.canvas.clientWidth+20,y:this.canvas.frame.canvas.clientHeight+20}),d={top:a.y,left:a.x,bottom:h.y,right:h.x},l=[];for(let a=0;a<o.length;a++)if(n=i[o[a]],n.hover)r.push(o[a]);else if(n.isSelected())s.push(o[a]);else if(!0===e){const e=n.draw(t);null!=e.drawExternalLabel&&l.push(e.drawExternalLabel)}else if(!0===n.isBoundingBoxOverlappingWith(d)){const e=n.draw(t);null!=e.drawExternalLabel&&l.push(e.drawExternalLabel)}else n.updateBoundingBox(t,n.selected);let c;const u=s.length,p=r.length;for(c=0;c<u;c++){n=i[s[c]];const e=n.draw(t);null!=e.drawExternalLabel&&l.push(e.drawExternalLabel)}for(c=0;c<p;c++){n=i[r[c]];const e=n.draw(t);null!=e.drawExternalLabel&&l.push(e.drawExternalLabel)}return{drawExternalLabels:()=>{for(const t of l)t()}}}_drawEdges(t){const e=this.body.edges,i=this.body.edgeIndices;for(let o=0;o<i.length;o++){const n=e[i[o]];!0===n.connected&&n.draw(t)}}_drawArrows(t){const e=this.body.edges,i=this.body.edgeIndices;for(let o=0;o<i.length;o++){const n=e[i[o]];!0===n.connected&&n.drawArrows(t)}}_drawSelectionBox(t){if(this.body.selectionBox.show){t.beginPath();const e=this.body.selectionBox.position.end.x-this.body.selectionBox.position.start.x,i=this.body.selectionBox.position.end.y-this.body.selectionBox.position.start.y;t.rect(this.body.selectionBox.position.start.x,this.body.selectionBox.position.start.y,e,i),t.fillStyle="rgba(151, 194, 252, 0.2)",t.fillRect(this.body.selectionBox.position.start.x,this.body.selectionBox.position.start.y,e,i),t.strokeStyle="rgba(151, 194, 252, 1)",t.stroke()}else t.closePath()}}function Y_(){return A_?N_:(A_=1,Np(),N_=St().setInterval)}var X_=o(j_?R_:(j_=1,R_=Y_()));function K_(t,e){e.inputHandler=function(t){t.isFirst&&e(t)},t.on("hammer.input",e.inputHandler)}function G_(t,e){return e.inputHandler=function(t){t.isFinal&&e(t)},t.on("hammer.input",e.inputHandler)}class Z_{constructor(t){this.body=t,this.pixelRatio=1,this.cameraState={},this.initialized=!1,this.canvasViewCenter={},this._cleanupCallbacks=[],this.options={},this.defaultOptions={autoResize:!0,height:"100%",width:"100%"},Zi(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){var t;this.body.emitter.once("resize",t=>{0!==t.width&&(this.body.view.translation.x=.5*t.width),0!==t.height&&(this.body.view.translation.y=.5*t.height)}),this.body.emitter.on("setSize",no(t=this.setSize).call(t,this)),this.body.emitter.on("destroy",()=>{this.hammerFrame.destroy(),this.hammer.destroy(),this._cleanUp()})}setOptions(t){if(void 0!==t){ps(["width","height","autoResize"],this.options,t)}if(this._cleanUp(),!0===this.options.autoResize){var e;if(window.ResizeObserver){const t=new ResizeObserver(()=>{!0===this.setSize()&&this.body.emitter.emit("_requestRedraw")}),{frame:e}=this;t.observe(e),this._cleanupCallbacks.push(()=>{t.unobserve(e)})}else{const t=X_(()=>{!0===this.setSize()&&this.body.emitter.emit("_requestRedraw")},1e3);this._cleanupCallbacks.push(()=>{clearInterval(t)})}const t=no(e=this._onResize).call(e,this);window.addEventListener("resize",t),this._cleanupCallbacks.push(()=>{window.removeEventListener("resize",t)})}}_cleanUp(){var t,e,i;Fh(t=P_(e=uh(i=this._cleanupCallbacks).call(i,0)).call(e)).call(t,t=>{try{t()}catch(t){console.error(t)}})}_onResize(){this.setSize(),this.body.emitter.emit("_redraw")}_getCameraState(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.pixelRatio;!0===this.initialized&&(this.cameraState.previousWidth=this.frame.canvas.width/t,this.cameraState.previousHeight=this.frame.canvas.height/t,this.cameraState.scale=this.body.view.scale,this.cameraState.position=this.DOMtoCanvas({x:.5*this.frame.canvas.width/t,y:.5*this.frame.canvas.height/t}))}_setCameraState(){if(void 0!==this.cameraState.scale&&0!==this.frame.canvas.clientWidth&&0!==this.frame.canvas.clientHeight&&0!==this.pixelRatio&&this.cameraState.previousWidth>0&&this.cameraState.previousHeight>0){const t=this.frame.canvas.width/this.pixelRatio/this.cameraState.previousWidth,e=this.frame.canvas.height/this.pixelRatio/this.cameraState.previousHeight;let i=this.cameraState.scale;1!=t&&1!=e?i=.5*this.cameraState.scale*(t+e):1!=t?i=this.cameraState.scale*t:1!=e&&(i=this.cameraState.scale*e),this.body.view.scale=i;const o=this.DOMtoCanvas({x:.5*this.frame.canvas.clientWidth,y:.5*this.frame.canvas.clientHeight}),n={x:o.x-this.cameraState.position.x,y:o.y-this.cameraState.position.y};this.body.view.translation.x+=n.x*this.body.view.scale,this.body.view.translation.y+=n.y*this.body.view.scale}}_prepareValue(t){if("number"==typeof t)return t+"px";if("string"==typeof t){if(-1!==zr(t).call(t,"%")||-1!==zr(t).call(t,"px"))return t;if(-1===zr(t).call(t,"%"))return t+"px"}throw new Error("Could not use the value supplied for width or height:"+t)}_create(){for(;this.body.container.hasChildNodes();)this.body.container.removeChild(this.body.container.firstChild);if(this.frame=document.createElement("div"),this.frame.className="vis-network",this.frame.style.position="relative",this.frame.style.overflow="hidden",this.frame.tabIndex=0,this.frame.canvas=document.createElement("canvas"),this.frame.canvas.style.position="relative",this.frame.appendChild(this.frame.canvas),this.frame.canvas.getContext)this._setPixelRatio(),this.setTransform();else{const t=document.createElement("DIV");t.style.color="red",t.style.fontWeight="bold",t.style.padding="10px",t.innerText="Error: your browser does not support HTML canvas",this.frame.canvas.appendChild(t)}this.body.container.appendChild(this.frame),this.body.view.scale=1,this.body.view.translation={x:.5*this.frame.canvas.clientWidth,y:.5*this.frame.canvas.clientHeight},this._bindHammer()}_bindHammer(){void 0!==this.hammer&&this.hammer.destroy(),this.drag={},this.pinch={},this.hammer=new Hs(this.frame.canvas),this.hammer.get("pinch").set({enable:!0}),this.hammer.get("pan").set({threshold:5,direction:Hs.DIRECTION_ALL}),K_(this.hammer,t=>{this.body.eventListeners.onTouch(t)}),this.hammer.on("tap",t=>{this.body.eventListeners.onTap(t)}),this.hammer.on("doubletap",t=>{this.body.eventListeners.onDoubleTap(t)}),this.hammer.on("press",t=>{this.body.eventListeners.onHold(t)}),this.hammer.on("panstart",t=>{this.body.eventListeners.onDragStart(t)}),this.hammer.on("panmove",t=>{this.body.eventListeners.onDrag(t)}),this.hammer.on("panend",t=>{this.body.eventListeners.onDragEnd(t)}),this.hammer.on("pinch",t=>{this.body.eventListeners.onPinch(t)}),this.frame.canvas.addEventListener("wheel",t=>{this.body.eventListeners.onMouseWheel(t)}),this.frame.canvas.addEventListener("mousemove",t=>{this.body.eventListeners.onMouseMove(t)}),this.frame.canvas.addEventListener("contextmenu",t=>{this.body.eventListeners.onContext(t)}),this.hammerFrame=new Hs(this.frame),G_(this.hammerFrame,t=>{this.body.eventListeners.onRelease(t)})}setSize(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.width,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.options.height;t=this._prepareValue(t),e=this._prepareValue(e);let i=!1;const o=this.frame.canvas.width,n=this.frame.canvas.height,s=this.pixelRatio;if(this._setPixelRatio(),t!=this.options.width||e!=this.options.height||this.frame.style.width!=t||this.frame.style.height!=e)this._getCameraState(s),this.frame.style.width=t,this.frame.style.height=e,this.frame.canvas.style.width="100%",this.frame.canvas.style.height="100%",this.frame.canvas.width=Math.round(this.frame.canvas.clientWidth*this.pixelRatio),this.frame.canvas.height=Math.round(this.frame.canvas.clientHeight*this.pixelRatio),this.options.width=t,this.options.height=e,this.canvasViewCenter={x:.5*this.frame.clientWidth,y:.5*this.frame.clientHeight},i=!0;else{const t=Math.round(this.frame.canvas.clientWidth*this.pixelRatio),e=Math.round(this.frame.canvas.clientHeight*this.pixelRatio);this.frame.canvas.width===t&&this.frame.canvas.height===e||this._getCameraState(s),this.frame.canvas.width!==t&&(this.frame.canvas.width=t,i=!0),this.frame.canvas.height!==e&&(this.frame.canvas.height=e,i=!0)}return!0===i&&(this.body.emitter.emit("resize",{width:Math.round(this.frame.canvas.width/this.pixelRatio),height:Math.round(this.frame.canvas.height/this.pixelRatio),oldWidth:Math.round(o/this.pixelRatio),oldHeight:Math.round(n/this.pixelRatio)}),this._setCameraState()),this.initialized=!0,i}getContext(){return this.frame.canvas.getContext("2d")}_determinePixelRatio(){const t=this.getContext();if(void 0===t)throw new Error("Could not get canvax context");let e=1;"undefined"!=typeof window&&(e=window.devicePixelRatio||1);return e/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1)}_setPixelRatio(){this.pixelRatio=this._determinePixelRatio()}setTransform(){const t=this.getContext();if(void 0===t)throw new Error("Could not get canvax context");t.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0)}_XconvertDOMtoCanvas(t){return(t-this.body.view.translation.x)/this.body.view.scale}_XconvertCanvasToDOM(t){return t*this.body.view.scale+this.body.view.translation.x}_YconvertDOMtoCanvas(t){return(t-this.body.view.translation.y)/this.body.view.scale}_YconvertCanvasToDOM(t){return t*this.body.view.scale+this.body.view.translation.y}canvasToDOM(t){return{x:this._XconvertCanvasToDOM(t.x),y:this._YconvertCanvasToDOM(t.y)}}DOMtoCanvas(t){return{x:this._XconvertDOMtoCanvas(t.x),y:this._YconvertDOMtoCanvas(t.y)}}}class Q_{constructor(t,e){var i,o;this.body=t,this.canvas=e,this.animationSpeed=1/this.renderRefreshRate,this.animationEasingFunction="easeInOutQuint",this.easingTime=0,this.sourceScale=0,this.targetScale=0,this.sourceTranslation=0,this.targetTranslation=0,this.lockedOnNodeId=void 0,this.lockedOnNodeOffset=void 0,this.touchTime=0,this.viewFunction=void 0,this.body.emitter.on("fit",no(i=this.fit).call(i,this)),this.body.emitter.on("animationFinished",()=>{this.body.emitter.emit("_stopRendering")}),this.body.emitter.on("unlockNode",no(o=this.releaseNode).call(o,this))}setOptions(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.options=t}fit(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t=function(t,e){const i=Zi({nodes:e,minZoomLevel:Number.MIN_VALUE,maxZoomLevel:1},null!=t?t:{});if(!Th(i.nodes))throw new TypeError("Nodes has to be an array of ids.");if(0===i.nodes.length&&(i.nodes=e),!("number"==typeof i.minZoomLevel&&i.minZoomLevel>0))throw new TypeError("Min zoom level has to be a number higher than zero.");if(!("number"==typeof i.maxZoomLevel&&i.minZoomLevel<=i.maxZoomLevel))throw new TypeError("Max zoom level has to be a number higher than min zoom level.");return i}(t,this.body.nodeIndices);const i=this.canvas.frame.canvas.clientWidth,o=this.canvas.frame.canvas.clientHeight;let n,s;if(0===i||0===o)s=1,n=W_.getRange(this.body.nodes,t.nodes);else if(!0===e){let e=0;for(const t in this.body.nodes)if(Object.prototype.hasOwnProperty.call(this.body.nodes,t)){!0===this.body.nodes[t].predefinedPosition&&(e+=1)}if(e>.5*this.body.nodeIndices.length)return void this.fit(t,!1);n=W_.getRange(this.body.nodes,t.nodes);s=12.662/(this.body.nodeIndices.length+7.4147)+.0964822;s*=Math.min(i/600,o/600)}else{this.body.emitter.emit("_resizeNodes"),n=W_.getRange(this.body.nodes,t.nodes);const e=i/(1.1*Math.abs(n.maxX-n.minX)),r=o/(1.1*Math.abs(n.maxY-n.minY));s=e<=r?e:r}s>t.maxZoomLevel?s=t.maxZoomLevel:s<t.minZoomLevel&&(s=t.minZoomLevel);const r={position:W_.findCenter(n),scale:s,animation:t.animation};this.moveTo(r)}focus(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==this.body.nodes[t]){const i={x:this.body.nodes[t].x,y:this.body.nodes[t].y};e.position=i,e.lockedOnNode=t,this.moveTo(e)}else console.error("Node: "+t+" cannot be found.")}moveTo(t){if(void 0!==t){if(null!=t.offset){if(null!=t.offset.x){if(t.offset.x=+t.offset.x,!$u(t.offset.x))throw new TypeError('The option "offset.x" has to be a finite number.')}else t.offset.x=0;if(null!=t.offset.y){if(t.offset.y=+t.offset.y,!$u(t.offset.y))throw new TypeError('The option "offset.y" has to be a finite number.')}else t.offset.x=0}else t.offset={x:0,y:0};if(null!=t.position){if(null!=t.position.x){if(t.position.x=+t.position.x,!$u(t.position.x))throw new TypeError('The option "position.x" has to be a finite number.')}else t.position.x=0;if(null!=t.position.y){if(t.position.y=+t.position.y,!$u(t.position.y))throw new TypeError('The option "position.y" has to be a finite number.')}else t.position.x=0}else t.position=this.getViewPosition();if(null!=t.scale){if(t.scale=+t.scale,!(t.scale>0))throw new TypeError('The option "scale" has to be a number greater than zero.')}else t.scale=this.body.view.scale;void 0===t.animation&&(t.animation={duration:0}),!1===t.animation&&(t.animation={duration:0}),!0===t.animation&&(t.animation={}),void 0===t.animation.duration&&(t.animation.duration=1e3),void 0===t.animation.easingFunction&&(t.animation.easingFunction="easeInOutQuad"),this.animateView(t)}else t={}}animateView(t){if(void 0===t)return;this.animationEasingFunction=t.animation.easingFunction,this.releaseNode(),!0===t.locked&&(this.lockedOnNodeId=t.lockedOnNode,this.lockedOnNodeOffset=t.offset),0!=this.easingTime&&this._transitionRedraw(!0),this.sourceScale=this.body.view.scale,this.sourceTranslation=this.body.view.translation,this.targetScale=t.scale,this.body.view.scale=this.targetScale;const e=this.canvas.DOMtoCanvas({x:.5*this.canvas.frame.canvas.clientWidth,y:.5*this.canvas.frame.canvas.clientHeight}),i=e.x-t.position.x,o=e.y-t.position.y;var n,s;(this.targetTranslation={x:this.sourceTranslation.x+i*this.targetScale+t.offset.x,y:this.sourceTranslation.y+o*this.targetScale+t.offset.y},0===t.animation.duration)?null!=this.lockedOnNodeId?(this.viewFunction=no(n=this._lockedRedraw).call(n,this),this.body.emitter.on("initRedraw",this.viewFunction)):(this.body.view.scale=this.targetScale,this.body.view.translation=this.targetTranslation,this.body.emitter.emit("_requestRedraw")):(this.animationSpeed=1/(60*t.animation.duration*.001)||1/60,this.animationEasingFunction=t.animation.easingFunction,this.viewFunction=no(s=this._transitionRedraw).call(s,this),this.body.emitter.on("initRedraw",this.viewFunction),this.body.emitter.emit("_startRendering"))}_lockedRedraw(){const t=this.body.nodes[this.lockedOnNodeId].x,e=this.body.nodes[this.lockedOnNodeId].y,i=this.canvas.DOMtoCanvas({x:.5*this.canvas.frame.canvas.clientWidth,y:.5*this.canvas.frame.canvas.clientHeight}),o=i.x-t,n=i.y-e,s=this.body.view.translation,r={x:s.x+o*this.body.view.scale+this.lockedOnNodeOffset.x,y:s.y+n*this.body.view.scale+this.lockedOnNodeOffset.y};this.body.view.translation=r}releaseNode(){void 0!==this.lockedOnNodeId&&void 0!==this.viewFunction&&(this.body.emitter.off("initRedraw",this.viewFunction),this.lockedOnNodeId=void 0,this.lockedOnNodeOffset=void 0)}_transitionRedraw(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.easingTime+=this.animationSpeed,this.easingTime=!0===t?1:this.easingTime;const e=Is[this.animationEasingFunction](this.easingTime);if(this.body.view.scale=this.sourceScale+(this.targetScale-this.sourceScale)*e,this.body.view.translation={x:this.sourceTranslation.x+(this.targetTranslation.x-this.sourceTranslation.x)*e,y:this.sourceTranslation.y+(this.targetTranslation.y-this.sourceTranslation.y)*e},this.easingTime>=1){var i;if(this.body.emitter.off("initRedraw",this.viewFunction),this.easingTime=0,null!=this.lockedOnNodeId)this.viewFunction=no(i=this._lockedRedraw).call(i,this),this.body.emitter.on("initRedraw",this.viewFunction);this.body.emitter.emit("animationFinished")}}getScale(){return this.body.view.scale}getViewPosition(){return this.canvas.DOMtoCanvas({x:.5*this.canvas.frame.canvas.clientWidth,y:.5*this.canvas.frame.canvas.clientHeight})}}function $_(t){var e,i=t&&t.preventDefault||!1,o=t&&t.container||window,n={},s={keydown:{},keyup:{}},r={};for(e=97;e<=122;e++)r[String.fromCharCode(e)]={code:e-97+65,shift:!1};for(e=65;e<=90;e++)r[String.fromCharCode(e)]={code:e,shift:!0};for(e=0;e<=9;e++)r[""+e]={code:48+e,shift:!1};for(e=1;e<=12;e++)r["F"+e]={code:111+e,shift:!1};for(e=0;e<=9;e++)r["num"+e]={code:96+e,shift:!1};r["num*"]={code:106,shift:!1},r["num+"]={code:107,shift:!1},r["num-"]={code:109,shift:!1},r["num/"]={code:111,shift:!1},r["num."]={code:110,shift:!1},r.left={code:37,shift:!1},r.up={code:38,shift:!1},r.right={code:39,shift:!1},r.down={code:40,shift:!1},r.space={code:32,shift:!1},r.enter={code:13,shift:!1},r.shift={code:16,shift:void 0},r.esc={code:27,shift:!1},r.backspace={code:8,shift:!1},r.tab={code:9,shift:!1},r.ctrl={code:17,shift:!1},r.alt={code:18,shift:!1},r.delete={code:46,shift:!1},r.pageup={code:33,shift:!1},r.pagedown={code:34,shift:!1},r["="]={code:187,shift:!1},r["-"]={code:189,shift:!1},r["]"]={code:221,shift:!1},r["["]={code:219,shift:!1};var a=function(t){d(t,"keydown")},h=function(t){d(t,"keyup")},d=function(t,e){if(void 0!==s[e][t.keyCode]){for(var o=s[e][t.keyCode],n=0;n<o.length;n++)(void 0===o[n].shift||1==o[n].shift&&1==t.shiftKey||0==o[n].shift&&0==t.shiftKey)&&o[n].fn(t);1==i&&t.preventDefault()}};return n.bind=function(t,e,i){if(void 0===i&&(i="keydown"),void 0===r[t])throw new Error("unsupported key: "+t);void 0===s[i][r[t].code]&&(s[i][r[t].code]=[]),s[i][r[t].code].push({fn:e,shift:r[t].shift})},n.bindAll=function(t,e){for(var i in void 0===e&&(e="keydown"),r)r.hasOwnProperty(i)&&n.bind(i,t,e)},n.getKey=function(t){for(var e in r)if(r.hasOwnProperty(e)){if(1==t.shiftKey&&1==r[e].shift&&t.keyCode==r[e].code)return e;if(0==t.shiftKey&&0==r[e].shift&&t.keyCode==r[e].code)return e;if(t.keyCode==r[e].code&&"shift"==e)return e}return"unknown key, currently not supported"},n.unbind=function(t,e,i){if(void 0===i&&(i="keydown"),void 0===r[t])throw new Error("unsupported key: "+t);if(void 0!==e){var o=[],n=s[i][r[t].code];if(void 0!==n)for(var a=0;a<n.length;a++)n[a].fn==e&&n[a].shift==r[t].shift||o.push(s[i][r[t].code][a]);s[i][r[t].code]=o}else s[i][r[t].code]=[]},n.reset=function(){s={keydown:{},keyup:{}}},n.destroy=function(){s={keydown:{},keyup:{}},o.removeEventListener("keydown",a,!0),o.removeEventListener("keyup",h,!0)},o.addEventListener("keydown",a,!0),o.addEventListener("keyup",h,!0),n}class J_{constructor(t,e){this.body=t,this.canvas=e,this.iconsCreated=!1,this.navigationHammers=[],this.boundFunctions={},this.touchTime=0,this.activated=!1,this.body.emitter.on("activate",()=>{this.activated=!0,this.configureKeyboardBindings()}),this.body.emitter.on("deactivate",()=>{this.activated=!1,this.configureKeyboardBindings()}),this.body.emitter.on("destroy",()=>{void 0!==this.keycharm&&this.keycharm.destroy()}),this.options={}}setOptions(t){void 0!==t&&(this.options=t,this.create())}create(){!0===this.options.navigationButtons?!1===this.iconsCreated&&this.loadNavigationElements():!0===this.iconsCreated&&this.cleanNavigation(),this.configureKeyboardBindings()}cleanNavigation(){if(0!=this.navigationHammers.length){for(let t=0;t<this.navigationHammers.length;t++)this.navigationHammers[t].destroy();this.navigationHammers=[]}this.navigationDOM&&this.navigationDOM.wrapper&&this.navigationDOM.wrapper.parentNode&&this.navigationDOM.wrapper.parentNode.removeChild(this.navigationDOM.wrapper),this.iconsCreated=!1}loadNavigationElements(){this.cleanNavigation(),this.navigationDOM={};const t=["up","down","left","right","zoomIn","zoomOut","zoomExtends"],e=["_moveUp","_moveDown","_moveLeft","_moveRight","_zoomIn","_zoomOut","_fit"];this.navigationDOM.wrapper=document.createElement("div"),this.navigationDOM.wrapper.className="vis-navigation",this.canvas.frame.appendChild(this.navigationDOM.wrapper);for(let n=0;n<t.length;n++){this.navigationDOM[t[n]]=document.createElement("div"),this.navigationDOM[t[n]].className="vis-button vis-"+t[n],this.navigationDOM.wrapper.appendChild(this.navigationDOM[t[n]]);const s=new Hs(this.navigationDOM[t[n]]);var i,o;if("_fit"===e[n])K_(s,no(i=this._fit).call(i,this));else K_(s,no(o=this.bindToRedraw).call(o,this,e[n]));this.navigationHammers.push(s)}const n=new Hs(this.canvas.frame);G_(n,()=>{this._stopMovement()}),this.navigationHammers.push(n),this.iconsCreated=!0}bindToRedraw(t){var e;void 0===this.boundFunctions[t]&&(this.boundFunctions[t]=no(e=this[t]).call(e,this),this.body.emitter.on("initRedraw",this.boundFunctions[t]),this.body.emitter.emit("_startRendering"))}unbindFromRedraw(t){void 0!==this.boundFunctions[t]&&(this.body.emitter.off("initRedraw",this.boundFunctions[t]),this.body.emitter.emit("_stopRendering"),delete this.boundFunctions[t])}_fit(){(new Date).valueOf()-this.touchTime>700&&(this.body.emitter.emit("fit",{duration:700}),this.touchTime=(new Date).valueOf())}_stopMovement(){for(const t in this.boundFunctions)Object.prototype.hasOwnProperty.call(this.boundFunctions,t)&&(this.body.emitter.off("initRedraw",this.boundFunctions[t]),this.body.emitter.emit("_stopRendering"));this.boundFunctions={}}_moveUp(){this.body.view.translation.y+=this.options.keyboard.speed.y}_moveDown(){this.body.view.translation.y-=this.options.keyboard.speed.y}_moveLeft(){this.body.view.translation.x+=this.options.keyboard.speed.x}_moveRight(){this.body.view.translation.x-=this.options.keyboard.speed.x}_zoomIn(){const t=this.body.view.scale,e=this.body.view.scale*(1+this.options.keyboard.speed.zoom),i=this.body.view.translation,o=e/t,n=(1-o)*this.canvas.canvasViewCenter.x+i.x*o,s=(1-o)*this.canvas.canvasViewCenter.y+i.y*o;this.body.view.scale=e,this.body.view.translation={x:n,y:s},this.body.emitter.emit("zoom",{direction:"+",scale:this.body.view.scale,pointer:null})}_zoomOut(){const t=this.body.view.scale,e=this.body.view.scale/(1+this.options.keyboard.speed.zoom),i=this.body.view.translation,o=e/t,n=(1-o)*this.canvas.canvasViewCenter.x+i.x*o,s=(1-o)*this.canvas.canvasViewCenter.y+i.y*o;this.body.view.scale=e,this.body.view.translation={x:n,y:s},this.body.emitter.emit("zoom",{direction:"-",scale:this.body.view.scale,pointer:null})}configureKeyboardBindings(){var t,e,i,o,n,s,r,a,h,d,l,c,u,p,f,g,v,m,y,b,w,_,x,E;(void 0!==this.keycharm&&this.keycharm.destroy(),!0===this.options.keyboard.enabled)&&(!0===this.options.keyboard.bindToWindow?this.keycharm=$_({container:window,preventDefault:!0}):this.keycharm=$_({container:this.canvas.frame,preventDefault:!0}),this.keycharm.reset(),!0===this.activated&&(no(t=this.keycharm).call(t,"up",()=>{this.bindToRedraw("_moveUp")},"keydown"),no(e=this.keycharm).call(e,"down",()=>{this.bindToRedraw("_moveDown")},"keydown"),no(i=this.keycharm).call(i,"left",()=>{this.bindToRedraw("_moveLeft")},"keydown"),no(o=this.keycharm).call(o,"right",()=>{this.bindToRedraw("_moveRight")},"keydown"),no(n=this.keycharm).call(n,"=",()=>{this.bindToRedraw("_zoomIn")},"keydown"),no(s=this.keycharm).call(s,"num+",()=>{this.bindToRedraw("_zoomIn")},"keydown"),no(r=this.keycharm).call(r,"num-",()=>{this.bindToRedraw("_zoomOut")},"keydown"),no(a=this.keycharm).call(a,"-",()=>{this.bindToRedraw("_zoomOut")},"keydown"),no(h=this.keycharm).call(h,"[",()=>{this.bindToRedraw("_zoomOut")},"keydown"),no(d=this.keycharm).call(d,"]",()=>{this.bindToRedraw("_zoomIn")},"keydown"),no(l=this.keycharm).call(l,"pageup",()=>{this.bindToRedraw("_zoomIn")},"keydown"),no(c=this.keycharm).call(c,"pagedown",()=>{this.bindToRedraw("_zoomOut")},"keydown"),no(u=this.keycharm).call(u,"up",()=>{this.unbindFromRedraw("_moveUp")},"keyup"),no(p=this.keycharm).call(p,"down",()=>{this.unbindFromRedraw("_moveDown")},"keyup"),no(f=this.keycharm).call(f,"left",()=>{this.unbindFromRedraw("_moveLeft")},"keyup"),no(g=this.keycharm).call(g,"right",()=>{this.unbindFromRedraw("_moveRight")},"keyup"),no(v=this.keycharm).call(v,"=",()=>{this.unbindFromRedraw("_zoomIn")},"keyup"),no(m=this.keycharm).call(m,"num+",()=>{this.unbindFromRedraw("_zoomIn")},"keyup"),no(y=this.keycharm).call(y,"num-",()=>{this.unbindFromRedraw("_zoomOut")},"keyup"),no(b=this.keycharm).call(b,"-",()=>{this.unbindFromRedraw("_zoomOut")},"keyup"),no(w=this.keycharm).call(w,"[",()=>{this.unbindFromRedraw("_zoomOut")},"keyup"),no(_=this.keycharm).call(_,"]",()=>{this.unbindFromRedraw("_zoomIn")},"keyup"),no(x=this.keycharm).call(x,"pageup",()=>{this.unbindFromRedraw("_zoomIn")},"keyup"),no(E=this.keycharm).call(E,"pagedown",()=>{this.unbindFromRedraw("_zoomOut")},"keyup")))}}class tx{constructor(t,e,i){var o,n,s,r,a,h,d,l,c,u,p,f,g;this.body=t,this.canvas=e,this.selectionHandler=i,this.navigationHandler=new J_(t,e),this.body.eventListeners.onTap=no(o=this.onTap).call(o,this),this.body.eventListeners.onTouch=no(n=this.onTouch).call(n,this),this.body.eventListeners.onDoubleTap=no(s=this.onDoubleTap).call(s,this),this.body.eventListeners.onHold=no(r=this.onHold).call(r,this),this.body.eventListeners.onDragStart=no(a=this.onDragStart).call(a,this),this.body.eventListeners.onDrag=no(h=this.onDrag).call(h,this),this.body.eventListeners.onDragEnd=no(d=this.onDragEnd).call(d,this),this.body.eventListeners.onMouseWheel=no(l=this.onMouseWheel).call(l,this),this.body.eventListeners.onPinch=no(c=this.onPinch).call(c,this),this.body.eventListeners.onMouseMove=no(u=this.onMouseMove).call(u,this),this.body.eventListeners.onRelease=no(p=this.onRelease).call(p,this),this.body.eventListeners.onContext=no(f=this.onContext).call(f,this),this.touchTime=0,this.drag={},this.pinch={},this.popup=void 0,this.popupObj=void 0,this.popupTimer=void 0,this.body.functions.getPointer=no(g=this.getPointer).call(g,this),this.options={},this.defaultOptions={dragNodes:!0,dragView:!0,hover:!1,keyboard:{enabled:!1,speed:{x:10,y:10,zoom:.02},bindToWindow:!0,autoFocus:!0},navigationButtons:!1,tooltipDelay:300,zoomView:!0,zoomSpeed:1},Zi(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){this.body.emitter.on("destroy",()=>{clearTimeout(this.popupTimer),delete this.body.functions.getPointer})}setOptions(t){if(void 0!==t){fs(["hideEdgesOnDrag","hideEdgesOnZoom","hideNodesOnDrag","keyboard","multiselect","selectable","selectConnectedEdges"],this.options,t),Ds(this.options,t,"keyboard"),t.tooltip&&(Zi(this.options.tooltip,t.tooltip),t.tooltip.color&&(this.options.tooltip.color=Es(t.tooltip.color)))}this.navigationHandler.setOptions(this.options)}getPointer(t){return{x:t.x-(e=this.canvas.frame.canvas,e.getBoundingClientRect().left),y:t.y-ys(this.canvas.frame.canvas)};var e}onTouch(t){(new Date).valueOf()-this.touchTime>50&&(this.drag.pointer=this.getPointer(t.center),this.drag.pinched=!1,this.pinch.scale=this.body.view.scale,this.touchTime=(new Date).valueOf())}onTap(t){const e=this.getPointer(t.center),i=this.selectionHandler.options.multiselect&&(t.changedPointers[0].ctrlKey||t.changedPointers[0].metaKey);this.checkSelectionChanges(e,i),this.selectionHandler.commitAndEmit(e,t),this.selectionHandler.generateClickEvent("click",t,e)}onDoubleTap(t){const e=this.getPointer(t.center);this.selectionHandler.generateClickEvent("doubleClick",t,e)}onHold(t){const e=this.getPointer(t.center),i=this.selectionHandler.options.multiselect;this.checkSelectionChanges(e,i),this.selectionHandler.commitAndEmit(e,t),this.selectionHandler.generateClickEvent("click",t,e),this.selectionHandler.generateClickEvent("hold",t,e)}onRelease(t){if((new Date).valueOf()-this.touchTime>10){const e=this.getPointer(t.center);this.selectionHandler.generateClickEvent("release",t,e),this.touchTime=(new Date).valueOf()}}onContext(t){const e=this.getPointer({x:t.clientX,y:t.clientY});this.selectionHandler.generateClickEvent("oncontext",t,e)}checkSelectionChanges(t){!0===(arguments.length>1&&void 0!==arguments[1]&&arguments[1])?this.selectionHandler.selectAdditionalOnPoint(t):this.selectionHandler.selectOnPoint(t)}_determineDifference(t,e){const i=function(t,e){const i=[];for(let o=0;o<t.length;o++){const n=t[o];-1===zr(e).call(e,n)&&i.push(n)}return i};return{nodes:i(t.nodes,e.nodes),edges:i(t.edges,e.edges)}}onDragStart(t){if(this.drag.dragging)return;void 0===this.drag.pointer&&this.onTouch(t);const e=this.selectionHandler.getNodeAt(this.drag.pointer);if(this.drag.dragging=!0,this.drag.selection=[],this.drag.translation=Zi({},this.body.view.translation),this.drag.nodeId=void 0,t.srcEvent.shiftKey){this.body.selectionBox.show=!0;const e=this.getPointer(t.center);this.body.selectionBox.position.start={x:this.canvas._XconvertDOMtoCanvas(e.x),y:this.canvas._YconvertDOMtoCanvas(e.y)},this.body.selectionBox.position.end={x:this.canvas._XconvertDOMtoCanvas(e.x),y:this.canvas._YconvertDOMtoCanvas(e.y)}}else if(void 0!==e&&!0===this.options.dragNodes){this.drag.nodeId=e.id,!1===e.isSelected()&&this.selectionHandler.setSelection({nodes:[e.id]}),this.selectionHandler.generateClickEvent("dragStart",t,this.drag.pointer);for(const t of this.selectionHandler.getSelectedNodes()){const e={id:t.id,node:t,x:t.x,y:t.y,xFixed:t.options.fixed.x,yFixed:t.options.fixed.y};t.options.fixed.x=!0,t.options.fixed.y=!0,this.drag.selection.push(e)}}else this.selectionHandler.generateClickEvent("dragStart",t,this.drag.pointer,void 0,!0)}onDrag(t){if(!0===this.drag.pinched)return;this.body.emitter.emit("unlockNode");const e=this.getPointer(t.center),i=this.drag.selection;if(i&&i.length&&!0===this.options.dragNodes){this.selectionHandler.generateClickEvent("dragging",t,e);const o=e.x-this.drag.pointer.x,n=e.y-this.drag.pointer.y;Fh(i).call(i,t=>{const e=t.node;!1===t.xFixed&&(e.x=this.canvas._XconvertDOMtoCanvas(this.canvas._XconvertCanvasToDOM(t.x)+o)),!1===t.yFixed&&(e.y=this.canvas._YconvertDOMtoCanvas(this.canvas._YconvertCanvasToDOM(t.y)+n))}),this.body.emitter.emit("startSimulation")}else{if(t.srcEvent.shiftKey){if(this.selectionHandler.generateClickEvent("dragging",t,e,void 0,!0),void 0===this.drag.pointer)return void this.onDragStart(t);this.body.selectionBox.position.end={x:this.canvas._XconvertDOMtoCanvas(e.x),y:this.canvas._YconvertDOMtoCanvas(e.y)},this.body.emitter.emit("_requestRedraw")}if(!0===this.options.dragView&&!t.srcEvent.shiftKey){if(this.selectionHandler.generateClickEvent("dragging",t,e,void 0,!0),void 0===this.drag.pointer)return void this.onDragStart(t);const i=e.x-this.drag.pointer.x,o=e.y-this.drag.pointer.y;this.body.view.translation={x:this.drag.translation.x+i,y:this.drag.translation.y+o},this.body.emitter.emit("_requestRedraw")}}}onDragEnd(t){if(this.drag.dragging=!1,this.body.selectionBox.show){var e;this.body.selectionBox.show=!1;const i=this.body.selectionBox.position,o={minX:Math.min(i.start.x,i.end.x),minY:Math.min(i.start.y,i.end.y),maxX:Math.max(i.start.x,i.end.x),maxY:Math.max(i.start.y,i.end.y)},n=Om(e=this.body.nodeIndices).call(e,t=>{const e=this.body.nodes[t];return e.x>=o.minX&&e.x<=o.maxX&&e.y>=o.minY&&e.y<=o.maxY});Fh(n).call(n,t=>this.selectionHandler.selectObject(this.body.nodes[t]));const s=this.getPointer(t.center);this.selectionHandler.commitAndEmit(s,t),this.selectionHandler.generateClickEvent("dragEnd",t,this.getPointer(t.center),void 0,!0),this.body.emitter.emit("_requestRedraw")}else{const e=this.drag.selection;e&&e.length?(Fh(e).call(e,function(t){t.node.options.fixed.x=t.xFixed,t.node.options.fixed.y=t.yFixed}),this.selectionHandler.generateClickEvent("dragEnd",t,this.getPointer(t.center)),this.body.emitter.emit("startSimulation")):(this.selectionHandler.generateClickEvent("dragEnd",t,this.getPointer(t.center),void 0,!0),this.body.emitter.emit("_requestRedraw"))}}onPinch(t){const e=this.getPointer(t.center);this.drag.pinched=!0,void 0===this.pinch.scale&&(this.pinch.scale=1);const i=this.pinch.scale*t.scale;this.zoom(i,e)}zoom(t,e){if(!0===this.options.zoomView){const i=this.body.view.scale;let o;t<1e-5&&(t=1e-5),t>10&&(t=10),void 0!==this.drag&&!0===this.drag.dragging&&(o=this.canvas.DOMtoCanvas(this.drag.pointer));const n=this.body.view.translation,s=t/i,r=(1-s)*e.x+n.x*s,a=(1-s)*e.y+n.y*s;if(this.body.view.scale=t,this.body.view.translation={x:r,y:a},null!=o){const t=this.canvas.canvasToDOM(o);this.drag.pointer.x=t.x,this.drag.pointer.y=t.y}this.body.emitter.emit("_requestRedraw"),i<t?this.body.emitter.emit("zoom",{direction:"+",scale:this.body.view.scale,pointer:e}):this.body.emitter.emit("zoom",{direction:"-",scale:this.body.view.scale,pointer:e})}}onMouseWheel(t){if(!0===this.options.zoomView){if(0!==t.deltaY){let e=this.body.view.scale;e*=1+(t.deltaY<0?1:-1)*(.1*this.options.zoomSpeed);const i=this.getPointer({x:t.clientX,y:t.clientY});this.zoom(e,i)}t.preventDefault()}}onMouseMove(t){const e=this.getPointer({x:t.clientX,y:t.clientY});let i=!1;void 0!==this.popup&&(!1===this.popup.hidden&&this._checkHidePopup(e),!1===this.popup.hidden&&(i=!0,this.popup.setPosition(e.x+3,e.y-5),this.popup.show())),this.options.keyboard.autoFocus&&!1===this.options.keyboard.bindToWindow&&!0===this.options.keyboard.enabled&&this.canvas.frame.focus(),!1===i&&(void 0!==this.popupTimer&&(clearInterval(this.popupTimer),this.popupTimer=void 0),this.drag.dragging||(this.popupTimer=Kp(()=>this._checkShowPopup(e),this.options.tooltipDelay))),!0===this.options.hover&&this.selectionHandler.hoverObject(t,e)}_checkShowPopup(t){const e=this.canvas._XconvertDOMtoCanvas(t.x),i=this.canvas._YconvertDOMtoCanvas(t.y),o={left:e,top:i,right:e,bottom:i},n=void 0===this.popupObj?void 0:this.popupObj.id;let s=!1,r="node";if(void 0===this.popupObj){const t=this.body.nodeIndices,e=this.body.nodes;let i;const n=[];for(let r=0;r<t.length;r++)i=e[t[r]],!0===i.isOverlappingWith(o)&&(s=!0,void 0!==i.getTitle()&&n.push(t[r]));n.length>0&&(this.popupObj=e[n[n.length-1]],s=!0)}if(void 0===this.popupObj&&!1===s){const t=this.body.edgeIndices,e=this.body.edges;let i;const n=[];for(let s=0;s<t.length;s++)i=e[t[s]],!0===i.isOverlappingWith(o)&&!0===i.connected&&void 0!==i.getTitle()&&n.push(t[s]);n.length>0&&(this.popupObj=e[n[n.length-1]],r="edge")}void 0!==this.popupObj?this.popupObj.id!==n&&(void 0===this.popup&&(this.popup=new Ws(this.canvas.frame)),this.popup.popupTargetType=r,this.popup.popupTargetId=this.popupObj.id,this.popup.setPosition(t.x+3,t.y-5),this.popup.setText(this.popupObj.getTitle()),this.popup.show(),this.body.emitter.emit("showPopup",this.popupObj.id)):void 0!==this.popup&&(this.popup.hide(),this.body.emitter.emit("hidePopup"))}_checkHidePopup(t){const e=this.selectionHandler._pointerToPositionObject(t);let i=!1;if("node"===this.popup.popupTargetType){if(void 0!==this.body.nodes[this.popup.popupTargetId]&&(i=this.body.nodes[this.popup.popupTargetId].isOverlappingWith(e),!0===i)){const e=this.selectionHandler.getNodeAt(t);i=void 0!==e&&e.id===this.popup.popupTargetId}}else void 0===this.selectionHandler.getNodeAt(t)&&void 0!==this.body.edges[this.popup.popupTargetId]&&(i=this.body.edges[this.popup.popupTargetId].isOverlappingWith(e));!1===i&&(this.popupObj=void 0,this.popup.hide(),this.body.emitter.emit("hidePopup"))}}var ex,ix,ox={};function nx(){return ix||(ix=1,ex||(ex=1,Hc()("Set",function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},Uc()))),ox}var sx,rx,ax,hx,dx,lx,cx,ux,px,fx,gx,vx,mx,yx,bx,wx,_x,xx,Ex,Ox,Cx,kx={};function Sx(){if(rx)return sx;rx=1;var t=Ft(),e=TypeError;return sx=function(i){if("object"==typeof i&&"size"in i&&"has"in i&&"add"in i&&"delete"in i&&"keys"in i)return i;throw new e(t(i)+" is not a set")},sx}function Tx(){if(hx)return ax;hx=1;var t=Tt(),e=Jc(),i=t("Set"),o=i.prototype;return ax={Set:i,add:e("add",1),has:e("has",1),remove:e("delete",1),proto:o}}function Mx(){if(lx)return dx;lx=1;var t=F();return dx=function(e,i,o){for(var n,s,r=o?e:e.iterator,a=e.next;!(n=t(a,r)).done;)if(void 0!==(s=i(n.value)))return s},dx}function Dx(){if(ux)return cx;ux=1;var t=Mx();return cx=function(e,i,o){return o?t(e.keys(),i,!0):e.forEach(i)},cx}function Ix(){if(fx)return px;fx=1;var t=Tx(),e=Dx(),i=t.Set,o=t.add;return px=function(t){var n=new i;return e(t,function(t){o(n,t)}),n},px}function Px(){return vx||(vx=1,gx=function(t){return t.size}),gx}function Bx(){return yx?mx:(yx=1,mx=function(t){return{iterator:t,next:t.next,done:!1}})}function zx(){if(wx)return bx;wx=1;var t=Nt(),e=ri(),i=F(),o=ci(),n=Bx(),s="Invalid size",r=RangeError,a=TypeError,h=Math.max,d=function(e,i){this.set=e,this.size=h(i,0),this.has=t(e.has),this.keys=t(e.keys)};return d.prototype={getIterator:function(){return n(e(i(this.keys,this.set)))},includes:function(t){return i(this.has,this.set,t)}},bx=function(t){e(t);var i=+t.size;if(i!=i)throw new a(s);var n=o(i);if(n<0)throw new r(s);return new d(t,n)}}function Fx(){if(xx)return _x;xx=1;var t=Sx(),e=Tx(),i=Ix(),o=Px(),n=zx(),s=Dx(),r=Mx(),a=e.has,h=e.remove;return _x=function(e){var d=t(this),l=n(e),c=i(d);return o(d)<=l.size?s(d,function(t){l.includes(t)&&h(c,t)}):r(l.getIterator(),function(t){a(c,t)&&h(c,t)}),c}}function Nx(){return Ox?Ex:(Ox=1,Ex=function(){return!1})}var Ax,Rx,jx,Lx={};function Hx(){if(Rx)return Ax;Rx=1;var t=Sx(),e=Tx(),i=Px(),o=zx(),n=Dx(),s=Mx(),r=e.Set,a=e.add,h=e.has;return Ax=function(e){var d=t(this),l=o(e),c=new r;return i(d)>l.size?s(l.getIterator(),function(t){h(d,t)&&a(c,t)}):n(d,function(t){l.includes(t)&&a(c,t)}),c}}var Wx,Vx,qx,Ux={};function Yx(){if(Vx)return Wx;Vx=1;var t=Sx(),e=Tx().has,i=Px(),o=zx(),n=Dx(),s=Mx(),r=Rc();return Wx=function(a){var h=t(this),d=o(a);if(i(h)<=d.size)return!1!==n(h,function(t){if(d.includes(t))return!1},!0);var l=d.getIterator();return!1!==s(l,function(t){if(e(h,t))return r(l,"normal",!1)})},Wx}var Xx,Kx,Gx,Zx={};function Qx(){if(Kx)return Xx;Kx=1;var t=Sx(),e=Px(),i=Dx(),o=zx();return Xx=function(n){var s=t(this),r=o(n);return!(e(s)>r.size)&&!1!==i(s,function(t){if(!r.includes(t))return!1},!0)}}var $x,Jx,tE,eE={};function iE(){if(Jx)return $x;Jx=1;var t=Sx(),e=Tx().has,i=Px(),o=zx(),n=Mx(),s=Rc();return $x=function(r){var a=t(this),h=o(r);if(i(a)<h.size)return!1;var d=h.getIterator();return!1!==n(d,function(t){if(!e(a,t))return s(d,"normal",!1)})},$x}var oE,nE,sE,rE,aE,hE={};function dE(){if(nE)return oE;nE=1;var t=Sx(),e=Tx(),i=Ix(),o=zx(),n=Mx(),s=e.add,r=e.has,a=e.remove;return oE=function(e){var h=t(this),d=o(e).getIterator(),l=i(h);return n(d,function(t){r(h,t)?a(l,t):s(l,t)}),l}}function lE(){return rE?sE:(rE=1,sE=function(t){try{var e=new Set,i={size:0,has:function(){return!0},keys:function(){return Object.defineProperty({},"next",{get:function(){return e.clear(),e.add(4),function(){return{done:!0}}}})}},o=e[t](i);return 1===o.size&&4===o.values().next().value}catch(t){return!1}})}var cE,uE,pE,fE,gE,vE,mE,yE,bE,wE={};function _E(){if(uE)return cE;uE=1;var t=Sx(),e=Tx().add,i=Ix(),o=zx(),n=Mx();return cE=function(s){var r=t(this),a=o(s).getIterator(),h=i(r);return n(a,function(t){e(h,t)}),h},cE}function xE(){return gE?fE:(gE=1,Ul(),nx(),function(){if(Cx)return kx;Cx=1;var t=di(),e=Fx(),i=x(),o=!Nx()("difference",function(t){return 0===t.size})||i(function(){var t={size:1,has:function(){return!0},keys:function(){var t=0;return{next:function(){var i=t++>1;return e.has(1)&&e.clear(),{done:i,value:2}}}}},e=new Set([1,2,3,4]);return 3!==e.difference(t).size});t({target:"Set",proto:!0,real:!0,forced:o},{difference:e})}(),function(){if(jx)return Lx;jx=1;var t=di(),e=x(),i=Hx();t({target:"Set",proto:!0,real:!0,forced:!Nx()("intersection",function(t){return 2===t.size&&t.has(1)&&t.has(2)})||e(function(){return"3,2"!==String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))})},{intersection:i})}(),function(){if(qx)return Ux;qx=1;var t=di(),e=Yx();t({target:"Set",proto:!0,real:!0,forced:!Nx()("isDisjointFrom",function(t){return!t})},{isDisjointFrom:e})}(),function(){if(Gx)return Zx;Gx=1;var t=di(),e=Qx();t({target:"Set",proto:!0,real:!0,forced:!Nx()("isSubsetOf",function(t){return t})},{isSubsetOf:e})}(),function(){if(tE)return eE;tE=1;var t=di(),e=iE();t({target:"Set",proto:!0,real:!0,forced:!Nx()("isSupersetOf",function(t){return!t})},{isSupersetOf:e})}(),function(){if(aE)return hE;aE=1;var t=di(),e=dE(),i=lE();t({target:"Set",proto:!0,real:!0,forced:!Nx()("symmetricDifference")||!i("symmetricDifference")},{symmetricDifference:e})}(),function(){if(pE)return wE;pE=1;var t=di(),e=_E(),i=lE();t({target:"Set",proto:!0,real:!0,forced:!Nx()("union")||!i("union")},{union:e})}(),hu(),fE=St().Set)}function EE(){if(mE)return vE;mE=1;var t=xE();return bu(),vE=t}var OE,CE,kE,SE,TE,ME,DE,IE,PE,BE,zE=o(bE?yE:(bE=1,yE=EE())),FE={},NE={};function AE(){if(CE)return OE;CE=1;var t=C(),e=Vc(),i=zc().getWeakData,o=Lc(),n=ri(),s=Et(),r=kt(),a=jc(),h=Dh(),d=be(),l=Dl(),c=l.set,u=l.getterFor,p=h.find,f=h.findIndex,g=t([].splice),v=0,m=function(t){return t.frozen||(t.frozen=new y)},y=function(){this.entries=[]},b=function(t,e){return p(t.entries,function(t){return t[0]===e})};return y.prototype={get:function(t){var e=b(this,t);if(e)return e[1]},has:function(t){return!!b(this,t)},set:function(t,e){var i=b(this,t);i?i[1]=e:this.entries.push([t,e])},delete:function(t){var e=f(this.entries,function(e){return e[0]===t});return~e&&g(this.entries,e,1),!!~e}},OE={getConstructor:function(t,h,l,p){var f=t(function(t,e){o(t,g),c(t,{type:h,id:v++,frozen:null}),s(e)||a(e,t[p],{that:t,AS_ENTRIES:l})}),g=f.prototype,y=u(h),b=function(t,e,o){var s=y(t),r=i(n(e),!0);return!0===r?m(s).set(e,o):r[s.id]=o,t};return e(g,{delete:function(t){var e=y(this);if(!r(t))return!1;var o=i(t);return!0===o?m(e).delete(t):o&&d(o,e.id)&&delete o[e.id]},has:function(t){var e=y(this);if(!r(t))return!1;var o=i(t);return!0===o?m(e).has(t):o&&d(o,e.id)}}),e(g,l?{get:function(t){var e=y(this);if(r(t)){var o=i(t);if(!0===o)return m(e).get(t);if(o)return o[e.id]}},set:function(t,e){return b(this,t,e)}}:{add:function(t){return b(this,t,!0)}}),f}}}function RE(){return SE||(SE=1,function(){if(kE)return NE;kE=1;var t,e=Bc(),i=_(),o=C(),n=Vc(),s=zc(),r=Hc(),a=AE(),h=kt(),d=Dl().enforce,l=x(),c=Ml(),u=Object,p=Array.isArray,f=u.isExtensible,g=u.isFrozen,v=u.isSealed,m=u.freeze,y=u.seal,b=!i.ActiveXObject&&"ActiveXObject"in i,w=function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},E=r("WeakMap",w,a),O=E.prototype,k=o(O.set);if(c)if(b){t=a.getConstructor(w,"WeakMap",!0),s.enable();var S=o(O.delete),T=o(O.has),M=o(O.get);n(O,{delete:function(e){if(h(e)&&!f(e)){var i=d(this);return i.frozen||(i.frozen=new t),S(this,e)||i.frozen.delete(e)}return S(this,e)},has:function(e){if(h(e)&&!f(e)){var i=d(this);return i.frozen||(i.frozen=new t),T(this,e)||i.frozen.has(e)}return T(this,e)},get:function(e){if(h(e)&&!f(e)){var i=d(this);return i.frozen||(i.frozen=new t),T(this,e)?M(this,e):i.frozen.get(e)}return M(this,e)},set:function(e,i){if(h(e)&&!f(e)){var o=d(this);o.frozen||(o.frozen=new t),T(this,e)?k(this,e,i):o.frozen.set(e,i)}else k(this,e,i);return this}})}else e&&l(function(){var t=m([]);return k(new E,t,1),!g(t)})&&n(O,{set:function(t,e){var i;return p(t)&&(g(t)?i=m:v(t)&&(i=y)),k(this,t,e),i&&i(t),this}})}()),FE}function jE(){return ME?TE:(ME=1,Ul(),RE(),TE=St().WeakMap)}function LE(){if(IE)return DE;IE=1;var t=jE();return bu(),DE=t}var HE=o(BE?PE:(BE=1,PE=LE()));function WE(t,e,i){(function(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")})(t,e),e.set(t,i)}function VE(t,e,i){return t.set(UE(t,e),i),i}function qE(t,e){return t.get(UE(t,e))}function UE(t,e,i){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:i;throw new TypeError("Private element is not present on this object")}function YE(t,e){const i=new zE;for(const o of e)t.has(o)||i.add(o);return i}var XE=new HE,KE=new HE;class GE{constructor(){WE(this,XE,new zE),WE(this,KE,new zE)}get size(){return qE(KE,this).size}add(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];for(const t of e)qE(KE,this).add(t)}delete(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];for(const t of e)qE(KE,this).delete(t)}clear(){qE(KE,this).clear()}getSelection(){return[...qE(KE,this)]}getChanges(){return{added:[...YE(qE(XE,this),qE(KE,this))],deleted:[...YE(qE(KE,this),qE(XE,this))],previous:[...new zE(qE(XE,this))],current:[...new zE(qE(KE,this))]}}commit(){const t=this.getChanges();VE(XE,this,qE(KE,this)),VE(KE,this,new zE(qE(XE,this)));for(const e of t.added)e.select();for(const e of t.deleted)e.unselect();return t}}var ZE=new HE,QE=new HE,$E=new HE;class JE{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{};WE(this,ZE,new GE),WE(this,QE,new GE),WE(this,$E,void 0),VE($E,this,t)}get sizeNodes(){return qE(ZE,this).size}get sizeEdges(){return qE(QE,this).size}getNodes(){return qE(ZE,this).getSelection()}getEdges(){return qE(QE,this).getSelection()}addNodes(){qE(ZE,this).add(...arguments)}addEdges(){qE(QE,this).add(...arguments)}deleteNodes(t){qE(ZE,this).delete(t)}deleteEdges(t){qE(QE,this).delete(t)}clear(){qE(ZE,this).clear(),qE(QE,this).clear()}commit(){const t={nodes:qE(ZE,this).commit(),edges:qE(QE,this).commit()};for(var e=arguments.length,i=new Array(e),o=0;o<e;o++)i[o]=arguments[o];return qE($E,this).call(this,t,...i),t}}class tO{constructor(t,e){this.body=t,this.canvas=e,this._selectionAccumulator=new JE,this.hoverObj={nodes:{},edges:{}},this.options={},this.defaultOptions={multiselect:!1,selectable:!0,selectConnectedEdges:!0,hoverConnectedEdges:!0},Zi(this.options,this.defaultOptions),this.body.emitter.on("_dataChanged",()=>{this.updateSelection()})}setOptions(t){if(void 0!==t){ps(["multiselect","hoverConnectedEdges","selectable","selectConnectedEdges"],this.options,t)}}selectOnPoint(t){let e=!1;if(!0===this.options.selectable){const i=this.getNodeAt(t)||this.getEdgeAt(t);this.unselectAll(),void 0!==i&&(e=this.selectObject(i)),this.body.emitter.emit("_requestRedraw")}return e}selectAdditionalOnPoint(t){let e=!1;if(!0===this.options.selectable){const i=this.getNodeAt(t)||this.getEdgeAt(t);void 0!==i&&(e=!0,!0===i.isSelected()?this.deselectObject(i):this.selectObject(i),this.body.emitter.emit("_requestRedraw"))}return e}_initBaseEvent(t,e){const i={};return i.pointer={DOM:{x:e.x,y:e.y},canvas:this.canvas.DOMtoCanvas(e)},i.event=t,i}generateClickEvent(t,e,i,o){let n=arguments.length>4&&void 0!==arguments[4]&&arguments[4];const s=this._initBaseEvent(e,i);if(!0===n)s.nodes=[],s.edges=[];else{const t=this.getSelection();s.nodes=t.nodes,s.edges=t.edges}void 0!==o&&(s.previousSelection=o),"click"==t&&(s.items=this.getClickedItems(i)),void 0!==e.controlEdge&&(s.controlEdge=e.controlEdge),this.body.emitter.emit(t,s)}selectObject(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.options.selectConnectedEdges;return void 0!==t&&(t instanceof Xb?(!0===e&&this._selectionAccumulator.addEdges(...t.edges),this._selectionAccumulator.addNodes(t)):this._selectionAccumulator.addEdges(t),!0)}deselectObject(t){!0===t.isSelected()&&(t.selected=!1,this._removeFromSelection(t))}_getAllNodesOverlappingWith(t){const e=[],i=this.body.nodes;for(let o=0;o<this.body.nodeIndices.length;o++){const n=this.body.nodeIndices[o];i[n].isOverlappingWith(t)&&e.push(n)}return e}_pointerToPositionObject(t){const e=this.canvas.DOMtoCanvas(t);return{left:e.x-1,top:e.y+1,right:e.x+1,bottom:e.y-1}}getNodeAt(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const i=this._pointerToPositionObject(t),o=this._getAllNodesOverlappingWith(i);return o.length>0?!0===e?this.body.nodes[o[o.length-1]]:o[o.length-1]:void 0}_getEdgesOverlappingWith(t,e){const i=this.body.edges;for(let o=0;o<this.body.edgeIndices.length;o++){const n=this.body.edgeIndices[o];i[n].isOverlappingWith(t)&&e.push(n)}}_getAllEdgesOverlappingWith(t){const e=[];return this._getEdgesOverlappingWith(t,e),e}getEdgeAt(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const i=this.canvas.DOMtoCanvas(t);let o=10,n=null;const s=this.body.edges;for(let t=0;t<this.body.edgeIndices.length;t++){const e=this.body.edgeIndices[t],r=s[e];if(r.connected){const t=r.from.x,s=r.from.y,a=r.to.x,h=r.to.y,d=r.edgeType.getDistanceToEdge(t,s,a,h,i.x,i.y);d<o&&(n=e,o=d)}}return null!==n?!0===e?this.body.edges[n]:n:void 0}_addToHover(t){t instanceof Xb?this.hoverObj.nodes[t.id]=t:this.hoverObj.edges[t.id]=t}_removeFromSelection(t){t instanceof Xb?(this._selectionAccumulator.deleteNodes(t),this._selectionAccumulator.deleteEdges(...t.edges)):this._selectionAccumulator.deleteEdges(t)}unselectAll(){this._selectionAccumulator.clear()}getSelectedNodeCount(){return this._selectionAccumulator.sizeNodes}getSelectedEdgeCount(){return this._selectionAccumulator.sizeEdges}_hoverConnectedEdges(t){for(let e=0;e<t.edges.length;e++){const i=t.edges[e];i.hover=!0,this._addToHover(i)}}emitBlurEvent(t,e,i){const o=this._initBaseEvent(t,e);!0===i.hover&&(i.hover=!1,i instanceof Xb?(o.node=i.id,this.body.emitter.emit("blurNode",o)):(o.edge=i.id,this.body.emitter.emit("blurEdge",o)))}emitHoverEvent(t,e,i){const o=this._initBaseEvent(t,e);let n=!1;return!1===i.hover&&(i.hover=!0,this._addToHover(i),n=!0,i instanceof Xb?(o.node=i.id,this.body.emitter.emit("hoverNode",o)):(o.edge=i.id,this.body.emitter.emit("hoverEdge",o))),n}hoverObject(t,e){let i=this.getNodeAt(e);void 0===i&&(i=this.getEdgeAt(e));let o=!1;for(const n in this.hoverObj.nodes)Object.prototype.hasOwnProperty.call(this.hoverObj.nodes,n)&&(void 0===i||i instanceof Xb&&i.id!=n||i instanceof Qw)&&(this.emitBlurEvent(t,e,this.hoverObj.nodes[n]),delete this.hoverObj.nodes[n],o=!0);for(const n in this.hoverObj.edges)Object.prototype.hasOwnProperty.call(this.hoverObj.edges,n)&&(!0===o?(this.hoverObj.edges[n].hover=!1,delete this.hoverObj.edges[n]):(void 0===i||i instanceof Qw&&i.id!=n||i instanceof Xb&&!i.hover)&&(this.emitBlurEvent(t,e,this.hoverObj.edges[n]),delete this.hoverObj.edges[n],o=!0));if(void 0!==i){const n=cp(this.hoverObj.edges).length,s=cp(this.hoverObj.nodes).length;(o||i instanceof Qw&&0===n&&0===s||i instanceof Xb&&0===n&&0===s)&&(o=this.emitHoverEvent(t,e,i)),i instanceof Xb&&!0===this.options.hoverConnectedEdges&&this._hoverConnectedEdges(i)}!0===o&&this.body.emitter.emit("_requestRedraw")}commitWithoutEmitting(){this._selectionAccumulator.commit()}commitAndEmit(t,e){let i=!1;const o=this._selectionAccumulator.commit(),n={nodes:o.nodes.previous,edges:o.edges.previous};o.edges.deleted.length>0&&(this.generateClickEvent("deselectEdge",e,t,n),i=!0),o.nodes.deleted.length>0&&(this.generateClickEvent("deselectNode",e,t,n),i=!0),o.nodes.added.length>0&&(this.generateClickEvent("selectNode",e,t),i=!0),o.edges.added.length>0&&(this.generateClickEvent("selectEdge",e,t),i=!0),!0===i&&this.generateClickEvent("select",e,t)}getSelection(){return{nodes:this.getSelectedNodeIds(),edges:this.getSelectedEdgeIds()}}getSelectedNodes(){return this._selectionAccumulator.getNodes()}getSelectedEdges(){return this._selectionAccumulator.getEdges()}getSelectedNodeIds(){var t;return Cd(t=this._selectionAccumulator.getNodes()).call(t,t=>t.id)}getSelectedEdgeIds(){var t;return Cd(t=this._selectionAccumulator.getEdges()).call(t,t=>t.id)}setSelection(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t||!t.nodes&&!t.edges)throw new TypeError("Selection must be an object with nodes and/or edges properties");if((e.unselectAll||void 0===e.unselectAll)&&this.unselectAll(),t.nodes)for(const i of t.nodes){const t=this.body.nodes[i];if(!t)throw new RangeError('Node with id "'+i+'" not found');this.selectObject(t,e.highlightEdges)}if(t.edges)for(const e of t.edges){const t=this.body.edges[e];if(!t)throw new RangeError('Edge with id "'+e+'" not found');this.selectObject(t)}this.body.emitter.emit("_requestRedraw"),this._selectionAccumulator.commit()}selectNodes(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(!t||void 0===t.length)throw"Selection must be an array with ids";this.setSelection({nodes:t},{highlightEdges:e})}selectEdges(t){if(!t||void 0===t.length)throw"Selection must be an array with ids";this.setSelection({edges:t})}updateSelection(){for(const t in this._selectionAccumulator.getNodes())Object.prototype.hasOwnProperty.call(this.body.nodes,t.id)||this._selectionAccumulator.deleteNodes(t);for(const t in this._selectionAccumulator.getEdges())Object.prototype.hasOwnProperty.call(this.body.edges,t.id)||this._selectionAccumulator.deleteEdges(t)}getClickedItems(t){const e=this.canvas.DOMtoCanvas(t),i=[],o=this.body.nodeIndices,n=this.body.nodes;for(let t=o.length-1;t>=0;t--){const s=n[o[t]].getItemsOnPoint(e);i.push.apply(i,s)}const s=this.body.edgeIndices,r=this.body.edges;for(let t=s.length-1;t>=0;t--){const o=r[s[t]].getItemsOnPoint(e);i.push.apply(i,o)}return i}}var eO,iO,oO,nO,sO,rO,aO,hO,dO,lO,cO,uO,pO,fO,gO,vO,mO,yO={};function bO(){if(iO)return eO;iO=1;var t=$i(),e=Math.floor,i=function(o,n){var s=o.length;if(s<8)for(var r,a,h=1;h<s;){for(a=h,r=o[h];a&&n(o[a-1],r)>0;)o[a]=o[--a];a!==h++&&(o[a]=r)}else for(var d=e(s/2),l=i(t(o,0,d),n),c=i(t(o,d),n),u=l.length,p=c.length,f=0,g=0;f<u||g<p;)o[f+g]=f<u&&g<p?n(l[f],c[g])<=0?l[f++]:c[g++]:f<u?l[f++]:c[g++];return o};return eO=i}function wO(){if(nO)return oO;nO=1;var t=Dt().match(/firefox\/(\d+)/i);return oO=!!t&&+t[1]}function _O(){return rO?sO:(rO=1,sO=/MSIE|Trident/.test(Dt()))}function xO(){if(hO)return aO;hO=1;var t=Dt().match(/AppleWebKit\/(\d+)\./);return aO=!!t&&+t[1]}function EO(){if(dO)return yO;dO=1;var t=di(),e=C(),i=Nt(),o=ye(),n=fi(),s=th(),r=la(),a=x(),h=bO(),d=Cr(),l=wO(),c=_O(),u=It(),p=xO(),f=[],g=e(f.sort),v=e(f.push),m=a(function(){f.sort(void 0)}),y=a(function(){f.sort(null)}),b=d("sort"),w=!a(function(){if(u)return u<70;if(!(l&&l>3)){if(c)return!0;if(p)return p<603;var t,e,i,o,n="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:i=3;break;case 68:case 71:i=4;break;default:i=2}for(o=0;o<47;o++)f.push({k:e+o,v:i})}for(f.sort(function(t,e){return e.v-t.v}),o=0;o<f.length;o++)e=f[o].k.charAt(0),n.charAt(n.length-1)!==e&&(n+=e);return"DGBEFHACIJK"!==n}});return t({target:"Array",proto:!0,forced:m||!y||!b||!w},{sort:function(t){void 0!==t&&i(t);var e=o(this);if(w)return void 0===t?g(e):g(e,t);var a,d,l=[],c=n(e);for(d=0;d<c;d++)d in e&&v(l,e[d]);for(h(l,function(t){return function(e,i){return void 0===i?-1:void 0===e?1:void 0!==t?+t(e,i)||0:r(e)>r(i)?1:-1}}(t)),a=n(l),d=0;d<a;)e[d]=l[d++];for(;d<c;)s(e,d++);return e}}),yO}function OO(){return cO?lO:(cO=1,EO(),lO=to()("Array","sort"))}function CO(){if(pO)return uO;pO=1;var t=Mt(),e=OO(),i=Array.prototype;return uO=function(o){var n=o.sort;return o===i||t(i,o)&&n===i.sort?e:n},uO}function kO(){return gO?fO:(gO=1,fO=CO())}var SO,TO,MO,DO,IO,PO,BO,zO,FO,NO=o(mO?vO:(mO=1,vO=kO())),AO={};function RO(){if(TO)return SO;TO=1;var t=z(),e=x(),i=C(),o=Bl(),n=bi(),s=Ct(),r=i(wt().f),a=i([].push),h=t&&e(function(){var t=Object.create(null);return t[2]=2,!r(t,2)}),d=function(e){return function(i){for(var d,l=s(i),c=n(l),u=h&&null===o(l),p=c.length,f=0,g=[];p>f;)d=c[f++],t&&!(u?d in l:r(l,d))||a(g,e?[d,l[d]]:l[d]);return g}};return SO={entries:d(!0),values:d(!1)}}function jO(){return IO?DO:(IO=1,function(){if(MO)return AO;MO=1;var t=di(),e=RO().values;t({target:"Object",stat:!0},{values:function(t){return e(t)}})}(),DO=St().Object.values)}function LO(){return BO?PO:(BO=1,PO=jO())}var HO,WO,VO,qO,UO,YO,XO,KO,GO,ZO,QO,$O,JO,tC=o(FO?zO:(FO=1,zO=LO())),eC={};function iC(){if(WO)return HO;WO=1;var t=Nt(),e=ye(),i=xt(),o=fi(),n=TypeError,s="Reduce of empty array with no initial value",r=function(r){return function(a,h,d,l){var c=e(a),u=i(c),p=o(c);if(t(h),0===p&&d<2)throw new n(s);var f=r?p-1:0,g=r?-1:1;if(d<2)for(;;){if(f in u){l=u[f],f+=g;break}if(f+=g,r?f<0:p<=f)throw new n(s)}for(;r?f>=0:p>f;f+=g)f in u&&(l=h(l,u[f],f,c));return l}};return HO={left:r(!1),right:r(!0)}}function oC(){return qO?VO:(qO=1,VO="NODE"===kp())}function nC(){return XO?YO:(XO=1,function(){if(UO)return eC;UO=1;var t=di(),e=iC().left,i=Cr(),o=It();t({target:"Array",proto:!0,forced:!oC()&&o>79&&o<83||!i("reduce")},{reduce:function(t){var i=arguments.length;return e(this,t,i,i>1?arguments[1]:void 0)}})}(),YO=to()("Array","reduce"))}function sC(){if(GO)return KO;GO=1;var t=Mt(),e=nC(),i=Array.prototype;return KO=function(o){var n=o.reduce;return o===i||t(i,o)&&n===i.reduce?e:n},KO}function rC(){return QO?ZO:(QO=1,ZO=sC())}var aC=o(JO?$O:(JO=1,$O=rC()));class hC{abstract(){throw new Error("Can't instantiate abstract class!")}fake_use(){}curveType(){return this.abstract()}getPosition(t){return this.fake_use(t),this.abstract()}setPosition(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;this.fake_use(t,e,i),this.abstract()}getTreeSize(t){return this.fake_use(t),this.abstract()}sort(t){this.fake_use(t),this.abstract()}fix(t,e){this.fake_use(t,e),this.abstract()}shift(t,e){this.fake_use(t,e),this.abstract()}}class dC extends hC{constructor(t){super(),this.layout=t}curveType(){return"horizontal"}getPosition(t){return t.x}setPosition(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;void 0!==i&&this.layout.hierarchical.addToOrdering(t,i),t.x=e}getTreeSize(t){const e=this.layout.hierarchical.getTreeSize(this.layout.body.nodes,t);return{min:e.min_x,max:e.max_x}}sort(t){NO(t).call(t,function(t,e){return t.x-e.x})}fix(t,e){t.y=this.layout.options.hierarchical.levelSeparation*e,t.options.fixed.y=!0}shift(t,e){this.layout.body.nodes[t].x+=e}}class lC extends hC{constructor(t){super(),this.layout=t}curveType(){return"vertical"}getPosition(t){return t.y}setPosition(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;void 0!==i&&this.layout.hierarchical.addToOrdering(t,i),t.y=e}getTreeSize(t){const e=this.layout.hierarchical.getTreeSize(this.layout.body.nodes,t);return{min:e.min_y,max:e.max_y}}sort(t){NO(t).call(t,function(t,e){return t.y-e.y})}fix(t,e){t.x=this.layout.options.hierarchical.levelSeparation*e,t.options.fixed.x=!0}shift(t,e){this.layout.body.nodes[t].y+=e}}var cC,uC,pC,fC,gC,vC,mC,yC,bC,wC={};function _C(){return pC?uC:(pC=1,function(){if(cC)return wC;cC=1;var t=di(),e=Dh().every;t({target:"Array",proto:!0,forced:!Cr()("every")},{every:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0)}})}(),uC=to()("Array","every"))}function xC(){if(gC)return fC;gC=1;var t=Mt(),e=_C(),i=Array.prototype;return fC=function(o){var n=o.every;return o===i||t(i,o)&&n===i.every?e:n},fC}function EC(){return mC?vC:(mC=1,vC=xC())}var OC=o(bC?yC:(bC=1,yC=EC()));function CC(t,e){const i=new zE;return Fh(t).call(t,t=>{var e;Fh(e=t.edges).call(e,t=>{t.connected&&i.add(t)})}),Fh(i).call(i,t=>{const i=t.from.id,o=t.to.id;null==e[i]&&(e[i]=0),(null==e[o]||e[i]>=e[o])&&(e[o]=e[i]+1)}),e}function kC(t,e,i,o){var n;const s=Er(null),r=aC(n=[...tb(o).call(o)]).call(n,(t,e)=>t+1+e.edges.length,0),a=i+"Id",h="to"===i?1:-1;for(const[n,c]of o){if(!o.has(n)||!t(c))continue;s[n]=0;const u=[c];let p,f=0;for(;p=u.pop();){var d,l;if(!o.has(n))continue;const t=s[p.id]+h;if(Fh(d=Om(l=p.edges).call(l,t=>t.connected&&t.to!==t.from&&t[i]!==p&&o.has(t.toId)&&o.has(t.fromId))).call(d,o=>{const n=o[a],r=s[n];(null==r||e(t,r))&&(s[n]=t,u.push(o[i]))}),f>r)return CC(o,s);++f}}return s}class SC{constructor(){this.childrenReference={},this.parentReference={},this.trees={},this.distributionOrdering={},this.levels={},this.distributionIndex={},this.isTree=!1,this.treeIndex=-1}addRelation(t,e){void 0===this.childrenReference[t]&&(this.childrenReference[t]=[]),this.childrenReference[t].push(e),void 0===this.parentReference[e]&&(this.parentReference[e]=[]),this.parentReference[e].push(t)}checkIfTree(){for(const t in this.parentReference)if(this.parentReference[t].length>1)return void(this.isTree=!1);this.isTree=!0}numTrees(){return this.treeIndex+1}setTreeIndex(t,e){void 0!==e&&void 0===this.trees[t.id]&&(this.trees[t.id]=e,this.treeIndex=Math.max(e,this.treeIndex))}ensureLevel(t){void 0===this.levels[t]&&(this.levels[t]=0)}getMaxLevel(t){const e={},i=t=>{if(void 0!==e[t])return e[t];let o=this.levels[t];if(this.childrenReference[t]){const e=this.childrenReference[t];if(e.length>0)for(let t=0;t<e.length;t++)o=Math.max(o,i(e[t]))}return e[t]=o,o};return i(t)}levelDownstream(t,e){void 0===this.levels[e.id]&&(void 0===this.levels[t.id]&&(this.levels[t.id]=0),this.levels[e.id]=this.levels[t.id]+1)}setMinLevelToZero(){var t;const e=new _u;let i=0;const o=NO(t=[...new zE(tC(this.levels))]).call(t,(t,e)=>t-e);for(const t of o)e.set(t,i++);for(const t in this.levels)Object.prototype.hasOwnProperty.call(this.levels,t)&&(this.levels[t]=e.get(this.levels[t]))}getTreeSize(t,e){let i=1e9,o=-1e9,n=1e9,s=-1e9;for(const r in this.trees)if(Object.prototype.hasOwnProperty.call(this.trees,r)&&this.trees[r]===e){const e=t[r];i=Math.min(e.x,i),o=Math.max(e.x,o),n=Math.min(e.y,n),s=Math.max(e.y,s)}return{min_x:i,max_x:o,min_y:n,max_y:s}}hasSameParent(t,e){const i=this.parentReference[t.id],o=this.parentReference[e.id];if(void 0===i||void 0===o)return!1;for(let t=0;t<i.length;t++)for(let e=0;e<o.length;e++)if(i[t]==o[e])return!0;return!1}inSameSubNetwork(t,e){return this.trees[t.id]===this.trees[e.id]}getLevels(){return cp(this.distributionOrdering)}addToOrdering(t,e){void 0===this.distributionOrdering[e]&&(this.distributionOrdering[e]=[]);let i=!1;const o=this.distributionOrdering[e];for(const e in o)if(o[e]===t){i=!0;break}i||(this.distributionOrdering[e].push(t),this.distributionIndex[t.id]=this.distributionOrdering[e].length-1)}}class TC{constructor(t){this.body=t,this._resetRNG(Math.random()+":"+d_()),this.setPhysics=!1,this.options={},this.optionsBackup={physics:{}},this.defaultOptions={randomSeed:void 0,improvedLayout:!0,clusterThreshold:150,hierarchical:{enabled:!1,levelSeparation:150,nodeSpacing:100,treeSpacing:200,blockShifting:!0,edgeMinimization:!0,parentCentralization:!0,direction:"UD",sortMethod:"hubsize"}},Zi(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){this.body.emitter.on("_dataChanged",()=>{this.setupHierarchicalLayout()}),this.body.emitter.on("_dataLoaded",()=>{this.layoutNetwork()}),this.body.emitter.on("_resetHierarchicalLayout",()=>{this.setupHierarchicalLayout()}),this.body.emitter.on("_adjustEdgesForHierarchicalLayout",()=>{if(!0!==this.options.hierarchical.enabled)return;const t=this.direction.curveType();this.body.emitter.emit("_forceDisableDynamicCurves",t,!1)})}setOptions(t,e){if(void 0!==t){const i=this.options.hierarchical,o=i.enabled;if(ps(["randomSeed","improvedLayout","clusterThreshold"],this.options,t),Ds(this.options,t,"hierarchical"),void 0!==t.randomSeed&&this._resetRNG(t.randomSeed),!0===i.enabled)return!0===o&&this.body.emitter.emit("refresh",!0),"RL"===i.direction||"DU"===i.direction?i.levelSeparation>0&&(i.levelSeparation*=-1):i.levelSeparation<0&&(i.levelSeparation*=-1),this.setDirectionStrategy(),this.body.emitter.emit("_resetHierarchicalLayout"),this.adaptAllOptionsForHierarchicalLayout(e);if(!0===o)return this.body.emitter.emit("refresh"),gs(e,this.optionsBackup)}return e}_resetRNG(t){this.initialRandomSeed=t,this._rng=es(this.initialRandomSeed)}adaptAllOptionsForHierarchicalLayout(t){if(!0===this.options.hierarchical.enabled){const e=this.optionsBackup.physics;void 0===t.physics||!0===t.physics?(t.physics={enabled:void 0===e.enabled||e.enabled,solver:"hierarchicalRepulsion"},e.enabled=void 0===e.enabled||e.enabled,e.solver=e.solver||"barnesHut"):"object"==typeof t.physics?(e.enabled=void 0===t.physics.enabled||t.physics.enabled,e.solver=t.physics.solver||"barnesHut",t.physics.solver="hierarchicalRepulsion"):!1!==t.physics&&(e.solver="barnesHut",t.physics={solver:"hierarchicalRepulsion"});let i=this.direction.curveType();if(void 0===t.edges)this.optionsBackup.edges={smooth:{enabled:!0,type:"dynamic"}},t.edges={smooth:!1};else if(void 0===t.edges.smooth)this.optionsBackup.edges={smooth:{enabled:!0,type:"dynamic"}},t.edges.smooth=!1;else if("boolean"==typeof t.edges.smooth)this.optionsBackup.edges={smooth:t.edges.smooth},t.edges.smooth={enabled:t.edges.smooth,type:i};else{const e=t.edges.smooth;void 0!==e.type&&"dynamic"!==e.type&&(i=e.type),this.optionsBackup.edges={smooth:{enabled:void 0===e.enabled||e.enabled,type:void 0===e.type?"dynamic":e.type,roundness:void 0===e.roundness?.5:e.roundness,forceDirection:void 0!==e.forceDirection&&e.forceDirection}},t.edges.smooth={enabled:void 0===e.enabled||e.enabled,type:i,roundness:void 0===e.roundness?.5:e.roundness,forceDirection:void 0!==e.forceDirection&&e.forceDirection}}this.body.emitter.emit("_forceDisableDynamicCurves",i)}return t}positionInitially(t){if(!0!==this.options.hierarchical.enabled){this._resetRNG(this.initialRandomSeed);const e=t.length+50;for(let i=0;i<t.length;i++){const o=t[i],n=2*Math.PI*this._rng();void 0===o.x&&(o.x=e*Math.cos(n)),void 0===o.y&&(o.y=e*Math.sin(n))}}}layoutNetwork(){if(!0!==this.options.hierarchical.enabled&&!0===this.options.improvedLayout){const t=this.body.nodeIndices;let e=0;for(let i=0;i<t.length;i++){!0===this.body.nodes[t[i]].predefinedPosition&&(e+=1)}if(e<.5*t.length){const e=10;let i=0;const o=this.options.clusterThreshold,n={clusterNodeProperties:{shape:"ellipse",label:"",group:"",font:{multi:!1}},clusterEdgeProperties:{label:"",font:{multi:!1},smooth:{enabled:!1}}};if(t.length>o){const s=t.length;for(;t.length>o&&i<=e;){i+=1;const e=t.length;i%3==0?this.body.modules.clustering.clusterBridges(n):this.body.modules.clustering.clusterOutliers(n);if(e==t.length&&i%3!=0)return this._declusterAll(),this.body.emitter.emit("_layoutFailed"),void console.info("This network could not be positioned by this version of the improved layout algorithm. Please disable improvedLayout for better performance.")}this.body.modules.kamadaKawai.setOptions({springLength:Math.max(150,2*s)})}i>e&&console.info("The clustering didn't succeed within the amount of interations allowed, progressing with partial result."),this.body.modules.kamadaKawai.solve(t,this.body.edgeIndices,!0),this._shiftToCenter();const s=70;for(let e=0;e<t.length;e++){const i=this.body.nodes[t[e]];!1===i.predefinedPosition&&(i.x+=(.5-this._rng())*s,i.y+=(.5-this._rng())*s)}this._declusterAll(),this.body.emitter.emit("_repositionBezierNodes")}}}_shiftToCenter(){const t=W_.getRangeCore(this.body.nodes,this.body.nodeIndices),e=W_.findCenter(t);for(let t=0;t<this.body.nodeIndices.length;t++){const i=this.body.nodes[this.body.nodeIndices[t]];i.x-=e.x,i.y-=e.y}}_declusterAll(){let t=!0;for(;!0===t;){t=!1;for(let e=0;e<this.body.nodeIndices.length;e++)!0===this.body.nodes[this.body.nodeIndices[e]].isCluster&&(t=!0,this.body.modules.clustering.openCluster(this.body.nodeIndices[e],{},!1));!0===t&&this.body.emitter.emit("_dataChanged")}}getSeed(){return this.initialRandomSeed}setupHierarchicalLayout(){if(!0===this.options.hierarchical.enabled&&this.body.nodeIndices.length>0){let t,e,i=!1,o=!1;for(e in this.lastNodeOnLevel={},this.hierarchical=new SC,this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,e)&&(t=this.body.nodes[e],void 0!==t.options.level?(i=!0,this.hierarchical.levels[e]=t.options.level):o=!0);if(!0===o&&!0===i)throw new Error("To use the hierarchical layout, nodes require either no predefined levels or levels have to be defined for all nodes.");{if(!0===o){const t=this.options.hierarchical.sortMethod;"hubsize"===t?this._determineLevelsByHubsize():"directed"===t?this._determineLevelsDirected():"custom"===t&&this._determineLevelsCustomCallback()}for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&this.hierarchical.ensureLevel(t);const t=this._getDistribution();this._generateMap(),this._placeNodesByHierarchy(t),this._condenseHierarchy(),this._shiftToCenter()}}}_condenseHierarchy(){var t=this;let e=!1;const i={},o=(t,e)=>{const i=this.hierarchical.trees;for(const o in i)Object.prototype.hasOwnProperty.call(i,o)&&i[o]===t&&this.direction.shift(o,e)},n=()=>{const t=[];for(let e=0;e<this.hierarchical.numTrees();e++)t.push(this.direction.getTreeSize(e));return t},s=(t,e)=>{if(!e[t.id]&&(e[t.id]=!0,this.hierarchical.childrenReference[t.id])){const i=this.hierarchical.childrenReference[t.id];if(i.length>0)for(let t=0;t<i.length;t++)s(this.body.nodes[i[t]],e)}},r=function(e){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e9,o=1e9,n=1e9,s=1e9,r=-1e9;for(const a in e)if(Object.prototype.hasOwnProperty.call(e,a)){const h=t.body.nodes[a],d=t.hierarchical.levels[h.id],l=t.direction.getPosition(h),[c,u]=t._getSpaceAroundNode(h,e);o=Math.min(c,o),n=Math.min(u,n),d<=i&&(s=Math.min(l,s),r=Math.max(l,r))}return[s,r,o,n]},a=(t,e)=>{const i=this.hierarchical.getMaxLevel(t.id),o=this.hierarchical.getMaxLevel(e.id);return Math.min(i,o)},h=(t,e,i)=>{const o=this.hierarchical;for(let n=0;n<e.length;n++){const s=e[n],r=o.distributionOrdering[s];if(r.length>1)for(let e=0;e<r.length-1;e++){const n=r[e],s=r[e+1];o.hasSameParent(n,s)&&o.inSameSubNetwork(n,s)&&t(n,s,i)}}},d=function(i,o){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const h=t.direction.getPosition(i),d=t.direction.getPosition(o),l=Math.abs(d-h),c=t.options.hierarchical.nodeSpacing;if(l>c){const h={},d={};s(i,h),s(o,d);const l=a(i,o),u=r(h,l),p=r(d,l),f=u[1],g=p[0],v=p[2];if(Math.abs(f-g)>c){let i=f-g+c;i<-v+c&&(i=-v+c),i<0&&(t._shiftBlock(o.id,i),e=!0,!0===n&&t._centerParent(o))}}},l=(t,o)=>{const n=o.id,a=o.edges,h=this.hierarchical.levels[o.id],d=this.options.hierarchical.levelSeparation*this.options.hierarchical.levelSeparation,l={},c=[];for(let t=0;t<a.length;t++){const e=a[t];if(e.toId!=e.fromId){const i=e.toId==n?e.from:e.to;l[a[t].id]=i,this.hierarchical.levels[i.id]<h&&c.push(e)}}const u=(t,e)=>{let i=0;for(let o=0;o<e.length;o++)if(void 0!==l[e[o].id]){const n=this.direction.getPosition(l[e[o].id])-t;i+=n/Math.sqrt(n*n+d)}return i},p=(t,e)=>{let i=0;for(let o=0;o<e.length;o++)if(void 0!==l[e[o].id]){const n=this.direction.getPosition(l[e[o].id])-t;i-=d*Math.pow(n*n+d,-1.5)}return i},f=(t,e)=>{let i=this.direction.getPosition(o);const n={};for(let o=0;o<t;o++){const t=u(i,e),s=p(i,e),r=40;if(i-=Math.max(-r,Math.min(r,Math.round(t/s))),void 0!==n[i])break;n[i]=o}return i};let g=f(t,c);(t=>{const n=this.direction.getPosition(o);if(void 0===i[o.id]){const t={};s(o,t),i[o.id]=t}const a=r(i[o.id]),h=a[2],d=a[3],l=t-n;let c=0;l>0?c=Math.min(l,d-this.options.hierarchical.nodeSpacing):l<0&&(c=-Math.min(-l,h-this.options.hierarchical.nodeSpacing)),0!=c&&(this._shiftBlock(o.id,c),e=!0)})(g),g=f(t,a),(t=>{const i=this.direction.getPosition(o),[n,s]=this._getSpaceAroundNode(o),r=t-i;let a=i;r>0?a=Math.min(i+(s-this.options.hierarchical.nodeSpacing),t):r<0&&(a=Math.max(i-(n-this.options.hierarchical.nodeSpacing),t)),a!==i&&(this.direction.setPosition(o,a),e=!0)})(g)},c=t=>{let i=this.hierarchical.getLevels();i=P_(i).call(i);for(let o=0;o<t;o++){e=!1;for(let t=0;t<i.length;t++){const e=i[t],o=this.hierarchical.distributionOrdering[e];for(let t=0;t<o.length;t++)l(1e3,o[t])}if(!0!==e)break}},u=t=>{let i=this.hierarchical.getLevels();i=P_(i).call(i);for(let o=0;o<t&&(e=!1,h(d,i,!0),!0===e);o++);},p=()=>{for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&this._centerParent(this.body.nodes[t])},f=()=>{let t=this.hierarchical.getLevels();t=P_(t).call(t);for(let e=0;e<t.length;e++){const i=t[e],o=this.hierarchical.distributionOrdering[i];for(let t=0;t<o.length;t++)this._centerParent(o[t])}};!0===this.options.hierarchical.blockShifting&&(u(5),p()),!0===this.options.hierarchical.edgeMinimization&&c(20),!0===this.options.hierarchical.parentCentralization&&f(),(()=>{const t=n();let e=0;for(let i=0;i<t.length-1;i++){e+=t[i].max-t[i+1].min+this.options.hierarchical.treeSpacing,o(i+1,e)}})()}_getSpaceAroundNode(t,e){let i=!0;void 0===e&&(i=!1);const o=this.hierarchical.levels[t.id];if(void 0!==o){const n=this.hierarchical.distributionIndex[t.id],s=this.direction.getPosition(t),r=this.hierarchical.distributionOrdering[o];let a=1e9,h=1e9;if(0!==n){const t=r[n-1];if(!0===i&&void 0===e[t.id]||!1===i){a=s-this.direction.getPosition(t)}}if(n!=r.length-1){const t=r[n+1];if(!0===i&&void 0===e[t.id]||!1===i){const e=this.direction.getPosition(t);h=Math.min(h,e-s)}}return[a,h]}return[0,0]}_centerParent(t){if(this.hierarchical.parentReference[t.id]){const e=this.hierarchical.parentReference[t.id];for(let t=0;t<e.length;t++){const i=e[t],o=this.body.nodes[i],n=this.hierarchical.childrenReference[i];if(void 0!==n){const t=this._getCenterPosition(n),e=this.direction.getPosition(o),[i,s]=this._getSpaceAroundNode(o),r=e-t;(r<0&&Math.abs(r)<s-this.options.hierarchical.nodeSpacing||r>0&&Math.abs(r)<i-this.options.hierarchical.nodeSpacing)&&this.direction.setPosition(o,t)}}}}_placeNodesByHierarchy(t){this.positionedNodes={};for(const i in t)if(Object.prototype.hasOwnProperty.call(t,i)){var e;let o=cp(t[i]);o=this._indexArrayToNodes(o),NO(e=this.direction).call(e,o);let n=0;for(let t=0;t<o.length;t++){const e=o[t];if(void 0===this.positionedNodes[e.id]){const s=this.options.hierarchical.nodeSpacing;let r=s*n;n>0&&(r=this.direction.getPosition(o[t-1])+s),this.direction.setPosition(e,r,i),this._validatePositionAndContinue(e,i,r),n++}}}}_placeBranchNodes(t,e){var i;const o=this.hierarchical.childrenReference[t];if(void 0===o)return;const n=[];for(let t=0;t<o.length;t++)n.push(this.body.nodes[o[t]]);NO(i=this.direction).call(i,n);for(let i=0;i<n.length;i++){const o=n[i],s=this.hierarchical.levels[o.id];if(!(s>e&&void 0===this.positionedNodes[o.id]))return;{const e=this.options.hierarchical.nodeSpacing;let r;r=0===i?this.direction.getPosition(this.body.nodes[t]):this.direction.getPosition(n[i-1])+e,this.direction.setPosition(o,r,s),this._validatePositionAndContinue(o,s,r)}}const s=this._getCenterPosition(n);this.direction.setPosition(this.body.nodes[t],s,e)}_validatePositionAndContinue(t,e,i){if(this.hierarchical.isTree){if(void 0!==this.lastNodeOnLevel[e]){const o=this.direction.getPosition(this.body.nodes[this.lastNodeOnLevel[e]]);if(i-o<this.options.hierarchical.nodeSpacing){const n=o+this.options.hierarchical.nodeSpacing-i,s=this._findCommonParent(this.lastNodeOnLevel[e],t.id);this._shiftBlock(s.withChild,n)}}this.lastNodeOnLevel[e]=t.id,this.positionedNodes[t.id]=!0,this._placeBranchNodes(t.id,e)}}_indexArrayToNodes(t){const e=[];for(let i=0;i<t.length;i++)e.push(this.body.nodes[t[i]]);return e}_getDistribution(){const t={};let e,i;for(e in this.body.nodes)if(Object.prototype.hasOwnProperty.call(this.body.nodes,e)){i=this.body.nodes[e];const o=void 0===this.hierarchical.levels[e]?0:this.hierarchical.levels[e];this.direction.fix(i,o),void 0===t[o]&&(t[o]={}),t[o][e]=i}return t}_getActiveEdges(t){const e=[];return bs(t.edges,t=>{var i;-1!==zr(i=this.body.edgeIndices).call(i,t.id)&&e.push(t)}),e}_getHubSizes(){const t={};bs(this.body.nodeIndices,e=>{const i=this.body.nodes[e],o=this._getActiveEdges(i).length;t[o]=!0});const e=[];return bs(t,t=>{e.push(Number(t))}),NO(e).call(e,function(t,e){return e-t}),e}_determineLevelsByHubsize(){const t=(t,e)=>{this.hierarchical.levelDownstream(t,e)},e=this._getHubSizes();for(let i=0;i<e.length;++i){const o=e[i];if(0===o)break;bs(this.body.nodeIndices,e=>{const i=this.body.nodes[e];o===this._getActiveEdges(i).length&&this._crawlNetwork(t,e)})}}_determineLevelsCustomCallback(){this._crawlNetwork((t,e,i)=>{let o=this.hierarchical.levels[t.id];void 0===o&&(o=this.hierarchical.levels[t.id]=1e5);const n=(W_.cloneOptions(t,"node"),W_.cloneOptions(e,"node"),void W_.cloneOptions(i,"edge"));this.hierarchical.levels[e.id]=o+n}),this.hierarchical.setMinLevelToZero()}_determineLevelsDirected(){var t;const e=aC(t=this.body.nodeIndices).call(t,(t,e)=>(t.set(e,this.body.nodes[e]),t),new _u);"roots"===this.options.hierarchical.shakeTowards?this.hierarchical.levels=function(t){return kC(e=>{var i,o;return OC(i=Om(o=e.edges).call(o,e=>t.has(e.toId))).call(i,t=>t.from===e)},(t,e)=>e<t,"to",t)}(e):this.hierarchical.levels=function(t){return kC(e=>{var i,o;return OC(i=Om(o=e.edges).call(o,e=>t.has(e.toId))).call(i,t=>t.to===e)},(t,e)=>e>t,"from",t)}(e),this.hierarchical.setMinLevelToZero()}_generateMap(){this._crawlNetwork((t,e)=>{this.hierarchical.levels[e.id]>this.hierarchical.levels[t.id]&&this.hierarchical.addRelation(t.id,e.id)}),this.hierarchical.checkIfTree()}_crawlNetwork(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){},e=arguments.length>1?arguments[1]:void 0;const i={},o=(e,n)=>{if(void 0===i[e.id]){let s;this.hierarchical.setTreeIndex(e,n),i[e.id]=!0;const r=this._getActiveEdges(e);for(let i=0;i<r.length;i++){const a=r[i];!0===a.connected&&(s=a.toId==e.id?a.from:a.to,e.id!=s.id&&(t(e,s,a),o(s,n)))}}};if(void 0===e){let t=0;for(let e=0;e<this.body.nodeIndices.length;e++){const n=this.body.nodeIndices[e];if(void 0===i[n]){const e=this.body.nodes[n];o(e,t),t+=1}}}else{const t=this.body.nodes[e];if(void 0===t)return void console.error("Node not found:",e);o(t)}}_shiftBlock(t,e){const i={},o=t=>{if(i[t])return;i[t]=!0,this.direction.shift(t,e);const n=this.hierarchical.childrenReference[t];if(void 0!==n)for(let t=0;t<n.length;t++)o(n[t])};o(t)}_findCommonParent(t,e){const i={},o=(t,e)=>{const i=this.hierarchical.parentReference[e];if(void 0!==i)for(let e=0;e<i.length;e++){const n=i[e];t[n]=!0,o(t,n)}},n=(t,e)=>{const i=this.hierarchical.parentReference[e];if(void 0!==i)for(let o=0;o<i.length;o++){const s=i[o];if(void 0!==t[s])return{foundParent:s,withChild:e};const r=n(t,s);if(null!==r.foundParent)return r}return{foundParent:null,withChild:e}};return o(i,t),n(i,e)}setDirectionStrategy(){const t="UD"===this.options.hierarchical.direction||"DU"===this.options.hierarchical.direction;this.direction=t?new dC(this):new lC(this)}_getCenterPosition(t){let e=1e9,i=-1e9;for(let o=0;o<t.length;o++){let n;if(void 0!==t[o].id)n=t[o];else{const e=t[o];n=this.body.nodes[e]}const s=this.direction.getPosition(n);e=Math.min(e,s),i=Math.max(i,s)}return.5*(e+i)}}class MC{constructor(t,e,i,o){var n,s;this.body=t,this.canvas=e,this.selectionHandler=i,this.interactionHandler=o,this.editMode=!1,this.manipulationDiv=void 0,this.editModeDiv=void 0,this.closeDiv=void 0,this._domEventListenerCleanupQueue=[],this.temporaryUIFunctions={},this.temporaryEventFunctions=[],this.touchTime=0,this.temporaryIds={nodes:[],edges:[]},this.guiEnabled=!1,this.inMode=!1,this.selectedControlNode=void 0,this.options={},this.defaultOptions={enabled:!1,initiallyActive:!1,addNode:!0,addEdge:!0,editNode:void 0,editEdge:!0,deleteNode:!0,deleteEdge:!0,controlNodeStyle:{shape:"dot",size:6,color:{background:"#ff0000",border:"#3c3c3c",highlight:{background:"#07f968",border:"#3c3c3c"}},borderWidth:2,borderWidthSelected:2}},Zi(this.options,this.defaultOptions),this.body.emitter.on("destroy",()=>{this._clean()}),this.body.emitter.on("_dataChanged",no(n=this._restore).call(n,this)),this.body.emitter.on("_resetData",no(s=this._restore).call(s,this))}_restore(){!1!==this.inMode&&(!0===this.options.initiallyActive?this.enableEditMode():this.disableEditMode())}setOptions(t,e,i){void 0!==e&&(void 0!==e.locale?this.options.locale=e.locale:this.options.locale=i.locale,void 0!==e.locales?this.options.locales=e.locales:this.options.locales=i.locales),void 0!==t&&("boolean"==typeof t?this.options.enabled=t:(this.options.enabled=!0,gs(this.options,t)),!0===this.options.initiallyActive&&(this.editMode=!0),this._setup())}toggleEditMode(){!0===this.editMode?this.disableEditMode():this.enableEditMode()}enableEditMode(){this.editMode=!0,this._clean(),!0===this.guiEnabled&&(this.manipulationDiv.style.display="block",this.closeDiv.style.display="block",this.editModeDiv.style.display="none",this.showManipulatorToolbar())}disableEditMode(){this.editMode=!1,this._clean(),!0===this.guiEnabled&&(this.manipulationDiv.style.display="none",this.closeDiv.style.display="none",this.editModeDiv.style.display="block",this._createEditButton())}showManipulatorToolbar(){if(this._clean(),this.manipulationDOM={},!0===this.guiEnabled){var t,e;this.editMode=!0,this.manipulationDiv.style.display="block",this.closeDiv.style.display="block";const i=this.selectionHandler.getSelectedNodeCount(),o=this.selectionHandler.getSelectedEdgeCount(),n=i+o,s=this.options.locales[this.options.locale];let r=!1;!1!==this.options.addNode&&(this._createAddNodeButton(s),r=!0),!1!==this.options.addEdge&&(!0===r?this._createSeperator(1):r=!0,this._createAddEdgeButton(s)),1===i&&"function"==typeof this.options.editNode?(!0===r?this._createSeperator(2):r=!0,this._createEditNodeButton(s)):1===o&&0===i&&!1!==this.options.editEdge&&(!0===r?this._createSeperator(3):r=!0,this._createEditEdgeButton(s)),0!==n&&(i>0&&!1!==this.options.deleteNode||0===i&&!1!==this.options.deleteEdge)&&(!0===r&&this._createSeperator(4),this._createDeleteButton(s)),this._bindElementEvents(this.closeDiv,no(t=this.toggleEditMode).call(t,this)),this._temporaryBindEvent("select",no(e=this.showManipulatorToolbar).call(e,this))}this.body.emitter.emit("_redraw")}addNodeMode(){var t;if(!0!==this.editMode&&this.enableEditMode(),this._clean(),this.inMode="addNode",!0===this.guiEnabled){var e;const t=this.options.locales[this.options.locale];this.manipulationDOM={},this._createBackButton(t),this._createSeperator(),this._createDescription(t.addDescription||this.options.locales.en.addDescription),this._bindElementEvents(this.closeDiv,no(e=this.toggleEditMode).call(e,this))}this._temporaryBindEvent("click",no(t=this._performAddNode).call(t,this))}editNode(){!0!==this.editMode&&this.enableEditMode(),this._clean();const t=this.selectionHandler.getSelectedNodes()[0];if(void 0!==t){if(this.inMode="editNode","function"!=typeof this.options.editNode)throw new Error("No function has been configured to handle the editing of nodes.");if(!0!==t.isCluster){const e=gs({},t.options,!1);if(e.x=t.x,e.y=t.y,2!==this.options.editNode.length)throw new Error("The function for edit does not support two arguments (data, callback)");this.options.editNode(e,t=>{null!=t&&"editNode"===this.inMode&&this.body.data.nodes.getDataSet().update(t),this.showManipulatorToolbar()})}else alert(this.options.locales[this.options.locale].editClusterError||this.options.locales.en.editClusterError)}else this.showManipulatorToolbar()}addEdgeMode(){var t,e,i,o,n;if(!0!==this.editMode&&this.enableEditMode(),this._clean(),this.inMode="addEdge",!0===this.guiEnabled){var s;const t=this.options.locales[this.options.locale];this.manipulationDOM={},this._createBackButton(t),this._createSeperator(),this._createDescription(t.edgeDescription||this.options.locales.en.edgeDescription),this._bindElementEvents(this.closeDiv,no(s=this.toggleEditMode).call(s,this))}this._temporaryBindUI("onTouch",no(t=this._handleConnect).call(t,this)),this._temporaryBindUI("onDragEnd",no(e=this._finishConnect).call(e,this)),this._temporaryBindUI("onDrag",no(i=this._dragControlNode).call(i,this)),this._temporaryBindUI("onRelease",no(o=this._finishConnect).call(o,this)),this._temporaryBindUI("onDragStart",no(n=this._dragStartEdge).call(n,this)),this._temporaryBindUI("onHold",()=>{})}editEdgeMode(){if(!0!==this.editMode&&this.enableEditMode(),this._clean(),this.inMode="editEdge","object"==typeof this.options.editEdge&&"function"==typeof this.options.editEdge.editWithoutDrag&&(this.edgeBeingEditedId=this.selectionHandler.getSelectedEdgeIds()[0],void 0!==this.edgeBeingEditedId)){const t=this.body.edges[this.edgeBeingEditedId];return void this._performEditEdge(t.from.id,t.to.id)}if(!0===this.guiEnabled){var t;const e=this.options.locales[this.options.locale];this.manipulationDOM={},this._createBackButton(e),this._createSeperator(),this._createDescription(e.editEdgeDescription||this.options.locales.en.editEdgeDescription),this._bindElementEvents(this.closeDiv,no(t=this.toggleEditMode).call(t,this))}if(this.edgeBeingEditedId=this.selectionHandler.getSelectedEdgeIds()[0],void 0!==this.edgeBeingEditedId){var e,i,o,n;const t=this.body.edges[this.edgeBeingEditedId],s=this._getNewTargetNode(t.from.x,t.from.y),r=this._getNewTargetNode(t.to.x,t.to.y);this.temporaryIds.nodes.push(s.id),this.temporaryIds.nodes.push(r.id),this.body.nodes[s.id]=s,this.body.nodeIndices.push(s.id),this.body.nodes[r.id]=r,this.body.nodeIndices.push(r.id),this._temporaryBindUI("onTouch",no(e=this._controlNodeTouch).call(e,this)),this._temporaryBindUI("onTap",()=>{}),this._temporaryBindUI("onHold",()=>{}),this._temporaryBindUI("onDragStart",no(i=this._controlNodeDragStart).call(i,this)),this._temporaryBindUI("onDrag",no(o=this._controlNodeDrag).call(o,this)),this._temporaryBindUI("onDragEnd",no(n=this._controlNodeDragEnd).call(n,this)),this._temporaryBindUI("onMouseMove",()=>{}),this._temporaryBindEvent("beforeDrawing",e=>{const i=t.edgeType.findBorderPositions(e);!1===s.selected&&(s.x=i.from.x,s.y=i.from.y),!1===r.selected&&(r.x=i.to.x,r.y=i.to.y)}),this.body.emitter.emit("_redraw")}else this.showManipulatorToolbar()}deleteSelected(){!0!==this.editMode&&this.enableEditMode(),this._clean(),this.inMode="delete";const t=this.selectionHandler.getSelectedNodeIds(),e=this.selectionHandler.getSelectedEdgeIds();let i;if(t.length>0){for(let e=0;e<t.length;e++)if(!0===this.body.nodes[t[e]].isCluster)return void alert(this.options.locales[this.options.locale].deleteClusterError||this.options.locales.en.deleteClusterError);"function"==typeof this.options.deleteNode&&(i=this.options.deleteNode)}else e.length>0&&"function"==typeof this.options.deleteEdge&&(i=this.options.deleteEdge);if("function"==typeof i){const o={nodes:t,edges:e};if(2!==i.length)throw new Error("The function for delete does not support two arguments (data, callback)");i(o,t=>{null!=t&&"delete"===this.inMode?(this.body.data.edges.getDataSet().remove(t.edges),this.body.data.nodes.getDataSet().remove(t.nodes),this.body.emitter.emit("startSimulation"),this.showManipulatorToolbar()):(this.body.emitter.emit("startSimulation"),this.showManipulatorToolbar())})}else this.body.data.edges.getDataSet().remove(e),this.body.data.nodes.getDataSet().remove(t),this.body.emitter.emit("startSimulation"),this.showManipulatorToolbar()}_setup(){!0===this.options.enabled?(this.guiEnabled=!0,this._createWrappers(),!1===this.editMode?this._createEditButton():this.showManipulatorToolbar()):(this._removeManipulationDOM(),this.guiEnabled=!1)}_createWrappers(){var t,e;(void 0===this.manipulationDiv&&(this.manipulationDiv=document.createElement("div"),this.manipulationDiv.className="vis-manipulation",!0===this.editMode?this.manipulationDiv.style.display="block":this.manipulationDiv.style.display="none",this.canvas.frame.appendChild(this.manipulationDiv)),void 0===this.editModeDiv&&(this.editModeDiv=document.createElement("div"),this.editModeDiv.className="vis-edit-mode",!0===this.editMode?this.editModeDiv.style.display="none":this.editModeDiv.style.display="block",this.canvas.frame.appendChild(this.editModeDiv)),void 0===this.closeDiv)&&(this.closeDiv=document.createElement("button"),this.closeDiv.className="vis-close",this.closeDiv.setAttribute("aria-label",null!==(t=null===(e=this.options.locales[this.options.locale])||void 0===e?void 0:e.close)&&void 0!==t?t:this.options.locales.en.close),this.closeDiv.style.display=this.manipulationDiv.style.display,this.canvas.frame.appendChild(this.closeDiv))}_getNewTargetNode(t,e){const i=gs({},this.options.controlNodeStyle);i.id="targetNode"+H_(),i.hidden=!1,i.physics=!1,i.x=t,i.y=e;const o=this.body.functions.createNode(i);return o.shape.boundingBox={left:t,right:t,top:e,bottom:e},o}_createEditButton(){var t;this._clean(),this.manipulationDOM={},hs(this.editModeDiv);const e=this.options.locales[this.options.locale],i=this._createButton("editMode","vis-edit vis-edit-mode",e.edit||this.options.locales.en.edit);this.editModeDiv.appendChild(i),this._bindElementEvents(i,no(t=this.toggleEditMode).call(t,this))}_clean(){this.inMode=!1,!0===this.guiEnabled&&(hs(this.editModeDiv),hs(this.manipulationDiv),this._cleanupDOMEventListeners()),this._cleanupTemporaryNodesAndEdges(),this._unbindTemporaryUIs(),this._unbindTemporaryEvents(),this.body.emitter.emit("restorePhysics")}_cleanupDOMEventListeners(){for(const e of uh(t=this._domEventListenerCleanupQueue).call(t,0)){var t;e()}}_removeManipulationDOM(){this._clean(),hs(this.manipulationDiv),hs(this.editModeDiv),hs(this.closeDiv),this.manipulationDiv&&this.canvas.frame.removeChild(this.manipulationDiv),this.editModeDiv&&this.canvas.frame.removeChild(this.editModeDiv),this.closeDiv&&this.canvas.frame.removeChild(this.closeDiv),this.manipulationDiv=void 0,this.editModeDiv=void 0,this.closeDiv=void 0}_createSeperator(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this.manipulationDOM["seperatorLineDiv"+t]=document.createElement("div"),this.manipulationDOM["seperatorLineDiv"+t].className="vis-separator-line",this.manipulationDiv.appendChild(this.manipulationDOM["seperatorLineDiv"+t])}_createAddNodeButton(t){var e;const i=this._createButton("addNode","vis-add",t.addNode||this.options.locales.en.addNode);this.manipulationDiv.appendChild(i),this._bindElementEvents(i,no(e=this.addNodeMode).call(e,this))}_createAddEdgeButton(t){var e;const i=this._createButton("addEdge","vis-connect",t.addEdge||this.options.locales.en.addEdge);this.manipulationDiv.appendChild(i),this._bindElementEvents(i,no(e=this.addEdgeMode).call(e,this))}_createEditNodeButton(t){var e;const i=this._createButton("editNode","vis-edit",t.editNode||this.options.locales.en.editNode);this.manipulationDiv.appendChild(i),this._bindElementEvents(i,no(e=this.editNode).call(e,this))}_createEditEdgeButton(t){var e;const i=this._createButton("editEdge","vis-edit",t.editEdge||this.options.locales.en.editEdge);this.manipulationDiv.appendChild(i),this._bindElementEvents(i,no(e=this.editEdgeMode).call(e,this))}_createDeleteButton(t){var e;let i;i=this.options.rtl?"vis-delete-rtl":"vis-delete";const o=this._createButton("delete",i,t.del||this.options.locales.en.del);this.manipulationDiv.appendChild(o),this._bindElementEvents(o,no(e=this.deleteSelected).call(e,this))}_createBackButton(t){var e;const i=this._createButton("back","vis-back",t.back||this.options.locales.en.back);this.manipulationDiv.appendChild(i),this._bindElementEvents(i,no(e=this.showManipulatorToolbar).call(e,this))}_createButton(t,e,i){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"vis-label";return this.manipulationDOM[t+"Div"]=document.createElement("button"),this.manipulationDOM[t+"Div"].className="vis-button "+e,this.manipulationDOM[t+"Label"]=document.createElement("div"),this.manipulationDOM[t+"Label"].className=o,this.manipulationDOM[t+"Label"].innerText=i,this.manipulationDOM[t+"Div"].appendChild(this.manipulationDOM[t+"Label"]),this.manipulationDOM[t+"Div"]}_createDescription(t){this.manipulationDOM.descriptionLabel=document.createElement("div"),this.manipulationDOM.descriptionLabel.className="vis-none",this.manipulationDOM.descriptionLabel.innerText=t,this.manipulationDiv.appendChild(this.manipulationDOM.descriptionLabel)}_temporaryBindEvent(t,e){this.temporaryEventFunctions.push({event:t,boundFunction:e}),this.body.emitter.on(t,e)}_temporaryBindUI(t,e){if(void 0===this.body.eventListeners[t])throw new Error("This UI function does not exist. Typo? You tried: "+t+" possible are: "+_w(cp(this.body.eventListeners)));this.temporaryUIFunctions[t]=this.body.eventListeners[t],this.body.eventListeners[t]=e}_unbindTemporaryUIs(){for(const t in this.temporaryUIFunctions)Object.prototype.hasOwnProperty.call(this.temporaryUIFunctions,t)&&(this.body.eventListeners[t]=this.temporaryUIFunctions[t],delete this.temporaryUIFunctions[t]);this.temporaryUIFunctions={}}_unbindTemporaryEvents(){for(let t=0;t<this.temporaryEventFunctions.length;t++){const e=this.temporaryEventFunctions[t].event,i=this.temporaryEventFunctions[t].boundFunction;this.body.emitter.off(e,i)}this.temporaryEventFunctions=[]}_bindElementEvents(t,e){const i=new Hs(t,{});K_(i,e),this._domEventListenerCleanupQueue.push(()=>{i.destroy()});const o=t=>{let{keyCode:i,key:o}=t;"Enter"!==o&&" "!==o&&13!==i&&32!==i||e()};t.addEventListener("keyup",o,!1),this._domEventListenerCleanupQueue.push(()=>{t.removeEventListener("keyup",o,!1)})}_cleanupTemporaryNodesAndEdges(){for(let i=0;i<this.temporaryIds.edges.length;i++){var t;this.body.edges[this.temporaryIds.edges[i]].disconnect(),delete this.body.edges[this.temporaryIds.edges[i]];const o=zr(t=this.body.edgeIndices).call(t,this.temporaryIds.edges[i]);var e;if(-1!==o)uh(e=this.body.edgeIndices).call(e,o,1)}for(let t=0;t<this.temporaryIds.nodes.length;t++){var i;delete this.body.nodes[this.temporaryIds.nodes[t]];const e=zr(i=this.body.nodeIndices).call(i,this.temporaryIds.nodes[t]);var o;if(-1!==e)uh(o=this.body.nodeIndices).call(o,e,1)}this.temporaryIds={nodes:[],edges:[]}}_controlNodeTouch(t){this.selectionHandler.unselectAll(),this.lastTouch=this.body.functions.getPointer(t.center),this.lastTouch.translation=Zi({},this.body.view.translation)}_controlNodeDragStart(){const t=this.lastTouch,e=this.selectionHandler._pointerToPositionObject(t),i=this.body.nodes[this.temporaryIds.nodes[0]],o=this.body.nodes[this.temporaryIds.nodes[1]],n=this.body.edges[this.edgeBeingEditedId];this.selectedControlNode=void 0;const s=i.isOverlappingWith(e),r=o.isOverlappingWith(e);!0===s?(this.selectedControlNode=i,n.edgeType.from=i):!0===r&&(this.selectedControlNode=o,n.edgeType.to=o),void 0!==this.selectedControlNode&&this.selectionHandler.selectObject(this.selectedControlNode),this.body.emitter.emit("_redraw")}_controlNodeDrag(t){this.body.emitter.emit("disablePhysics");const e=this.body.functions.getPointer(t.center),i=this.canvas.DOMtoCanvas(e);void 0!==this.selectedControlNode?(this.selectedControlNode.x=i.x,this.selectedControlNode.y=i.y):this.interactionHandler.onDrag(t),this.body.emitter.emit("_redraw")}_controlNodeDragEnd(t){const e=this.body.functions.getPointer(t.center),i=this.selectionHandler._pointerToPositionObject(e),o=this.body.edges[this.edgeBeingEditedId];if(void 0===this.selectedControlNode)return;this.selectionHandler.unselectAll();const n=this.selectionHandler._getAllNodesOverlappingWith(i);let s;for(let t=n.length-1;t>=0;t--)if(n[t]!==this.selectedControlNode.id){s=this.body.nodes[n[t]];break}if(void 0!==s&&void 0!==this.selectedControlNode)if(!0===s.isCluster)alert(this.options.locales[this.options.locale].createEdgeError||this.options.locales.en.createEdgeError);else{const t=this.body.nodes[this.temporaryIds.nodes[0]];this.selectedControlNode.id===t.id?this._performEditEdge(s.id,o.to.id):this._performEditEdge(o.from.id,s.id)}else o.updateEdgeType(),this.body.emitter.emit("restorePhysics");this.body.emitter.emit("_redraw")}_handleConnect(t){if((new Date).valueOf()-this.touchTime>100){this.lastTouch=this.body.functions.getPointer(t.center),this.lastTouch.translation=Zi({},this.body.view.translation),this.interactionHandler.drag.pointer=this.lastTouch,this.interactionHandler.drag.translation=this.lastTouch.translation;const e=this.lastTouch,i=this.selectionHandler.getNodeAt(e);if(void 0!==i)if(!0===i.isCluster)alert(this.options.locales[this.options.locale].createEdgeError||this.options.locales.en.createEdgeError);else{const t=this._getNewTargetNode(i.x,i.y);this.body.nodes[t.id]=t,this.body.nodeIndices.push(t.id);const e=this.body.functions.createEdge({id:"connectionEdge"+H_(),from:i.id,to:t.id,physics:!1,smooth:{enabled:!0,type:"continuous",roundness:.5}});this.body.edges[e.id]=e,this.body.edgeIndices.push(e.id),this.temporaryIds.nodes.push(t.id),this.temporaryIds.edges.push(e.id)}this.touchTime=(new Date).valueOf()}}_dragControlNode(t){const e=this.body.functions.getPointer(t.center),i=this.selectionHandler._pointerToPositionObject(e);let o;void 0!==this.temporaryIds.edges[0]&&(o=this.body.edges[this.temporaryIds.edges[0]].fromId);const n=this.selectionHandler._getAllNodesOverlappingWith(i);let s;for(let t=n.length-1;t>=0;t--){var r;if(-1===zr(r=this.temporaryIds.nodes).call(r,n[t])){s=this.body.nodes[n[t]];break}}if(t.controlEdge={from:o,to:s?s.id:void 0},this.selectionHandler.generateClickEvent("controlNodeDragging",t,e),void 0!==this.temporaryIds.nodes[0]){const t=this.body.nodes[this.temporaryIds.nodes[0]];t.x=this.canvas._XconvertDOMtoCanvas(e.x),t.y=this.canvas._YconvertDOMtoCanvas(e.y),this.body.emitter.emit("_redraw")}else this.interactionHandler.onDrag(t)}_finishConnect(t){const e=this.body.functions.getPointer(t.center),i=this.selectionHandler._pointerToPositionObject(e);let o;void 0!==this.temporaryIds.edges[0]&&(o=this.body.edges[this.temporaryIds.edges[0]].fromId);const n=this.selectionHandler._getAllNodesOverlappingWith(i);let s;for(let t=n.length-1;t>=0;t--){var r;if(-1===zr(r=this.temporaryIds.nodes).call(r,n[t])){s=this.body.nodes[n[t]];break}}this._cleanupTemporaryNodesAndEdges(),void 0!==s&&(!0===s.isCluster?alert(this.options.locales[this.options.locale].createEdgeError||this.options.locales.en.createEdgeError):void 0!==this.body.nodes[o]&&void 0!==this.body.nodes[s.id]&&this._performAddEdge(o,s.id)),t.controlEdge={from:o,to:s?s.id:void 0},this.selectionHandler.generateClickEvent("controlNodeDragEnd",t,e),this.body.emitter.emit("_redraw")}_dragStartEdge(t){const e=this.lastTouch;this.selectionHandler.generateClickEvent("dragStart",t,e,void 0,!0)}_performAddNode(t){const e={id:H_(),x:t.pointer.canvas.x,y:t.pointer.canvas.y,label:"new"};if("function"==typeof this.options.addNode){if(2!==this.options.addNode.length)throw this.showManipulatorToolbar(),new Error("The function for add does not support two arguments (data,callback)");this.options.addNode(e,t=>{null!=t&&"addNode"===this.inMode&&this.body.data.nodes.getDataSet().add(t),this.showManipulatorToolbar()})}else this.body.data.nodes.getDataSet().add(e),this.showManipulatorToolbar()}_performAddEdge(t,e){const i={from:t,to:e};if("function"==typeof this.options.addEdge){if(2!==this.options.addEdge.length)throw new Error("The function for connect does not support two arguments (data,callback)");this.options.addEdge(i,t=>{null!=t&&"addEdge"===this.inMode&&(this.body.data.edges.getDataSet().add(t),this.selectionHandler.unselectAll(),this.showManipulatorToolbar())})}else this.body.data.edges.getDataSet().add(i),this.selectionHandler.unselectAll(),this.showManipulatorToolbar()}_performEditEdge(t,e){const i={id:this.edgeBeingEditedId,from:t,to:e,label:this.body.data.edges.get(this.edgeBeingEditedId).label};let o=this.options.editEdge;if("object"==typeof o&&(o=o.editWithoutDrag),"function"==typeof o){if(2!==o.length)throw new Error("The function for edit does not support two arguments (data, callback)");o(i,t=>{null==t||"editEdge"!==this.inMode?(this.body.edges[i.id].updateEdgeType(),this.body.emitter.emit("_redraw"),this.showManipulatorToolbar()):(this.body.data.edges.getDataSet().update(t),this.selectionHandler.unselectAll(),this.showManipulatorToolbar())})}else this.body.data.edges.getDataSet().update(i),this.selectionHandler.unselectAll(),this.showManipulatorToolbar()}}const DC="string",IC="boolean",PC="number",BC="array",zC="object",FC=["arrow","bar","box","circle","crow","curve","diamond","image","inv_curve","inv_triangle","triangle","vee"],NC={borderWidth:{number:PC},borderWidthSelected:{number:PC,undefined:"undefined"},brokenImage:{string:DC,undefined:"undefined"},chosen:{label:{boolean:IC,function:"function"},node:{boolean:IC,function:"function"},__type__:{object:zC,boolean:IC}},color:{border:{string:DC},background:{string:DC},highlight:{border:{string:DC},background:{string:DC},__type__:{object:zC,string:DC}},hover:{border:{string:DC},background:{string:DC},__type__:{object:zC,string:DC}},__type__:{object:zC,string:DC}},opacity:{number:PC,undefined:"undefined"},fixed:{x:{boolean:IC},y:{boolean:IC},__type__:{object:zC,boolean:IC}},font:{align:{string:DC},color:{string:DC},size:{number:PC},face:{string:DC},background:{string:DC},strokeWidth:{number:PC},strokeColor:{string:DC},vadjust:{number:PC},multi:{boolean:IC,string:DC},bold:{color:{string:DC},size:{number:PC},face:{string:DC},mod:{string:DC},vadjust:{number:PC},__type__:{object:zC,string:DC}},boldital:{color:{string:DC},size:{number:PC},face:{string:DC},mod:{string:DC},vadjust:{number:PC},__type__:{object:zC,string:DC}},ital:{color:{string:DC},size:{number:PC},face:{string:DC},mod:{string:DC},vadjust:{number:PC},__type__:{object:zC,string:DC}},mono:{color:{string:DC},size:{number:PC},face:{string:DC},mod:{string:DC},vadjust:{number:PC},__type__:{object:zC,string:DC}},__type__:{object:zC,string:DC}},group:{string:DC,number:PC,undefined:"undefined"},heightConstraint:{minimum:{number:PC},valign:{string:DC},__type__:{object:zC,boolean:IC,number:PC}},hidden:{boolean:IC},icon:{face:{string:DC},code:{string:DC},size:{number:PC},color:{string:DC},weight:{string:DC,number:PC},__type__:{object:zC}},id:{string:DC,number:PC},image:{selected:{string:DC,undefined:"undefined"},unselected:{string:DC,undefined:"undefined"},__type__:{object:zC,string:DC}},imagePadding:{top:{number:PC},right:{number:PC},bottom:{number:PC},left:{number:PC},__type__:{object:zC,number:PC}},label:{string:DC,undefined:"undefined"},labelHighlightBold:{boolean:IC},level:{number:PC,undefined:"undefined"},margin:{top:{number:PC},right:{number:PC},bottom:{number:PC},left:{number:PC},__type__:{object:zC,number:PC}},mass:{number:PC},physics:{boolean:IC},scaling:{min:{number:PC},max:{number:PC},label:{enabled:{boolean:IC},min:{number:PC},max:{number:PC},maxVisible:{number:PC},drawThreshold:{number:PC},__type__:{object:zC,boolean:IC}},customScalingFunction:{function:"function"},__type__:{object:zC}},shadow:{enabled:{boolean:IC},color:{string:DC},size:{number:PC},x:{number:PC},y:{number:PC},__type__:{object:zC,boolean:IC}},shape:{string:["custom","ellipse","circle","database","box","text","image","circularImage","diamond","dot","star","triangle","triangleDown","square","icon","hexagon"]},ctxRenderer:{function:"function"},shapeProperties:{borderDashes:{boolean:IC,array:BC},borderRadius:{number:PC},interpolation:{boolean:IC},useImageSize:{boolean:IC},useBorderWithImage:{boolean:IC},coordinateOrigin:{string:["center","top-left"]},__type__:{object:zC}},size:{number:PC},title:{string:DC,dom:"dom",undefined:"undefined"},value:{number:PC,undefined:"undefined"},widthConstraint:{minimum:{number:PC},maximum:{number:PC},__type__:{object:zC,boolean:IC,number:PC}},x:{number:PC},y:{number:PC},__type__:{object:zC}},AC={configure:{enabled:{boolean:IC},filter:{boolean:IC,string:DC,array:BC,function:"function"},container:{dom:"dom"},showButton:{boolean:IC},__type__:{object:zC,boolean:IC,string:DC,array:BC,function:"function"}},edges:{arrows:{to:{enabled:{boolean:IC},scaleFactor:{number:PC},type:{string:FC},imageHeight:{number:PC},imageWidth:{number:PC},src:{string:DC},__type__:{object:zC,boolean:IC}},middle:{enabled:{boolean:IC},scaleFactor:{number:PC},type:{string:FC},imageWidth:{number:PC},imageHeight:{number:PC},src:{string:DC},__type__:{object:zC,boolean:IC}},from:{enabled:{boolean:IC},scaleFactor:{number:PC},type:{string:FC},imageWidth:{number:PC},imageHeight:{number:PC},src:{string:DC},__type__:{object:zC,boolean:IC}},__type__:{string:["from","to","middle"],object:zC}},endPointOffset:{from:{number:PC},to:{number:PC},__type__:{object:zC,number:PC}},arrowStrikethrough:{boolean:IC},background:{enabled:{boolean:IC},color:{string:DC},size:{number:PC},dashes:{boolean:IC,array:BC},__type__:{object:zC,boolean:IC}},chosen:{label:{boolean:IC,function:"function"},edge:{boolean:IC,function:"function"},__type__:{object:zC,boolean:IC}},color:{color:{string:DC},highlight:{string:DC},hover:{string:DC},inherit:{string:["from","to","both"],boolean:IC},opacity:{number:PC},__type__:{object:zC,string:DC}},dashes:{boolean:IC,array:BC},font:{color:{string:DC},size:{number:PC},face:{string:DC},background:{string:DC},strokeWidth:{number:PC},strokeColor:{string:DC},align:{string:["horizontal","top","middle","bottom"]},vadjust:{number:PC},multi:{boolean:IC,string:DC},bold:{color:{string:DC},size:{number:PC},face:{string:DC},mod:{string:DC},vadjust:{number:PC},__type__:{object:zC,string:DC}},boldital:{color:{string:DC},size:{number:PC},face:{string:DC},mod:{string:DC},vadjust:{number:PC},__type__:{object:zC,string:DC}},ital:{color:{string:DC},size:{number:PC},face:{string:DC},mod:{string:DC},vadjust:{number:PC},__type__:{object:zC,string:DC}},mono:{color:{string:DC},size:{number:PC},face:{string:DC},mod:{string:DC},vadjust:{number:PC},__type__:{object:zC,string:DC}},__type__:{object:zC,string:DC}},hidden:{boolean:IC},hoverWidth:{function:"function",number:PC},label:{string:DC,undefined:"undefined"},labelHighlightBold:{boolean:IC},length:{number:PC,undefined:"undefined"},physics:{boolean:IC},scaling:{min:{number:PC},max:{number:PC},label:{enabled:{boolean:IC},min:{number:PC},max:{number:PC},maxVisible:{number:PC},drawThreshold:{number:PC},__type__:{object:zC,boolean:IC}},customScalingFunction:{function:"function"},__type__:{object:zC}},selectionWidth:{function:"function",number:PC},selfReferenceSize:{number:PC},selfReference:{size:{number:PC},angle:{number:PC},renderBehindTheNode:{boolean:IC},__type__:{object:zC}},shadow:{enabled:{boolean:IC},color:{string:DC},size:{number:PC},x:{number:PC},y:{number:PC},__type__:{object:zC,boolean:IC}},smooth:{enabled:{boolean:IC},type:{string:["dynamic","continuous","discrete","diagonalCross","straightCross","horizontal","vertical","curvedCW","curvedCCW","cubicBezier"]},roundness:{number:PC},forceDirection:{string:["horizontal","vertical","none"],boolean:IC},__type__:{object:zC,boolean:IC}},title:{string:DC,undefined:"undefined"},width:{number:PC},widthConstraint:{maximum:{number:PC},__type__:{object:zC,boolean:IC,number:PC}},value:{number:PC,undefined:"undefined"},__type__:{object:zC}},groups:{useDefaultGroups:{boolean:IC},__any__:NC,__type__:{object:zC}},interaction:{dragNodes:{boolean:IC},dragView:{boolean:IC},hideEdgesOnDrag:{boolean:IC},hideEdgesOnZoom:{boolean:IC},hideNodesOnDrag:{boolean:IC},hover:{boolean:IC},keyboard:{enabled:{boolean:IC},speed:{x:{number:PC},y:{number:PC},zoom:{number:PC},__type__:{object:zC}},bindToWindow:{boolean:IC},autoFocus:{boolean:IC},__type__:{object:zC,boolean:IC}},multiselect:{boolean:IC},navigationButtons:{boolean:IC},selectable:{boolean:IC},selectConnectedEdges:{boolean:IC},hoverConnectedEdges:{boolean:IC},tooltipDelay:{number:PC},zoomView:{boolean:IC},zoomSpeed:{number:PC},__type__:{object:zC}},layout:{randomSeed:{undefined:"undefined",number:PC,string:DC},improvedLayout:{boolean:IC},clusterThreshold:{number:PC},hierarchical:{enabled:{boolean:IC},levelSeparation:{number:PC},nodeSpacing:{number:PC},treeSpacing:{number:PC},blockShifting:{boolean:IC},edgeMinimization:{boolean:IC},parentCentralization:{boolean:IC},direction:{string:["UD","DU","LR","RL"]},sortMethod:{string:["hubsize","directed"]},shakeTowards:{string:["leaves","roots"]},__type__:{object:zC,boolean:IC}},__type__:{object:zC}},manipulation:{enabled:{boolean:IC},initiallyActive:{boolean:IC},addNode:{boolean:IC,function:"function"},addEdge:{boolean:IC,function:"function"},editNode:{function:"function"},editEdge:{editWithoutDrag:{function:"function"},__type__:{object:zC,boolean:IC,function:"function"}},deleteNode:{boolean:IC,function:"function"},deleteEdge:{boolean:IC,function:"function"},controlNodeStyle:NC,__type__:{object:zC,boolean:IC}},nodes:NC,physics:{enabled:{boolean:IC},barnesHut:{theta:{number:PC},gravitationalConstant:{number:PC},centralGravity:{number:PC},springLength:{number:PC},springConstant:{number:PC},damping:{number:PC},avoidOverlap:{number:PC},__type__:{object:zC}},forceAtlas2Based:{theta:{number:PC},gravitationalConstant:{number:PC},centralGravity:{number:PC},springLength:{number:PC},springConstant:{number:PC},damping:{number:PC},avoidOverlap:{number:PC},__type__:{object:zC}},repulsion:{centralGravity:{number:PC},springLength:{number:PC},springConstant:{number:PC},nodeDistance:{number:PC},damping:{number:PC},__type__:{object:zC}},hierarchicalRepulsion:{centralGravity:{number:PC},springLength:{number:PC},springConstant:{number:PC},nodeDistance:{number:PC},damping:{number:PC},avoidOverlap:{number:PC},__type__:{object:zC}},maxVelocity:{number:PC},minVelocity:{number:PC},solver:{string:["barnesHut","repulsion","hierarchicalRepulsion","forceAtlas2Based"]},stabilization:{enabled:{boolean:IC},iterations:{number:PC},updateInterval:{number:PC},onlyDynamicEdges:{boolean:IC},fit:{boolean:IC},__type__:{object:zC,boolean:IC}},timestep:{number:PC},adaptiveTimestep:{boolean:IC},wind:{x:{number:PC},y:{number:PC},__type__:{object:zC}},__type__:{object:zC,boolean:IC}},autoResize:{boolean:IC},clickToUse:{boolean:IC},locale:{string:DC},locales:{__any__:{any:"any"},__type__:{object:zC}},height:{string:DC},width:{string:DC},__type__:{object:zC}},RC={nodes:{borderWidth:[1,0,10,1],borderWidthSelected:[2,0,10,1],color:{border:["color","#2B7CE9"],background:["color","#97C2FC"],highlight:{border:["color","#2B7CE9"],background:["color","#D2E5FF"]},hover:{border:["color","#2B7CE9"],background:["color","#D2E5FF"]}},opacity:[0,0,1,.1],fixed:{x:!1,y:!1},font:{color:["color","#343434"],size:[14,0,100,1],face:["arial","verdana","tahoma"],background:["color","none"],strokeWidth:[0,0,50,1],strokeColor:["color","#ffffff"]},hidden:!1,labelHighlightBold:!0,physics:!0,scaling:{min:[10,0,200,1],max:[30,0,200,1],label:{enabled:!1,min:[14,0,200,1],max:[30,0,200,1],maxVisible:[30,0,200,1],drawThreshold:[5,0,20,1]}},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:[10,0,20,1],x:[5,-30,30,1],y:[5,-30,30,1]},shape:["ellipse","box","circle","database","diamond","dot","square","star","text","triangle","triangleDown","hexagon"],shapeProperties:{borderDashes:!1,borderRadius:[6,0,20,1],interpolation:!0,useImageSize:!1},size:[25,0,200,1]},edges:{arrows:{to:{enabled:!1,scaleFactor:[1,0,3,.05],type:"arrow"},middle:{enabled:!1,scaleFactor:[1,0,3,.05],type:"arrow"},from:{enabled:!1,scaleFactor:[1,0,3,.05],type:"arrow"}},endPointOffset:{from:[0,-10,10,1],to:[0,-10,10,1]},arrowStrikethrough:!0,color:{color:["color","#848484"],highlight:["color","#848484"],hover:["color","#848484"],inherit:["from","to","both",!0,!1],opacity:[1,0,1,.05]},dashes:!1,font:{color:["color","#343434"],size:[14,0,100,1],face:["arial","verdana","tahoma"],background:["color","none"],strokeWidth:[2,0,50,1],strokeColor:["color","#ffffff"],align:["horizontal","top","middle","bottom"]},hidden:!1,hoverWidth:[1.5,0,5,.1],labelHighlightBold:!0,physics:!0,scaling:{min:[1,0,100,1],max:[15,0,100,1],label:{enabled:!0,min:[14,0,200,1],max:[30,0,200,1],maxVisible:[30,0,200,1],drawThreshold:[5,0,20,1]}},selectionWidth:[1.5,0,5,.1],selfReferenceSize:[20,0,200,1],selfReference:{size:[20,0,200,1],angle:[Math.PI/2,-6*Math.PI,6*Math.PI,Math.PI/8],renderBehindTheNode:!0},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:[10,0,20,1],x:[5,-30,30,1],y:[5,-30,30,1]},smooth:{enabled:!0,type:["dynamic","continuous","discrete","diagonalCross","straightCross","horizontal","vertical","curvedCW","curvedCCW","cubicBezier"],forceDirection:["horizontal","vertical","none"],roundness:[.5,0,1,.05]},width:[1,0,30,1]},layout:{hierarchical:{enabled:!1,levelSeparation:[150,20,500,5],nodeSpacing:[100,20,500,5],treeSpacing:[200,20,500,5],blockShifting:!0,edgeMinimization:!0,parentCentralization:!0,direction:["UD","DU","LR","RL"],sortMethod:["hubsize","directed"],shakeTowards:["leaves","roots"]}},interaction:{dragNodes:!0,dragView:!0,hideEdgesOnDrag:!1,hideEdgesOnZoom:!1,hideNodesOnDrag:!1,hover:!1,keyboard:{enabled:!1,speed:{x:[10,0,40,1],y:[10,0,40,1],zoom:[.02,0,.1,.005]},bindToWindow:!0,autoFocus:!0},multiselect:!1,navigationButtons:!1,selectable:!0,selectConnectedEdges:!0,hoverConnectedEdges:!0,tooltipDelay:[300,0,1e3,25],zoomView:!0,zoomSpeed:[1,.1,2,.1]},manipulation:{enabled:!1,initiallyActive:!1},physics:{enabled:!0,barnesHut:{theta:[.5,.1,1,.05],gravitationalConstant:[-2e3,-3e4,0,50],centralGravity:[.3,0,10,.05],springLength:[95,0,500,5],springConstant:[.04,0,1.2,.005],damping:[.09,0,1,.01],avoidOverlap:[0,0,1,.01]},forceAtlas2Based:{theta:[.5,.1,1,.05],gravitationalConstant:[-50,-500,0,1],centralGravity:[.01,0,1,.005],springLength:[95,0,500,5],springConstant:[.08,0,1.2,.005],damping:[.4,0,1,.01],avoidOverlap:[0,0,1,.01]},repulsion:{centralGravity:[.2,0,10,.05],springLength:[200,0,500,5],springConstant:[.05,0,1.2,.005],nodeDistance:[100,0,500,5],damping:[.09,0,1,.01]},hierarchicalRepulsion:{centralGravity:[.2,0,10,.05],springLength:[100,0,500,5],springConstant:[.01,0,1.2,.005],nodeDistance:[120,0,500,5],damping:[.09,0,1,.01],avoidOverlap:[0,0,1,.01]},maxVelocity:[50,0,150,1],minVelocity:[.1,.01,.5,.01],solver:["barnesHut","forceAtlas2Based","repulsion","hierarchicalRepulsion"],timestep:[.5,.01,1,.01],wind:{x:[0,-10,10,.1],y:[0,-10,10,.1]}}},jC=(t,e,i)=>{var o;return!(!qa(t).call(t,"physics")||!qa(o=RC.physics.solver).call(o,e)||i.physics.solver===e||"wind"===e)};var LC=Object.freeze({__proto__:null,allOptions:AC,configuratorHideOption:jC,configureOptions:RC});class HC{constructor(){}getDistances(t,e,i){const o={},n=t.edges;for(let t=0;t<e.length;t++){const i={};o[e[t]]=i;for(let o=0;o<e.length;o++)i[e[o]]=t==o?0:1e9}for(let t=0;t<i.length;t++){const e=n[i[t]];!0===e.connected&&void 0!==o[e.fromId]&&void 0!==o[e.toId]&&(o[e.fromId][e.toId]=1,o[e.toId][e.fromId]=1)}const s=e.length;for(let t=0;t<s;t++){const i=e[t],n=o[i];for(let t=0;t<s-1;t++){const r=e[t],a=o[r];for(let h=t+1;h<s;h++){const t=e[h],s=o[t],d=Math.min(a[t],a[i]+n[t]);a[t]=d,s[r]=d}}}return o}}class WC{constructor(t,e,i){this.body=t,this.springLength=e,this.springConstant=i,this.distanceSolver=new HC}setOptions(t){t&&(t.springLength&&(this.springLength=t.springLength),t.springConstant&&(this.springConstant=t.springConstant))}solve(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const o=this.distanceSolver.getDistances(this.body,t,e);this._createL_matrix(o),this._createK_matrix(o),this._createE_matrix();let n=0;const s=Math.max(1e3,Math.min(10*this.body.nodeIndices.length,6e3));let r=1e9,a=0,h=0,d=0,l=0,c=0;for(;r>.01&&n<s;)for(n+=1,[a,r,h,d]=this._getHighestEnergyNode(i),l=r,c=0;l>1&&c<5;)c+=1,this._moveNode(a,h,d),[l,h,d]=this._getEnergy(a)}_getHighestEnergyNode(t){const e=this.body.nodeIndices,i=this.body.nodes;let o=0,n=e[0],s=0,r=0;for(let a=0;a<e.length;a++){const h=e[a];if(!0!==i[h].predefinedPosition||!0===i[h].isCluster&&!0===t||!0!==i[h].options.fixed.x||!0!==i[h].options.fixed.y){const[t,e,i]=this._getEnergy(h);o<t&&(o=t,n=h,s=e,r=i)}}return[n,o,s,r]}_getEnergy(t){const[e,i]=this.E_sums[t];return[Math.sqrt(e**2+i**2),e,i]}_moveNode(t,e,i){const o=this.body.nodeIndices,n=this.body.nodes;let s=0,r=0,a=0;const h=n[t].x,d=n[t].y,l=this.K_matrix[t],c=this.L_matrix[t];for(let e=0;e<o.length;e++){const i=o[e];if(i!==t){const t=n[i].x,e=n[i].y,o=l[i],u=c[i],p=1/((h-t)**2+(d-e)**2)**1.5;s+=o*(1-u*(d-e)**2*p),r+=o*(u*(h-t)*(d-e)*p),a+=o*(1-u*(h-t)**2*p)}}const u=(e/s+i/r)/(r/s-a/r),p=-(r*u+e)/s;n[t].x+=p,n[t].y+=u,this._updateE_matrix(t)}_createL_matrix(t){const e=this.body.nodeIndices,i=this.springLength;this.L_matrix=[];for(let o=0;o<e.length;o++){this.L_matrix[e[o]]={};for(let n=0;n<e.length;n++)this.L_matrix[e[o]][e[n]]=i*t[e[o]][e[n]]}}_createK_matrix(t){const e=this.body.nodeIndices,i=this.springConstant;this.K_matrix=[];for(let o=0;o<e.length;o++){this.K_matrix[e[o]]={};for(let n=0;n<e.length;n++)this.K_matrix[e[o]][e[n]]=i*t[e[o]][e[n]]**-2}}_createE_matrix(){const t=this.body.nodeIndices,e=this.body.nodes;this.E_matrix={},this.E_sums={};for(let e=0;e<t.length;e++)this.E_matrix[t[e]]=[];for(let i=0;i<t.length;i++){const o=t[i],n=e[o].x,s=e[o].y;let r=0,a=0;for(let h=i;h<t.length;h++){const d=t[h];if(d!==o){const t=e[d].x,l=e[d].y,c=1/Math.sqrt((n-t)**2+(s-l)**2);this.E_matrix[o][h]=[this.K_matrix[o][d]*(n-t-this.L_matrix[o][d]*(n-t)*c),this.K_matrix[o][d]*(s-l-this.L_matrix[o][d]*(s-l)*c)],this.E_matrix[d][i]=this.E_matrix[o][h],r+=this.E_matrix[o][h][0],a+=this.E_matrix[o][h][1]}}this.E_sums[o]=[r,a]}}_updateE_matrix(t){const e=this.body.nodeIndices,i=this.body.nodes,o=this.E_matrix[t],n=this.K_matrix[t],s=this.L_matrix[t],r=i[t].x,a=i[t].y;let h=0,d=0;for(let l=0;l<e.length;l++){const c=e[l];if(c!==t){const t=o[l],e=t[0],u=t[1],p=i[c].x,f=i[c].y,g=1/Math.sqrt((r-p)**2+(a-f)**2),v=n[c]*(r-p-s[c]*(r-p)*g),m=n[c]*(a-f-s[c]*(a-f)*g);o[l]=[v,m],h+=v,d+=m;const y=this.E_sums[c];y[0]+=v-e,y[1]+=m-u}}this.E_sums[t]=[h,d]}}function VC(t,e,i){var o,n,s,r;if(!(this instanceof VC))throw new SyntaxError("Constructor must be called with the new operator");this.options={},this.defaultOptions={locale:"en",locales:Ad,clickToUse:!1},Zi(this.options,this.defaultOptions),this.body={container:t,nodes:{},nodeIndices:[],edges:{},edgeIndices:[],emitter:{on:no(o=this.on).call(o,this),off:no(n=this.off).call(n,this),emit:no(s=this.emit).call(s,this),once:no(r=this.once).call(r,this)},eventListeners:{onTap:function(){},onTouch:function(){},onDoubleTap:function(){},onHold:function(){},onDragStart:function(){},onDrag:function(){},onDragEnd:function(){},onMouseWheel:function(){},onPinch:function(){},onMouseMove:function(){},onRelease:function(){},onContext:function(){}},data:{nodes:null,edges:null},functions:{createNode:function(){},createEdge:function(){},getPointer:function(){}},modules:{},view:{scale:1,translation:{x:0,y:0}},selectionBox:{show:!1,position:{start:{x:0,y:0},end:{x:0,y:0}}}},this.bindEventListeners(),this.images=new Sl(()=>this.body.emitter.emit("_requestRedraw")),this.groups=new xu,this.canvas=new Z_(this.body),this.selectionHandler=new tO(this.body,this.canvas),this.interactionHandler=new tx(this.body,this.canvas,this.selectionHandler),this.view=new Q_(this.body,this.canvas),this.renderer=new U_(this.body,this.canvas),this.physics=new y_(this.body),this.layoutEngine=new TC(this.body),this.clustering=new q_(this.body),this.manipulation=new MC(this.body,this.canvas,this.selectionHandler,this.interactionHandler),this.nodesHandler=new Kb(this.body,this.images,this.groups,this.layoutEngine),this.edgesHandler=new $w(this.body,this.images,this.groups),this.body.modules.kamadaKawai=new WC(this.body,150,.05),this.body.modules.clustering=this.clustering,this.canvas._create(),this.setOptions(i),this.setData(e)}vo(VC.prototype),VC.prototype.setOptions=function(t){if(null===t&&(t=void 0),void 0!==t){!0===qs.validate(t,AC)&&console.error("%cErrors have been found in the supplied options object.",Vs);if(ps(["locale","locales","clickToUse"],this.options,t),void 0!==t.locale&&(t.locale=function(t,e){try{const[o,n]=e.split(/[-_ /]/,2),s=null!=o?o.toLowerCase():null,r=null!=n?n.toUpperCase():null;if(s&&r){const e=s+"-"+r;if(Object.prototype.hasOwnProperty.call(t,e))return e;var i;console.warn(Cl(i="Unknown variant ".concat(r," of language ")).call(i,s,"."))}if(s){const e=s;if(Object.prototype.hasOwnProperty.call(t,e))return e;console.warn("Unknown language ".concat(s))}return console.warn("Unknown locale ".concat(e,", falling back to English.")),"en"}catch(t){return console.error(t),console.warn("Unexpected error while normalizing locale ".concat(e,", falling back to English.")),"en"}}(t.locales||this.options.locales,t.locale)),t=this.layoutEngine.setOptions(t.layout,t),this.canvas.setOptions(t),this.groups.setOptions(t.groups),this.nodesHandler.setOptions(t.nodes),this.edgesHandler.setOptions(t.edges),this.physics.setOptions(t.physics),this.manipulation.setOptions(t.manipulation,t,this.options),this.interactionHandler.setOptions(t.interaction),this.renderer.setOptions(t.interaction),this.selectionHandler.setOptions(t.interaction),void 0!==t.groups&&this.body.emitter.emit("refreshNodes"),"configure"in t&&(this.configurator||(this.configurator=new Ls(this,this.body.container,RC,this.canvas.pixelRatio,jC)),this.configurator.setOptions(t.configure)),this.configurator&&!0===this.configurator.options.enabled){const t={nodes:{},edges:{},layout:{},interaction:{},manipulation:{},physics:{},global:{}};gs(t.nodes,this.nodesHandler.options),gs(t.edges,this.edgesHandler.options),gs(t.layout,this.layoutEngine.options),gs(t.interaction,this.selectionHandler.options),gs(t.interaction,this.renderer.options),gs(t.interaction,this.interactionHandler.options),gs(t.manipulation,this.manipulation.options),gs(t.physics,this.physics.options),gs(t.global,this.canvas.options),gs(t.global,this.options),this.configurator.setModuleOptions(t)}void 0!==t.clickToUse?!0===t.clickToUse?void 0===this.activator&&(this.activator=new js(this.canvas.frame),this.activator.on("change",()=>{this.body.emitter.emit("activate")})):(void 0!==this.activator&&(this.activator.destroy(),delete this.activator),this.body.emitter.emit("activate")):this.body.emitter.emit("activate"),this.canvas.setSize(),this.body.emitter.emit("startSimulation")}},VC.prototype._updateVisibleIndices=function(){const t=this.body.nodes,e=this.body.edges;this.body.nodeIndices=[],this.body.edgeIndices=[];for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(this.clustering._isClusteredNode(e)||!1!==t[e].options.hidden||this.body.nodeIndices.push(t[e].id));for(const i in e)if(Object.prototype.hasOwnProperty.call(e,i)){const o=e[i],n=t[o.fromId],s=t[o.toId],r=void 0!==n&&void 0!==s;!this.clustering._isClusteredEdge(i)&&!1===o.options.hidden&&r&&!1===n.options.hidden&&!1===s.options.hidden&&this.body.edgeIndices.push(o.id)}},VC.prototype.bindEventListeners=function(){this.body.emitter.on("_dataChanged",()=>{this.edgesHandler._updateState(),this.body.emitter.emit("_dataUpdated")}),this.body.emitter.on("_dataUpdated",()=>{this.clustering._updateState(),this._updateVisibleIndices(),this._updateValueRange(this.body.nodes),this._updateValueRange(this.body.edges),this.body.emitter.emit("startSimulation"),this.body.emitter.emit("_requestRedraw")})},VC.prototype.setData=function(t){if(this.body.emitter.emit("resetPhysics"),this.body.emitter.emit("_resetData"),this.selectionHandler.unselectAll(),t&&t.dot&&(t.nodes||t.edges))throw new SyntaxError('Data must contain either parameter "dot" or  parameter pair "nodes" and "edges", but not both.');if(this.setOptions(t&&t.options),t&&t.dot){console.warn("The dot property has been deprecated. Please use the static convertDot method to convert DOT into vis.network format and use the normal data format with nodes and edges. This converter is used like this: var data = vis.network.convertDot(dotString);");const e=ld(t.dot);return void this.setData(e)}if(t&&t.gephi){console.warn("The gephi property has been deprecated. Please use the static convertGephi method to convert gephi into vis.network format and use the normal data format with nodes and edges. This converter is used like this: var data = vis.network.convertGephi(gephiJson);");const e=kd(t.gephi);return void this.setData(e)}this.nodesHandler.setData(t&&t.nodes,!0),this.edgesHandler.setData(t&&t.edges,!0),this.body.emitter.emit("_dataChanged"),this.body.emitter.emit("_dataLoaded"),this.body.emitter.emit("initPhysics")},VC.prototype.destroy=function(){this.body.emitter.emit("destroy"),this.body.emitter.off(),this.off(),delete this.groups,delete this.canvas,delete this.selectionHandler,delete this.interactionHandler,delete this.view,delete this.renderer,delete this.physics,delete this.layoutEngine,delete this.clustering,delete this.manipulation,delete this.nodesHandler,delete this.edgesHandler,delete this.configurator,delete this.images;for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&delete this.body.nodes[t];for(const t in this.body.edges)Object.prototype.hasOwnProperty.call(this.body.edges,t)&&delete this.body.edges[t];hs(this.body.container)},VC.prototype._updateValueRange=function(t){let e,i,o,n=0;for(e in t)if(Object.prototype.hasOwnProperty.call(t,e)){const s=t[e].getValue();void 0!==s&&(i=void 0===i?s:Math.min(s,i),o=void 0===o?s:Math.max(s,o),n+=s)}if(void 0!==i&&void 0!==o)for(e in t)Object.prototype.hasOwnProperty.call(t,e)&&t[e].setValueRange(i,o,n)},VC.prototype.isActive=function(){return!this.activator||this.activator.active},VC.prototype.setSize=function(){return this.canvas.setSize.apply(this.canvas,arguments)},VC.prototype.canvasToDOM=function(){return this.canvas.canvasToDOM.apply(this.canvas,arguments)},VC.prototype.DOMtoCanvas=function(){return this.canvas.DOMtoCanvas.apply(this.canvas,arguments)},VC.prototype.findNode=function(){return this.clustering.findNode.apply(this.clustering,arguments)},VC.prototype.isCluster=function(){return this.clustering.isCluster.apply(this.clustering,arguments)},VC.prototype.openCluster=function(){return this.clustering.openCluster.apply(this.clustering,arguments)},VC.prototype.cluster=function(){return this.clustering.cluster.apply(this.clustering,arguments)},VC.prototype.getNodesInCluster=function(){return this.clustering.getNodesInCluster.apply(this.clustering,arguments)},VC.prototype.clusterByConnection=function(){return this.clustering.clusterByConnection.apply(this.clustering,arguments)},VC.prototype.clusterByHubsize=function(){return this.clustering.clusterByHubsize.apply(this.clustering,arguments)},VC.prototype.updateClusteredNode=function(){return this.clustering.updateClusteredNode.apply(this.clustering,arguments)},VC.prototype.getClusteredEdges=function(){return this.clustering.getClusteredEdges.apply(this.clustering,arguments)},VC.prototype.getBaseEdge=function(){return this.clustering.getBaseEdge.apply(this.clustering,arguments)},VC.prototype.getBaseEdges=function(){return this.clustering.getBaseEdges.apply(this.clustering,arguments)},VC.prototype.updateEdge=function(){return this.clustering.updateEdge.apply(this.clustering,arguments)},VC.prototype.clusterOutliers=function(){return this.clustering.clusterOutliers.apply(this.clustering,arguments)},VC.prototype.getSeed=function(){return this.layoutEngine.getSeed.apply(this.layoutEngine,arguments)},VC.prototype.enableEditMode=function(){return this.manipulation.enableEditMode.apply(this.manipulation,arguments)},VC.prototype.disableEditMode=function(){return this.manipulation.disableEditMode.apply(this.manipulation,arguments)},VC.prototype.addNodeMode=function(){return this.manipulation.addNodeMode.apply(this.manipulation,arguments)},VC.prototype.editNode=function(){return this.manipulation.editNode.apply(this.manipulation,arguments)},VC.prototype.editNodeMode=function(){return console.warn("Deprecated: Please use editNode instead of editNodeMode."),this.manipulation.editNode.apply(this.manipulation,arguments)},VC.prototype.addEdgeMode=function(){return this.manipulation.addEdgeMode.apply(this.manipulation,arguments)},VC.prototype.editEdgeMode=function(){return this.manipulation.editEdgeMode.apply(this.manipulation,arguments)},VC.prototype.deleteSelected=function(){return this.manipulation.deleteSelected.apply(this.manipulation,arguments)},VC.prototype.getPositions=function(){return this.nodesHandler.getPositions.apply(this.nodesHandler,arguments)},VC.prototype.getPosition=function(){return this.nodesHandler.getPosition.apply(this.nodesHandler,arguments)},VC.prototype.storePositions=function(){return this.nodesHandler.storePositions.apply(this.nodesHandler,arguments)},VC.prototype.moveNode=function(){return this.nodesHandler.moveNode.apply(this.nodesHandler,arguments)},VC.prototype.getBoundingBox=function(){return this.nodesHandler.getBoundingBox.apply(this.nodesHandler,arguments)},VC.prototype.getConnectedNodes=function(t){return void 0!==this.body.nodes[t]?this.nodesHandler.getConnectedNodes.apply(this.nodesHandler,arguments):this.edgesHandler.getConnectedNodes.apply(this.edgesHandler,arguments)},VC.prototype.getConnectedEdges=function(){return this.nodesHandler.getConnectedEdges.apply(this.nodesHandler,arguments)},VC.prototype.startSimulation=function(){return this.physics.startSimulation.apply(this.physics,arguments)},VC.prototype.stopSimulation=function(){return this.physics.stopSimulation.apply(this.physics,arguments)},VC.prototype.stabilize=function(){return this.physics.stabilize.apply(this.physics,arguments)},VC.prototype.getSelection=function(){return this.selectionHandler.getSelection.apply(this.selectionHandler,arguments)},VC.prototype.setSelection=function(){return this.selectionHandler.setSelection.apply(this.selectionHandler,arguments)},VC.prototype.getSelectedNodes=function(){return this.selectionHandler.getSelectedNodeIds.apply(this.selectionHandler,arguments)},VC.prototype.getSelectedEdges=function(){return this.selectionHandler.getSelectedEdgeIds.apply(this.selectionHandler,arguments)},VC.prototype.getNodeAt=function(){const t=this.selectionHandler.getNodeAt.apply(this.selectionHandler,arguments);return void 0!==t&&void 0!==t.id?t.id:t},VC.prototype.getEdgeAt=function(){const t=this.selectionHandler.getEdgeAt.apply(this.selectionHandler,arguments);return void 0!==t&&void 0!==t.id?t.id:t},VC.prototype.selectNodes=function(){return this.selectionHandler.selectNodes.apply(this.selectionHandler,arguments)},VC.prototype.selectEdges=function(){return this.selectionHandler.selectEdges.apply(this.selectionHandler,arguments)},VC.prototype.unselectAll=function(){this.selectionHandler.unselectAll.apply(this.selectionHandler,arguments),this.selectionHandler.commitWithoutEmitting.apply(this.selectionHandler),this.redraw()},VC.prototype.redraw=function(){return this.renderer.redraw.apply(this.renderer,arguments)},VC.prototype.getScale=function(){return this.view.getScale.apply(this.view,arguments)},VC.prototype.getViewPosition=function(){return this.view.getViewPosition.apply(this.view,arguments)},VC.prototype.fit=function(){return this.view.fit.apply(this.view,arguments)},VC.prototype.moveTo=function(){return this.view.moveTo.apply(this.view,arguments)},VC.prototype.focus=function(){return this.view.focus.apply(this.view,arguments)},VC.prototype.releaseNode=function(){return this.view.releaseNode.apply(this.view,arguments)},VC.prototype.getOptionsFromConfigurator=function(){let t={};return this.configurator&&(t=this.configurator.getOptions.apply(this.configurator)),t};const qC=ld;t.Network=VC,t.NetworkImages=Sl,t.networkDOTParser=wd,t.networkGephiParser=Sd,t.networkOptions=LC,t.parseDOTNetwork=qC,t.parseGephiNetwork=kd});
//# sourceMappingURL=vis-network.min.js.map
