# Obsidian 思维导图插件

这是一个为 Obsidian (https://obsidian.md) 开发的思维导图插件。

## 功能特性

- 创建和编辑思维导图
- 节点选择和编辑功能
- 键盘快捷键支持
- 节点的增删改操作

## 使用方法

### 基本操作

1. **创建思维导图**：使用命令面板 (Ctrl/Cmd + P) 搜索"创建新的思维导图"
2. **选择节点**：点击任意节点进行选择
3. **编辑节点**：双击节点或选中节点后按 Enter 键
4. **完成编辑**：按 Enter 键确认编辑，按 Escape 键取消编辑

### 键盘快捷键

- **Tab**：为选中节点创建子节点
- **Shift + Tab**：为选中节点创建同级节点
- **Enter**：编辑选中的节点
- **Escape**：取消编辑
- **Ctrl/Cmd + Delete**：删除选中的节点
- **方向键**：在节点间导航
  - ↑/↓：选择上/下同级节点
  - ←：折叠节点或选择父节点
  - →：展开节点或选择第一个子节点

### 鼠标操作

- **单击**：选择节点
- **双击**：编辑节点内容
- **点击空白处**：完成编辑（如果正在编辑）

## 修复内容

本次修复解决了以下问题：

1. **节点选择问题**：修复了无法正确选中节点的问题
2. **编辑功能问题**：修复了双击编辑节点无法正常工作的问题
3. **事件处理优化**：改进了鼠标和键盘事件的处理逻辑
4. **视觉反馈**：添加了选中节点的高亮显示
5. **键盘导航**：完善了键盘快捷键的支持

## 技术实现

- 使用 markmap-lib 和 markmap-view 进行思维导图渲染
- 使用 D3.js 进行 SVG 操作
- TypeScript 开发，提供类型安全

## 开发说明

- 运行 `npm install` 安装依赖
- 运行 `npm run dev` 进行开发模式编译
- 运行 `npm run build` 进行生产模式编译

## 安装方法

1. 将编译后的 `main.js`、`styles.css`、`manifest.json` 文件复制到你的 vault 目录下的 `.obsidian/plugins/obsidian-mindmap/` 文件夹中
2. 在 Obsidian 设置中启用插件

## 注意事项

- 确保 Obsidian 版本 >= 0.15.0
- 插件需要在 Obsidian 的插件设置中启用
- 如果遇到问题，请检查浏览器控制台的错误信息
