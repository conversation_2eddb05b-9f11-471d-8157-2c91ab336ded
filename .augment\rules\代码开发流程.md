---
type: "always_apply"
description: "代码开发遵循的原则"
---
# 代码项目开发流程规则（Augment风格，v1.1）
# 适用于：功能开发、问题定位、重构、性能优化等场景
# 更新：2025-07-26，新增执行/评审文档输出、统一路径、800行限制

project_dev_rules:
  meta:
    version: "v1.1"
    author: "AI Assistant"
    description: "基于Augment风格的多角度开发流程规则，含执行与评审文档输出、800行限制"

  workflow:
    phases:
      - name: "分析"
        mode_label: "[分析]"
        rules:
          - rule: "使用多角度思维分析需求或问题"
            priority: "critical"
          - rule: "使用codebase-retrieval扫描现有代码结构"
            priority: "high"
          - rule: "使用context7-mcp查询相关技术文档"
            priority: "high"
          - rule: "使用deepwiki-mcp补充背景知识"
            priority: "medium"
          - rule: "输出Markdown文档：./[任务标题]-docs/[任务标题]-分析.md"
            priority: "critical"

      - name: "构思"
        mode_label: "[构思]"
        rules:
          - rule: "提出1~3个可行方案，包含优缺点与工作量评估"
            priority: "high"
          - rule: "使用sequential-thinking进行方案对比"
            priority: "high"
          - rule: "输出Markdown文档：./[任务标题]-docs/[任务标题]-构思.md"
            priority: "critical"
          - rule: "禁止直接写代码，仅提供方案描述"
            priority: "critical"

      - name: "计划"
        mode_label: "[计划]"
        rules:
          - rule: "将选定方案拆解为可执行步骤"
            priority: "critical"
          - rule: "使用mcp-shrimp-task-manager管理任务依赖"
            priority: "high"
          - rule: "每个步骤必须包含：文件路径、类/方法名、修改范围、预期结果"
            priority: "high"
          - rule: "输出Markdown文档：./[任务标题]-docs/[任务标题]-计划.md"
            priority: "critical"
          - rule: "使用编号1.1、1.2、1.3等标识步骤，不涉及具体时间点"
            priority: "critical"

      - name: "执行"
        mode_label: "[执行]"
        rules:
          - rule: "必须等待用户确认后方可执行"
            priority: "critical"
          - rule: "使用str-replace-editor分步修改代码（每次≤800行）"
            priority: "critical"
          - rule: "每完成一个步骤后记录状态并反馈"
            priority: "high"
          - rule: "禁止跳过计划步骤，禁止一次性提交大量代码"
            priority: "critical"
          - rule: "输出Markdown文档：./[任务标题]-docs/[任务标题]-执行.md"
            priority: "critical"

      - name: "评审"
        mode_label: "[评审]"
        rules:
          - rule: "对照原计划检查功能是否完整实现"
            priority: "critical"
          - rule: "运行编译测试，确保无语法错误"
            priority: "high"
          - rule: "使用sequential-thinking进行质量分析"
            priority: "medium"
          - rule: "输出Markdown文档：./[任务标题]-docs/[任务标题]-评审.md"
            priority: "critical"
          - rule: "总结完成内容、遗留问题与下一步建议"
            priority: "high"

  output_format:
    markdown:
      naming_rule: "./[任务标题]-docs/[任务标题]-[阶段].md"
      structure:
        - title: "# [任务标题] - [阶段]"
        - section: "## 1.1 步骤描述"
        - section: "## 1.2 技术细节"
        - section: "## 1.3 风险与回退方案"

  constraints:
    - rule: "所有文档必须使用UTF-8编码，LF换行符"
      priority: "medium"
    - rule: "禁止在文档中使用具体时间点，仅使用编号"
      priority: "critical"
    - rule: "所有变更必须可追溯，禁止无来源的代码"
      priority: "high"
    - rule: "任何单个源文件行数不得超过800行"
      priority: "critical"