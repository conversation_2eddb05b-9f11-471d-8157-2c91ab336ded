/**
 * vis-data
 * http://visjs.org/
 *
 * Manage unstructured data using DataSet. Add, update, and remove data, and listen for changes in the data.
 *
 * @version 8.0.1
 * @date    2025-07-13T02:52:37.151Z
 *
 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
 *
 * @license
 * vis.js is dual licensed under both
 *
 *   1. The Apache 2.0 License
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *   and
 *
 *   2. The MIT License
 *      http://opensource.org/licenses/MIT
 *
 * vis.js may be distributed under either license.
 */
!function(t,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((t="undefined"!=typeof globalThis?globalThis:t||self).vis=t.vis||{})}(this,function(t){var r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function e(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var n,i,o,u,a,s,c,f,l,h,p,v,d,y,g,m,b={exports:{}},_={};function w(){if(i)return n;i=1;var t=function(t){return t&&t.Math===Math&&t};return n=t("object"==typeof globalThis&&globalThis)||t("object"==typeof window&&window)||t("object"==typeof self&&self)||t("object"==typeof r&&r)||t("object"==typeof n&&n)||function(){return this}()||Function("return this")()}function S(){return u?o:(u=1,o=function(t){try{return!!t()}catch(t){return!0}})}function T(){return s?a:(s=1,a=!S()(function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))}function O(){if(f)return c;f=1;var t=T(),r=Function.prototype,e=r.apply,n=r.call;return c="object"==typeof Reflect&&Reflect.apply||(t?n.bind(e):function(){return n.apply(e,arguments)}),c}function E(){if(h)return l;h=1;var t=T(),r=Function.prototype,e=r.call,n=t&&r.bind.bind(e,e);return l=t?n:function(t){return function(){return e.apply(t,arguments)}},l}function A(){if(v)return p;v=1;var t=E(),r=t({}.toString),e=t("".slice);return p=function(t){return e(r(t),8,-1)}}function I(){if(y)return d;y=1;var t=A(),r=E();return d=function(e){if("Function"===t(e))return r(e)}}function P(){if(m)return g;m=1;var t="object"==typeof document&&document.all;return g=void 0===t&&void 0!==t?function(r){return"function"==typeof r||r===t}:function(t){return"function"==typeof t}}var D,j,x,k,C={};function L(){return j?D:(j=1,D=!S()(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))}function M(){if(k)return x;k=1;var t=T(),r=Function.prototype.call;return x=t?r.bind(r):function(){return r.apply(r,arguments)},x}var N,R,F,z,q,U,W,Y,X,B,V,G,H,K,J,Q,Z,$,tt,rt,et,nt,it,ot,ut,at,st,ct,ft,lt,ht,pt,vt,dt,yt,gt,mt,bt={};function _t(){if(N)return bt;N=1;var t={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,e=r&&!t.call({1:2},1);return bt.f=e?function(t){var e=r(this,t);return!!e&&e.enumerable}:t,bt}function wt(){return F?R:(F=1,R=function(t,r){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:r}})}function St(){if(q)return z;q=1;var t=E(),r=S(),e=A(),n=Object,i=t("".split);return z=r(function(){return!n("z").propertyIsEnumerable(0)})?function(t){return"String"===e(t)?i(t,""):n(t)}:n}function Tt(){return W?U:(W=1,U=function(t){return null==t})}function Ot(){if(X)return Y;X=1;var t=Tt(),r=TypeError;return Y=function(e){if(t(e))throw new r("Can't call method on "+e);return e}}function Et(){if(V)return B;V=1;var t=St(),r=Ot();return B=function(e){return t(r(e))}}function At(){if(H)return G;H=1;var t=P();return G=function(r){return"object"==typeof r?null!==r:t(r)}}function It(){return J?K:(J=1,K={})}function Pt(){if(Z)return Q;Z=1;var t=It(),r=w(),e=P(),n=function(t){return e(t)?t:void 0};return Q=function(e,i){return arguments.length<2?n(t[e])||n(r[e]):t[e]&&t[e][i]||r[e]&&r[e][i]},Q}function Dt(){return tt?$:(tt=1,$=E()({}.isPrototypeOf))}function jt(){if(et)return rt;et=1;var t=w().navigator,r=t&&t.userAgent;return rt=r?String(r):""}function xt(){if(it)return nt;it=1;var t,r,e=w(),n=jt(),i=e.process,o=e.Deno,u=i&&i.versions||o&&o.version,a=u&&u.v8;return a&&(r=(t=a.split("."))[0]>0&&t[0]<4?1:+(t[0]+t[1])),!r&&n&&(!(t=n.match(/Edge\/(\d+)/))||t[1]>=74)&&(t=n.match(/Chrome\/(\d+)/))&&(r=+t[1]),nt=r}function kt(){if(ut)return ot;ut=1;var t=xt(),r=S(),e=w().String;return ot=!!Object.getOwnPropertySymbols&&!r(function(){var r=Symbol("symbol detection");return!e(r)||!(Object(r)instanceof Symbol)||!Symbol.sham&&t&&t<41}),ot}function Ct(){return st?at:(st=1,at=kt()&&!Symbol.sham&&"symbol"==typeof Symbol.iterator)}function Lt(){if(ft)return ct;ft=1;var t=Pt(),r=P(),e=Dt(),n=Object;return ct=Ct()?function(t){return"symbol"==typeof t}:function(i){var o=t("Symbol");return r(o)&&e(o.prototype,n(i))}}function Mt(){if(ht)return lt;ht=1;var t=String;return lt=function(r){try{return t(r)}catch(t){return"Object"}}}function Nt(){if(vt)return pt;vt=1;var t=P(),r=Mt(),e=TypeError;return pt=function(n){if(t(n))return n;throw new e(r(n)+" is not a function")}}function Rt(){if(yt)return dt;yt=1;var t=Nt(),r=Tt();return dt=function(e,n){var i=e[n];return r(i)?void 0:t(i)}}function Ft(){if(mt)return gt;mt=1;var t=M(),r=P(),e=At(),n=TypeError;return gt=function(i,o){var u,a;if("string"===o&&r(u=i.toString)&&!e(a=t(u,i)))return a;if(r(u=i.valueOf)&&!e(a=t(u,i)))return a;if("string"!==o&&r(u=i.toString)&&!e(a=t(u,i)))return a;throw new n("Can't convert object to primitive value")}}var zt,qt,Ut,Wt,Yt,Xt,Bt,Vt,Gt,Ht,Kt,Jt,Qt,Zt,$t,tr,rr,er,nr,ir,or,ur,ar,sr,cr,fr,lr,hr,pr={exports:{}};function vr(){return qt?zt:(qt=1,zt=!0)}function dr(){if(Wt)return Ut;Wt=1;var t=w(),r=Object.defineProperty;return Ut=function(e,n){try{r(t,e,{value:n,configurable:!0,writable:!0})}catch(r){t[e]=n}return n}}function yr(){if(Yt)return pr.exports;Yt=1;var t=vr(),r=w(),e=dr(),n="__core-js_shared__",i=pr.exports=r[n]||e(n,{});return(i.versions||(i.versions=[])).push({version:"3.44.0",mode:t?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.44.0/LICENSE",source:"https://github.com/zloirock/core-js"}),pr.exports}function gr(){if(Bt)return Xt;Bt=1;var t=yr();return Xt=function(r,e){return t[r]||(t[r]=e||{})}}function mr(){if(Gt)return Vt;Gt=1;var t=Ot(),r=Object;return Vt=function(e){return r(t(e))}}function br(){if(Kt)return Ht;Kt=1;var t=E(),r=mr(),e=t({}.hasOwnProperty);return Ht=Object.hasOwn||function(t,n){return e(r(t),n)}}function _r(){if(Qt)return Jt;Qt=1;var t=E(),r=0,e=Math.random(),n=t(1.1.toString);return Jt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+n(++r+e,36)}}function wr(){if($t)return Zt;$t=1;var t=w(),r=gr(),e=br(),n=_r(),i=kt(),o=Ct(),u=t.Symbol,a=r("wks"),s=o?u.for||u:u&&u.withoutSetter||n;return Zt=function(t){return e(a,t)||(a[t]=i&&e(u,t)?u[t]:s("Symbol."+t)),a[t]}}function Sr(){if(rr)return tr;rr=1;var t=M(),r=At(),e=Lt(),n=Rt(),i=Ft(),o=TypeError,u=wr()("toPrimitive");return tr=function(a,s){if(!r(a)||e(a))return a;var c,f=n(a,u);if(f){if(void 0===s&&(s="default"),c=t(f,a,s),!r(c)||e(c))return c;throw new o("Can't convert object to primitive value")}return void 0===s&&(s="number"),i(a,s)}}function Tr(){if(nr)return er;nr=1;var t=Sr(),r=Lt();return er=function(e){var n=t(e,"string");return r(n)?n:n+""}}function Or(){if(or)return ir;or=1;var t=w(),r=At(),e=t.document,n=r(e)&&r(e.createElement);return ir=function(t){return n?e.createElement(t):{}}}function Er(){if(ar)return ur;ar=1;var t=L(),r=S(),e=Or();return ur=!t&&!r(function(){return 7!==Object.defineProperty(e("div"),"a",{get:function(){return 7}}).a})}function Ar(){if(sr)return C;sr=1;var t=L(),r=M(),e=_t(),n=wt(),i=Et(),o=Tr(),u=br(),a=Er(),s=Object.getOwnPropertyDescriptor;return C.f=t?s:function(t,c){if(t=i(t),c=o(c),a)try{return s(t,c)}catch(t){}if(u(t,c))return n(!r(e.f,t,c),t[c])},C}function Ir(){if(fr)return cr;fr=1;var t=S(),r=P(),e=/#|\.prototype\./,n=function(e,n){var s=o[i(e)];return s===a||s!==u&&(r(n)?t(n):!!n)},i=n.normalize=function(t){return String(t).replace(e,".").toLowerCase()},o=n.data={},u=n.NATIVE="N",a=n.POLYFILL="P";return cr=n}function Pr(){if(hr)return lr;hr=1;var t=I(),r=Nt(),e=T(),n=t(t.bind);return lr=function(t,i){return r(t),void 0===i?t:e?n(t,i):function(){return t.apply(i,arguments)}},lr}var Dr,jr,xr,kr,Cr,Lr,Mr,Nr,Rr,Fr,zr,qr,Ur,Wr,Yr,Xr,Br,Vr,Gr,Hr={};function Kr(){return jr?Dr:(jr=1,Dr=L()&&S()(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype}))}function Jr(){if(kr)return xr;kr=1;var t=At(),r=String,e=TypeError;return xr=function(n){if(t(n))return n;throw new e(r(n)+" is not an object")}}function Qr(){if(Cr)return Hr;Cr=1;var t=L(),r=Er(),e=Kr(),n=Jr(),i=Tr(),o=TypeError,u=Object.defineProperty,a=Object.getOwnPropertyDescriptor,s="enumerable",c="configurable",f="writable";return Hr.f=t?e?function(t,r,e){if(n(t),r=i(r),n(e),"function"==typeof t&&"prototype"===r&&"value"in e&&f in e&&!e[f]){var o=a(t,r);o&&o[f]&&(t[r]=e.value,e={configurable:c in e?e[c]:o[c],enumerable:s in e?e[s]:o[s],writable:!1})}return u(t,r,e)}:u:function(t,e,a){if(n(t),e=i(e),n(a),r)try{return u(t,e,a)}catch(t){}if("get"in a||"set"in a)throw new o("Accessors not supported");return"value"in a&&(t[e]=a.value),t},Hr}function Zr(){if(Mr)return Lr;Mr=1;var t=L(),r=Qr(),e=wt();return Lr=t?function(t,n,i){return r.f(t,n,e(1,i))}:function(t,r,e){return t[r]=e,t}}function $r(){if(Rr)return Nr;Rr=1;var t=w(),r=O(),e=I(),n=P(),i=Ar().f,o=Ir(),u=It(),a=Pr(),s=Zr(),c=br(),f=function(t){var e=function(n,i,o){if(this instanceof e){switch(arguments.length){case 0:return new t;case 1:return new t(n);case 2:return new t(n,i)}return new t(n,i,o)}return r(t,this,arguments)};return e.prototype=t.prototype,e};return Nr=function(r,l){var h,p,v,d,y,g,m,b,_,w=r.target,S=r.global,T=r.stat,O=r.proto,E=S?t:T?t[w]:t[w]&&t[w].prototype,A=S?u:u[w]||s(u,w,{})[w],I=A.prototype;for(d in l)p=!(h=o(S?d:w+(T?".":"#")+d,r.forced))&&E&&c(E,d),g=A[d],p&&(m=r.dontCallGetSet?(_=i(E,d))&&_.value:E[d]),y=p&&m?m:l[d],(h||O||typeof g!=typeof y)&&(b=r.bind&&p?a(y,t):r.wrap&&p?f(y):O&&n(y)?e(y):y,(r.sham||y&&y.sham||g&&g.sham)&&s(b,"sham",!0),s(A,d,b),O&&(c(u,v=w+"Prototype")||s(u,v,{}),s(u[v],d,y),r.real&&I&&(h||!I[d])&&s(I,d,y)))}}function te(){if(Fr)return _;Fr=1;var t=$r(),r=L(),e=Qr().f;return t({target:"Object",stat:!0,forced:Object.defineProperty!==e,sham:!r},{defineProperty:e}),_}function re(){if(zr)return b.exports;zr=1,te();var t=It().Object,r=b.exports=function(r,e,n){return t.defineProperty(r,e,n)};return t.defineProperty.sham&&(r.sham=!0),b.exports}function ee(){return Ur?qr:(Ur=1,qr=re())}function ne(){return Yr?Wr:(Yr=1,Wr=ee())}function ie(){return Br?Xr:(Br=1,Xr=ne())}function oe(){return Gr?Vr:(Gr=1,Vr=ie())}var ue,ae,se,ce,fe,le,he,pe,ve,de,ye,ge,me,be,_e,we,Se,Te,Oe,Ee,Ae,Ie,Pe,De,je,xe,ke,Ce,Le,Me=e(oe()),Ne={};function Re(){if(ae)return ue;ae=1;var t=A();return ue=Array.isArray||function(r){return"Array"===t(r)}}function Fe(){if(ce)return se;ce=1;var t=Math.ceil,r=Math.floor;return se=Math.trunc||function(e){var n=+e;return(n>0?r:t)(n)}}function ze(){if(le)return fe;le=1;var t=Fe();return fe=function(r){var e=+r;return e!=e||0===e?0:t(e)}}function qe(){if(pe)return he;pe=1;var t=ze(),r=Math.min;return he=function(e){var n=t(e);return n>0?r(n,9007199254740991):0}}function Ue(){if(de)return ve;de=1;var t=qe();return ve=function(r){return t(r.length)}}function We(){if(ge)return ye;ge=1;var t=TypeError;return ye=function(r){if(r>9007199254740991)throw t("Maximum allowed index exceeded");return r}}function Ye(){if(be)return me;be=1;var t=L(),r=Qr(),e=wt();return me=function(n,i,o){t?r.f(n,i,e(0,o)):n[i]=o}}function Xe(){if(we)return _e;we=1;var t={};return t[wr()("toStringTag")]="z",_e="[object z]"===String(t)}function Be(){if(Te)return Se;Te=1;var t=Xe(),r=P(),e=A(),n=wr()("toStringTag"),i=Object,o="Arguments"===e(function(){return arguments}());return Se=t?e:function(t){var u,a,s;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(a=function(t,r){try{return t[r]}catch(t){}}(u=i(t),n))?a:o?e(u):"Object"===(s=e(u))&&r(u.callee)?"Arguments":s}}function Ve(){if(Ee)return Oe;Ee=1;var t=E(),r=P(),e=yr(),n=t(Function.toString);return r(e.inspectSource)||(e.inspectSource=function(t){return n(t)}),Oe=e.inspectSource}function Ge(){if(Ie)return Ae;Ie=1;var t=E(),r=S(),e=P(),n=Be(),i=Pt(),o=Ve(),u=function(){},a=i("Reflect","construct"),s=/^\s*(?:class|function)\b/,c=t(s.exec),f=!s.test(u),l=function(t){if(!e(t))return!1;try{return a(u,[],t),!0}catch(t){return!1}},h=function(t){if(!e(t))return!1;switch(n(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return f||!!c(s,o(t))}catch(t){return!0}};return h.sham=!0,Ae=!a||r(function(){var t;return l(l.call)||!l(Object)||!l(function(){t=!0})||t})?h:l}function He(){if(De)return Pe;De=1;var t=Re(),r=Ge(),e=At(),n=wr()("species"),i=Array;return Pe=function(o){var u;return t(o)&&(u=o.constructor,(r(u)&&(u===i||t(u.prototype))||e(u)&&null===(u=u[n]))&&(u=void 0)),void 0===u?i:u}}function Ke(){if(xe)return je;xe=1;var t=He();return je=function(r,e){return new(t(r))(0===e?0:e)}}function Je(){if(Ce)return ke;Ce=1;var t=S(),r=wr(),e=xt(),n=r("species");return ke=function(r){return e>=51||!t(function(){var t=[];return(t.constructor={})[n]=function(){return{foo:1}},1!==t[r](Boolean).foo})}}function Qe(){if(Le)return Ne;Le=1;var t=$r(),r=S(),e=Re(),n=At(),i=mr(),o=Ue(),u=We(),a=Ye(),s=Ke(),c=Je(),f=wr(),l=xt(),h=f("isConcatSpreadable"),p=l>=51||!r(function(){var t=[];return t[h]=!1,t.concat()[0]!==t}),v=function(t){if(!n(t))return!1;var r=t[h];return void 0!==r?!!r:e(t)};return t({target:"Array",proto:!0,arity:1,forced:!p||!c("concat")},{concat:function(t){var r,e,n,c,f,l=i(this),h=s(l,0),p=0;for(r=-1,n=arguments.length;r<n;r++)if(v(f=-1===r?l:arguments[r]))for(c=o(f),u(p+c),e=0;e<c;e++,p++)e in f&&a(h,p,f[e]);else u(p+1),a(h,p++,f);return h.length=p,h}}),Ne}var Ze,$e,tn={},rn={};function en(){if($e)return Ze;$e=1;var t=Be(),r=String;return Ze=function(e){if("Symbol"===t(e))throw new TypeError("Cannot convert a Symbol value to a string");return r(e)}}var nn,on,un,an,sn,cn,fn,ln,hn,pn,vn,dn,yn,gn,mn,bn,_n,wn,Sn,Tn={};function On(){if(on)return nn;on=1;var t=ze(),r=Math.max,e=Math.min;return nn=function(n,i){var o=t(n);return o<0?r(o+i,0):e(o,i)}}function En(){if(an)return un;an=1;var t=Et(),r=On(),e=Ue(),n=function(n){return function(i,o,u){var a=t(i),s=e(a);if(0===s)return!n&&-1;var c,f=r(u,s);if(n&&o!=o){for(;s>f;)if((c=a[f++])!=c)return!0}else for(;s>f;f++)if((n||f in a)&&a[f]===o)return n||f||0;return!n&&-1}};return un={includes:n(!0),indexOf:n(!1)}}function An(){return cn?sn:(cn=1,sn={})}function In(){if(ln)return fn;ln=1;var t=E(),r=br(),e=Et(),n=En().indexOf,i=An(),o=t([].push);return fn=function(t,u){var a,s=e(t),c=0,f=[];for(a in s)!r(i,a)&&r(s,a)&&o(f,a);for(;u.length>c;)r(s,a=u[c++])&&(~n(f,a)||o(f,a));return f}}function Pn(){return pn?hn:(pn=1,hn=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"])}function Dn(){if(dn)return vn;dn=1;var t=In(),r=Pn();return vn=Object.keys||function(e){return t(e,r)}}function jn(){if(yn)return Tn;yn=1;var t=L(),r=Kr(),e=Qr(),n=Jr(),i=Et(),o=Dn();return Tn.f=t&&!r?Object.defineProperties:function(t,r){n(t);for(var u,a=i(r),s=o(r),c=s.length,f=0;c>f;)e.f(t,u=s[f++],a[u]);return t},Tn}function xn(){return mn?gn:(mn=1,gn=Pt()("document","documentElement"))}function kn(){if(_n)return bn;_n=1;var t=gr(),r=_r(),e=t("keys");return bn=function(t){return e[t]||(e[t]=r(t))}}function Cn(){if(Sn)return wn;Sn=1;var t,r=Jr(),e=jn(),n=Pn(),i=An(),o=xn(),u=Or(),a="prototype",s="script",c=kn()("IE_PROTO"),f=function(){},l=function(t){return"<"+s+">"+t+"</"+s+">"},h=function(t){t.write(l("")),t.close();var r=t.parentWindow.Object;return t=null,r},p=function(){try{t=new ActiveXObject("htmlfile")}catch(t){}var r,e,i;p="undefined"!=typeof document?document.domain&&t?h(t):(e=u("iframe"),i="java"+s+":",e.style.display="none",o.appendChild(e),e.src=String(i),(r=e.contentWindow.document).open(),r.write(l("document.F=Object")),r.close(),r.F):h(t);for(var c=n.length;c--;)delete p[a][n[c]];return p()};return i[c]=!0,wn=Object.create||function(t,n){var i;return null!==t?(f[a]=r(t),i=new f,f[a]=null,i[c]=t):i=p(),void 0===n?i:e.f(i,n)}}var Ln,Mn={};function Nn(){if(Ln)return Mn;Ln=1;var t=In(),r=Pn().concat("length","prototype");return Mn.f=Object.getOwnPropertyNames||function(e){return t(e,r)},Mn}var Rn,Fn,zn,qn={};function Un(){return Fn?Rn:(Fn=1,Rn=E()([].slice))}function Wn(){if(zn)return qn;zn=1;var t=A(),r=Et(),e=Nn().f,n=Un(),i="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];return qn.f=function(o){return i&&"Window"===t(o)?function(t){try{return e(t)}catch(t){return n(i)}}(o):e(r(o))},qn}var Yn,Xn,Bn,Vn,Gn,Hn={};function Kn(){return Yn||(Yn=1,Hn.f=Object.getOwnPropertySymbols),Hn}function Jn(){if(Bn)return Xn;Bn=1;var t=Zr();return Xn=function(r,e,n,i){return i&&i.enumerable?r[e]=n:t(r,e,n),r}}function Qn(){if(Gn)return Vn;Gn=1;var t=Qr();return Vn=function(r,e,n){return t.f(r,e,n)}}var Zn,$n,ti,ri,ei,ni,ii,oi,ui,ai,si,ci,fi,li,hi,pi,vi={};function di(){if(Zn)return vi;Zn=1;var t=wr();return vi.f=t,vi}function yi(){if(ti)return $n;ti=1;var t=It(),r=br(),e=di(),n=Qr().f;return $n=function(i){var o=t.Symbol||(t.Symbol={});r(o,i)||n(o,i,{value:e.f(i)})}}function gi(){if(ei)return ri;ei=1;var t=M(),r=Pt(),e=wr(),n=Jn();return ri=function(){var i=r("Symbol"),o=i&&i.prototype,u=o&&o.valueOf,a=e("toPrimitive");o&&!o[a]&&n(o,a,function(r){return t(u,this)},{arity:1})}}function mi(){if(ii)return ni;ii=1;var t=Xe(),r=Be();return ni=t?{}.toString:function(){return"[object "+r(this)+"]"}}function bi(){if(ui)return oi;ui=1;var t=Xe(),r=Qr().f,e=Zr(),n=br(),i=mi(),o=wr()("toStringTag");return oi=function(u,a,s,c){var f=s?u:u&&u.prototype;f&&(n(f,o)||r(f,o,{configurable:!0,value:a}),c&&!t&&e(f,"toString",i))}}function _i(){if(si)return ai;si=1;var t=w(),r=P(),e=t.WeakMap;return ai=r(e)&&/native code/.test(String(e))}function wi(){if(fi)return ci;fi=1;var t,r,e,n=_i(),i=w(),o=At(),u=Zr(),a=br(),s=yr(),c=kn(),f=An(),l="Object already initialized",h=i.TypeError,p=i.WeakMap;if(n||s.state){var v=s.state||(s.state=new p);v.get=v.get,v.has=v.has,v.set=v.set,t=function(t,r){if(v.has(t))throw new h(l);return r.facade=t,v.set(t,r),r},r=function(t){return v.get(t)||{}},e=function(t){return v.has(t)}}else{var d=c("state");f[d]=!0,t=function(t,r){if(a(t,d))throw new h(l);return r.facade=t,u(t,d,r),r},r=function(t){return a(t,d)?t[d]:{}},e=function(t){return a(t,d)}}return ci={set:t,get:r,has:e,enforce:function(n){return e(n)?r(n):t(n,{})},getterFor:function(t){return function(e){var n;if(!o(e)||(n=r(e)).type!==t)throw new h("Incompatible receiver, "+t+" required");return n}}}}function Si(){if(hi)return li;hi=1;var t=Pr(),r=E(),e=St(),n=mr(),i=Ue(),o=Ke(),u=r([].push),a=function(r){var a=1===r,s=2===r,c=3===r,f=4===r,l=6===r,h=7===r,p=5===r||l;return function(v,d,y,g){for(var m,b,_=n(v),w=e(_),S=i(w),T=t(d,y),O=0,E=g||o,A=a?E(v,S):s||h?E(v,0):void 0;S>O;O++)if((p||O in w)&&(b=T(m=w[O],O,_),r))if(a)A[O]=b;else if(b)switch(r){case 3:return!0;case 5:return m;case 6:return O;case 2:u(A,m)}else switch(r){case 4:return!1;case 7:u(A,m)}return l?-1:c||f?f:A}};return li={forEach:a(0),map:a(1),filter:a(2),some:a(3),every:a(4),find:a(5),findIndex:a(6),filterReject:a(7)}}var Ti,Oi,Ei,Ai={};function Ii(){return Oi?Ti:(Oi=1,Ti=kt()&&!!Symbol.for&&!!Symbol.keyFor)}var Pi,Di={};var ji,xi,ki,Ci={};function Li(){if(xi)return ji;xi=1;var t=E(),r=Re(),e=P(),n=A(),i=en(),o=t([].push);return ji=function(t){if(e(t))return t;if(r(t)){for(var u=t.length,a=[],s=0;s<u;s++){var c=t[s];"string"==typeof c?o(a,c):"number"!=typeof c&&"Number"!==n(c)&&"String"!==n(c)||o(a,i(c))}var f=a.length,l=!0;return function(t,e){if(l)return l=!1,e;if(r(this))return e;for(var n=0;n<f;n++)if(a[n]===t)return e}}},ji}function Mi(){if(ki)return Ci;ki=1;var t=$r(),r=Pt(),e=O(),n=M(),i=E(),o=S(),u=P(),a=Lt(),s=Un(),c=Li(),f=kt(),l=String,h=r("JSON","stringify"),p=i(/./.exec),v=i("".charAt),d=i("".charCodeAt),y=i("".replace),g=i(1.1.toString),m=/[\uD800-\uDFFF]/g,b=/^[\uD800-\uDBFF]$/,_=/^[\uDC00-\uDFFF]$/,w=!f||o(function(){var t=r("Symbol")("stringify detection");return"[null]"!==h([t])||"{}"!==h({a:t})||"{}"!==h(Object(t))}),T=o(function(){return'"\\udf06\\ud834"'!==h("\udf06\ud834")||'"\\udead"'!==h("\udead")}),A=function(t,r){var i=s(arguments),o=c(r);if(u(o)||void 0!==t&&!a(t))return i[1]=function(t,r){if(u(o)&&(r=n(o,this,l(t),r)),!a(r))return r},e(h,null,i)},I=function(t,r,e){var n=v(e,r-1),i=v(e,r+1);return p(b,t)&&!p(_,i)||p(_,t)&&!p(b,n)?"\\u"+g(d(t,0),16):t};return h&&t({target:"JSON",stat:!0,arity:3,forced:w||T},{stringify:function(t,r,n){var i=s(arguments),o=e(w?A:h,null,i);return T&&"string"==typeof o?y(o,m,I):o}}),Ci}var Ni,Ri,Fi={};function zi(){return Ri||(Ri=1,function(){if(pi)return rn;pi=1;var t=$r(),r=w(),e=M(),n=E(),i=vr(),o=L(),u=kt(),a=S(),s=br(),c=Dt(),f=Jr(),l=Et(),h=Tr(),p=en(),v=wt(),d=Cn(),y=Dn(),g=Nn(),m=Wn(),b=Kn(),_=Ar(),T=Qr(),O=jn(),A=_t(),I=Jn(),P=Qn(),D=gr(),j=kn(),x=An(),k=_r(),C=wr(),N=di(),R=yi(),F=gi(),z=bi(),q=wi(),U=Si().forEach,W=j("hidden"),Y="Symbol",X="prototype",B=q.set,V=q.getterFor(Y),G=Object[X],H=r.Symbol,K=H&&H[X],J=r.RangeError,Q=r.TypeError,Z=r.QObject,$=_.f,tt=T.f,rt=m.f,et=A.f,nt=n([].push),it=D("symbols"),ot=D("op-symbols"),ut=D("wks"),at=!Z||!Z[X]||!Z[X].findChild,st=function(t,r,e){var n=$(G,r);n&&delete G[r],tt(t,r,e),n&&t!==G&&tt(G,r,n)},ct=o&&a(function(){return 7!==d(tt({},"a",{get:function(){return tt(this,"a",{value:7}).a}})).a})?st:tt,ft=function(t,r){var e=it[t]=d(K);return B(e,{type:Y,tag:t,description:r}),o||(e.description=r),e},lt=function(t,r,e){t===G&&lt(ot,r,e),f(t);var n=h(r);return f(e),s(it,n)?(e.enumerable?(s(t,W)&&t[W][n]&&(t[W][n]=!1),e=d(e,{enumerable:v(0,!1)})):(s(t,W)||tt(t,W,v(1,d(null))),t[W][n]=!0),ct(t,n,e)):tt(t,n,e)},ht=function(t,r){f(t);var n=l(r),i=y(n).concat(yt(n));return U(i,function(r){o&&!e(pt,n,r)||lt(t,r,n[r])}),t},pt=function(t){var r=h(t),n=e(et,this,r);return!(this===G&&s(it,r)&&!s(ot,r))&&(!(n||!s(this,r)||!s(it,r)||s(this,W)&&this[W][r])||n)},vt=function(t,r){var e=l(t),n=h(r);if(e!==G||!s(it,n)||s(ot,n)){var i=$(e,n);return!i||!s(it,n)||s(e,W)&&e[W][n]||(i.enumerable=!0),i}},dt=function(t){var r=rt(l(t)),e=[];return U(r,function(t){s(it,t)||s(x,t)||nt(e,t)}),e},yt=function(t){var r=t===G,e=rt(r?ot:l(t)),n=[];return U(e,function(t){!s(it,t)||r&&!s(G,t)||nt(n,it[t])}),n};u||(H=function(){if(c(K,this))throw new Q("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?p(arguments[0]):void 0,n=k(t),i=function(t){var o=void 0===this?r:this;o===G&&e(i,ot,t),s(o,W)&&s(o[W],n)&&(o[W][n]=!1);var u=v(1,t);try{ct(o,n,u)}catch(t){if(!(t instanceof J))throw t;st(o,n,u)}};return o&&at&&ct(G,n,{configurable:!0,set:i}),ft(n,t)},I(K=H[X],"toString",function(){return V(this).tag}),I(H,"withoutSetter",function(t){return ft(k(t),t)}),A.f=pt,T.f=lt,O.f=ht,_.f=vt,g.f=m.f=dt,b.f=yt,N.f=function(t){return ft(C(t),t)},o&&(P(K,"description",{configurable:!0,get:function(){return V(this).description}}),i||I(G,"propertyIsEnumerable",pt,{unsafe:!0}))),t({global:!0,constructor:!0,wrap:!0,forced:!u,sham:!u},{Symbol:H}),U(y(ut),function(t){R(t)}),t({target:Y,stat:!0,forced:!u},{useSetter:function(){at=!0},useSimple:function(){at=!1}}),t({target:"Object",stat:!0,forced:!u,sham:!o},{create:function(t,r){return void 0===r?d(t):ht(d(t),r)},defineProperty:lt,defineProperties:ht,getOwnPropertyDescriptor:vt}),t({target:"Object",stat:!0,forced:!u},{getOwnPropertyNames:dt}),F(),z(H,Y),x[W]=!0}(),function(){if(Ei)return Ai;Ei=1;var t=$r(),r=Pt(),e=br(),n=en(),i=gr(),o=Ii(),u=i("string-to-symbol-registry"),a=i("symbol-to-string-registry");t({target:"Symbol",stat:!0,forced:!o},{for:function(t){var i=n(t);if(e(u,i))return u[i];var o=r("Symbol")(i);return u[i]=o,a[o]=i,o}})}(),function(){if(Pi)return Di;Pi=1;var t=$r(),r=br(),e=Lt(),n=Mt(),i=gr(),o=Ii(),u=i("symbol-to-string-registry");t({target:"Symbol",stat:!0,forced:!o},{keyFor:function(t){if(!e(t))throw new TypeError(n(t)+" is not a symbol");if(r(u,t))return u[t]}})}(),Mi(),function(){if(Ni)return Fi;Ni=1;var t=$r(),r=kt(),e=S(),n=Kn(),i=mr();t({target:"Object",stat:!0,forced:!r||e(function(){n.f(1)})},{getOwnPropertySymbols:function(t){var r=n.f;return r?r(i(t)):[]}})}()),tn}var qi,Ui={};function Wi(){return qi||(qi=1,yi()("asyncDispose")),Ui}var Yi;var Xi,Bi={};function Vi(){return Xi||(Xi=1,yi()("dispose")),Bi}var Gi;var Hi;var Ki,Ji={};function Qi(){return Ki||(Ki=1,yi()("iterator")),Ji}var Zi;var $i;var to;var ro;var eo;var no;var io,oo={};function uo(){if(io)return oo;io=1;var t=yi(),r=gi();return t("toPrimitive"),r(),oo}var ao,so={};var co;var fo,lo,ho,po={};function vo(){return ho?lo:(ho=1,Qe(),zi(),Wi(),Yi||(Yi=1,yi()("asyncIterator")),Vi(),Gi||(Gi=1,yi()("hasInstance")),Hi||(Hi=1,yi()("isConcatSpreadable")),Qi(),Zi||(Zi=1,yi()("match")),$i||($i=1,yi()("matchAll")),to||(to=1,yi()("replace")),ro||(ro=1,yi()("search")),eo||(eo=1,yi()("species")),no||(no=1,yi()("split")),uo(),function(){if(ao)return so;ao=1;var t=Pt(),r=yi(),e=bi();r("toStringTag"),e(t("Symbol"),"Symbol")}(),co||(co=1,yi()("unscopables")),function(){if(fo)return po;fo=1;var t=w();bi()(t.JSON,"JSON",!0)}(),lo=It().Symbol)}var yo,go,mo,bo,_o,wo,So,To,Oo,Eo,Ao,Io,Po,Do,jo,xo,ko,Co,Lo,Mo,No,Ro,Fo,zo,qo,Uo,Wo,Yo,Xo,Bo,Vo,Go,Ho,Ko={};function Jo(){return go?yo:(go=1,yo=function(){})}function Qo(){return bo?mo:(bo=1,mo={})}function Zo(){if(wo)return _o;wo=1;var t=L(),r=br(),e=Function.prototype,n=t&&Object.getOwnPropertyDescriptor,i=r(e,"name"),o=i&&"something"===function(){}.name,u=i&&(!t||t&&n(e,"name").configurable);return _o={EXISTS:i,PROPER:o,CONFIGURABLE:u}}function $o(){return To?So:(To=1,So=!S()(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))}function tu(){if(Eo)return Oo;Eo=1;var t=br(),r=P(),e=mr(),n=kn(),i=$o(),o=n("IE_PROTO"),u=Object,a=u.prototype;return Oo=i?u.getPrototypeOf:function(n){var i=e(n);if(t(i,o))return i[o];var s=i.constructor;return r(s)&&i instanceof s?s.prototype:i instanceof u?a:null}}function ru(){if(Io)return Ao;Io=1;var t,r,e,n=S(),i=P(),o=At(),u=Cn(),a=tu(),s=Jn(),c=wr(),f=vr(),l=c("iterator"),h=!1;return[].keys&&("next"in(e=[].keys())?(r=a(a(e)))!==Object.prototype&&(t=r):h=!0),!o(t)||n(function(){var r={};return t[l].call(r)!==r})?t={}:f&&(t=u(t)),i(t[l])||s(t,l,function(){return this}),Ao={IteratorPrototype:t,BUGGY_SAFARI_ITERATORS:h}}function eu(){if(Do)return Po;Do=1;var t=ru().IteratorPrototype,r=Cn(),e=wt(),n=bi(),i=Qo(),o=function(){return this};return Po=function(u,a,s,c){var f=a+" Iterator";return u.prototype=r(t,{next:e(+!c,s)}),n(u,f,!1,!0),i[f]=o,u}}function nu(){if(xo)return jo;xo=1;var t=E(),r=Nt();return jo=function(e,n,i){try{return t(r(Object.getOwnPropertyDescriptor(e,n)[i]))}catch(t){}}}function iu(){if(Co)return ko;Co=1;var t=At();return ko=function(r){return t(r)||null===r}}function ou(){if(Mo)return Lo;Mo=1;var t=iu(),r=String,e=TypeError;return Lo=function(n){if(t(n))return n;throw new e("Can't set "+r(n)+" as a prototype")}}function uu(){if(Ro)return No;Ro=1;var t=nu(),r=At(),e=Ot(),n=ou();return No=Object.setPrototypeOf||("__proto__"in{}?function(){var i,o=!1,u={};try{(i=t(Object.prototype,"__proto__","set"))(u,[]),o=u instanceof Array}catch(t){}return function(t,u){return e(t),n(u),r(t)?(o?i(t,u):t.__proto__=u,t):t}}():void 0)}function au(){if(zo)return Fo;zo=1;var t=$r(),r=M(),e=vr(),n=Zo(),i=P(),o=eu(),u=tu(),a=uu(),s=bi(),c=Zr(),f=Jn(),l=wr(),h=Qo(),p=ru(),v=n.PROPER,d=n.CONFIGURABLE,y=p.IteratorPrototype,g=p.BUGGY_SAFARI_ITERATORS,m=l("iterator"),b="keys",_="values",w="entries",S=function(){return this};return Fo=function(n,l,p,T,O,E,A){o(p,l,T);var I,P,D,j=function(t){if(t===O&&M)return M;if(!g&&t&&t in C)return C[t];switch(t){case b:case _:case w:return function(){return new p(this,t)}}return function(){return new p(this)}},x=l+" Iterator",k=!1,C=n.prototype,L=C[m]||C["@@iterator"]||O&&C[O],M=!g&&L||j(O),N="Array"===l&&C.entries||L;if(N&&(I=u(N.call(new n)))!==Object.prototype&&I.next&&(e||u(I)===y||(a?a(I,y):i(I[m])||f(I,m,S)),s(I,x,!0,!0),e&&(h[x]=S)),v&&O===_&&L&&L.name!==_&&(!e&&d?c(C,"name",_):(k=!0,M=function(){return r(L,this)})),O)if(P={values:j(_),keys:E?M:j(b),entries:j(w)},A)for(D in P)(g||k||!(D in C))&&f(C,D,P[D]);else t({target:l,proto:!0,forced:g||k},P);return e&&!A||C[m]===M||f(C,m,M,{name:O}),h[l]=M,P}}function su(){return Uo?qo:(Uo=1,qo=function(t,r){return{value:t,done:r}})}function cu(){if(Yo)return Wo;Yo=1;var t=Et(),r=Jo(),e=Qo(),n=wi(),i=Qr().f,o=au(),u=su(),a=vr(),s=L(),c="Array Iterator",f=n.set,l=n.getterFor(c);Wo=o(Array,"Array",function(r,e){f(this,{type:c,target:t(r),index:0,kind:e})},function(){var t=l(this),r=t.target,e=t.index++;if(!r||e>=r.length)return t.target=null,u(void 0,!0);switch(t.kind){case"keys":return u(e,!1);case"values":return u(r[e],!1)}return u([e,r[e]],!1)},"values");var h=e.Arguments=e.Array;if(r("keys"),r("values"),r("entries"),!a&&s&&"values"!==h.name)try{i(h,"name",{value:"values"})}catch(t){}return Wo}function fu(){return Bo?Xo:(Bo=1,Xo={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0})}function lu(){if(Vo)return Ko;Vo=1,cu();var t=fu(),r=w(),e=bi(),n=Qo();for(var i in t)e(r[i],i),n[i]=n.Array;return Ko}function hu(){if(Ho)return Go;Ho=1;var t=vo();return lu(),Go=t}var pu,vu={};var du;var yu;var gu,mu,bu;function _u(){if(bu)return mu;bu=1;var t=hu();return function(){if(pu)return vu;pu=1;var t=wr(),r=Qr().f,e=t("metadata"),n=Function.prototype;void 0===n[e]&&r(n,e,{value:null})}(),du||(du=1,Wi()),yu||(yu=1,Vi()),gu||(gu=1,yi()("metadata")),mu=t}var wu,Su,Tu;function Ou(){if(Su)return wu;Su=1;var t=Pt(),r=E(),e=t("Symbol"),n=e.keyFor,i=r(e.prototype.valueOf);return wu=e.isRegisteredSymbol||function(t){try{return void 0!==n(i(t))}catch(t){return!1}}}var Eu,Au,Iu;function Pu(){if(Au)return Eu;Au=1;for(var t=gr(),r=Pt(),e=E(),n=Lt(),i=wr(),o=r("Symbol"),u=o.isWellKnownSymbol,a=r("Object","getOwnPropertyNames"),s=e(o.prototype.valueOf),c=t("wks"),f=0,l=a(o),h=l.length;f<h;f++)try{var p=l[f];n(o[p])&&i(p)}catch(t){}return Eu=function(t){if(u&&u(t))return!0;try{for(var r=s(t),e=0,n=a(c),i=n.length;e<i;e++)if(c[n[e]]==r)return!0}catch(t){}return!1},Eu}var Du;var ju;var xu;var ku;var Cu;var Lu;var Mu;var Nu,Ru,Fu,zu,qu;function Uu(){if(Fu)return Ru;Fu=1;var t=_u();return Tu||(Tu=1,$r()({target:"Symbol",stat:!0},{isRegisteredSymbol:Ou()})),Iu||(Iu=1,$r()({target:"Symbol",stat:!0,forced:!0},{isWellKnownSymbol:Pu()})),Du||(Du=1,yi()("customMatcher")),ju||(ju=1,yi()("observable")),xu||(xu=1,$r()({target:"Symbol",stat:!0,name:"isRegisteredSymbol"},{isRegistered:Ou()})),ku||(ku=1,$r()({target:"Symbol",stat:!0,name:"isWellKnownSymbol",forced:!0},{isWellKnown:Pu()})),Cu||(Cu=1,yi()("matcher")),Lu||(Lu=1,yi()("metadataKey")),Mu||(Mu=1,yi()("patternMatch")),Nu||(Nu=1,yi()("replaceAll")),Ru=t}function Wu(){return qu?zu:(qu=1,zu=Uu())}var Yu,Xu,Bu,Vu,Gu,Hu,Ku,Ju,Qu,Zu,$u,ta,ra,ea=e(Wu()),na={};function ia(){if(Xu)return Yu;Xu=1;var t=E(),r=ze(),e=en(),n=Ot(),i=t("".charAt),o=t("".charCodeAt),u=t("".slice),a=function(t){return function(a,s){var c,f,l=e(n(a)),h=r(s),p=l.length;return h<0||h>=p?t?"":void 0:(c=o(l,h))<55296||c>56319||h+1===p||(f=o(l,h+1))<56320||f>57343?t?i(l,h):c:t?u(l,h,h+2):f-56320+(c-55296<<10)+65536}};return Yu={codeAt:a(!1),charAt:a(!0)}}function oa(){if(Bu)return na;Bu=1;var t=ia().charAt,r=en(),e=wi(),n=au(),i=su(),o="String Iterator",u=e.set,a=e.getterFor(o);return n(String,"String",function(t){u(this,{type:o,string:r(t),index:0})},function(){var r,e=a(this),n=e.string,o=e.index;return o>=n.length?i(void 0,!0):(r=t(n,o),e.index+=r.length,i(r,!1))}),na}function ua(){return Gu?Vu:(Gu=1,cu(),oa(),Qi(),Vu=di().f("iterator"))}function aa(){if(Ku)return Hu;Ku=1;var t=ua();return lu(),Hu=t}function sa(){return Qu?Ju:(Qu=1,Ju=aa())}function ca(){return $u?Zu:($u=1,Zu=sa())}function fa(){return ra?ta:(ra=1,ta=ca())}var la,ha,pa,va,da,ya,ga,ma,ba,_a,wa=e(fa());function Sa(t){return Sa="function"==typeof ea&&"symbol"==typeof wa?function(t){return typeof t}:function(t){return t&&"function"==typeof ea&&t.constructor===ea&&t!==ea.prototype?"symbol":typeof t},Sa(t)}function Ta(){return ha?la:(ha=1,uo(),la=di().f("toPrimitive"))}function Oa(){return va?pa:(va=1,pa=Ta())}function Ea(){return ya?da:(ya=1,da=Oa())}function Aa(){return ma?ga:(ma=1,ga=Ea())}function Ia(){return _a?ba:(_a=1,ba=Aa())}var Pa=e(Ia());function Da(t){var r=function(t,r){if("object"!=Sa(t)||!t)return t;var e=t[Pa];if(void 0!==e){var n=e.call(t,r);if("object"!=Sa(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==Sa(r)?r:r+""}function ja(t,r,e){return(r=Da(r))in t?Me(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}var xa,ka,Ca,La,Ma,Na,Ra,Fa,za,qa,Ua,Wa,Ya,Xa={};function Ba(){if(ka)return xa;ka=1;var t=E(),r=Nt(),e=At(),n=br(),i=Un(),o=T(),u=Function,a=t([].concat),s=t([].join),c={};return xa=o?u.bind:function(t){var o=r(this),f=o.prototype,l=i(arguments,1),h=function(){var r=a(l,i(arguments));return this instanceof h?function(t,r,e){if(!n(c,r)){for(var i=[],o=0;o<r;o++)i[o]="a["+o+"]";c[r]=u("C,a","return new C("+s(i,",")+")")}return c[r](t,e)}(o,r.length,r):o.apply(t,r)};return e(f)&&(h.prototype=f),h},xa}function Va(){if(Ma)return La;Ma=1;var t=w(),r=It();return La=function(e,n){var i=r[e+"Prototype"],o=i&&i[n];if(o)return o;var u=t[e],a=u&&u.prototype;return a&&a[n]}}function Ga(){return Ra?Na:(Ra=1,function(){if(Ca)return Xa;Ca=1;var t=$r(),r=Ba();t({target:"Function",proto:!0,forced:Function.bind!==r},{bind:r})}(),Na=Va()("Function","bind"))}function Ha(){if(za)return Fa;za=1;var t=Dt(),r=Ga(),e=Function.prototype;return Fa=function(n){var i=n.bind;return n===e||t(e,n)&&i===e.bind?r:i}}function Ka(){return Ua?qa:(Ua=1,qa=Ha())}var Ja,Qa,Za,$a,ts,rs,es,ns,is,os,us,as,ss,cs,fs,ls,hs,ps=e(Ya?Wa:(Ya=1,Wa=Ka())),vs={};function ds(){if(Qa)return Ja;Qa=1;var t=Nt(),r=mr(),e=St(),n=Ue(),i=TypeError,o="Reduce of empty array with no initial value",u=function(u){return function(a,s,c,f){var l=r(a),h=e(l),p=n(l);if(t(s),0===p&&c<2)throw new i(o);var v=u?p-1:0,d=u?-1:1;if(c<2)for(;;){if(v in h){f=h[v],v+=d;break}if(v+=d,u?v<0:p<=v)throw new i(o)}for(;u?v>=0:p>v;v+=d)v in h&&(f=s(f,h[v],v,l));return f}};return Ja={left:u(!1),right:u(!0)}}function ys(){if($a)return Za;$a=1;var t=S();return Za=function(r,e){var n=[][r];return!!n&&t(function(){n.call(null,e||function(){return 1},1)})}}function gs(){if(rs)return ts;rs=1;var t=w(),r=jt(),e=A(),n=function(t){return r.slice(0,t.length)===t};return ts=n("Bun/")?"BUN":n("Cloudflare-Workers")?"CLOUDFLARE":n("Deno/")?"DENO":n("Node.js/")?"NODE":t.Bun&&"string"==typeof Bun.version?"BUN":t.Deno&&"object"==typeof Deno.version?"DENO":"process"===e(t.process)?"NODE":t.window&&t.document?"BROWSER":"REST"}function ms(){return ns?es:(ns=1,es="NODE"===gs())}function bs(){return us?os:(us=1,function(){if(is)return vs;is=1;var t=$r(),r=ds().left,e=ys(),n=xt();t({target:"Array",proto:!0,forced:!ms()&&n>79&&n<83||!e("reduce")},{reduce:function(t){var e=arguments.length;return r(this,t,e,e>1?arguments[1]:void 0)}})}(),os=Va()("Array","reduce"))}function _s(){if(ss)return as;ss=1;var t=Dt(),r=bs(),e=Array.prototype;return as=function(n){var i=n.reduce;return n===e||t(e,n)&&i===e.reduce?r:i}}function ws(){return fs?cs:(fs=1,cs=_s())}var Ss,Ts,Os,Es,As,Is,Ps,Ds,js,xs=e(hs?ls:(hs=1,ls=ws())),ks={};function Cs(){return Os?Ts:(Os=1,function(){if(Ss)return ks;Ss=1;var t=$r(),r=Si().filter;t({target:"Array",proto:!0,forced:!Je()("filter")},{filter:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})}(),Ts=Va()("Array","filter"))}function Ls(){if(As)return Es;As=1;var t=Dt(),r=Cs(),e=Array.prototype;return Es=function(n){var i=n.filter;return n===e||t(e,n)&&i===e.filter?r:i}}function Ms(){return Ps?Is:(Ps=1,Is=Ls())}var Ns,Rs,Fs,zs,qs,Us,Ws,Ys,Xs,Bs=e(js?Ds:(js=1,Ds=Ms())),Vs={};function Gs(){return Fs?Rs:(Fs=1,function(){if(Ns)return Vs;Ns=1;var t=$r(),r=Si().map;t({target:"Array",proto:!0,forced:!Je()("map")},{map:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})}(),Rs=Va()("Array","map"))}function Hs(){if(qs)return zs;qs=1;var t=Dt(),r=Gs(),e=Array.prototype;return zs=function(n){var i=n.map;return n===e||t(e,n)&&i===e.map?r:i}}function Ks(){return Ws?Us:(Ws=1,Us=Hs())}var Js,Qs,Zs,$s=e(Xs?Ys:(Xs=1,Ys=Ks())),tc={};function rc(){if(Qs)return Js;Qs=1;var t=Re(),r=Ue(),e=We(),n=Pr(),i=function(o,u,a,s,c,f,l,h){for(var p,v,d=c,y=0,g=!!l&&n(l,h);y<s;)y in a&&(p=g?g(a[y],y,u):a[y],f>0&&t(p)?(v=r(p),d=i(o,u,p,v,d,f-1)-1):(e(d+1),o[d]=p),d++),y++;return d};return Js=i}var ec,nc,ic,oc,uc,ac,sc,cc,fc;function lc(){return ic?nc:(ic=1,function(){if(Zs)return tc;Zs=1;var t=$r(),r=rc(),e=Nt(),n=mr(),i=Ue(),o=Ke();t({target:"Array",proto:!0},{flatMap:function(t){var u,a=n(this),s=i(a);return e(t),(u=o(a,0)).length=r(u,a,a,s,0,1,t,arguments.length>1?arguments[1]:void 0),u}})}(),ec||(ec=1,Jo()("flatMap")),nc=Va()("Array","flatMap"))}function hc(){if(uc)return oc;uc=1;var t=Dt(),r=lc(),e=Array.prototype;return oc=function(n){var i=n.flatMap;return n===e||t(e,n)&&i===e.flatMap?r:i}}function pc(){return sc?ac:(sc=1,ac=hc())}var vc=e(fc?cc:(fc=1,cc=pc()));class dc{constructor(t,r,e){var n,i,o;ja(this,"_listeners",{add:ps(n=this._add).call(n,this),remove:ps(i=this._remove).call(i,this),update:ps(o=this._update).call(o,this)}),this._source=t,this._transformers=r,this._target=e}all(){return this._target.update(this._transformItems(this._source.get())),this}start(){return this._source.on("add",this._listeners.add),this._source.on("remove",this._listeners.remove),this._source.on("update",this._listeners.update),this}stop(){return this._source.off("add",this._listeners.add),this._source.off("remove",this._listeners.remove),this._source.off("update",this._listeners.update),this}_transformItems(t){var r;return xs(r=this._transformers).call(r,(t,r)=>r(t),t)}_add(t,r){null!=r&&this._target.add(this._transformItems(this._source.get(r.items)))}_update(t,r){null!=r&&this._target.update(this._transformItems(this._source.get(r.items)))}_remove(t,r){null!=r&&this._target.remove(this._transformItems(r.oldData))}}class yc{constructor(t){ja(this,"_transformers",[]),this._source=t}filter(t){return this._transformers.push(r=>Bs(r).call(r,t)),this}map(t){return this._transformers.push(r=>$s(r).call(r,t)),this}flatMap(t){return this._transformers.push(r=>vc(r).call(r,t)),this}to(t){return new dc(this._source,this._transformers,t)}}var gc,mc={exports:{}};function bc(){return gc||(gc=1,function(t){function r(t){if(t)return function(t){return Object.assign(t,r.prototype),t._callbacks=new Map,t}(t);this._callbacks=new Map}r.prototype.on=function(t,r){const e=this._callbacks.get(t)??[];return e.push(r),this._callbacks.set(t,e),this},r.prototype.once=function(t,r){const e=(...n)=>{this.off(t,e),r.apply(this,n)};return e.fn=r,this.on(t,e),this},r.prototype.off=function(t,r){if(void 0===t&&void 0===r)return this._callbacks.clear(),this;if(void 0===r)return this._callbacks.delete(t),this;const e=this._callbacks.get(t);if(e){for(const[t,n]of e.entries())if(n===r||n.fn===r){e.splice(t,1);break}0===e.length?this._callbacks.delete(t):this._callbacks.set(t,e)}return this},r.prototype.emit=function(t,...r){const e=this._callbacks.get(t);if(e){const t=[...e];for(const e of t)e.apply(this,r)}return this},r.prototype.listeners=function(t){return this._callbacks.get(t)??[]},r.prototype.listenerCount=function(t){if(t)return this.listeners(t).length;let r=0;for(const t of this._callbacks.values())r+=t.length;return r},r.prototype.hasListeners=function(t){return this.listenerCount(t)>0},r.prototype.addEventListener=r.prototype.on,r.prototype.removeListener=r.prototype.off,r.prototype.removeEventListener=r.prototype.off,r.prototype.removeAllListeners=r.prototype.off,t.exports=r}(mc)),mc.exports}var _c,wc=e(bc());
/*! Hammer.JS - v2.0.17-rc - 2019-12-16
	 * http://naver.github.io/egjs
	 *
	 * Forked By Naver egjs
	 * Copyright (c) hammerjs
	 * Licensed under the MIT license */
function Sc(){return Sc=Object.assign||function(t){for(var r=1;r<arguments.length;r++){var e=arguments[r];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}return t},Sc.apply(this,arguments)}function Tc(t,r){t.prototype=Object.create(r.prototype),t.prototype.constructor=t,t.__proto__=r}function Oc(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}_c="function"!=typeof Object.assign?function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var r=Object(t),e=1;e<arguments.length;e++){var n=arguments[e];if(null!=n)for(var i in n)n.hasOwnProperty(i)&&(r[i]=n[i])}return r}:Object.assign;var Ec,Ac=_c,Ic=["","webkit","Moz","MS","ms","o"],Pc="undefined"==typeof document?{style:{}}:document.createElement("div"),Dc=Math.round,jc=Math.abs,xc=Date.now;function kc(t,r){for(var e,n,i=r[0].toUpperCase()+r.slice(1),o=0;o<Ic.length;){if((n=(e=Ic[o])?e+i:r)in t)return n;o++}}Ec="undefined"==typeof window?{}:window;var Cc=kc(Pc.style,"touchAction"),Lc=void 0!==Cc;var Mc="compute",Nc="auto",Rc="manipulation",Fc="none",zc="pan-x",qc="pan-y",Uc=function(){if(!Lc)return!1;var t={},r=Ec.CSS&&Ec.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach(function(e){return t[e]=!r||Ec.CSS.supports("touch-action",e)}),t}(),Wc="ontouchstart"in Ec,Yc=void 0!==kc(Ec,"PointerEvent"),Xc=Wc&&/mobile|tablet|ip(ad|hone|od)|android/i.test(navigator.userAgent),Bc="touch",Vc="mouse",Gc=16,Hc=24,Kc=["x","y"],Jc=["clientX","clientY"];function Qc(t,r,e){var n;if(t)if(t.forEach)t.forEach(r,e);else if(void 0!==t.length)for(n=0;n<t.length;)r.call(e,t[n],n,t),n++;else for(n in t)t.hasOwnProperty(n)&&r.call(e,t[n],n,t)}function Zc(t,r){return"function"==typeof t?t.apply(r&&r[0]||void 0,r):t}function $c(t,r){return t.indexOf(r)>-1}var tf=function(){function t(t,r){this.manager=t,this.set(r)}var r=t.prototype;return r.set=function(t){t===Mc&&(t=this.compute()),Lc&&this.manager.element.style&&Uc[t]&&(this.manager.element.style[Cc]=t),this.actions=t.toLowerCase().trim()},r.update=function(){this.set(this.manager.options.touchAction)},r.compute=function(){var t=[];return Qc(this.manager.recognizers,function(r){Zc(r.options.enable,[r])&&(t=t.concat(r.getTouchAction()))}),function(t){if($c(t,Fc))return Fc;var r=$c(t,zc),e=$c(t,qc);return r&&e?Fc:r||e?r?zc:qc:$c(t,Rc)?Rc:Nc}(t.join(" "))},r.preventDefaults=function(t){var r=t.srcEvent,e=t.offsetDirection;if(this.manager.session.prevented)r.preventDefault();else{var n=this.actions,i=$c(n,Fc)&&!Uc[Fc],o=$c(n,qc)&&!Uc[qc],u=$c(n,zc)&&!Uc[zc];if(i){var a=1===t.pointers.length,s=t.distance<2,c=t.deltaTime<250;if(a&&s&&c)return}if(!u||!o)return i||o&&6&e||u&&e&Hc?this.preventSrc(r):void 0}},r.preventSrc=function(t){this.manager.session.prevented=!0,t.preventDefault()},t}();function rf(t,r){for(;t;){if(t===r)return!0;t=t.parentNode}return!1}function ef(t){var r=t.length;if(1===r)return{x:Dc(t[0].clientX),y:Dc(t[0].clientY)};for(var e=0,n=0,i=0;i<r;)e+=t[i].clientX,n+=t[i].clientY,i++;return{x:Dc(e/r),y:Dc(n/r)}}function nf(t){for(var r=[],e=0;e<t.pointers.length;)r[e]={clientX:Dc(t.pointers[e].clientX),clientY:Dc(t.pointers[e].clientY)},e++;return{timeStamp:xc(),pointers:r,center:ef(r),deltaX:t.deltaX,deltaY:t.deltaY}}function of(t,r,e){e||(e=Kc);var n=r[e[0]]-t[e[0]],i=r[e[1]]-t[e[1]];return Math.sqrt(n*n+i*i)}function uf(t,r,e){e||(e=Kc);var n=r[e[0]]-t[e[0]],i=r[e[1]]-t[e[1]];return 180*Math.atan2(i,n)/Math.PI}function af(t,r){return t===r?1:jc(t)>=jc(r)?t<0?2:4:r<0?8:Gc}function sf(t,r,e){return{x:r/t||0,y:e/t||0}}function cf(t,r){var e=t.session,n=r.pointers,i=n.length;e.firstInput||(e.firstInput=nf(r)),i>1&&!e.firstMultiple?e.firstMultiple=nf(r):1===i&&(e.firstMultiple=!1);var o=e.firstInput,u=e.firstMultiple,a=u?u.center:o.center,s=r.center=ef(n);r.timeStamp=xc(),r.deltaTime=r.timeStamp-o.timeStamp,r.angle=uf(a,s),r.distance=of(a,s),function(t,r){var e=r.center,n=t.offsetDelta||{},i=t.prevDelta||{},o=t.prevInput||{};1!==r.eventType&&4!==o.eventType||(i=t.prevDelta={x:o.deltaX||0,y:o.deltaY||0},n=t.offsetDelta={x:e.x,y:e.y}),r.deltaX=i.x+(e.x-n.x),r.deltaY=i.y+(e.y-n.y)}(e,r),r.offsetDirection=af(r.deltaX,r.deltaY);var c,f,l=sf(r.deltaTime,r.deltaX,r.deltaY);r.overallVelocityX=l.x,r.overallVelocityY=l.y,r.overallVelocity=jc(l.x)>jc(l.y)?l.x:l.y,r.scale=u?(c=u.pointers,of((f=n)[0],f[1],Jc)/of(c[0],c[1],Jc)):1,r.rotation=u?function(t,r){return uf(r[1],r[0],Jc)+uf(t[1],t[0],Jc)}(u.pointers,n):0,r.maxPointers=e.prevInput?r.pointers.length>e.prevInput.maxPointers?r.pointers.length:e.prevInput.maxPointers:r.pointers.length,function(t,r){var e,n,i,o,u=t.lastInterval||r,a=r.timeStamp-u.timeStamp;if(8!==r.eventType&&(a>25||void 0===u.velocity)){var s=r.deltaX-u.deltaX,c=r.deltaY-u.deltaY,f=sf(a,s,c);n=f.x,i=f.y,e=jc(f.x)>jc(f.y)?f.x:f.y,o=af(s,c),t.lastInterval=r}else e=u.velocity,n=u.velocityX,i=u.velocityY,o=u.direction;r.velocity=e,r.velocityX=n,r.velocityY=i,r.direction=o}(e,r);var h,p=t.element,v=r.srcEvent;rf(h=v.composedPath?v.composedPath()[0]:v.path?v.path[0]:v.target,p)&&(p=h),r.target=p}function ff(t,r,e){var n=e.pointers.length,i=e.changedPointers.length,o=1&r&&n-i===0,u=12&r&&n-i===0;e.isFirst=!!o,e.isFinal=!!u,o&&(t.session={}),e.eventType=r,cf(t,e),t.emit("hammer.input",e),t.recognize(e),t.session.prevInput=e}function lf(t){return t.trim().split(/\s+/g)}function hf(t,r,e){Qc(lf(r),function(r){t.addEventListener(r,e,!1)})}function pf(t,r,e){Qc(lf(r),function(r){t.removeEventListener(r,e,!1)})}function vf(t){var r=t.ownerDocument||t;return r.defaultView||r.parentWindow||window}var df=function(){function t(t,r){var e=this;this.manager=t,this.callback=r,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(r){Zc(t.options.enable,[t])&&e.handler(r)},this.init()}var r=t.prototype;return r.handler=function(){},r.init=function(){this.evEl&&hf(this.element,this.evEl,this.domHandler),this.evTarget&&hf(this.target,this.evTarget,this.domHandler),this.evWin&&hf(vf(this.element),this.evWin,this.domHandler)},r.destroy=function(){this.evEl&&pf(this.element,this.evEl,this.domHandler),this.evTarget&&pf(this.target,this.evTarget,this.domHandler),this.evWin&&pf(vf(this.element),this.evWin,this.domHandler)},t}();function yf(t,r,e){if(t.indexOf&&!e)return t.indexOf(r);for(var n=0;n<t.length;){if(e&&t[n][e]==r||!e&&t[n]===r)return n;n++}return-1}var gf={pointerdown:1,pointermove:2,pointerup:4,pointercancel:8,pointerout:8},mf={2:Bc,3:"pen",4:Vc,5:"kinect"},bf="pointerdown",_f="pointermove pointerup pointercancel";Ec.MSPointerEvent&&!Ec.PointerEvent&&(bf="MSPointerDown",_f="MSPointerMove MSPointerUp MSPointerCancel");var wf=function(t){function r(){var e,n=r.prototype;return n.evEl=bf,n.evWin=_f,(e=t.apply(this,arguments)||this).store=e.manager.session.pointerEvents=[],e}return Tc(r,t),r.prototype.handler=function(t){var r=this.store,e=!1,n=t.type.toLowerCase().replace("ms",""),i=gf[n],o=mf[t.pointerType]||t.pointerType,u=o===Bc,a=yf(r,t.pointerId,"pointerId");1&i&&(0===t.button||u)?a<0&&(r.push(t),a=r.length-1):12&i&&(e=!0),a<0||(r[a]=t,this.callback(this.manager,i,{pointers:r,changedPointers:[t],pointerType:o,srcEvent:t}),e&&r.splice(a,1))},r}(df);function Sf(t){return Array.prototype.slice.call(t,0)}function Tf(t,r,e){for(var n=[],i=[],o=0;o<t.length;){var u=r?t[o][r]:t[o];yf(i,u)<0&&n.push(t[o]),i[o]=u,o++}return e&&(n=r?n.sort(function(t,e){return t[r]>e[r]}):n.sort()),n}var Of={touchstart:1,touchmove:2,touchend:4,touchcancel:8},Ef=function(t){function r(){var e;return r.prototype.evTarget="touchstart touchmove touchend touchcancel",(e=t.apply(this,arguments)||this).targetIds={},e}return Tc(r,t),r.prototype.handler=function(t){var r=Of[t.type],e=Af.call(this,t,r);e&&this.callback(this.manager,r,{pointers:e[0],changedPointers:e[1],pointerType:Bc,srcEvent:t})},r}(df);function Af(t,r){var e,n,i=Sf(t.touches),o=this.targetIds;if(3&r&&1===i.length)return o[i[0].identifier]=!0,[i,i];var u=Sf(t.changedTouches),a=[],s=this.target;if(n=i.filter(function(t){return rf(t.target,s)}),1===r)for(e=0;e<n.length;)o[n[e].identifier]=!0,e++;for(e=0;e<u.length;)o[u[e].identifier]&&a.push(u[e]),12&r&&delete o[u[e].identifier],e++;return a.length?[Tf(n.concat(a),"identifier",!0),a]:void 0}var If={mousedown:1,mousemove:2,mouseup:4},Pf=function(t){function r(){var e,n=r.prototype;return n.evEl="mousedown",n.evWin="mousemove mouseup",(e=t.apply(this,arguments)||this).pressed=!1,e}return Tc(r,t),r.prototype.handler=function(t){var r=If[t.type];1&r&&0===t.button&&(this.pressed=!0),2&r&&1!==t.which&&(r=4),this.pressed&&(4&r&&(this.pressed=!1),this.callback(this.manager,r,{pointers:[t],changedPointers:[t],pointerType:Vc,srcEvent:t}))},r}(df);function Df(t){var r=t.changedPointers[0];if(r.identifier===this.primaryTouch){var e={x:r.clientX,y:r.clientY},n=this.lastTouches;this.lastTouches.push(e);setTimeout(function(){var t=n.indexOf(e);t>-1&&n.splice(t,1)},2500)}}function jf(t,r){1&t?(this.primaryTouch=r.changedPointers[0].identifier,Df.call(this,r)):12&t&&Df.call(this,r)}function xf(t){for(var r=t.srcEvent.clientX,e=t.srcEvent.clientY,n=0;n<this.lastTouches.length;n++){var i=this.lastTouches[n],o=Math.abs(r-i.x),u=Math.abs(e-i.y);if(o<=25&&u<=25)return!0}return!1}var kf=function(){return function(t){function r(r,e){var n;return(n=t.call(this,r,e)||this).handler=function(t,r,e){var i=e.pointerType===Bc,o=e.pointerType===Vc;if(!(o&&e.sourceCapabilities&&e.sourceCapabilities.firesTouchEvents)){if(i)jf.call(Oc(Oc(n)),r,e);else if(o&&xf.call(Oc(Oc(n)),e))return;n.callback(t,r,e)}},n.touch=new Ef(n.manager,n.handler),n.mouse=new Pf(n.manager,n.handler),n.primaryTouch=null,n.lastTouches=[],n}return Tc(r,t),r.prototype.destroy=function(){this.touch.destroy(),this.mouse.destroy()},r}(df)}();function Cf(t,r,e){return!!Array.isArray(t)&&(Qc(t,e[r],e),!0)}var Lf=32,Mf=1;function Nf(t,r){var e=r.manager;return e?e.get(t):t}function Rf(t){return 16&t?"cancel":8&t?"end":4&t?"move":2&t?"start":""}var Ff=function(){function t(t){void 0===t&&(t={}),this.options=Sc({enable:!0},t),this.id=Mf++,this.manager=null,this.state=1,this.simultaneous={},this.requireFail=[]}var r=t.prototype;return r.set=function(t){return Ac(this.options,t),this.manager&&this.manager.touchAction.update(),this},r.recognizeWith=function(t){if(Cf(t,"recognizeWith",this))return this;var r=this.simultaneous;return r[(t=Nf(t,this)).id]||(r[t.id]=t,t.recognizeWith(this)),this},r.dropRecognizeWith=function(t){return Cf(t,"dropRecognizeWith",this)||(t=Nf(t,this),delete this.simultaneous[t.id]),this},r.requireFailure=function(t){if(Cf(t,"requireFailure",this))return this;var r=this.requireFail;return-1===yf(r,t=Nf(t,this))&&(r.push(t),t.requireFailure(this)),this},r.dropRequireFailure=function(t){if(Cf(t,"dropRequireFailure",this))return this;t=Nf(t,this);var r=yf(this.requireFail,t);return r>-1&&this.requireFail.splice(r,1),this},r.hasRequireFailures=function(){return this.requireFail.length>0},r.canRecognizeWith=function(t){return!!this.simultaneous[t.id]},r.emit=function(t){var r=this,e=this.state;function n(e){r.manager.emit(e,t)}e<8&&n(r.options.event+Rf(e)),n(r.options.event),t.additionalEvent&&n(t.additionalEvent),e>=8&&n(r.options.event+Rf(e))},r.tryEmit=function(t){if(this.canEmit())return this.emit(t);this.state=Lf},r.canEmit=function(){for(var t=0;t<this.requireFail.length;){if(!(33&this.requireFail[t].state))return!1;t++}return!0},r.recognize=function(t){var r=Ac({},t);if(!Zc(this.options.enable,[this,r]))return this.reset(),void(this.state=Lf);56&this.state&&(this.state=1),this.state=this.process(r),30&this.state&&this.tryEmit(r)},r.process=function(t){},r.getTouchAction=function(){},r.reset=function(){},t}(),zf=function(t){function r(r){var e;return void 0===r&&(r={}),(e=t.call(this,Sc({event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},r))||this).pTime=!1,e.pCenter=!1,e._timer=null,e._input=null,e.count=0,e}Tc(r,t);var e=r.prototype;return e.getTouchAction=function(){return[Rc]},e.process=function(t){var r=this,e=this.options,n=t.pointers.length===e.pointers,i=t.distance<e.threshold,o=t.deltaTime<e.time;if(this.reset(),1&t.eventType&&0===this.count)return this.failTimeout();if(i&&o&&n){if(4!==t.eventType)return this.failTimeout();var u=!this.pTime||t.timeStamp-this.pTime<e.interval,a=!this.pCenter||of(this.pCenter,t.center)<e.posThreshold;if(this.pTime=t.timeStamp,this.pCenter=t.center,a&&u?this.count+=1:this.count=1,this._input=t,0===this.count%e.taps)return this.hasRequireFailures()?(this._timer=setTimeout(function(){r.state=8,r.tryEmit()},e.interval),2):8}return Lf},e.failTimeout=function(){var t=this;return this._timer=setTimeout(function(){t.state=Lf},this.options.interval),Lf},e.reset=function(){clearTimeout(this._timer)},e.emit=function(){8===this.state&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))},r}(Ff),qf=function(t){function r(r){return void 0===r&&(r={}),t.call(this,Sc({pointers:1},r))||this}Tc(r,t);var e=r.prototype;return e.attrTest=function(t){var r=this.options.pointers;return 0===r||t.pointers.length===r},e.process=function(t){var r=this.state,e=t.eventType,n=6&r,i=this.attrTest(t);return n&&(8&e||!i)?16|r:n||i?4&e?8|r:2&r?4|r:2:Lf},r}(Ff);function Uf(t){return t===Gc?"down":8===t?"up":2===t?"left":4===t?"right":""}var Wf=function(t){function r(r){var e;return void 0===r&&(r={}),(e=t.call(this,Sc({event:"pan",threshold:10,pointers:1,direction:30},r))||this).pX=null,e.pY=null,e}Tc(r,t);var e=r.prototype;return e.getTouchAction=function(){var t=this.options.direction,r=[];return 6&t&&r.push(qc),t&Hc&&r.push(zc),r},e.directionTest=function(t){var r=this.options,e=!0,n=t.distance,i=t.direction,o=t.deltaX,u=t.deltaY;return i&r.direction||(6&r.direction?(i=0===o?1:o<0?2:4,e=o!==this.pX,n=Math.abs(t.deltaX)):(i=0===u?1:u<0?8:Gc,e=u!==this.pY,n=Math.abs(t.deltaY))),t.direction=i,e&&n>r.threshold&&i&r.direction},e.attrTest=function(t){return qf.prototype.attrTest.call(this,t)&&(2&this.state||!(2&this.state)&&this.directionTest(t))},e.emit=function(r){this.pX=r.deltaX,this.pY=r.deltaY;var e=Uf(r.direction);e&&(r.additionalEvent=this.options.event+e),t.prototype.emit.call(this,r)},r}(qf),Yf=function(t){function r(r){return void 0===r&&(r={}),t.call(this,Sc({event:"swipe",threshold:10,velocity:.3,direction:30,pointers:1},r))||this}Tc(r,t);var e=r.prototype;return e.getTouchAction=function(){return Wf.prototype.getTouchAction.call(this)},e.attrTest=function(r){var e,n=this.options.direction;return 30&n?e=r.overallVelocity:6&n?e=r.overallVelocityX:n&Hc&&(e=r.overallVelocityY),t.prototype.attrTest.call(this,r)&&n&r.offsetDirection&&r.distance>this.options.threshold&&r.maxPointers===this.options.pointers&&jc(e)>this.options.velocity&&4&r.eventType},e.emit=function(t){var r=Uf(t.offsetDirection);r&&this.manager.emit(this.options.event+r,t),this.manager.emit(this.options.event,t)},r}(qf),Xf=function(t){function r(r){return void 0===r&&(r={}),t.call(this,Sc({event:"pinch",threshold:0,pointers:2},r))||this}Tc(r,t);var e=r.prototype;return e.getTouchAction=function(){return[Fc]},e.attrTest=function(r){return t.prototype.attrTest.call(this,r)&&(Math.abs(r.scale-1)>this.options.threshold||2&this.state)},e.emit=function(r){if(1!==r.scale){var e=r.scale<1?"in":"out";r.additionalEvent=this.options.event+e}t.prototype.emit.call(this,r)},r}(qf),Bf=function(t){function r(r){return void 0===r&&(r={}),t.call(this,Sc({event:"rotate",threshold:0,pointers:2},r))||this}Tc(r,t);var e=r.prototype;return e.getTouchAction=function(){return[Fc]},e.attrTest=function(r){return t.prototype.attrTest.call(this,r)&&(Math.abs(r.rotation)>this.options.threshold||2&this.state)},r}(qf),Vf=function(t){function r(r){var e;return void 0===r&&(r={}),(e=t.call(this,Sc({event:"press",pointers:1,time:251,threshold:9},r))||this)._timer=null,e._input=null,e}Tc(r,t);var e=r.prototype;return e.getTouchAction=function(){return[Nc]},e.process=function(t){var r=this,e=this.options,n=t.pointers.length===e.pointers,i=t.distance<e.threshold,o=t.deltaTime>e.time;if(this._input=t,!i||!n||12&t.eventType&&!o)this.reset();else if(1&t.eventType)this.reset(),this._timer=setTimeout(function(){r.state=8,r.tryEmit()},e.time);else if(4&t.eventType)return 8;return Lf},e.reset=function(){clearTimeout(this._timer)},e.emit=function(t){8===this.state&&(t&&4&t.eventType?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=xc(),this.manager.emit(this.options.event,this._input)))},r}(Ff),Gf={domEvents:!1,touchAction:Mc,enable:!0,inputTarget:null,inputClass:null,cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}},Hf=[[Bf,{enable:!1}],[Xf,{enable:!1},["rotate"]],[Yf,{direction:6}],[Wf,{direction:6},["swipe"]],[zf],[zf,{event:"doubletap",taps:2},["tap"]],[Vf]];function Kf(t,r){var e,n=t.element;n.style&&(Qc(t.options.cssProps,function(i,o){e=kc(n.style,o),r?(t.oldCssProps[e]=n.style[e],n.style[e]=i):n.style[e]=t.oldCssProps[e]||""}),r||(t.oldCssProps={}))}var Jf=function(){function t(t,r){var e,n=this;this.options=Ac({},Gf,r||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=new((e=this).options.inputClass||(Yc?wf:Xc?Ef:Wc?kf:Pf))(e,ff),this.touchAction=new tf(this,this.options.touchAction),Kf(this,!0),Qc(this.options.recognizers,function(t){var r=n.add(new t[0](t[1]));t[2]&&r.recognizeWith(t[2]),t[3]&&r.requireFailure(t[3])},this)}var r=t.prototype;return r.set=function(t){return Ac(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},r.stop=function(t){this.session.stopped=t?2:1},r.recognize=function(t){var r=this.session;if(!r.stopped){var e;this.touchAction.preventDefaults(t);var n=this.recognizers,i=r.curRecognizer;(!i||i&&8&i.state)&&(r.curRecognizer=null,i=null);for(var o=0;o<n.length;)e=n[o],2===r.stopped||i&&e!==i&&!e.canRecognizeWith(i)?e.reset():e.recognize(t),!i&&14&e.state&&(r.curRecognizer=e,i=e),o++}},r.get=function(t){if(t instanceof Ff)return t;for(var r=this.recognizers,e=0;e<r.length;e++)if(r[e].options.event===t)return r[e];return null},r.add=function(t){if(Cf(t,"add",this))return this;var r=this.get(t.options.event);return r&&this.remove(r),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},r.remove=function(t){if(Cf(t,"remove",this))return this;var r=this.get(t);if(t){var e=this.recognizers,n=yf(e,r);-1!==n&&(e.splice(n,1),this.touchAction.update())}return this},r.on=function(t,r){if(void 0===t||void 0===r)return this;var e=this.handlers;return Qc(lf(t),function(t){e[t]=e[t]||[],e[t].push(r)}),this},r.off=function(t,r){if(void 0===t)return this;var e=this.handlers;return Qc(lf(t),function(t){r?e[t]&&e[t].splice(yf(e[t],r),1):delete e[t]}),this},r.emit=function(t,r){this.options.domEvents&&function(t,r){var e=document.createEvent("Event");e.initEvent(t,!0,!0),e.gesture=r,r.target.dispatchEvent(e)}(t,r);var e=this.handlers[t]&&this.handlers[t].slice();if(e&&e.length){r.type=t,r.preventDefault=function(){r.srcEvent.preventDefault()};for(var n=0;n<e.length;)e[n](r),n++}},r.destroy=function(){this.element&&Kf(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null},t}(),Qf={touchstart:1,touchmove:2,touchend:4,touchcancel:8},Zf=function(t){function r(){var e,n=r.prototype;return n.evTarget="touchstart",n.evWin="touchstart touchmove touchend touchcancel",(e=t.apply(this,arguments)||this).started=!1,e}return Tc(r,t),r.prototype.handler=function(t){var r=Qf[t.type];if(1===r&&(this.started=!0),this.started){var e=$f.call(this,t,r);12&r&&e[0].length-e[1].length===0&&(this.started=!1),this.callback(this.manager,r,{pointers:e[0],changedPointers:e[1],pointerType:Bc,srcEvent:t})}},r}(df);function $f(t,r){var e=Sf(t.touches),n=Sf(t.changedTouches);return 12&r&&(e=Tf(e.concat(n),"identifier",!0)),[e,n]}function tl(t,r,e){var n="DEPRECATED METHOD: "+r+"\n"+e+" AT \n";return function(){var r=new Error("get-stack-trace"),e=r&&r.stack?r.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",i=window.console&&(window.console.warn||window.console.log);return i&&i.call(window.console,n,e),t.apply(this,arguments)}}var rl=tl(function(t,r,e){for(var n=Object.keys(r),i=0;i<n.length;)(!e||e&&void 0===t[n[i]])&&(t[n[i]]=r[n[i]]),i++;return t},"extend","Use `assign`."),el=tl(function(t,r){return rl(t,r,!0)},"merge","Use `assign`.");function nl(t,r,e){var n,i=r.prototype;(n=t.prototype=Object.create(i)).constructor=t,n._super=i,e&&Ac(n,e)}function il(t,r){return function(){return t.apply(r,arguments)}}var ol=function(){var t=function(t,r){return void 0===r&&(r={}),new Jf(t,Sc({recognizers:Hf.concat()},r))};return t.VERSION="2.0.17-rc",t.DIRECTION_ALL=30,t.DIRECTION_DOWN=Gc,t.DIRECTION_LEFT=2,t.DIRECTION_RIGHT=4,t.DIRECTION_UP=8,t.DIRECTION_HORIZONTAL=6,t.DIRECTION_VERTICAL=Hc,t.DIRECTION_NONE=1,t.DIRECTION_DOWN=Gc,t.INPUT_START=1,t.INPUT_MOVE=2,t.INPUT_END=4,t.INPUT_CANCEL=8,t.STATE_POSSIBLE=1,t.STATE_BEGAN=2,t.STATE_CHANGED=4,t.STATE_ENDED=8,t.STATE_RECOGNIZED=8,t.STATE_CANCELLED=16,t.STATE_FAILED=Lf,t.Manager=Jf,t.Input=df,t.TouchAction=tf,t.TouchInput=Ef,t.MouseInput=Pf,t.PointerEventInput=wf,t.TouchMouseInput=kf,t.SingleTouchInput=Zf,t.Recognizer=Ff,t.AttrRecognizer=qf,t.Tap=zf,t.Pan=Wf,t.Swipe=Yf,t.Pinch=Xf,t.Rotate=Bf,t.Press=Vf,t.on=hf,t.off=pf,t.each=Qc,t.merge=el,t.extend=rl,t.bindFn=il,t.assign=Ac,t.inherit=nl,t.bindFn=il,t.prefixed=kc,t.toArray=Sf,t.inArray=yf,t.uniqueArray=Tf,t.splitStr=lf,t.boolOrFn=Zc,t.hasParent=rf,t.addEventListeners=hf,t.removeEventListeners=pf,t.defaults=Ac({},Gf,{preset:Hf}),t}();
/**
	 * vis-util
	 * https://github.com/visjs/vis-util
	 *
	 * utilitie collection for visjs
	 *
	 * @version 6.0.0
	 * @date    2025-07-12T18:02:43.836Z
	 *
	 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
	 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
	 *
	 * @license
	 * vis.js is dual licensed under both
	 *
	 *   1. The Apache 2.0 License
	 *      http://www.apache.org/licenses/LICENSE-2.0
	 *
	 *   and
	 *
	 *   2. The MIT License
	 *      http://opensource.org/licenses/MIT
	 *
	 * vis.js may be distributed under either license.
	 */const ul=Symbol("DELETE");function al(...t){const r=sl(...t);return fl(r),r}function sl(...t){if(t.length<2)return t[0];if(t.length>2)return sl(al(t[0],t[1]),...t.slice(2));const r=t[0],e=t[1];if(r instanceof Date&&e instanceof Date)return r.setTime(e.getTime()),r;for(const t of Reflect.ownKeys(e))Object.prototype.propertyIsEnumerable.call(e,t)&&(e[t]===ul?delete r[t]:null===r[t]||null===e[t]||"object"!=typeof r[t]||"object"!=typeof e[t]||Array.isArray(r[t])||Array.isArray(e[t])?r[t]=cl(e[t]):r[t]=sl(r[t],e[t]));return r}function cl(t){return Array.isArray(t)?t.map(t=>cl(t)):"object"==typeof t&&null!==t?t instanceof Date?new Date(t.getTime()):sl({},t):t}function fl(t){for(const r of Object.keys(t))t[r]===ul?delete t[r]:"object"==typeof t[r]&&null!==t[r]&&fl(t[r])}const ll="undefined"!=typeof window?window.Hammer||ol:function(){return function(){const t=()=>{};return{on:t,off:t,destroy:t,emit:t,get:()=>({set:t})}}()};function hl(t){this._cleanupQueue=[],this.active=!1,this._dom={container:t,overlay:document.createElement("div")},this._dom.overlay.classList.add("vis-overlay"),this._dom.container.appendChild(this._dom.overlay),this._cleanupQueue.push(()=>{this._dom.overlay.parentNode.removeChild(this._dom.overlay)});const r=ll(this._dom.overlay);r.on("tap",this._onTapOverlay.bind(this)),this._cleanupQueue.push(()=>{r.destroy()});["tap","doubletap","press","pinch","pan","panstart","panmove","panend"].forEach(t=>{r.on(t,t=>{t.srcEvent.stopPropagation()})}),document&&document.body&&(this._onClick=r=>{(function(t,r){for(;t;){if(t===r)return!0;t=t.parentNode}return!1})(r.target,t)||this.deactivate()},document.body.addEventListener("click",this._onClick),this._cleanupQueue.push(()=>{document.body.removeEventListener("click",this._onClick)})),this._escListener=t=>{("key"in t?"Escape"===t.key:27===t.keyCode)&&this.deactivate()}}var pl,vl,dl,yl,gl,ml;function bl(){return vl?pl:(vl=1,zi(),pl=It().Object.getOwnPropertySymbols)}function _l(){return yl?dl:(yl=1,dl=bl())}wc(hl.prototype),hl.current=null,hl.prototype.destroy=function(){this.deactivate();for(const t of this._cleanupQueue.splice(0).reverse())t()},hl.prototype.activate=function(){hl.current&&hl.current.deactivate(),hl.current=this,this.active=!0,this._dom.overlay.style.display="none",this._dom.container.classList.add("vis-active"),this.emit("change"),this.emit("activate"),document.body.addEventListener("keydown",this._escListener)},hl.prototype.deactivate=function(){this.active=!1,this._dom.overlay.style.display="block",this._dom.container.classList.remove("vis-active"),document.body.removeEventListener("keydown",this._escListener),this.emit("change"),this.emit("deactivate")},hl.prototype._onTapOverlay=function(t){this.activate(),t.srcEvent.stopPropagation()};var wl,Sl,Tl,Ol,El,Al,Il=e(ml?gl:(ml=1,gl=_l())),Pl={exports:{}},Dl={};function jl(){if(Sl)return Pl.exports;Sl=1,function(){if(wl)return Dl;wl=1;var t=$r(),r=S(),e=Et(),n=Ar().f,i=L();t({target:"Object",stat:!0,forced:!i||r(function(){n(1)}),sham:!i},{getOwnPropertyDescriptor:function(t,r){return n(e(t),r)}})}();var t=It().Object,r=Pl.exports=function(r,e){return t.getOwnPropertyDescriptor(r,e)};return t.getOwnPropertyDescriptor.sham&&(r.sham=!0),Pl.exports}function xl(){return Ol?Tl:(Ol=1,Tl=jl())}var kl,Cl,Ll,Ml,Nl,Rl,Fl,zl,ql,Ul,Wl,Yl=e(Al?El:(Al=1,El=xl())),Xl={};function Bl(){if(Cl)return kl;Cl=1;var t=Si().forEach,r=ys()("forEach");return kl=r?[].forEach:function(r){return t(this,r,arguments.length>1?arguments[1]:void 0)},kl}function Vl(){return Nl?Ml:(Nl=1,function(){if(Ll)return Xl;Ll=1;var t=$r(),r=Bl();t({target:"Array",proto:!0,forced:[].forEach!==r},{forEach:r})}(),Ml=Va()("Array","forEach"))}function Gl(){return Fl?Rl:(Fl=1,Rl=Vl())}function Hl(){if(ql)return zl;ql=1;var t=Be(),r=br(),e=Dt(),n=Gl(),i=Array.prototype,o={DOMTokenList:!0,NodeList:!0};return zl=function(u){var a=u.forEach;return u===i||e(i,u)&&a===i.forEach||r(o,t(u))?n:a}}var Kl,Jl,Ql,Zl,$l,th,rh,eh,nh,ih=e(Wl?Ul:(Wl=1,Ul=Hl())),oh={};function uh(){if(Jl)return Kl;Jl=1;var t=Pt(),r=E(),e=Nn(),n=Kn(),i=Jr(),o=r([].concat);return Kl=t("Reflect","ownKeys")||function(t){var r=e.f(i(t)),u=n.f;return u?o(r,u(t)):r},Kl}function ah(){return $l?Zl:($l=1,function(){if(Ql)return oh;Ql=1;var t=$r(),r=L(),e=uh(),n=Et(),i=Ar(),o=Ye();t({target:"Object",stat:!0,sham:!r},{getOwnPropertyDescriptors:function(t){for(var r,u,a=n(t),s=i.f,c=e(a),f={},l=0;c.length>l;)void 0!==(u=s(a,r=c[l++]))&&o(f,r,u);return f}})}(),Zl=It().Object.getOwnPropertyDescriptors)}function sh(){return rh?th:(rh=1,th=ah())}var ch,fh,lh,hh,ph,vh,dh=e(nh?eh:(nh=1,eh=sh())),yh={exports:{}},gh={};function mh(){if(ch)return gh;ch=1;var t=$r(),r=L(),e=jn().f;return t({target:"Object",stat:!0,forced:Object.defineProperties!==e,sham:!r},{defineProperties:e}),gh}function bh(){if(fh)return yh.exports;fh=1,mh();var t=It().Object,r=yh.exports=function(r,e){return t.defineProperties(r,e)};return t.defineProperties.sham&&(r.sham=!0),yh.exports}function _h(){return hh?lh:(hh=1,lh=bh())}var wh,Sh,Th=e(vh?ph:(vh=1,ph=_h()));var Oh,Eh,Ah,Ih,Ph,Dh,jh,xh=e(Sh?wh:(Sh=1,wh=ee()));function kh(){return Ah?Eh:(Ah=1,Oh||(Oh=1,$r()({target:"Array",stat:!0},{isArray:Re()})),Eh=It().Array.isArray)}function Ch(){return Ph?Ih:(Ph=1,Ih=kh())}var Lh,Mh,Nh,Rh,Fh,zh,qh,Uh,Wh,Yh,Xh,Bh,Vh,Gh,Hh,Kh,Jh,Qh,Zh,$h,tp,rp,ep,np,ip,op,up,ap,sp,cp=e(jh?Dh:(jh=1,Dh=Ch())),fp={},lp={exports:{}};function hp(){return Mh?Lh:(Mh=1,Lh=S()(function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}}))}function pp(){if(Rh)return Nh;Rh=1;var t=S(),r=At(),e=A(),n=hp(),i=Object.isExtensible,o=t(function(){});return Nh=o||n?function(t){return!!r(t)&&((!n||"ArrayBuffer"!==e(t))&&(!i||i(t)))}:i}function vp(){return zh?Fh:(zh=1,Fh=!S()(function(){return Object.isExtensible(Object.preventExtensions({}))}))}function dp(){if(qh)return lp.exports;qh=1;var t=$r(),r=E(),e=An(),n=At(),i=br(),o=Qr().f,u=Nn(),a=Wn(),s=pp(),c=_r(),f=vp(),l=!1,h=c("meta"),p=0,v=function(t){o(t,h,{value:{objectID:"O"+p++,weakData:{}}})},d=lp.exports={enable:function(){d.enable=function(){},l=!0;var e=u.f,n=r([].splice),i={};i[h]=1,e(i).length&&(u.f=function(t){for(var r=e(t),i=0,o=r.length;i<o;i++)if(r[i]===h){n(r,i,1);break}return r},t({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:a.f}))},fastKey:function(t,r){if(!n(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!i(t,h)){if(!s(t))return"F";if(!r)return"E";v(t)}return t[h].objectID},getWeakData:function(t,r){if(!i(t,h)){if(!s(t))return!0;if(!r)return!1;v(t)}return t[h].weakData},onFreeze:function(t){return f&&l&&s(t)&&!i(t,h)&&v(t),t}};return e[h]=!0,lp.exports}function yp(){if(Wh)return Uh;Wh=1;var t=wr(),r=Qo(),e=t("iterator"),n=Array.prototype;return Uh=function(t){return void 0!==t&&(r.Array===t||n[e]===t)}}function gp(){if(Xh)return Yh;Xh=1;var t=Be(),r=Rt(),e=Tt(),n=Qo(),i=wr()("iterator");return Yh=function(o){if(!e(o))return r(o,i)||r(o,"@@iterator")||n[t(o)]}}function mp(){if(Vh)return Bh;Vh=1;var t=M(),r=Nt(),e=Jr(),n=Mt(),i=gp(),o=TypeError;return Bh=function(u,a){var s=arguments.length<2?i(u):a;if(r(s))return e(t(s,u));throw new o(n(u)+" is not iterable")},Bh}function bp(){if(Hh)return Gh;Hh=1;var t=M(),r=Jr(),e=Rt();return Gh=function(n,i,o){var u,a;r(n);try{if(!(u=e(n,"return"))){if("throw"===i)throw o;return o}u=t(u,n)}catch(t){a=!0,u=t}if("throw"===i)throw o;if(a)throw u;return r(u),o},Gh}function _p(){if(Jh)return Kh;Jh=1;var t=Pr(),r=M(),e=Jr(),n=Mt(),i=yp(),o=Ue(),u=Dt(),a=mp(),s=gp(),c=bp(),f=TypeError,l=function(t,r){this.stopped=t,this.result=r},h=l.prototype;return Kh=function(p,v,d){var y,g,m,b,_,w,S,T=d&&d.that,O=!(!d||!d.AS_ENTRIES),E=!(!d||!d.IS_RECORD),A=!(!d||!d.IS_ITERATOR),I=!(!d||!d.INTERRUPTED),P=t(v,T),D=function(t){return y&&c(y,"normal"),new l(!0,t)},j=function(t){return O?(e(t),I?P(t[0],t[1],D):P(t[0],t[1])):I?P(t,D):P(t)};if(E)y=p.iterator;else if(A)y=p;else{if(!(g=s(p)))throw new f(n(p)+" is not iterable");if(i(g)){for(m=0,b=o(p);b>m;m++)if((_=j(p[m]))&&u(h,_))return _;return new l(!1)}y=a(p,g)}for(w=E?p.next:y.next;!(S=r(w,y)).done;){try{_=j(S.value)}catch(t){c(y,"throw",t)}if("object"==typeof _&&_&&u(h,_))return _}return new l(!1)},Kh}function wp(){if(Zh)return Qh;Zh=1;var t=Dt(),r=TypeError;return Qh=function(e,n){if(t(n,e))return e;throw new r("Incorrect invocation")}}function Sp(){if(tp)return $h;tp=1;var t=$r(),r=w(),e=dp(),n=S(),i=Zr(),o=_p(),u=wp(),a=P(),s=At(),c=Tt(),f=bi(),l=Qr().f,h=Si().forEach,p=L(),v=wi(),d=v.set,y=v.getterFor;return $h=function(v,g,m){var b,_=-1!==v.indexOf("Map"),w=-1!==v.indexOf("Weak"),S=_?"set":"add",T=r[v],O=T&&T.prototype,E={};if(p&&a(T)&&(w||O.forEach&&!n(function(){(new T).entries().next()}))){var A=(b=g(function(t,r){d(u(t,A),{type:v,collection:new T}),c(r)||o(r,t[S],{that:t,AS_ENTRIES:_})})).prototype,I=y(v);h(["add","clear","delete","forEach","get","has","set","keys","values","entries"],function(t){var r="add"===t||"set"===t;!(t in O)||w&&"clear"===t||i(A,t,function(e,n){var i=I(this).collection;if(!r&&w&&!s(e))return"get"===t&&void 0;var o=i[t](0===e?0:e,n);return r?this:o})}),w||l(A,"size",{configurable:!0,get:function(){return I(this).collection.size}})}else b=m.getConstructor(g,v,_,S),e.enable();return f(b,v,!1,!0),E[v]=b,t({global:!0,forced:!0},E),w||m.setStrong(b,v,_),b}}function Tp(){if(ep)return rp;ep=1;var t=Jn();return rp=function(r,e,n){for(var i in e)n&&n.unsafe&&r[i]?r[i]=e[i]:t(r,i,e[i],n);return r}}function Op(){if(ip)return np;ip=1;var t=Pt(),r=Qn(),e=wr(),n=L(),i=e("species");return np=function(e){var o=t(e);n&&o&&!o[i]&&r(o,i,{configurable:!0,get:function(){return this}})}}function Ep(){if(up)return op;up=1;var t=Cn(),r=Qn(),e=Tp(),n=Pr(),i=wp(),o=Tt(),u=_p(),a=au(),s=su(),c=Op(),f=L(),l=dp().fastKey,h=wi(),p=h.set,v=h.getterFor;return op={getConstructor:function(a,s,c,h){var d=a(function(r,e){i(r,y),p(r,{type:s,index:t(null),first:null,last:null,size:0}),f||(r.size=0),o(e)||u(e,r[h],{that:r,AS_ENTRIES:c})}),y=d.prototype,g=v(s),m=function(t,r,e){var n,i,o=g(t),u=b(t,r);return u?u.value=e:(o.last=u={index:i=l(r,!0),key:r,value:e,previous:n=o.last,next:null,removed:!1},o.first||(o.first=u),n&&(n.next=u),f?o.size++:t.size++,"F"!==i&&(o.index[i]=u)),t},b=function(t,r){var e,n=g(t),i=l(r);if("F"!==i)return n.index[i];for(e=n.first;e;e=e.next)if(e.key===r)return e};return e(y,{clear:function(){for(var r=g(this),e=r.first;e;)e.removed=!0,e.previous&&(e.previous=e.previous.next=null),e=e.next;r.first=r.last=null,r.index=t(null),f?r.size=0:this.size=0},delete:function(t){var r=this,e=g(r),n=b(r,t);if(n){var i=n.next,o=n.previous;delete e.index[n.index],n.removed=!0,o&&(o.next=i),i&&(i.previous=o),e.first===n&&(e.first=i),e.last===n&&(e.last=o),f?e.size--:r.size--}return!!n},forEach:function(t){for(var r,e=g(this),i=n(t,arguments.length>1?arguments[1]:void 0);r=r?r.next:e.first;)for(i(r.value,r.key,this);r&&r.removed;)r=r.previous},has:function(t){return!!b(this,t)}}),e(y,c?{get:function(t){var r=b(this,t);return r&&r.value},set:function(t,r){return m(this,0===t?0:t,r)}}:{add:function(t){return m(this,t=0===t?0:t,t)}}),f&&r(y,"size",{configurable:!0,get:function(){return g(this).size}}),d},setStrong:function(t,r,e){var n=r+" Iterator",i=v(r),o=v(n);a(t,r,function(t,r){p(this,{type:n,target:t,state:i(t),kind:r,last:null})},function(){for(var t=o(this),r=t.kind,e=t.last;e&&e.removed;)e=e.previous;return t.target&&(t.last=e=e?e.next:t.state.first)?s("keys"===r?e.key:"values"===r?e.value:[e.key,e.value],!1):(t.target=null,s(void 0,!0))},e?"entries":"values",!e,!0),c(r)}},op}function Ap(){return sp||(sp=1,ap||(ap=1,Sp()("Map",function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},Ep()))),fp}var Ip,Pp,Dp,jp,xp,kp,Cp,Lp,Mp,Np,Rp,Fp={};function zp(){return Pp?Ip:(Pp=1,Ip=function(t,r){return 1===r?function(r,e){return r[t](e)}:function(r,e,n){return r[t](e,n)}})}function qp(){if(jp)return Dp;jp=1;var t=Pt(),r=zp(),e=t("Map");return Dp={Map:e,set:r("set",2),get:r("get",1),has:r("has",1),remove:r("delete",1),proto:e.prototype}}function Up(){return Cp?kp:(Cp=1,cu(),Ap(),function(){if(xp)return Fp;xp=1;var t=$r(),r=E(),e=Nt(),n=Ot(),i=_p(),o=qp(),u=vr(),a=S(),s=o.Map,c=o.has,f=o.get,l=o.set,h=r([].push),p=u||a(function(){return 1!==s.groupBy("ab",function(t){return t}).get("a").length});t({target:"Map",stat:!0,forced:u||p},{groupBy:function(t,r){n(t),e(r);var o=new s,u=0;return i(t,function(t){var e=r(t,u++);c(o,e)?h(f(o,e),t):l(o,e,[t])}),o}})}(),oa(),kp=It().Map)}function Wp(){if(Mp)return Lp;Mp=1;var t=Up();return lu(),Lp=t}var Yp,Xp,Bp,Vp,Gp,Hp,Kp,Jp,Qp,Zp=e(Rp?Np:(Rp=1,Np=Wp())),$p={};function tv(){return Bp?Xp:(Bp=1,function(){if(Yp)return $p;Yp=1;var t=$r(),r=Si().some;t({target:"Array",proto:!0,forced:!ys()("some")},{some:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})}(),Xp=Va()("Array","some"))}function rv(){if(Gp)return Vp;Gp=1;var t=Dt(),r=tv(),e=Array.prototype;return Vp=function(n){var i=n.some;return n===e||t(e,n)&&i===e.some?r:i}}function ev(){return Kp?Hp:(Kp=1,Hp=rv())}var nv,iv,ov,uv,av,sv,cv,fv,lv,hv=e(Qp?Jp:(Qp=1,Jp=ev())),pv={};function vv(){if(iv)return nv;iv=1;var t=L(),r=E(),e=M(),n=S(),i=Dn(),o=Kn(),u=_t(),a=mr(),s=St(),c=Object.assign,f=Object.defineProperty,l=r([].concat);return nv=!c||n(function(){if(t&&1!==c({b:1},c(f({},"a",{enumerable:!0,get:function(){f(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var r={},e={},n=Symbol("assign detection"),o="abcdefghijklmnopqrst";return r[n]=7,o.split("").forEach(function(t){e[t]=t}),7!==c({},r)[n]||i(c({},e)).join("")!==o})?function(r,n){for(var c=a(r),f=arguments.length,h=1,p=o.f,v=u.f;f>h;)for(var d,y=s(arguments[h++]),g=p?l(i(y),p(y)):i(y),m=g.length,b=0;m>b;)d=g[b++],t&&!e(v,y,d)||(c[d]=y[d]);return c}:c,nv}function dv(){return av?uv:(av=1,function(){if(ov)return pv;ov=1;var t=$r(),r=vv();t({target:"Object",stat:!0,arity:2,forced:Object.assign!==r},{assign:r})}(),uv=It().Object.assign)}function yv(){return cv?sv:(cv=1,sv=dv())}var gv,mv,bv,_v,wv,Sv,Tv,Ov,Ev=e(lv?fv:(lv=1,fv=yv()));function Av(){return mv?gv:(mv=1,Qe(),gv=Va()("Array","concat"))}function Iv(){if(_v)return bv;_v=1;var t=Dt(),r=Av(),e=Array.prototype;return bv=function(n){var i=n.concat;return n===e||t(e,n)&&i===e.concat?r:i}}function Pv(){return Sv?wv:(Sv=1,wv=Iv())}var Dv,jv,xv,kv,Cv,Lv,Mv,Nv,Rv=e(Ov?Tv:(Ov=1,Tv=Pv()));function Fv(){return jv?Dv:(jv=1,cu(),Dv=Va()("Array","keys"))}function zv(){return kv?xv:(kv=1,xv=Fv())}function qv(){if(Lv)return Cv;Lv=1,lu();var t=Be(),r=br(),e=Dt(),n=zv(),i=Array.prototype,o={DOMTokenList:!0,NodeList:!0};return Cv=function(u){var a=u.keys;return u===i||e(i,u)&&a===i.keys||r(o,t(u))?n:a}}var Uv,Wv,Yv,Xv,Bv,Vv,Gv,Hv=e(Nv?Mv:(Nv=1,Mv=qv())),Kv={};function Jv(){return Yv?Wv:(Yv=1,function(){if(Uv)return Kv;Uv=1;var t=$r(),r=mr(),e=Dn();t({target:"Object",stat:!0,forced:S()(function(){e(1)})},{keys:function(t){return e(r(t))}})}(),Wv=It().Object.keys)}function Qv(){return Bv?Xv:(Bv=1,Xv=Jv())}var Zv,$v,td,rd,ed,nd,id,od,ud,ad,sd,cd,fd,ld,hd,pd,vd,dd,yd,gd=e(Gv?Vv:(Gv=1,Vv=Qv())),md={};function bd(){if($v)return Zv;$v=1;var t=Mt(),r=TypeError;return Zv=function(e,n){if(!delete e[n])throw new r("Cannot delete property "+t(n)+" of "+t(e))}}function _d(){if(rd)return td;rd=1;var t=Un(),r=Math.floor,e=function(n,i){var o=n.length;if(o<8)for(var u,a,s=1;s<o;){for(a=s,u=n[s];a&&i(n[a-1],u)>0;)n[a]=n[--a];a!==s++&&(n[a]=u)}else for(var c=r(o/2),f=e(t(n,0,c),i),l=e(t(n,c),i),h=f.length,p=l.length,v=0,d=0;v<h||d<p;)n[v+d]=v<h&&d<p?i(f[v],l[d])<=0?f[v++]:l[d++]:v<h?f[v++]:l[d++];return n};return td=e}function wd(){if(nd)return ed;nd=1;var t=jt().match(/firefox\/(\d+)/i);return ed=!!t&&+t[1]}function Sd(){return od?id:(od=1,id=/MSIE|Trident/.test(jt()))}function Td(){if(ad)return ud;ad=1;var t=jt().match(/AppleWebKit\/(\d+)\./);return ud=!!t&&+t[1]}function Od(){if(sd)return md;sd=1;var t=$r(),r=E(),e=Nt(),n=mr(),i=Ue(),o=bd(),u=en(),a=S(),s=_d(),c=ys(),f=wd(),l=Sd(),h=xt(),p=Td(),v=[],d=r(v.sort),y=r(v.push),g=a(function(){v.sort(void 0)}),m=a(function(){v.sort(null)}),b=c("sort"),_=!a(function(){if(h)return h<70;if(!(f&&f>3)){if(l)return!0;if(p)return p<603;var t,r,e,n,i="";for(t=65;t<76;t++){switch(r=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:e=3;break;case 68:case 71:e=4;break;default:e=2}for(n=0;n<47;n++)v.push({k:r+n,v:e})}for(v.sort(function(t,r){return r.v-t.v}),n=0;n<v.length;n++)r=v[n].k.charAt(0),i.charAt(i.length-1)!==r&&(i+=r);return"DGBEFHACIJK"!==i}});return t({target:"Array",proto:!0,forced:g||!m||!b||!_},{sort:function(t){void 0!==t&&e(t);var r=n(this);if(_)return void 0===t?d(r):d(r,t);var a,c,f=[],l=i(r);for(c=0;c<l;c++)c in r&&y(f,r[c]);for(s(f,function(t){return function(r,e){return void 0===e?-1:void 0===r?1:void 0!==t?+t(r,e)||0:u(r)>u(e)?1:-1}}(t)),a=i(f),c=0;c<a;)r[c]=f[c++];for(;c<l;)o(r,c++);return r}}),md}function Ed(){return fd?cd:(fd=1,Od(),cd=Va()("Array","sort"))}function Ad(){if(hd)return ld;hd=1;var t=Dt(),r=Ed(),e=Array.prototype;return ld=function(n){var i=n.sort;return n===e||t(e,n)&&i===e.sort?r:i}}function Id(){return vd?pd:(vd=1,pd=Ad())}var Pd,Dd,jd,xd,kd,Cd,Ld,Md,Nd=e(yd?dd:(yd=1,dd=Id()));function Rd(){return Dd?Pd:(Dd=1,cu(),Pd=Va()("Array","values"))}function Fd(){return xd?jd:(xd=1,jd=Rd())}function zd(){if(Cd)return kd;Cd=1,lu();var t=Be(),r=br(),e=Dt(),n=Fd(),i=Array.prototype,o={DOMTokenList:!0,NodeList:!0};return kd=function(u){var a=u.values;return u===i||e(i,u)&&a===i.values||r(o,t(u))?n:a}}var qd,Ud,Wd,Yd,Xd,Bd,Vd,Gd,Hd,Kd,Jd,Qd,Zd,$d=e(Md?Ld:(Md=1,Ld=zd())),ty={};function ry(){if(Ud)return qd;Ud=1;var t=ze(),r=en(),e=Ot(),n=RangeError;return qd=function(i){var o=r(e(this)),u="",a=t(i);if(a<0||a===1/0)throw new n("Wrong number of repetitions");for(;a>0;(a>>>=1)&&(o+=o))1&a&&(u+=o);return u}}function ey(){if(Yd)return Wd;Yd=1;var t=E(),r=qe(),e=en(),n=ry(),i=Ot(),o=t(n),u=t("".slice),a=Math.ceil,s=function(t){return function(n,s,c){var f,l,h=e(i(n)),p=r(s),v=h.length,d=void 0===c?" ":e(c);return p<=v||""===d?h:((l=o(d,a((f=p-v)/d.length))).length>f&&(l=u(l,0,f)),t?h+l:l+h)}};return Wd={start:s(!1),end:s(!0)}}function ny(){if(Bd)return Xd;Bd=1;var t=E(),r=S(),e=ey().start,n=RangeError,i=isFinite,o=Math.abs,u=Date.prototype,a=u.toISOString,s=t(u.getTime),c=t(u.getUTCDate),f=t(u.getUTCFullYear),l=t(u.getUTCHours),h=t(u.getUTCMilliseconds),p=t(u.getUTCMinutes),v=t(u.getUTCMonth),d=t(u.getUTCSeconds);return Xd=r(function(){return"0385-07-25T07:06:39.999Z"!==a.call(new Date(-50000000000001))})||!r(function(){a.call(new Date(NaN))})?function(){if(!i(s(this)))throw new n("Invalid time value");var t=this,r=f(t),u=h(t),a=r<0?"-":r>9999?"+":"";return a+e(o(r),a?6:4,0)+"-"+e(v(t)+1,2,0)+"-"+e(c(t),2,0)+"T"+e(l(t),2,0)+":"+e(p(t),2,0)+":"+e(d(t),2,0)+"."+e(u,3,0)+"Z"}:a}function iy(){if(Hd)return Gd;Hd=1,function(){if(Vd)return ty;Vd=1;var t=$r(),r=M(),e=mr(),n=Sr(),i=ny(),o=A();t({target:"Date",proto:!0,forced:S()(function(){return null!==new Date(NaN).toJSON()||1!==r(Date.prototype.toJSON,{toISOString:function(){return 1}})})},{toJSON:function(t){var u=e(this),a=n(u,"number");return"number"!=typeof a||isFinite(a)?"toISOString"in u||"Date"!==o(u)?u.toISOString():r(i,u):null}})}(),Mi();var t=It(),r=O();return t.JSON||(t.JSON={stringify:JSON.stringify}),Gd=function(e,n,i){return r(t.JSON.stringify,null,arguments)},Gd}function oy(){return Jd?Kd:(Jd=1,Kd=iy())}var uy,ay,sy=e(Zd?Qd:(Zd=1,Qd=oy()));var cy,fy,ly,hy,py,vy,dy,yy,gy=e(ay?uy:(ay=1,uy=aa()));function my(){return fy?cy:(fy=1,cu(),cy=Va()("Array","entries"))}function by(){return hy?ly:(hy=1,ly=my())}function _y(){if(vy)return py;vy=1,lu();var t=Be(),r=br(),e=Dt(),n=by(),i=Array.prototype,o={DOMTokenList:!0,NodeList:!0};return py=function(u){var a=u.entries;return u===i||e(i,u)&&a===i.entries||r(o,t(u))?n:a}}var wy=e(yy?dy:(yy=1,dy=_y()));const Sy=[];for(let t=0;t<256;++t)Sy.push((t+256).toString(16).slice(1));let Ty;const Oy=new Uint8Array(16);var Ey={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function Ay(t,r,e){if(Ey.randomUUID&&!t)return Ey.randomUUID();const n=(t=t||{}).random??t.rng?.()??function(){if(!Ty){if("undefined"==typeof crypto||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");Ty=crypto.getRandomValues.bind(crypto)}return Ty(Oy)}();if(n.length<16)throw new Error("Random bytes length must be >= 16");return n[6]=15&n[6]|64,n[8]=63&n[8]|128,function(t,r=0){return(Sy[t[r+0]]+Sy[t[r+1]]+Sy[t[r+2]]+Sy[t[r+3]]+"-"+Sy[t[r+4]]+Sy[t[r+5]]+"-"+Sy[t[r+6]]+Sy[t[r+7]]+"-"+Sy[t[r+8]]+Sy[t[r+9]]+"-"+Sy[t[r+10]]+Sy[t[r+11]]+Sy[t[r+12]]+Sy[t[r+13]]+Sy[t[r+14]]+Sy[t[r+15]]).toLowerCase()}(n)}function Iy(t){return"string"==typeof t||"number"==typeof t}var Py,Dy,jy,xy,ky,Cy={},Ly={};function My(){if(Dy)return Py;Dy=1;var t=TypeError;return Py=function(r,e){if(r<e)throw new t("Not enough arguments");return r}}function Ny(){if(xy)return jy;xy=1;var t,r=w(),e=O(),n=P(),i=gs(),o=jt(),u=Un(),a=My(),s=r.Function,c=/MSIE .\./.test(o)||"BUN"===i&&((t=r.Bun.version.split(".")).length<3||"0"===t[0]&&(t[1]<3||"3"===t[1]&&"0"===t[2]));return jy=function(t,r){var i=r?2:1;return c?function(o,c){var f=a(arguments.length,1)>i,l=n(o)?o:s(o),h=f?u(arguments,i):[],p=f?function(){e(l,this,h)}:l;return r?t(p,c):t(p)}:t},jy}var Ry,Fy,zy,qy,Uy,Wy,Yy={};function Xy(){return Fy||(Fy=1,function(){if(ky)return Ly;ky=1;var t=$r(),r=w(),e=Ny()(r.setInterval,!0);t({global:!0,bind:!0,forced:r.setInterval!==e},{setInterval:e})}(),function(){if(Ry)return Yy;Ry=1;var t=$r(),r=w(),e=Ny()(r.setTimeout,!0);t({global:!0,bind:!0,forced:r.setTimeout!==e},{setTimeout:e})}()),Cy}function By(){return qy?zy:(qy=1,Xy(),zy=It().setTimeout)}var Vy,Gy,Hy,Ky,Jy,Qy,Zy,$y,tg,rg,eg,ng=e(Wy?Uy:(Wy=1,Uy=By())),ig={};function og(){if(Gy)return Vy;Gy=1;var t=L(),r=Re(),e=TypeError,n=Object.getOwnPropertyDescriptor,i=t&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();return Vy=i?function(t,i){if(r(t)&&!n(t,"length").writable)throw new e("Cannot set read only .length");return t.length=i}:function(t,r){return t.length=r}}function ug(){return Jy?Ky:(Jy=1,function(){if(Hy)return ig;Hy=1;var t=$r(),r=mr(),e=On(),n=ze(),i=Ue(),o=og(),u=We(),a=Ke(),s=Ye(),c=bd(),f=Je()("splice"),l=Math.max,h=Math.min;t({target:"Array",proto:!0,forced:!f},{splice:function(t,f){var p,v,d,y,g,m,b=r(this),_=i(b),w=e(t,_),S=arguments.length;for(0===S?p=v=0:1===S?(p=0,v=_-w):(p=S-2,v=h(l(n(f),0),_-w)),u(_+p-v),d=a(b,v),y=0;y<v;y++)(g=w+y)in b&&s(d,y,b[g]);if(d.length=v,p<v){for(y=w;y<_-v;y++)m=y+p,(g=y+v)in b?b[m]=b[g]:c(b,m);for(y=_;y>_-v+p;y--)c(b,y-1)}else if(p>v)for(y=_-v;y>w;y--)m=y+p-1,(g=y+v-1)in b?b[m]=b[g]:c(b,m);for(y=0;y<p;y++)b[y+w]=arguments[y+2];return o(b,_-v+p),d}})}(),Ky=Va()("Array","splice"))}function ag(){if(Zy)return Qy;Zy=1;var t=Dt(),r=ug(),e=Array.prototype;return Qy=function(n){var i=n.splice;return n===e||t(e,n)&&i===e.splice?r:i}}function sg(){return tg?$y:(tg=1,$y=ag())}var cg=e(eg?rg:(eg=1,rg=sg()));class fg{constructor(t){ja(this,"_queue",[]),ja(this,"_timeout",null),ja(this,"_extended",null),this.delay=null,this.max=1/0,this.setOptions(t)}setOptions(t){t&&void 0!==t.delay&&(this.delay=t.delay),t&&void 0!==t.max&&(this.max=t.max),this._flushIfNeeded()}static extend(t,r){const e=new fg(r);if(void 0!==t.flush)throw new Error("Target object already has a property flush");t.flush=()=>{e.flush()};const n=[{name:"flush",original:void 0}];if(r&&r.replace)for(let i=0;i<r.replace.length;i++){const o=r.replace[i];n.push({name:o,original:t[o]}),e.replace(t,o)}return e._extended={object:t,methods:n},e}destroy(){if(this.flush(),this._extended){const t=this._extended.object,r=this._extended.methods;for(let e=0;e<r.length;e++){const n=r[e];n.original?t[n.name]=n.original:delete t[n.name]}this._extended=null}}replace(t,r){const e=this,n=t[r];if(!n)throw new Error("Method "+r+" undefined");t[r]=function(){for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];e.queue({args:r,fn:n,context:this})}}queue(t){"function"==typeof t?this._queue.push({fn:t}):this._queue.push(t),this._flushIfNeeded()}_flushIfNeeded(){this._queue.length>this.max&&this.flush(),null!=this._timeout&&(clearTimeout(this._timeout),this._timeout=null),this.queue.length>0&&"number"==typeof this.delay&&(this._timeout=ng(()=>{this.flush()},this.delay))}flush(){var t,r;ih(t=cg(r=this._queue).call(r,0)).call(t,t=>{t.fn.apply(t.context||t.fn,t.args||[])})}}class lg{constructor(){ja(this,"_subscribers",{"*":[],add:[],remove:[],update:[]}),ja(this,"subscribe",lg.prototype.on),ja(this,"unsubscribe",lg.prototype.off)}_trigger(t,r,e){var n;if("*"===t)throw new Error("Cannot trigger event *");ih(n=[...this._subscribers[t],...this._subscribers["*"]]).call(n,n=>{n(t,r,null!=e?e:null)})}on(t,r){"function"==typeof r&&this._subscribers[t].push(r)}off(t,r){var e;this._subscribers[t]=Bs(e=this._subscribers[t]).call(e,t=>t!==r)}get testLeakSubscribers(){return this._subscribers}}var hg,pg,vg,dg,yg,gg,mg;function bg(){if(vg)return pg;vg=1,hg||(hg=1,$r()({target:"Object",stat:!0,sham:!L()},{create:Cn()}));var t=It().Object;return pg=function(r,e){return t.create(r,e)}}function _g(){return yg?dg:(yg=1,dg=bg())}var wg,Sg,Tg=e(mg?gg:(mg=1,gg=_g())),Og={};function Eg(){return Sg||(Sg=1,wg||(wg=1,Sp()("Set",function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},Ep()))),Og}var Ag,Ig,Pg,Dg,jg,xg,kg,Cg,Lg,Mg,Ng,Rg,Fg,zg,qg,Ug,Wg,Yg,Xg,Bg,Vg,Gg={};function Hg(){if(Ig)return Ag;Ig=1;var t=Mt(),r=TypeError;return Ag=function(e){if("object"==typeof e&&"size"in e&&"has"in e&&"add"in e&&"delete"in e&&"keys"in e)return e;throw new r(t(e)+" is not a set")}}function Kg(){if(Dg)return Pg;Dg=1;var t=Pt(),r=zp(),e=t("Set"),n=e.prototype;return Pg={Set:e,add:r("add",1),has:r("has",1),remove:r("delete",1),proto:n}}function Jg(){if(xg)return jg;xg=1;var t=M();return jg=function(r,e,n){for(var i,o,u=n?r:r.iterator,a=r.next;!(i=t(a,u)).done;)if(void 0!==(o=e(i.value)))return o},jg}function Qg(){if(Cg)return kg;Cg=1;var t=Jg();return kg=function(r,e,n){return n?t(r.keys(),e,!0):r.forEach(e)},kg}function Zg(){if(Mg)return Lg;Mg=1;var t=Kg(),r=Qg(),e=t.Set,n=t.add;return Lg=function(t){var i=new e;return r(t,function(t){n(i,t)}),i},Lg}function $g(){return Rg||(Rg=1,Ng=function(t){return t.size}),Ng}function tm(){return zg?Fg:(zg=1,Fg=function(t){return{iterator:t,next:t.next,done:!1}})}function rm(){if(Ug)return qg;Ug=1;var t=Nt(),r=Jr(),e=M(),n=ze(),i=tm(),o="Invalid size",u=RangeError,a=TypeError,s=Math.max,c=function(r,e){this.set=r,this.size=s(e,0),this.has=t(r.has),this.keys=t(r.keys)};return c.prototype={getIterator:function(){return i(r(e(this.keys,this.set)))},includes:function(t){return e(this.has,this.set,t)}},qg=function(t){r(t);var e=+t.size;if(e!=e)throw new a(o);var i=n(e);if(i<0)throw new u(o);return new c(t,i)}}function em(){if(Yg)return Wg;Yg=1;var t=Hg(),r=Kg(),e=Zg(),n=$g(),i=rm(),o=Qg(),u=Jg(),a=r.has,s=r.remove;return Wg=function(r){var c=t(this),f=i(r),l=e(c);return n(c)<=f.size?o(c,function(t){f.includes(t)&&s(l,t)}):u(f.getIterator(),function(t){a(l,t)&&s(l,t)}),l}}function nm(){return Bg?Xg:(Bg=1,Xg=function(){return!1})}var im,om,um,am={};function sm(){if(om)return im;om=1;var t=Hg(),r=Kg(),e=$g(),n=rm(),i=Qg(),o=Jg(),u=r.Set,a=r.add,s=r.has;return im=function(r){var c=t(this),f=n(r),l=new u;return e(c)>f.size?o(f.getIterator(),function(t){s(c,t)&&a(l,t)}):i(c,function(t){f.includes(t)&&a(l,t)}),l}}var cm,fm,lm,hm={};function pm(){if(fm)return cm;fm=1;var t=Hg(),r=Kg().has,e=$g(),n=rm(),i=Qg(),o=Jg(),u=bp();return cm=function(a){var s=t(this),c=n(a);if(e(s)<=c.size)return!1!==i(s,function(t){if(c.includes(t))return!1},!0);var f=c.getIterator();return!1!==o(f,function(t){if(r(s,t))return u(f,"normal",!1)})},cm}var vm,dm,ym,gm={};function mm(){if(dm)return vm;dm=1;var t=Hg(),r=$g(),e=Qg(),n=rm();return vm=function(i){var o=t(this),u=n(i);return!(r(o)>u.size)&&!1!==e(o,function(t){if(!u.includes(t))return!1},!0)}}var bm,_m,wm,Sm={};function Tm(){if(_m)return bm;_m=1;var t=Hg(),r=Kg().has,e=$g(),n=rm(),i=Jg(),o=bp();return bm=function(u){var a=t(this),s=n(u);if(e(a)<s.size)return!1;var c=s.getIterator();return!1!==i(c,function(t){if(!r(a,t))return o(c,"normal",!1)})},bm}var Om,Em,Am,Im,Pm,Dm={};function jm(){if(Em)return Om;Em=1;var t=Hg(),r=Kg(),e=Zg(),n=rm(),i=Jg(),o=r.add,u=r.has,a=r.remove;return Om=function(r){var s=t(this),c=n(r).getIterator(),f=e(s);return i(c,function(t){u(s,t)?a(f,t):o(f,t)}),f}}function xm(){return Im?Am:(Im=1,Am=function(t){try{var r=new Set,e={size:0,has:function(){return!0},keys:function(){return Object.defineProperty({},"next",{get:function(){return r.clear(),r.add(4),function(){return{done:!0}}}})}},n=r[t](e);return 1===n.size&&4===n.values().next().value}catch(t){return!1}})}var km,Cm,Lm,Mm,Nm,Rm,Fm,zm,qm,Um={};function Wm(){if(Cm)return km;Cm=1;var t=Hg(),r=Kg().add,e=Zg(),n=rm(),i=Jg();return km=function(o){var u=t(this),a=n(o).getIterator(),s=e(u);return i(a,function(t){r(s,t)}),s}}function Ym(){return Nm?Mm:(Nm=1,cu(),Eg(),function(){if(Vg)return Gg;Vg=1;var t=$r(),r=em(),e=S();t({target:"Set",proto:!0,real:!0,forced:!nm()("difference",function(t){return 0===t.size})||e(function(){var t={size:1,has:function(){return!0},keys:function(){var t=0;return{next:function(){var e=t++>1;return r.has(1)&&r.clear(),{done:e,value:2}}}}},r=new Set([1,2,3,4]);return 3!==r.difference(t).size})},{difference:r})}(),function(){if(um)return am;um=1;var t=$r(),r=S(),e=sm();t({target:"Set",proto:!0,real:!0,forced:!nm()("intersection",function(t){return 2===t.size&&t.has(1)&&t.has(2)})||r(function(){return"3,2"!==String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))})},{intersection:e})}(),function(){if(lm)return hm;lm=1;var t=$r(),r=pm();t({target:"Set",proto:!0,real:!0,forced:!nm()("isDisjointFrom",function(t){return!t})},{isDisjointFrom:r})}(),function(){if(ym)return gm;ym=1;var t=$r(),r=mm();t({target:"Set",proto:!0,real:!0,forced:!nm()("isSubsetOf",function(t){return t})},{isSubsetOf:r})}(),function(){if(wm)return Sm;wm=1;var t=$r(),r=Tm();t({target:"Set",proto:!0,real:!0,forced:!nm()("isSupersetOf",function(t){return!t})},{isSupersetOf:r})}(),function(){if(Pm)return Dm;Pm=1;var t=$r(),r=jm(),e=xm();t({target:"Set",proto:!0,real:!0,forced:!nm()("symmetricDifference")||!e("symmetricDifference")},{symmetricDifference:r})}(),function(){if(Lm)return Um;Lm=1;var t=$r(),r=Wm(),e=xm();t({target:"Set",proto:!0,real:!0,forced:!nm()("union")||!e("union")},{union:r})}(),oa(),Mm=It().Set)}function Xm(){if(Fm)return Rm;Fm=1;var t=Ym();return lu(),Rm=t}var Bm,Vm,Gm,Hm,Km,Jm,Qm,Zm,$m,tb,rb,eb,nb=e(qm?zm:(qm=1,zm=Xm()));function ib(){return Vm?Bm:(Vm=1,cu(),oa(),Bm=mp())}function ob(){if(Hm)return Gm;Hm=1;var t=ib();return lu(),Gm=t}function ub(){return Jm?Km:(Jm=1,Km=ob())}function ab(){return Zm?Qm:(Zm=1,Qm=ub())}function sb(){return tb?$m:(tb=1,$m=ab())}var cb=e(eb?rb:(eb=1,rb=sb()));class fb{constructor(t){this._pairs=t}*[gy](){for(const[t,r]of this._pairs)yield[t,r]}*entries(){for(const[t,r]of this._pairs)yield[t,r]}*keys(){for(const[t]of this._pairs)yield t}*values(){for(const[,t]of this._pairs)yield t}toIdArray(){var t;return $s(t=[...this._pairs]).call(t,t=>t[0])}toItemArray(){var t;return $s(t=[...this._pairs]).call(t,t=>t[1])}toEntryArray(){return[...this._pairs]}toObjectMap(){const t=Tg(null);for(const[r,e]of this._pairs)t[r]=e;return t}toMap(){return new Zp(this._pairs)}toIdSet(){return new nb(this.toIdArray())}toItemSet(){return new nb(this.toItemArray())}cache(){return new fb([...this._pairs])}distinct(t){const r=new nb;for(const[e,n]of this._pairs)r.add(t(n,e));return r}filter(t){const r=this._pairs;return new fb({*[gy](){for(const[e,n]of r)t(n,e)&&(yield[e,n])}})}forEach(t){for(const[r,e]of this._pairs)t(e,r)}map(t){const r=this._pairs;return new fb({*[gy](){for(const[e,n]of r)yield[e,t(n,e)]}})}max(t){const r=cb(this._pairs);let e=r.next();if(e.done)return null;let n=e.value[1],i=t(e.value[1],e.value[0]);for(;!(e=r.next()).done;){const[r,o]=e.value,u=t(o,r);u>i&&(i=u,n=o)}return n}min(t){const r=cb(this._pairs);let e=r.next();if(e.done)return null;let n=e.value[1],i=t(e.value[1],e.value[0]);for(;!(e=r.next()).done;){const[r,o]=e.value,u=t(o,r);u<i&&(i=u,n=o)}return n}reduce(t,r){for(const[e,n]of this._pairs)r=t(r,n,e);return r}sort(t){return new fb({[gy]:()=>{var r;return cb(Nd(r=[...this._pairs]).call(r,(r,e)=>{let[n,i]=r,[o,u]=e;return t(i,u,n,o)}))}})}}function lb(t,r){var e=gd(t);if(Il){var n=Il(t);r&&(n=Bs(n).call(n,function(r){return Yl(t,r).enumerable})),e.push.apply(e,n)}return e}function hb(t){for(var r=1;r<arguments.length;r++){var e,n,i=null!=arguments[r]?arguments[r]:{};r%2?ih(e=lb(Object(i),!0)).call(e,function(r){ja(t,r,i[r])}):dh?Th(t,dh(i)):ih(n=lb(Object(i))).call(n,function(r){xh(t,r,Yl(i,r))})}return t}class pb extends lg{get idProp(){return this._idProp}constructor(t,r){super(),ja(this,"_queue",null),t&&!cp(t)&&(r=t,t=[]),this._options=r||{},this._data=new Zp,this.length=0,this._idProp=this._options.fieldId||"id",t&&t.length&&this.add(t),this.setOptions(r)}setOptions(t){t&&void 0!==t.queue&&(!1===t.queue?this._queue&&(this._queue.destroy(),this._queue=null):(this._queue||(this._queue=fg.extend(this,{replace:["add","update","remove"]})),t.queue&&"object"==typeof t.queue&&this._queue.setOptions(t.queue)))}add(t,r){const e=[];let n;if(cp(t)){const r=$s(t).call(t,t=>t[this._idProp]);if(hv(r).call(r,t=>this._data.has(t)))throw new Error("A duplicate id was found in the parameter array.");for(let r=0,i=t.length;r<i;r++)n=this._addItem(t[r]),e.push(n)}else{if(!t||"object"!=typeof t)throw new Error("Unknown dataType");n=this._addItem(t),e.push(n)}return e.length&&this._trigger("add",{items:e},r),e}update(t,r){const e=[],n=[],i=[],o=[],u=this._idProp,a=t=>{const r=t[u];if(null!=r&&this._data.has(r)){const e=t,u=Ev({},this._data.get(r)),a=this._updateItem(e);n.push(a),o.push(e),i.push(u)}else{const r=this._addItem(t);e.push(r)}};if(cp(t))for(let r=0,e=t.length;r<e;r++)t[r]&&"object"==typeof t[r]?a(t[r]):console.warn("Ignoring input item, which is not an object at index "+r);else{if(!t||"object"!=typeof t)throw new Error("Unknown dataType");a(t)}if(e.length&&this._trigger("add",{items:e},r),n.length){const t={items:n,oldData:i,data:o};this._trigger("update",t,r)}return Rv(e).call(e,n)}updateOnly(t,r){var e;cp(t)||(t=[t]);const n=$s(e=$s(t).call(t,t=>{const r=this._data.get(t[this._idProp]);if(null==r)throw new Error("Updating non-existent items is not allowed.");return{oldData:r,update:t}})).call(e,t=>{let{oldData:r,update:e}=t;const n=r[this._idProp],i=function(t,...r){return al({},t,...r)}(r,e);return this._data.set(n,i),{id:n,oldData:r,updatedData:i}});if(n.length){const t={items:$s(n).call(n,t=>t.id),oldData:$s(n).call(n,t=>t.oldData),data:$s(n).call(n,t=>t.updatedData)};return this._trigger("update",t,r),t.items}return[]}get(t,r){let e,n,i;Iy(t)?(e=t,i=r):cp(t)?(n=t,i=r):i=t;const o=i&&"Object"===i.returnType?"Object":"Array",u=i&&Bs(i),a=[];let s,c,f;if(null!=e)s=this._data.get(e),s&&u&&!u(s)&&(s=void 0);else if(null!=n)for(let t=0,r=n.length;t<r;t++)s=this._data.get(n[t]),null==s||u&&!u(s)||a.push(s);else{var l;c=[...Hv(l=this._data).call(l)];for(let t=0,r=c.length;t<r;t++)f=c[t],s=this._data.get(f),null==s||u&&!u(s)||a.push(s)}if(i&&i.order&&null==e&&this._sort(a,i.order),i&&i.fields){const t=i.fields;if(null!=e&&null!=s)s=this._filterFields(s,t);else for(let r=0,e=a.length;r<e;r++)a[r]=this._filterFields(a[r],t)}if("Object"==o){const t={};for(let r=0,e=a.length;r<e;r++){const e=a[r];t[e[this._idProp]]=e}return t}return null!=e?null!=s?s:null:a}getIds(t){const r=this._data,e=t&&Bs(t),n=t&&t.order,i=[...Hv(r).call(r)],o=[];if(e)if(n){const t=[];for(let r=0,n=i.length;r<n;r++){const n=i[r],o=this._data.get(n);null!=o&&e(o)&&t.push(o)}this._sort(t,n);for(let r=0,e=t.length;r<e;r++)o.push(t[r][this._idProp])}else for(let t=0,r=i.length;t<r;t++){const r=i[t],n=this._data.get(r);null!=n&&e(n)&&o.push(n[this._idProp])}else if(n){const t=[];for(let e=0,n=i.length;e<n;e++){const n=i[e];t.push(r.get(n))}this._sort(t,n);for(let r=0,e=t.length;r<e;r++)o.push(t[r][this._idProp])}else for(let t=0,e=i.length;t<e;t++){const e=i[t],n=r.get(e);null!=n&&o.push(n[this._idProp])}return o}getDataSet(){return this}forEach(t,r){const e=r&&Bs(r),n=this._data,i=[...Hv(n).call(n)];if(r&&r.order){const e=this.get(r);for(let r=0,n=e.length;r<n;r++){const n=e[r];t(n,n[this._idProp])}}else for(let r=0,n=i.length;r<n;r++){const n=i[r],o=this._data.get(n);null==o||e&&!e(o)||t(o,n)}}map(t,r){const e=r&&Bs(r),n=[],i=this._data,o=[...Hv(i).call(i)];for(let r=0,i=o.length;r<i;r++){const i=o[r],u=this._data.get(i);null==u||e&&!e(u)||n.push(t(u,i))}return r&&r.order&&this._sort(n,r.order),n}_filterFields(t,r){var e;return t?xs(e=cp(r)?r:gd(r)).call(e,(r,e)=>(r[e]=t[e],r),{}):t}_sort(t,r){if("string"==typeof r){const e=r;Nd(t).call(t,(t,r)=>{const n=t[e],i=r[e];return n>i?1:n<i?-1:0})}else{if("function"!=typeof r)throw new TypeError("Order must be a function or a string");Nd(t).call(t,r)}}remove(t,r){const e=[],n=[],i=cp(t)?t:[t];for(let t=0,r=i.length;t<r;t++){const r=this._remove(i[t]);if(r){const t=r[this._idProp];null!=t&&(e.push(t),n.push(r))}}return e.length&&this._trigger("remove",{items:e,oldData:n},r),e}_remove(t){let r;if(Iy(t)?r=t:t&&"object"==typeof t&&(r=t[this._idProp]),null!=r&&this._data.has(r)){const t=this._data.get(r)||null;return this._data.delete(r),--this.length,t}return null}clear(t){var r;const e=[...Hv(r=this._data).call(r)],n=[];for(let t=0,r=e.length;t<r;t++)n.push(this._data.get(e[t]));return this._data.clear(),this.length=0,this._trigger("remove",{items:e,oldData:n},t),e}max(t){let r=null,e=null;for(const i of $d(n=this._data).call(n)){var n;const o=i[t];"number"==typeof o&&(null==e||o>e)&&(r=i,e=o)}return r||null}min(t){let r=null,e=null;for(const i of $d(n=this._data).call(n)){var n;const o=i[t];"number"==typeof o&&(null==e||o<e)&&(r=i,e=o)}return r||null}distinct(t){const r=this._data,e=[...Hv(r).call(r)],n=[];let i=0;for(let o=0,u=e.length;o<u;o++){const u=e[o],a=r.get(u)[t];let s=!1;for(let t=0;t<i;t++)if(n[t]==a){s=!0;break}s||void 0===a||(n[i]=a,i++)}return n}_addItem(t){const r=function(t,r){return null==t[r]&&(t[r]=Ay()),t}(t,this._idProp),e=r[this._idProp];if(this._data.has(e))throw new Error("Cannot add item: item with id "+e+" already exists");return this._data.set(e,r),++this.length,e}_updateItem(t){const r=t[this._idProp];if(null==r)throw new Error("Cannot update item: item has no id (item: "+sy(t)+")");const e=this._data.get(r);if(!e)throw new Error("Cannot update item: no item with id "+r+" found");return this._data.set(r,hb(hb({},e),t)),r}stream(t){if(t){const r=this._data;return new fb({*[gy](){for(const e of t){const t=r.get(e);null!=t&&(yield[e,t])}}})}var r;return new fb({[gy]:ps(r=wy(this._data)).call(r,this._data)})}get testLeakData(){return this._data}get testLeakIdProp(){return this._idProp}get testLeakOptions(){return this._options}get testLeakQueue(){return this._queue}set testLeakQueue(t){this._queue=t}}var vb,db,yb,gb,mb,bb,_b;function wb(){return yb?db:(yb=1,vb||(vb=1,$r()({target:"Reflect",stat:!0},{ownKeys:uh()})),db=It().Reflect.ownKeys)}function Sb(){return mb?gb:(mb=1,gb=wb())}var Tb=e(_b?bb:(_b=1,bb=Sb()));class Ob extends lg{get idProp(){return this.getDataSet().idProp}constructor(t,r){var e;super(),ja(this,"length",0),ja(this,"_ids",new nb),this._options=r||{},this._listener=ps(e=this._onEvent).call(e,this),this.setData(t)}setData(t){if(this._data){this._data.off&&this._data.off("*",this._listener);const t=this._data.getIds({filter:Bs(this._options)}),r=this._data.get(t);this._ids.clear(),this.length=0,this._trigger("remove",{items:t,oldData:r})}if(null!=t){this._data=t;const r=this._data.getIds({filter:Bs(this._options)});for(let t=0,e=r.length;t<e;t++){const e=r[t];this._ids.add(e)}this.length=r.length,this._trigger("add",{items:r})}else this._data=new pb;this._data.on&&this._data.on("*",this._listener)}refresh(){const t=this._data.getIds({filter:Bs(this._options)}),r=[...this._ids],e={},n=[],i=[],o=[];for(let r=0,i=t.length;r<i;r++){const i=t[r];e[i]=!0,this._ids.has(i)||(n.push(i),this._ids.add(i))}for(let t=0,n=r.length;t<n;t++){const n=r[t],u=this._data.get(n);null==u?console.error("If you see this, report it please."):e[n]||(i.push(n),o.push(u),this._ids.delete(n))}this.length+=n.length-i.length,n.length&&this._trigger("add",{items:n}),i.length&&this._trigger("remove",{items:i,oldData:o})}get(t,r){if(null==this._data)return null;let e,n=null;Iy(t)||cp(t)?(n=t,e=r):e=t;const i=Ev({},this._options,e),o=Bs(this._options),u=e&&Bs(e);return o&&u&&(i.filter=t=>o(t)&&u(t)),null==n?this._data.get(i):this._data.get(n,i)}getIds(t){if(this._data.length){const r=Bs(this._options),e=null!=t?Bs(t):null;let n;return n=e?r?t=>r(t)&&e(t):e:r,this._data.getIds({filter:n,order:t&&t.order})}return[]}forEach(t,r){if(this._data){var e;const n=Bs(this._options),i=r&&Bs(r);let o;o=i?n?function(t){return n(t)&&i(t)}:i:n,ih(e=this._data).call(e,t,{filter:o,order:r&&r.order})}}map(t,r){if(this._data){var e;const n=Bs(this._options),i=r&&Bs(r);let o;return o=i?n?t=>n(t)&&i(t):i:n,$s(e=this._data).call(e,t,{filter:o,order:r&&r.order})}return[]}getDataSet(){return this._data.getDataSet()}stream(t){var r;return this._data.stream(t||{[gy]:ps(r=Hv(this._ids)).call(r,this._ids)})}dispose(){var t;null!==(t=this._data)&&void 0!==t&&t.off&&this._data.off("*",this._listener);const r="This data view has already been disposed of.",e={get:()=>{throw new Error(r)},set:()=>{throw new Error(r)},configurable:!1};for(const t of Tb(Ob.prototype))xh(this,t,e)}_onEvent(t,r,e){if(!r||!r.items||!this._data)return;const n=r.items,i=[],o=[],u=[],a=[],s=[],c=[];switch(t){case"add":for(let t=0,r=n.length;t<r;t++){const r=n[t];this.get(r)&&(this._ids.add(r),i.push(r))}break;case"update":for(let t=0,e=n.length;t<e;t++){const e=n[t];this.get(e)?this._ids.has(e)?(o.push(e),s.push(r.data[t]),a.push(r.oldData[t])):(this._ids.add(e),i.push(e)):this._ids.has(e)&&(this._ids.delete(e),u.push(e),c.push(r.oldData[t]))}break;case"remove":for(let t=0,e=n.length;t<e;t++){const e=n[t];this._ids.has(e)&&(this._ids.delete(e),u.push(e),c.push(r.oldData[t]))}}this.length+=i.length-u.length,i.length&&this._trigger("add",{items:i},e),o.length&&this._trigger("update",{items:o,oldData:a,data:s},e),u.length&&this._trigger("remove",{items:u,oldData:c},e)}}function Eb(t,r){return"object"==typeof r&&null!==r&&t===r.idProp&&"function"==typeof r.add&&"function"==typeof r.clear&&"function"==typeof r.distinct&&"function"==typeof ih(r)&&"function"==typeof r.get&&"function"==typeof r.getDataSet&&"function"==typeof r.getIds&&"number"==typeof r.length&&"function"==typeof $s(r)&&"function"==typeof r.max&&"function"==typeof r.min&&"function"==typeof r.off&&"function"==typeof r.on&&"function"==typeof r.remove&&"function"==typeof r.setOptions&&"function"==typeof r.stream&&"function"==typeof r.update&&"function"==typeof r.updateOnly}console.warn("You're running a development build."),t.DELETE=ul,t.DataSet=pb,t.DataStream=fb,t.DataView=Ob,t.Queue=fg,t.createNewDataPipeFrom=function(t){return new yc(t)},t.isDataSetLike=Eb,t.isDataViewLike=function(t,r){return"object"==typeof r&&null!==r&&t===r.idProp&&"function"==typeof ih(r)&&"function"==typeof r.get&&"function"==typeof r.getDataSet&&"function"==typeof r.getIds&&"number"==typeof r.length&&"function"==typeof $s(r)&&"function"==typeof r.off&&"function"==typeof r.on&&"function"==typeof r.stream&&Eb(t,r.getDataSet())}});
//# sourceMappingURL=vis-data.min.cjs.map
