# Obsidian 思维导图插件 - 项目文档

## 📋 项目概述

这是一个为 Obsidian 开发的思维导图插件，能够将 Markdown 文档自动转换为交互式思维导图，支持双向同步、实时预览和丰富的交互功能。

### 🎯 核心功能

- **智能解析**：自动解析 Markdown 标题结构（`#`, `##`, `###` 等）和列表项（`-`, `*`, `+`）
- **双向同步**：Markdown 文件与思维导图之间的实时双向同步
- **交互式编辑**：支持节点的增删改查、展开折叠等操作
- **快捷切换**：`Ctrl+M` 快速在 Markdown 视图和思维导图视图间切换
- **分屏预览**：支持分屏显示，同时查看 Markdown 和思维导图
- **文件监听**：自动监听源文件变化，实时更新思维导图

### 🛠️ 技术栈

- **前端框架**：TypeScript + Obsidian API
- **思维导图渲染**：Markmap + D3.js
- **构建工具**：ESBuild + TypeScript
- **样式**：CSS3 + CSS Variables（支持主题适配）

---

## 📁 项目结构

```
obsidian-sample-plugin/
├── 📄 核心文件
│   ├── main.ts                    # 插件主入口文件
│   ├── manifest.json              # 插件清单文件
│   ├── styles.css                 # 插件样式文件
│   └── main.js                    # 构建输出文件
│
├── 🔧 配置文件
│   ├── package.json               # 项目依赖配置
│   ├── tsconfig.json              # TypeScript 配置
│   ├── esbuild.config.mjs         # 构建配置
│   └── .eslintrc                  # 代码规范配置
│
├── 📚 文档文件
│   ├── README.md                  # 基础说明文档
│   ├── README_MINDMAP.md          # 思维导图功能说明
│   ├── PROJECT_DOCUMENTATION.md  # 项目详细文档（本文件）
│   └── debug.md                   # 调试信息文档
│
├── 🧪 开发文件
│   ├── main.ts.backup             # 原始代码备份
│   ├── version-bump.mjs           # 版本更新脚本
│   └── versions.json              # 版本历史记录
│
└── 🏗️ 模块化结构（开发中）
    └── src/
        ├── types/                 # 类型定义
        ├── core/                  # 核心功能模块
        ├── views/                 # 视图组件
        ├── utils/                 # 工具函数
        └── main.ts                # 模块化入口（开发中）
```

---

## 🔍 核心文件详解

### 📄 main.ts - 插件主入口文件

**功能**：插件的核心逻辑实现，包含所有主要功能

**主要组件**：
- `MindMapPlugin` 类：插件主类，继承自 Obsidian Plugin
- `MindMapView` 类：思维导图视图类，继承自 ItemView
- `MindMapNode` 接口：思维导图节点数据结构

**核心方法**：
```typescript
// 插件生命周期
async onload()                     // 插件加载
onunload()                         // 插件卸载

// 视图切换
toggleMindMapMarkdown()            // 切换 MD/思维导图视图
createMindMapPreview()             // 创建思维导图预览
closeMindMapView()                 // 关闭思维导图视图

// 数据处理
parseMarkdownToMindMap()           // 解析 MD 为思维导图数据
mindmapNodeToMarkdown()            // 思维导图数据转 MD
reconstructNode()                  // 重建节点树结构

// 渲染相关
renderMindMap()                    // 渲染思维导图
setupEventListeners()             // 设置事件监听
highlightSelectedNode()           // 高亮选中节点

// 节点操作
selectNode()                       // 选择节点
editNode()                         // 编辑节点
createChildNode()                  // 创建子节点
createSiblingNode()               // 创建兄弟节点
deleteNode()                      // 删除节点
```

### 📄 manifest.json - 插件清单

```json
{
    "id": "obsidian-mindmap-plugin",
    "name": "Mind Map",
    "version": "1.0.0",
    "minAppVersion": "0.15.0",
    "description": "将 Markdown 文档转换为交互式思维导图",
    "author": "Your Name",
    "authorUrl": "",
    "fundingUrl": "",
    "isDesktopOnly": false
}
```

### 🎨 styles.css - 样式文件

**功能**：定义思维导图的视觉样式和交互效果

**主要样式模块**：
- `.mindmap-container`：思维导图容器样式
- `.mindmap-node-input`：节点编辑输入框样式
- `.selected-node`：选中节点高亮样式
- `.markmap-node`：节点基础样式
- `.markmap-fold`：展开/折叠按钮样式

**特色功能**：
- 响应式布局适配
- 主题色彩自适应（使用 CSS Variables）
- 高 z-index 确保编辑框不被遮挡
- 平滑动画过渡效果

---

## ⚙️ 技术方案详解

### 🎯 核心技术选型

#### 1. **Markmap + D3.js**
- **选择原因**：Markmap 是专门为 Markdown 设计的思维导图库，与 D3.js 深度集成
- **优势**：
  - 原生支持 Markdown 语法解析
  - 丰富的交互功能（缩放、拖拽、展开折叠）
  - 高度可定制的样式系统
  - 优秀的性能表现

#### 2. **TypeScript**
- **选择原因**：提供类型安全和更好的开发体验
- **优势**：
  - 编译时错误检查
  - 智能代码提示
  - 更好的代码维护性
  - 与 Obsidian API 完美集成

#### 3. **ESBuild**
- **选择原因**：极快的构建速度和简单的配置
- **优势**：
  - 构建速度比 Webpack 快 10-100 倍
  - 零配置即可使用
  - 内置 TypeScript 支持
  - 小巧的输出文件

### 🔄 数据流架构

```
Markdown 文件 ←→ 解析器 ←→ 思维导图数据 ←→ 渲染器 ←→ 可视化界面
     ↑                                                        ↓
     └─────────────── 文件监听器 ←─────────────────────────────┘
```

#### 数据转换流程：

1. **Markdown → 思维导图数据**：
   ```typescript
   parseMarkdownToMindMap(content: string) → MindMapNode
   ```

2. **思维导图数据 → Markmap 格式**：
   ```typescript
   mindmapNodeToMarkdown(node: MindMapNode) → string
   transformer.transform(markdown) → MarkmapData
   ```

3. **渲染到 DOM**：
   ```typescript
   Markmap.create(svg, options)
   markmap.setData(data)
   ```

### 🎮 交互系统设计

#### 1. **事件监听架构**
```typescript
// DOM 事件监听
container.addEventListener('click', handleNodeClick)
container.addEventListener('dblclick', handleNodeDoubleClick)
document.addEventListener('keydown', handleKeyboardShortcuts)

// 文件系统监听
app.vault.on('modify', handleFileModification)
```

#### 2. **状态管理**
```typescript
interface PluginState {
    rootNode: MindMapNode | null;        // 根节点数据
    selectedNode: MindMapNode | null;    // 当前选中节点
    editingNode: MindMapNode | null;     // 正在编辑的节点
    currentNode: MindMapNode | null;     // 当前焦点节点
}
```

#### 3. **视图同步机制**
- **文件监听**：监听源 Markdown 文件变化
- **实时解析**：文件变化时重新解析内容
- **增量更新**：只更新变化的部分，保持用户操作状态

---

## 🚀 开发指南

### 📦 环境搭建

```bash
# 安装依赖
npm install

# 开发模式（监听文件变化）
npm run dev

# 生产构建
npm run build

# 版本发布
npm run version
```

### 🔧 开发工作流

1. **修改代码**：编辑 `main.ts` 或相关文件
2. **实时构建**：`npm run dev` 自动监听并构建
3. **热重载**：在 Obsidian 中 `Ctrl+Shift+P` → "Reload app without saving"
4. **测试功能**：创建测试 MD 文件，按 `Ctrl+M` 测试

### 🧪 调试技巧

1. **控制台调试**：
   ```typescript
   console.log('Debug info:', data);
   ```

2. **断点调试**：
   - 在 Obsidian 中按 `F12` 打开开发者工具
   - 在 Sources 面板中找到插件代码设置断点

3. **错误处理**：
   ```typescript
   try {
       // 危险操作
   } catch (error) {
       console.error('Error:', error);
       new Notice('操作失败: ' + error.message);
   }
   ```

---

## 🎨 自定义指南

### 🎯 样式定制

编辑 `styles.css` 文件：

```css
/* 自定义节点颜色 */
.markmap-node[data-depth="0"] text {
    fill: #ff6b6b;  /* 根节点红色 */
}

.markmap-node[data-depth="1"] text {
    fill: #4ecdc4;  /* 一级节点青色 */
}

/* 自定义连接线样式 */
.markmap-link {
    stroke-width: 3px;
    stroke: #95a5a6;
}
```

### ⌨️ 快捷键定制

在 `main.ts` 中修改：

```typescript
this.addCommand({
    id: 'toggle-mindmap-markdown',
    name: '切换思维导图/Markdown视图',
    hotkeys: [{ modifiers: ['Ctrl', 'Shift'], key: 'M' }], // 修改快捷键
    callback: () => this.toggleMindMapMarkdown()
});
```

### 🔧 功能扩展

添加新功能的步骤：

1. **定义接口**：在类型定义中添加新的接口
2. **实现逻辑**：在 `MindMapPlugin` 类中添加方法
3. **注册命令**：在 `onload()` 中注册新命令
4. **添加样式**：在 `styles.css` 中添加相关样式

---

## 📈 性能优化

### 🚀 已实现的优化

1. **异步渲染**：使用 `requestAnimationFrame` 避免阻塞 UI
2. **增量更新**：只重新渲染变化的节点
3. **事件委托**：使用事件委托减少事件监听器数量
4. **内存管理**：及时清理不需要的引用和监听器

### 🎯 进一步优化建议

1. **虚拟滚动**：对于大型思维导图，实现虚拟滚动
2. **懒加载**：延迟加载非可视区域的节点
3. **缓存机制**：缓存解析结果，避免重复计算
4. **Web Workers**：将复杂计算移到 Web Worker

---

## 🐛 常见问题

### ❓ 思维导图不显示
- **检查控制台**：是否有 JavaScript 错误
- **检查文件格式**：确保是有效的 Markdown 文件
- **重新加载插件**：`Ctrl+Shift+P` → "Reload app without saving"

### ❓ 快捷键不生效
- **检查冲突**：是否与其他插件快捷键冲突
- **检查焦点**：确保焦点在正确的视图上
- **重启 Obsidian**：有时需要重启应用

### ❓ 文件同步问题
- **检查文件权限**：确保文件可读写
- **检查文件路径**：确保路径正确
- **查看控制台**：检查是否有错误信息

---

## 🔮 未来规划

### 🎯 短期目标（v1.1）
- [ ] 完成模块化重构
- [ ] 添加更多节点样式选项
- [ ] 支持图片和链接节点
- [ ] 改进移动端体验

### 🚀 中期目标（v1.5）
- [ ] 支持多种导出格式（PNG、SVG、PDF）
- [ ] 添加协作功能
- [ ] 集成 AI 辅助功能
- [ ] 支持自定义主题

### 🌟 长期目标（v2.0）
- [ ] 3D 思维导图视图
- [ ] 实时协作编辑
- [ ] 插件生态系统
- [ ] 云端同步功能

---

## 📞 联系方式

- **项目地址**：[GitHub Repository]
- **问题反馈**：[GitHub Issues]
- **功能建议**：[GitHub Discussions]
- **开发者**：[Your Contact Info]

---

*最后更新：2025年7月20日*
