# 思维导图插件调试指南

## 当前问题
SVG尺寸错误：`Failed to read the 'value' property from 'SVGLength': Could not resolve relative length`

## 调试步骤

### 1. 打开浏览器开发者工具
按 `F12` 或 `Ctrl+Shift+I`

### 2. 重新加载插件
在Obsidian设置中禁用并重新启用"Mind Map"插件

### 3. 创建思维导图
使用命令面板 (`Ctrl+P`) 搜索"创建新的思维导图"

### 4. 检查控制台输出
查看是否有以下信息：
- "MindMap view opened, container created"
- "Container dimensions: [width] [height]"
- "Markmap data: [object]"

### 5. 运行调试命令
在命令面板中运行：
- "调试思维导图状态"
- "强制选择根节点"

### 6. 手动检查DOM结构
在控制台中运行：
```javascript
// 检查容器
document.querySelector('.mindmap-container')

// 检查SVG
document.querySelector('.mindmap-container svg')

// 检查文本元素
document.querySelectorAll('.mindmap-container text')
```

### 7. 如果仍然有问题
尝试以下解决方案：

#### 方案1：手动设置容器尺寸
```javascript
const container = document.querySelector('.mindmap-container');
if (container) {
    container.style.width = '800px';
    container.style.height = '600px';
}
```

#### 方案2：重新创建思维导图
在控制台中运行：
```javascript
// 获取插件实例
const plugin = app.plugins.plugins['obsidian-mindmap'];
if (plugin) {
    plugin.createNewMindMap();
}
```

## 预期结果
- 应该看到一个中心主题和两个子节点
- 点击节点应该有高亮效果
- 双击节点应该进入编辑模式
- 控制台应该显示点击事件日志

## 如果问题持续存在
请提供以下信息：
1. 控制台的完整错误信息
2. DOM结构截图
3. 容器尺寸信息
