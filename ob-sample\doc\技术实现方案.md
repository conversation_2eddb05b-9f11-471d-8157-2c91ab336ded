# 思维导图插件技术实现方案

## 一、核心问题定位

### 1.1 渲染引擎架构问题

当前实现使用D3.js自定义渲染器，存在以下问题：
- 布局算法过于简单，缺乏智能排布
- 节点样式计算不准确
- 缺乏动画和交互效果
- 性能优化不足

### 1.2 数据流问题

```mermaid
graph TD
    A[Markdown文档] --> B[MarkdownParser]
    B --> C[NodeData数组]
    C --> D[MindMapRenderer]
    D --> E[D3.js渲染]
    
    F[问题点1: 解析逻辑] --> B
    G[问题点2: 数据转换] --> C
    H[问题点3: 渲染算法] --> D
    I[问题点4: 视觉效果] --> E
```

## 二、短期修复方案（1-2天实施）

### 2.1 修复容器尺寸问题

**问题**：容器尺寸硬编码，无法适应窗口变化

**解决方案**：
```typescript
// MindMapRenderer.ts 修改
private updateContainerSize(): void {
  const rect = this.container.getBoundingClientRect();
  this.width = rect.width;
  this.height = rect.height;
  
  if (this.svg) {
    this.svg
      .attr('width', this.width)
      .attr('height', this.height);
  }
}

// 添加窗口大小变化监听
private setupResizeObserver(): void {
  const resizeObserver = new ResizeObserver(() => {
    this.updateContainerSize();
    this.render(this.lastMarkdown); // 重新渲染
  });
  
  resizeObserver.observe(this.container);
}
```

### 2.2 改进节点布局算法

**问题**：节点重叠，布局混乱

**解决方案**：
```typescript
// 新的智能布局算法
private layoutNodes(nodes: TreeNode[], startX: number, startY: number, depth: number): void {
  if (nodes.length === 0) return;
  
  const nodeHeight = 60;
  const levelWidth = Math.max(200, this.width / 8);
  const minVerticalSpacing = 20;
  
  // 计算每个节点及其子树的高度
  const nodeHeights = nodes.map(node => this.calculateSubtreeHeight(node));
  const totalHeight = nodeHeights.reduce((sum, h) => sum + h, 0) * nodeHeight + 
                     (nodes.length - 1) * minVerticalSpacing;
  
  let currentY = startY - totalHeight / 2;
  
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];
    const subtreeHeight = nodeHeights[i];
    
    node.x = startX + depth * levelWidth;
    node.y = currentY + subtreeHeight * nodeHeight / 2;
    
    if (node.children.length > 0) {
      this.layoutNodes(node.children, node.x, node.y, depth + 1);
    }
    
    currentY += subtreeHeight * nodeHeight + minVerticalSpacing;
  }
}

private calculateSubtreeHeight(node: TreeNode): number {
  if (node.children.length === 0) return 1;
  
  return node.children.reduce((sum, child) => 
    sum + this.calculateSubtreeHeight(child), 0);
}
```

### 2.3 优化节点样式和尺寸

**问题**：节点大小计算不准确，样式单一

**解决方案**：
```typescript
// 精确的文本尺寸计算
private measureTextSize(text: string, fontSize: number = 12): { width: number; height: number } {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  if (!context) return { width: 100, height: 30 };
  
  context.font = `${fontSize}px var(--font-text)`;
  const metrics = context.measureText(text);
  
  return {
    width: metrics.width,
    height: fontSize * 1.2 // 行高
  };
}

// 改进的节点渲染
private renderNode(nodeGroup: any, node: TreeNode): void {
  const textSize = this.measureTextSize(node.content);
  const padding = { x: 16, y: 8 };
  const nodeWidth = Math.max(textSize.width + padding.x * 2, 80);
  const nodeHeight = Math.max(textSize.height + padding.y * 2, 30);
  
  // 根据节点类型设置样式
  const style = this.getNodeStyle(node);
  
  // 添加节点背景
  nodeGroup.append('rect')
    .attr('width', nodeWidth)
    .attr('height', nodeHeight)
    .attr('x', -nodeWidth / 2)
    .attr('y', -nodeHeight / 2)
    .attr('rx', node.type === 'heading' ? 8 : 4)
    .style('fill', style.fill)
    .style('stroke', style.stroke)
    .style('stroke-width', style.strokeWidth)
    .style('filter', node.type === 'heading' ? 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' : 'none');
  
  // 添加文本
  nodeGroup.append('text')
    .attr('text-anchor', 'middle')
    .attr('dy', '0.35em')
    .style('fill', 'var(--text-normal)')
    .style('font-size', `${node.type === 'heading' ? 14 : 12}px`)
    .style('font-weight', node.type === 'heading' ? '600' : '400')
    .text(this.truncateText(node.content, nodeWidth - padding.x * 2));
    
  // AI节点特殊标识
  if (node.type === 'ai-generated') {
    nodeGroup.append('text')
      .attr('x', nodeWidth / 2 - 8)
      .attr('y', -nodeHeight / 2 + 8)
      .style('font-size', '10px')
      .text('🤖');
  }
}
```

## 三、中期重构方案（1-2周实施）

### 3.1 迁移到Markmap渲染引擎

**优势**：
- 成熟的思维导图渲染算法
- 丰富的交互功能
- 更好的性能表现
- 内置动画效果

**实施步骤**：

1. **安装依赖**
```bash
npm install markmap-lib markmap-view
```

2. **创建新的渲染器**
```typescript
// src/core/MarkmapRenderer.ts
import { Markmap } from 'markmap-view';
import { Transformer } from 'markmap-lib';

export class MarkmapRenderer implements MindMapAPI {
  private markmap: Markmap | null = null;
  private transformer: Transformer;
  private container: HTMLElement;
  
  constructor(container: HTMLElement, logger: Logger) {
    this.container = container;
    this.transformer = new Transformer();
    this.initializeMarkmap();
  }
  
  private initializeMarkmap(): void {
    // 清空容器
    this.container.innerHTML = '';
    
    // 创建SVG
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.style.width = '100%';
    svg.style.height = '100%';
    this.container.appendChild(svg);
    
    // 初始化Markmap
    this.markmap = Markmap.create(svg, {
      colorFreezeLevel: 2,
      duration: 500,
      maxWidth: 300,
      spacingVertical: 10,
      spacingHorizontal: 80,
      autoFit: true,
      pan: true,
      zoom: true
    });
  }
  
  render(markdown: string): void {
    if (!this.markmap) return;
    
    try {
      const { root } = this.transformer.transform(markdown);
      this.markmap.setData(root);
      this.markmap.fit();
    } catch (error) {
      this.logger.error('Markmap渲染失败', error);
    }
  }
}
```

### 3.2 改进Markdown解析逻辑

**问题**：段落文本被错误处理为列表节点

**解决方案**：
```typescript
// MarkdownParser.ts 修改
parse(content: string): NodeData[] {
  const lines = content.split('\n');
  const nodes: NodeData[] = [];
  const stack: NodeData[] = [];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();
    
    if (!trimmedLine) continue;
    
    // 优先处理标题
    const headingLevel = parseHeadingLevel(line);
    if (headingLevel > 0) {
      const node = this.createNode(line, headingLevel, 'heading', i);
      this.addNodeToTree(nodes, stack, node);
      continue;
    }
    
    // 处理列表
    const listLevel = parseListLevel(line);
    if (listLevel > 0) {
      const node = this.createNode(line, listLevel, 'list', i);
      this.addNodeToTree(nodes, stack, node);
      continue;
    }
    
    // 处理段落文本（保留为描述性内容，不创建新节点）
    if (stack.length > 0) {
      const parentNode = stack[stack.length - 1];
      if (parentNode.description) {
        parentNode.description += '\n' + trimmedLine;
      } else {
        parentNode.description = trimmedLine;
      }
    }
  }
  
  return nodes;
}
```

## 四、长期优化方案（后续版本）

### 4.1 性能优化
- 虚拟滚动实现
- 节点懒加载
- 渲染缓存机制

### 4.2 交互增强
- 节点搜索功能
- 快捷键支持
- 拖拽编辑功能

### 4.3 主题定制
- 自定义颜色方案
- 节点样式模板
- 导出功能

## 五、实施时间表

| 阶段 | 时间 | 任务 | 优先级 |
|------|------|------|--------|
| 第1天 | 立即 | 修复容器尺寸和布局算法 | 高 |
| 第2天 | 立即 | 优化节点样式和文本处理 | 高 |
| 第1周 | 近期 | 迁移到Markmap渲染引擎 | 中 |
| 第2周 | 近期 | 改进解析逻辑和交互功能 | 中 |
| 后续 | 长期 | 性能优化和功能增强 | 低 |

这个方案提供了分阶段的实施路径，可以快速解决当前问题，同时为长期发展奠定基础。
