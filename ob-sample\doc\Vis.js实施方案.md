# Vis.js 思维导图渲染器实施方案

## 一、方案概述

### 1.1 为什么选择Vis.js

**技术优势**：
- 专门为网络图和思维导图设计
- 内置物理引擎，自动优化节点布局
- 丰富的交互功能（拖拽、缩放、选择、悬停）
- 良好的性能表现，支持大型图表
- 与现有D3.js架构兼容性好

**项目适配性**：
- 可以保持现有`MindMapAPI`接口
- 支持层次化布局，完美适配Markdown结构
- 样式系统灵活，易于适配Obsidian主题
- 学习曲线平缓，开发效率高

### 1.2 实施目标

1. **功能对等**：保持现有所有功能
2. **体验提升**：更好的布局算法和交互效果
3. **性能优化**：更快的渲染速度和更低的内存占用
4. **扩展性**：为未来功能扩展奠定基础

## 二、技术实施方案

### 2.1 依赖管理

**添加依赖**：
```json
{
  "dependencies": {
    "d3": "^7.8.5",
    "vis-network": "^9.1.6"
  },
  "devDependencies": {
    "@types/vis-network": "^4.25.0"
  }
}
```

**安装命令**：
```bash
npm install vis-network
npm install --save-dev @types/vis-network
```

### 2.2 新渲染器架构

```typescript
// src/core/VisJsRenderer.ts
import { Network, DataSet, Node, Edge, Options } from 'vis-network';
import { MindMapAPI, NodeData } from '../types';
import { Logger } from '../utils';

interface VisNode extends Node {
  id: string;
  label: string;
  level: number;
  type: string;
  group: string;
  originalData: NodeData;
}

interface VisEdge extends Edge {
  from: string;
  to: string;
}

export class VisJsRenderer implements MindMapAPI {
  private container: HTMLElement;
  private network: Network | null = null;
  private nodes: DataSet<VisNode>;
  private edges: DataSet<VisEdge>;
  private logger: Logger;
  private onNodeChangeCallback?: (nodeId: string, changes: any) => void;
  private nodeMap: Map<string, NodeData> = new Map();

  constructor(container: HTMLElement, logger: Logger) {
    this.container = container;
    this.logger = logger;
    this.nodes = new DataSet<VisNode>();
    this.edges = new DataSet<VisEdge>();
    
    this.initializeNetwork();
  }

  private initializeNetwork(): void {
    // 清空容器
    this.container.innerHTML = '';
    
    // 配置网络选项
    const options: Options = {
      layout: {
        hierarchical: {
          enabled: true,
          direction: 'LR', // 从左到右布局
          sortMethod: 'directed',
          shakeTowards: 'roots',
          levelSeparation: 200,
          nodeSpacing: 150,
          treeSpacing: 200
        }
      },
      physics: {
        enabled: false // 禁用物理引擎，使用层次布局
      },
      nodes: {
        shape: 'box',
        margin: 10,
        font: {
          size: 14,
          face: 'var(--font-text)',
          color: 'var(--text-normal)'
        },
        borderWidth: 2,
        shadow: {
          enabled: true,
          color: 'rgba(0,0,0,0.1)',
          size: 5,
          x: 2,
          y: 2
        },
        chosen: {
          node: (values: any, id: string, selected: boolean, hovering: boolean) => {
            if (selected || hovering) {
              values.borderWidth = 3;
              values.shadow.size = 8;
            }
          }
        }
      },
      edges: {
        arrows: {
          to: { enabled: false }
        },
        color: {
          color: 'var(--text-muted)',
          opacity: 0.6
        },
        width: 2,
        smooth: {
          enabled: true,
          type: 'cubicBezier',
          roundness: 0.3
        }
      },
      groups: {
        heading1: {
          color: { background: '#e3f2fd', border: '#1976d2' },
          font: { size: 18, face: 'var(--font-text)', color: '#1976d2' }
        },
        heading2: {
          color: { background: '#f3e5f5', border: '#7b1fa2' },
          font: { size: 16, face: 'var(--font-text)', color: '#7b1fa2' }
        },
        heading3: {
          color: { background: '#e8f5e8', border: '#388e3c' },
          font: { size: 14, face: 'var(--font-text)', color: '#388e3c' }
        },
        list: {
          color: { background: 'var(--background-secondary)', border: 'var(--text-muted)' },
          font: { size: 12, face: 'var(--font-text)', color: 'var(--text-normal)' }
        },
        'ai-generated': {
          color: { background: 'rgba(34, 197, 94, 0.1)', border: 'var(--color-green)' },
          font: { size: 12, face: 'var(--font-text)', color: 'var(--text-normal)' }
        },
        code: {
          color: { background: 'var(--background-primary-alt)', border: 'var(--text-accent)' },
          font: { size: 11, face: 'var(--font-monospace)', color: 'var(--text-normal)' }
        }
      },
      interaction: {
        dragNodes: true,
        dragView: true,
        zoomView: true,
        selectConnectedEdges: false,
        hover: true,
        hoverConnectedEdges: false
      }
    };

    // 创建网络
    this.network = new Network(this.container, {
      nodes: this.nodes,
      edges: this.edges
    }, options);

    // 设置事件监听
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    if (!this.network) return;

    // 节点点击事件
    this.network.on('click', (params) => {
      if (params.nodes.length > 0) {
        const nodeId = params.nodes[0];
        this.handleNodeClick(nodeId);
      }
    });

    // 节点双击事件
    this.network.on('doubleClick', (params) => {
      if (params.nodes.length > 0) {
        const nodeId = params.nodes[0];
        this.handleNodeEdit(nodeId);
      }
    });

    // 节点悬停事件
    this.network.on('hoverNode', (params) => {
      this.handleNodeHover(params.node, true);
    });

    this.network.on('blurNode', (params) => {
      this.handleNodeHover(params.node, false);
    });

    // 拖拽结束事件
    this.network.on('dragEnd', (params) => {
      if (params.nodes.length > 0) {
        this.handleNodeDrag(params.nodes[0], params.pointer.canvas);
      }
    });
  }

  render(markdown: string): void {
    this.logger.debug('开始使用Vis.js渲染思维导图');

    try {
      // 解析Markdown为节点数据
      const nodeData = this.parseMarkdownToNodes(markdown);
      
      if (nodeData.length === 0) {
        this.renderEmptyState();
        return;
      }

      // 转换为Vis.js格式
      const { visNodes, visEdges } = this.convertToVisFormat(nodeData);

      // 更新数据
      this.nodes.clear();
      this.edges.clear();
      this.nodes.add(visNodes);
      this.edges.add(visEdges);

      // 适应视图
      setTimeout(() => {
        this.fit();
      }, 100);

      this.logger.debug('Vis.js思维导图渲染完成');
    } catch (error) {
      this.logger.error('Vis.js思维导图渲染失败', error);
      this.renderErrorState(error);
    }
  }

  private parseMarkdownToNodes(markdown: string): NodeData[] {
    // 复用现有的解析逻辑
    const lines = markdown.split('\n').filter(line => line.trim());
    const nodes: NodeData[] = [];
    const stack: NodeData[] = [];

    for (const line of lines) {
      const level = this.getLineLevel(line);
      const content = this.cleanLineContent(line);
      const type = this.getNodeType(line);

      if (!content) continue;

      const node: NodeData = {
        id: this.generateId(),
        content,
        level,
        type,
        children: [],
        position: { line: 0, ch: 0 }
      };

      // 构建层次结构
      while (stack.length > 0 && stack[stack.length - 1].level >= level) {
        stack.pop();
      }

      if (stack.length === 0) {
        nodes.push(node);
      } else {
        stack[stack.length - 1].children.push(node);
      }

      stack.push(node);
      this.nodeMap.set(node.id, node);
    }

    return nodes;
  }

  private convertToVisFormat(nodeData: NodeData[]): { visNodes: VisNode[], visEdges: VisEdge[] } {
    const visNodes: VisNode[] = [];
    const visEdges: VisEdge[] = [];

    const processNode = (node: NodeData, parentId?: string) => {
      // 创建Vis节点
      const visNode: VisNode = {
        id: node.id,
        label: this.formatNodeLabel(node),
        level: node.level,
        type: node.type,
        group: this.getNodeGroup(node),
        originalData: node
      };

      visNodes.push(visNode);

      // 创建边（如果有父节点）
      if (parentId) {
        visEdges.push({
          from: parentId,
          to: node.id
        });
      }

      // 递归处理子节点
      node.children.forEach(child => {
        processNode(child, node.id);
      });
    };

    nodeData.forEach(node => processNode(node));
    return { visNodes, visEdges };
  }

  private formatNodeLabel(node: NodeData): string {
    let label = node.content;
    
    // 限制标签长度
    if (label.length > 30) {
      label = label.substring(0, 27) + '...';
    }

    // AI节点添加特殊标识
    if (node.type === 'ai-generated') {
      label = '🤖 ' + label;
    }

    return label;
  }

  private getNodeGroup(node: NodeData): string {
    if (node.type === 'heading') {
      return `heading${Math.min(node.level, 3)}`;
    }
    return node.type;
  }

  // 实现MindMapAPI接口方法
  updateNode(nodeId: string, data: Partial<NodeData>): void {
    const node = this.nodeMap.get(nodeId);
    if (!node) return;

    // 更新节点数据
    Object.assign(node, data);
    
    // 更新Vis节点
    const visNode = this.nodes.get(nodeId);
    if (visNode) {
      this.nodes.update({
        ...visNode,
        label: this.formatNodeLabel(node),
        group: this.getNodeGroup(node)
      });
    }

    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, data);
    }
  }

  addNode(parentId: string, data: NodeData): string {
    this.nodeMap.set(data.id, data);
    
    // 添加Vis节点
    const visNode: VisNode = {
      id: data.id,
      label: this.formatNodeLabel(data),
      level: data.level,
      type: data.type,
      group: this.getNodeGroup(data),
      originalData: data
    };

    this.nodes.add(visNode);

    // 添加边
    this.edges.add({
      from: parentId,
      to: data.id
    });

    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(data.id, { action: 'add', parentId });
    }

    return data.id;
  }

  deleteNode(nodeId: string): void {
    this.nodeMap.delete(nodeId);
    this.nodes.remove(nodeId);
    
    // 删除相关的边
    const edgesToRemove = this.edges.get({
      filter: (edge) => edge.from === nodeId || edge.to === nodeId
    });
    
    this.edges.remove(edgesToRemove.map(edge => edge.id!));

    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'delete' });
    }
  }

  moveNode(nodeId: string, targetParentId: string): void {
    // 删除旧的边
    const oldEdges = this.edges.get({
      filter: (edge) => edge.to === nodeId
    });
    this.edges.remove(oldEdges.map(edge => edge.id!));

    // 添加新的边
    this.edges.add({
      from: targetParentId,
      to: nodeId
    });

    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'move', targetParentId });
    }
  }

  getNodeByPosition(pos: { line: number; ch: number }): NodeData | null {
    // 暂时返回null，后续可以实现位置映射
    return null;
  }

  getPositionByNode(nodeId: string): { line: number; ch: number } | null {
    // 暂时返回null，后续可以实现位置映射
    return null;
  }

  setNodeChangeCallback(callback: (nodeId: string, changes: any) => void): void {
    this.onNodeChangeCallback = callback;
  }

  fit(): void {
    if (this.network) {
      this.network.fit({
        animation: {
          duration: 500,
          easingFunction: 'easeInOutQuad'
        }
      });
    }
  }

  destroy(): void {
    if (this.network) {
      this.network.destroy();
      this.network = null;
    }
    this.nodes.clear();
    this.edges.clear();
    this.nodeMap.clear();
  }

  // 私有辅助方法
  private getLineLevel(line: string): number {
    const headingMatch = line.match(/^(#{1,6})\s/);
    if (headingMatch) return headingMatch[1].length;

    const listMatch = line.match(/^(\s*)([-*+]|\d+\.)\s/);
    if (listMatch) return Math.floor(listMatch[1].length / 2) + 1;

    return 1;
  }

  private cleanLineContent(line: string): string {
    return line
      .replace(/^#{1,6}\s+/, '')
      .replace(/^\s*[-*+]\s+/, '')
      .replace(/^\s*\d+\.\s+/, '')
      .trim();
  }

  private getNodeType(line: string): string {
    if (line.match(/^#{1,6}\s/)) return 'heading';
    if (line.match(/^\s*[-*+]\s/)) return 'list';
    if (line.match(/^\s*\d+\.\s/)) return 'list';
    if (line.includes('```')) return 'code';
    return 'text';
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private handleNodeClick(nodeId: string): void {
    this.logger.debug('节点被点击', nodeId);
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'click' });
    }
  }

  private handleNodeEdit(nodeId: string): void {
    this.logger.debug('节点被双击编辑', nodeId);
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'edit' });
    }
  }

  private handleNodeHover(nodeId: string, isHover: boolean): void {
    // 可以在这里添加悬停效果
  }

  private handleNodeDrag(nodeId: string, position: any): void {
    this.logger.debug('节点被拖拽', { nodeId, position });
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'drag', position });
    }
  }

  private renderEmptyState(): void {
    this.container.innerHTML = `
      <div class="mindmap-empty">
        <div class="empty-icon">🧠</div>
        <div class="empty-message">暂无内容</div>
        <div class="empty-hint">请在Markdown文档中添加标题或列表</div>
      </div>
    `;
  }

  private renderErrorState(error: any): void {
    this.container.innerHTML = `
      <div class="mindmap-error">
        <div class="error-icon">⚠️</div>
        <div class="error-message">思维导图渲染失败</div>
        <div class="error-detail">${error.message || '未知错误'}</div>
      </div>
    `;
  }
}
```

## 三、迁移步骤

### 3.1 第一步：安装依赖（5分钟）
```bash
npm install vis-network
npm install --save-dev @types/vis-network
```

### 3.2 第二步：创建新渲染器（30分钟）
- 创建 `src/core/VisJsRenderer.ts`
- 实现上述代码

### 3.3 第三步：更新视图组件（10分钟）
```typescript
// 在 MindMapView.ts 中替换渲染器
import { VisJsRenderer } from '../core/VisJsRenderer';

// 替换初始化代码
this.renderer = new VisJsRenderer(mindmapContainer, this.logger);
```

### 3.4 第四步：测试验证（15分钟）
- 测试基础渲染功能
- 测试交互功能
- 验证样式效果

## 四、预期效果

1. **更好的布局**：自动层次化布局，节点不重叠
2. **丰富的交互**：拖拽、缩放、悬停效果
3. **更好的性能**：优化的渲染算法
4. **美观的样式**：专业的视觉效果

这个方案可以在1小时内完成迁移，立即改善思维导图的显示效果！
