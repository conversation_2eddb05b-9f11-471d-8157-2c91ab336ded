import { App, PluginSettingTab, Setting } from 'obsidian';
import MindMapPlugin from './main';
import { LogLevel, setLogLevel } from './logger';

/**
 * 思维导图插件设置接口
 */
export interface MindMapPluginSettings {
    /**
     * 日志级别
     */
    logLevel: LogLevel;
    
    /**
     * 默认视图模式
     * 'markmap' - 使用markmap库渲染
     * 'simple' - 使用简单HTML渲染
     */
    defaultViewMode: 'markmap' | 'simple';
    
    /**
     * 自动保存编辑
     */
    autoSaveEdits: boolean;
    
    /**
     * 默认分割模式
     * 'split' - 分割视图
     * 'tab' - 新标签页
     */
    defaultSplitMode: 'split' | 'tab';
    
    /**
     * 自动同步间隔（毫秒）
     */
    autoSyncInterval: number;
    
    /**
     * 保持节点状态
     */
    preserveNodeStates: boolean;
    
    /**
     * 实时同步
     */
    realTimeSync: boolean;
}

/**
 * 默认设置
 */
export const DEFAULT_SETTINGS: MindMapPluginSettings = {
    logLevel: LogLevel.INFO,
    defaultViewMode: 'markmap',
    autoSaveEdits: true,
    defaultSplitMode: 'split',
    autoSyncInterval: 1000,
    preserveNodeStates: true,
    realTimeSync: true
};

/**
 * 思维导图插件设置标签页
 */
export class MindMapSettingTab extends PluginSettingTab {
    plugin: MindMapPlugin;

    constructor(app: App, plugin: MindMapPlugin) {
        super(app, plugin);
        this.plugin = plugin;
    }

    display(): void {
        const { containerEl } = this;

        containerEl.empty();

        containerEl.createEl('h2', { text: '思维导图设置' });

        // 日志级别设置
        new Setting(containerEl)
            .setName('日志级别')
            .setDesc('设置插件的日志记录详细程度')
            .addDropdown(dropdown => dropdown
                .addOption(LogLevel.ERROR.toString(), '错误')
                .addOption(LogLevel.WARN.toString(), '警告')
                .addOption(LogLevel.INFO.toString(), '信息')
                .addOption(LogLevel.DEBUG.toString(), '调试')
                .addOption(LogLevel.TRACE.toString(), '跟踪')
                .setValue(this.plugin.settings.logLevel.toString())
                .onChange(async (value) => {
                    const level = parseInt(value) as LogLevel;
                    this.plugin.settings.logLevel = level;
                    setLogLevel(level);
                    await this.plugin.saveSettings();
                }));

        // 默认视图模式设置
        new Setting(containerEl)
            .setName('默认视图模式')
            .setDesc('设置思维导图的默认渲染模式')
            .addDropdown(dropdown => dropdown
                .addOption('markmap', 'Markmap (SVG渲染)')
                .addOption('simple', '简单HTML模式')
                .setValue(this.plugin.settings.defaultViewMode)
                .onChange(async (value) => {
                    this.plugin.settings.defaultViewMode = value as 'markmap' | 'simple';
                    await this.plugin.saveSettings();
                }));

        // 自动保存设置
        new Setting(containerEl)
            .setName('自动保存编辑')
            .setDesc('编辑节点后自动保存到文件')
            .addToggle(toggle => toggle
                .setValue(this.plugin.settings.autoSaveEdits)
                .onChange(async (value) => {
                    this.plugin.settings.autoSaveEdits = value;
                    await this.plugin.saveSettings();
                }));

        // 默认分割模式设置
        new Setting(containerEl)
            .setName('默认分割模式')
            .setDesc('创建思维导图预览时的默认打开方式')
            .addDropdown(dropdown => dropdown
                .addOption('split', '分割视图')
                .addOption('tab', '新标签页')
                .setValue(this.plugin.settings.defaultSplitMode)
                .onChange(async (value) => {
                    this.plugin.settings.defaultSplitMode = value as 'split' | 'tab';
                    await this.plugin.saveSettings();
                }));

        // 实时同步设置
        new Setting(containerEl)
            .setName('实时同步')
            .setDesc('启用思维导图和Markdown文件之间的实时同步')
            .addToggle(toggle => toggle
                .setValue(this.plugin.settings.realTimeSync)
                .onChange(async (value) => {
                    this.plugin.settings.realTimeSync = value;
                    await this.plugin.saveSettings();
                }));

        // 保持节点状态设置
        new Setting(containerEl)
            .setName('保持节点状态')
            .setDesc('在同步时保持节点的展开/折叠状态')
            .addToggle(toggle => toggle
                .setValue(this.plugin.settings.preserveNodeStates)
                .onChange(async (value) => {
                    this.plugin.settings.preserveNodeStates = value;
                    await this.plugin.saveSettings();
                }));

        // 自动同步间隔设置
        new Setting(containerEl)
            .setName('自动同步间隔')
            .setDesc('自动同步的时间间隔（毫秒），设置为0禁用自动同步')
            .addText(text => text
                .setPlaceholder('1000')
                .setValue(this.plugin.settings.autoSyncInterval.toString())
                .onChange(async (value) => {
                    const interval = parseInt(value) || 1000;
                    this.plugin.settings.autoSyncInterval = Math.max(0, interval);
                    await this.plugin.saveSettings();
                }));
    }
}