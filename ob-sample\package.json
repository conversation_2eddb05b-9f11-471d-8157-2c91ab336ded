{"name": "obsidian-mindmap-sync-ai", "version": "1.0.0", "description": "Obsidian智能思维导图同步插件，实现Markdown与思维导图双向同步，集成AI提问功能", "main": "main.js", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "version": "node version-bump.mjs && git add manifest.json versions.json", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["obsidian", "mindmap", "markdown", "ai", "sync", "思维导图"], "author": "MindMap Sync Team", "license": "MIT", "dependencies": {"d3": "^7.8.5", "vis-data": "8.0.1", "vis-network": "^10.0.1"}, "devDependencies": {"@types/d3": "^7.4.0", "@types/jest": "^29.5.5", "@types/node": "^16.11.6", "@typescript-eslint/eslint-plugin": "5.29.0", "@typescript-eslint/parser": "5.29.0", "builtin-modules": "3.3.0", "esbuild": "0.17.3", "jest": "^29.7.0", "obsidian": "latest", "ts-jest": "^29.1.1", "tslib": "2.4.0", "typescript": "4.7.4"}}