# 更新日志

所有重要的项目变更都会记录在这个文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
版本号遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 计划中
- [ ] 完成模块化重构
- [ ] 添加导出为图片功能
- [ ] 支持更多 Markdown 语法
- [ ] 改进移动端体验
- [ ] 添加主题自定义功能

---

## [1.0.0] - 2025-07-20

### 🎉 首次发布

#### ✨ 新增功能
- **核心功能**
  - ✅ Markdown 到思维导图的自动转换
  - ✅ 支持标题结构解析（# ## ### 等）
  - ✅ 支持列表结构解析（- * + 等）
  - ✅ 实时双向同步（Markdown ↔ 思维导图）
  
- **交互功能**
  - ✅ `Ctrl+M` 快捷键快速切换视图
  - ✅ 节点点击选择
  - ✅ 节点双击编辑
  - ✅ 节点展开/折叠
  - ✅ 思维导图缩放和拖拽
  
- **视图管理**
  - ✅ 分屏预览模式
  - ✅ 独立思维导图视图
  - ✅ 文件变化自动监听
  - ✅ 视图状态保持

- **节点操作**
  - ✅ 创建子节点
  - ✅ 创建兄弟节点
  - ✅ 删除节点
  - ✅ 编辑节点内容
  - ✅ 节点导航（键盘快捷键）

#### 🎨 界面优化
- **样式系统**
  - ✅ 响应式布局设计
  - ✅ 主题色彩自适应
  - ✅ 节点高亮效果
  - ✅ 平滑动画过渡
  
- **用户体验**
  - ✅ 编辑框高 z-index 防遮挡
  - ✅ 容器内边距优化
  - ✅ 节点文本完整显示
  - ✅ 错误提示和用户反馈

#### ⚡ 性能优化
- **渲染优化**
  - ✅ 异步渲染避免 UI 阻塞
  - ✅ 使用 requestAnimationFrame
  - ✅ 容器尺寸智能计算
  - ✅ SVG 响应式配置

- **内存管理**
  - ✅ 事件监听器自动清理
  - ✅ 组件销毁时资源释放
  - ✅ 文件监听器生命周期管理

#### 🔧 技术实现
- **核心技术栈**
  - ✅ TypeScript + Obsidian API
  - ✅ Markmap + D3.js 渲染引擎
  - ✅ ESBuild 构建工具
  - ✅ CSS3 + CSS Variables

- **架构设计**
  - ✅ 插件主类 (MindMapPlugin)
  - ✅ 视图管理类 (MindMapView)
  - ✅ 数据结构定义 (MindMapNode)
  - ✅ 事件系统和状态管理

#### 📚 文档完善
- ✅ 项目详细文档 (PROJECT_DOCUMENTATION.md)
- ✅ 技术架构文档 (ARCHITECTURE.md)
- ✅ 快速开始指南 (QUICK_START.md)
- ✅ 更新日志 (CHANGELOG.md)
- ✅ 功能说明文档 (README_MINDMAP.md)

---

## [0.9.0] - 2025-07-20

### 🔄 重构阶段

#### 🏗️ 代码重构
- **模块化尝试**
  - ✅ 创建 src/ 目录结构
  - ✅ 类型定义分离 (src/types/)
  - ✅ 工具函数模块化 (src/utils/)
  - ✅ 核心功能模块化 (src/core/)
  - ⚠️ 暂时回退到单文件结构确保稳定性

- **构建系统优化**
  - ✅ ESBuild 配置优化
  - ✅ TypeScript 配置调整
  - ✅ 开发模式热重载
  - ✅ 生产模式代码压缩

#### 🐛 问题修复
- **显示问题修复**
  - ✅ 修复思维导图边界裁剪问题
  - ✅ 修复编辑输入框被遮挡问题
  - ✅ 修复容器尺寸计算错误
  - ✅ 修复节点定位偏移问题

- **交互问题修复**
  - ✅ 修复节点选择状态丢失
  - ✅ 修复双击编辑功能异常
  - ✅ 修复快捷键冲突问题
  - ✅ 修复文件监听器内存泄漏

---

## [0.8.0] - 2025-07-19

### 🎨 界面优化版本

#### ✨ 新增功能
- **样式系统重构**
  - ✅ 新增 styles.css 专用样式文件
  - ✅ CSS Variables 主题适配
  - ✅ 响应式布局支持
  - ✅ 节点样式分层设计

- **交互体验改进**
  - ✅ 节点悬停效果
  - ✅ 选中状态高亮
  - ✅ 编辑模式视觉反馈
  - ✅ 加载状态提示

#### 🔧 技术改进
- **渲染引擎优化**
  - ✅ Markmap 配置参数调优
  - ✅ D3.js 事件处理优化
  - ✅ SVG 元素性能优化
  - ✅ 动画效果流畅度提升

---

## [0.7.0] - 2025-07-18

### 🔄 同步功能版本

#### ✨ 新增功能
- **文件同步系统**
  - ✅ 实时文件监听
  - ✅ 自动内容同步
  - ✅ 双向数据绑定
  - ✅ 冲突处理机制

- **视图管理增强**
  - ✅ 分屏预览模式
  - ✅ 视图状态保持
  - ✅ 多文件支持
  - ✅ 标签页管理

#### 🐛 问题修复
- **解析器改进**
  - ✅ 修复嵌套列表解析错误
  - ✅ 修复特殊字符处理问题
  - ✅ 修复空行处理逻辑
  - ✅ 改进错误处理机制

---

## [0.6.0] - 2025-07-17

### 🎯 核心功能版本

#### ✨ 新增功能
- **基础思维导图功能**
  - ✅ Markdown 解析引擎
  - ✅ 思维导图渲染
  - ✅ 基础节点操作
  - ✅ 视图切换功能

- **命令系统**
  - ✅ 插件命令注册
  - ✅ 快捷键绑定
  - ✅ 命令面板集成
  - ✅ 上下文菜单

#### 🔧 技术基础
- **项目架构**
  - ✅ TypeScript 项目结构
  - ✅ Obsidian Plugin API 集成
  - ✅ Markmap 库集成
  - ✅ 构建系统搭建

---

## 开发里程碑

### 🎯 主要目标达成
- [x] **MVP 功能完成** (v0.6.0)
- [x] **同步功能实现** (v0.7.0)  
- [x] **界面优化完成** (v0.8.0)
- [x] **代码重构尝试** (v0.9.0)
- [x] **正式版本发布** (v1.0.0)

### 📊 开发统计
- **开发周期**：4 天
- **代码行数**：~2000 行 TypeScript
- **功能模块**：8 个主要模块
- **测试用例**：手动测试覆盖
- **文档页面**：5 个文档文件

### 🏆 技术成就
- ✅ 完整的 Markdown 解析算法
- ✅ 高性能的思维导图渲染
- ✅ 稳定的文件同步机制
- ✅ 优秀的用户交互体验
- ✅ 完善的错误处理系统

---

## 贡献者

- **主要开发者**：[Your Name]
- **技术顾问**：Obsidian 社区
- **测试支持**：Beta 用户群体

---

## 致谢

感谢以下开源项目和社区的支持：

- [Obsidian](https://obsidian.md/) - 强大的知识管理平台
- [Markmap](https://markmap.js.org/) - 优秀的思维导图库
- [D3.js](https://d3js.org/) - 数据可视化框架
- [TypeScript](https://www.typescriptlang.org/) - 类型安全的 JavaScript
- [ESBuild](https://esbuild.github.io/) - 极速构建工具

---

*更新日志 - 最后更新：2025年7月20日*
