# 代码修改指南

## 一、立即修复项（高优先级）

### 1.1 修复MindMapRenderer.ts容器尺寸问题

**文件位置**：`src/core/MindMapRenderer.ts`

**修改内容**：

#### 1.1.1 添加动态尺寸属性
```typescript
// 在第26-27行替换
private width = 800;
private height = 600;

// 替换为：
private width = 0;
private height = 0;
private resizeObserver: ResizeObserver | null = null;
```

#### 1.1.2 修改初始化方法
```typescript
// 在initializeContainer方法中添加（第39行后）
private initializeContainer(): void {
  this.container.style.width = '100%';
  this.container.style.height = '100%';
  this.container.style.position = 'relative';
  this.container.style.overflow = 'hidden';

  // 获取实际尺寸
  this.updateContainerSize();

  // 创建SVG
  this.svg = d3.select(this.container)
    .append('svg')
    .attr('width', '100%')
    .attr('height', '100%')
    .style('background', 'var(--background-primary)');

  // 设置缩放
  const zoom = d3.zoom<SVGSVGElement, unknown>()
    .scaleExtent([0.1, 3])
    .on('zoom', (event) => {
      this.svg?.select('g').attr('transform', event.transform);
    });

  this.svg.call(zoom);
  this.svg.append('g').attr('class', 'mindmap-content');

  // 设置尺寸监听
  this.setupResizeObserver();
}

// 添加新方法
private updateContainerSize(): void {
  const rect = this.container.getBoundingClientRect();
  this.width = Math.max(rect.width, 400);
  this.height = Math.max(rect.height, 300);
}

private setupResizeObserver(): void {
  this.resizeObserver = new ResizeObserver(() => {
    this.updateContainerSize();
    // 重新计算布局
    if (this.nodeMap.size > 0) {
      this.render(this.lastMarkdown || '');
    }
  });
  
  this.resizeObserver.observe(this.container);
}
```

### 1.2 改进节点布局算法

**修改位置**：`MindMapRenderer.ts` 第177-203行

```typescript
// 替换layoutNodes方法
private layoutNodes(nodes: TreeNode[], startX: number, startY: number, depth: number): void {
  if (nodes.length === 0) return;
  
  const nodeHeight = 60;
  const levelWidth = Math.max(200, this.width / 8);
  const minVerticalSpacing = 20;
  
  // 计算每个节点的子树高度
  const subtreeHeights = nodes.map(node => this.calculateSubtreeHeight(node));
  const totalUnits = subtreeHeights.reduce((sum, h) => sum + h, 0);
  const totalHeight = totalUnits * nodeHeight + (nodes.length - 1) * minVerticalSpacing;
  
  let currentY = startY - totalHeight / 2;
  
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];
    const subtreeHeight = subtreeHeights[i];
    const nodeSpaceHeight = subtreeHeight * nodeHeight;
    
    node.x = startX + depth * levelWidth;
    node.y = currentY + nodeSpaceHeight / 2;
    
    if (node.children.length > 0) {
      this.layoutNodes(node.children, node.x, node.y, depth + 1);
    }
    
    currentY += nodeSpaceHeight + minVerticalSpacing;
  }
}

// 添加新方法
private calculateSubtreeHeight(node: TreeNode): number {
  if (node.children.length === 0) return 1;
  
  return node.children.reduce((sum, child) => 
    sum + this.calculateSubtreeHeight(child), 0);
}
```

### 1.3 优化节点样式计算

**修改位置**：`MindMapRenderer.ts` 第242-265行

```typescript
// 替换renderNodes方法中的节点渲染部分
private renderNodes(nodes: TreeNode[]): void {
  if (!this.svg) return;

  const g = this.svg.select('.mindmap-content');
  g.selectAll('*').remove();

  const allNodes: TreeNode[] = [];
  const links: { source: TreeNode; target: TreeNode }[] = [];

  this.collectNodesAndLinks(nodes, allNodes, links);

  // 渲染连线
  g.selectAll('.link')
    .data(links)
    .enter()
    .append('line')
    .attr('class', 'link')
    .attr('x1', d => d.source.x || 0)
    .attr('y1', d => d.source.y || 0)
    .attr('x2', d => d.target.x || 0)
    .attr('y2', d => d.target.y || 0)
    .style('stroke', 'var(--text-muted)')
    .style('stroke-width', 2)
    .style('opacity', 0.6);

  // 渲染节点
  const nodeGroups = g.selectAll('.node')
    .data(allNodes)
    .enter()
    .append('g')
    .attr('class', 'node')
    .attr('transform', d => `translate(${d.x || 0}, ${d.y || 0})`)
    .style('cursor', 'pointer');

  // 为每个节点计算尺寸
  nodeGroups.each((d, i, nodes) => {
    this.renderSingleNode(d3.select(nodes[i]), d);
  });

  // 添加事件监听
  nodeGroups
    .on('click', (event, d) => this.handleNodeClick(d))
    .on('dblclick', (event, d) => this.handleNodeEdit(d))
    .on('mouseenter', (event, d) => this.handleNodeHover(d, true))
    .on('mouseleave', (event, d) => this.handleNodeHover(d, false));
}

// 添加新方法
private renderSingleNode(nodeGroup: any, node: TreeNode): void {
  const textSize = this.measureTextSize(node.content, node.type === 'heading' ? 14 : 12);
  const padding = { x: 16, y: 8 };
  const nodeWidth = Math.max(textSize.width + padding.x * 2, 80);
  const nodeHeight = Math.max(textSize.height + padding.y * 2, 30);
  
  const style = this.getEnhancedNodeStyle(node);
  
  // 节点背景
  nodeGroup.append('rect')
    .attr('width', nodeWidth)
    .attr('height', nodeHeight)
    .attr('x', -nodeWidth / 2)
    .attr('y', -nodeHeight / 2)
    .attr('rx', node.type === 'heading' ? 8 : 4)
    .style('fill', style.fill)
    .style('stroke', style.stroke)
    .style('stroke-width', style.strokeWidth)
    .style('filter', node.type === 'heading' ? 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' : 'none')
    .style('transition', 'all 0.2s ease');
  
  // 节点文本
  nodeGroup.append('text')
    .attr('text-anchor', 'middle')
    .attr('dy', '0.35em')
    .style('fill', 'var(--text-normal)')
    .style('font-size', `${node.type === 'heading' ? 14 : 12}px`)
    .style('font-weight', node.type === 'heading' ? '600' : '400')
    .style('pointer-events', 'none')
    .text(this.truncateText(node.content, nodeWidth - padding.x * 2));
    
  // AI节点标识
  if (node.type === 'ai-generated') {
    nodeGroup.append('circle')
      .attr('cx', nodeWidth / 2 - 8)
      .attr('cy', -nodeHeight / 2 + 8)
      .attr('r', 6)
      .style('fill', 'var(--color-green)')
      .style('stroke', 'white')
      .style('stroke-width', 1);
      
    nodeGroup.append('text')
      .attr('x', nodeWidth / 2 - 8)
      .attr('y', -nodeHeight / 2 + 8)
      .attr('text-anchor', 'middle')
      .attr('dy', '0.35em')
      .style('font-size', '8px')
      .style('fill', 'white')
      .style('pointer-events', 'none')
      .text('AI');
  }
}

private measureTextSize(text: string, fontSize: number = 12): { width: number; height: number } {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  if (!context) return { width: 100, height: 20 };
  
  context.font = `${fontSize}px var(--font-text)`;
  const metrics = context.measureText(text);
  
  return {
    width: metrics.width,
    height: fontSize * 1.2
  };
}

private getEnhancedNodeStyle(node: TreeNode): { fill: string; stroke: string; strokeWidth: number } {
  const baseStyle = {
    fill: 'var(--background-primary)',
    stroke: 'var(--background-modifier-border)',
    strokeWidth: 1
  };
  
  switch (node.type) {
    case 'heading':
      const hue = 210 + (node.level - 1) * 30;
      return {
        ...baseStyle,
        fill: `hsl(${hue}, 60%, 95%)`,
        stroke: `hsl(${hue}, 60%, 70%)`,
        strokeWidth: 2
      };
    case 'list':
      return {
        ...baseStyle,
        fill: 'var(--background-secondary)',
        stroke: 'var(--text-muted)'
      };
    case 'ai-generated':
      return {
        ...baseStyle,
        fill: 'rgba(34, 197, 94, 0.1)',
        stroke: 'var(--color-green)',
        strokeWidth: 2
      };
    default:
      return baseStyle;
  }
}

private truncateText(text: string, maxWidth: number): string {
  const avgCharWidth = 7; // 平均字符宽度
  const maxChars = Math.floor(maxWidth / avgCharWidth);
  
  if (text.length <= maxChars) return text;
  return text.substring(0, maxChars - 3) + '...';
}

private handleNodeHover(node: TreeNode, isEnter: boolean): void {
  if (!this.svg) return;
  
  const nodeSelection = this.svg.selectAll('.node')
    .filter((d: any) => d.id === node.id);
    
  if (isEnter) {
    nodeSelection.select('rect')
      .style('stroke-width', 3)
      .style('filter', 'drop-shadow(0 4px 8px rgba(0,0,0,0.15))');
  } else {
    const style = this.getEnhancedNodeStyle(node);
    nodeSelection.select('rect')
      .style('stroke-width', style.strokeWidth)
      .style('filter', node.type === 'heading' ? 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' : 'none');
  }
}
```

## 二、必要的类型定义更新

**文件位置**：`src/types/index.ts`

```typescript
// 添加到现有类型定义中
export interface TreeNode {
  id: string;
  content: string;
  level: number;
  type: 'heading' | 'list' | 'code' | 'ai-generated' | 'text';
  children: TreeNode[];
  x?: number;
  y?: number;
  description?: string; // 新增：用于存储段落描述
}
```

## 三、销毁方法更新

**修改位置**：`MindMapRenderer.ts` destroy方法

```typescript
destroy(): void {
  if (this.resizeObserver) {
    this.resizeObserver.disconnect();
    this.resizeObserver = null;
  }
  
  if (this.svg) {
    this.svg.remove();
    this.svg = null;
  }
  
  this.nodeMap.clear();
}
```

## 四、测试验证

修改完成后，请验证以下功能：

1. **容器自适应**：调整Obsidian窗口大小，思维导图应该自动适应
2. **节点布局**：创建多层级的Markdown文档，检查节点是否正确排列
3. **节点样式**：不同类型的节点应该有明显的视觉区别
4. **交互效果**：鼠标悬停应该有高亮效果
5. **性能表现**：大文档的渲染速度应该有所改善

这些修改将显著改善思维导图的显示效果和用户体验。
