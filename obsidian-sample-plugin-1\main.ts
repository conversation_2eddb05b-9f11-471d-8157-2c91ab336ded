import { A<PERSON>, Editor, ItemView, Plugin, WorkspaceLeaf, ViewStateResult, Notice } from 'obsidian';
import { Transformer } from 'markmap-lib';
import { Markmap } from 'markmap-view';
import * as d3 from 'd3';
import { Logger, createLogger, LogLevel, setLogLevel } from './logger';
import { MindMapPluginSettings, DEFAULT_SETTINGS, MindMapSettingTab } from './settings';

// 思维导图节点接口定义
// 描述思维导图中每个节点的数据结构
interface MindMapNode {
    id: string; // 节点的唯一标识符
    content: string; // 节点显示的内容文本
    children: MindMapNode[]; // 子节点数组，用于构建树状结构
    parent?: MindMapNode; // 父节点引用，用于向上遍历
    isSelected?: boolean; // 标记节点是否被选中
    isExpanded?: boolean; // 标记节点是否展开
    isHeading?: boolean; // 标识是否为原始标题节点
    originalLevel?: number; // 原始标题级别，用于从Markdown解析时保留层级信息
    nodeType?: 'heading' | 'paragraph' | 'list' | 'root'; // 节点类型，区分不同来源的内容
}

// 思维导图视图状态接口
// 用于保存和恢复思维导图的显示状态
interface MindMapViewState {
    [key: string]: unknown; // 允许存储任意键值对
    data?: MindMapNode; // 思维导图的根节点数据
    selectedNodeId?: string; // 当前选中节点的ID
    sourceFile?: string; // 关联的源文件路径，用于同步更新
}

const MIND_MAP_VIEW_TYPE = "mindmap";

export default class MindMapPlugin extends Plugin {
    // 转换器实例，用于解析Markdown内容为思维导图结构
    private transformer: Transformer;
    // Markmap实例，用于渲染SVG格式的思维导图
    private mindmap: Markmap | null = null;
    // 思维导图的根节点数据
    private rootNode: MindMapNode | null = null;
    // 当前操作的节点
    private currentNode: MindMapNode | null = null;
    // 正在编辑的节点
    private editingNode: MindMapNode | null = null;
    // 当前选中的节点
    private selectedNode: MindMapNode | null = null;
    // 标志是否正在同步，避免循环更新
    public isSyncing: boolean = false;
    
    // 插件设置对象，存储用户配置
    settings: MindMapPluginSettings;
    
    // 日志记录器，用于调试和信息输出
    private logger: Logger;


    // 插件加载时执行的初始化逻辑
    async onload() {
        // 加载设置
        this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
        
        // 初始化日志记录器
        this.logger = createLogger('MindMapPlugin');
        setLogLevel(this.settings.logLevel);
        this.logger.info('插件加载中...');
        
        // 初始化转换器
        this.transformer = new Transformer();

        // 注册视图
        this.registerView(
            MIND_MAP_VIEW_TYPE,
            (leaf: WorkspaceLeaf) => new MindMapView(leaf, this)
        );

        // 添加命令
        this.addCommand({
            id: 'create-mindmap',
            name: '创建新的思维导图',
            callback: () => this.createNewMindMap()
        });

        // 添加更多命令
        this.addCommand({
            id: 'select-parent-node',
            name: '选择父节点',
            callback: () => this.selectParentNode()
        });

        this.addCommand({
            id: 'select-first-child',
            name: '选择第一个子节点',
            callback: () => this.selectFirstChild()
        });

        this.addCommand({
            id: 'select-next-sibling',
            name: '选择下一个同级节点',
            callback: () => this.selectNextSibling()
        });

        this.addCommand({
            id: 'select-previous-sibling',
            name: '选择上一个同级节点',
            callback: () => this.selectPreviousSibling()
        });

        // 添加调试命令
        this.addCommand({
            id: 'debug-mindmap',
            name: '调试思维导图状态',
            callback: () => this.debugMindMapState()
        });

        this.addCommand({
            id: 'force-select-root',
            name: '强制选择根节点',
            callback: () => {
                if (this.rootNode) {
                    console.log('Force selecting root node:', this.rootNode);
                    this.selectNode(this.rootNode);
                }
            }
        });

        this.addCommand({
            id: 'create-simple-test',
            name: '创建简单测试视图',
            callback: () => this.createSimpleTestView()
        });

        this.addCommand({
            id: 'test-edit-selected',
            name: '测试编辑选中节点',
            callback: () => {
                if (this.selectedNode) {
                    console.log('Testing edit for selected node:', this.selectedNode.content);
                    const nodeEl = document.querySelector(`[data-node-id="${this.selectedNode.id}"]`) as HTMLElement;
                    if (nodeEl) {
                        this.startSimpleEditing(this.selectedNode, nodeEl);
                    } else {
                        console.log('Node element not found');
                    }
                } else {
                    console.log('No node selected');
                }
            }
        });

        this.addCommand({
            id: 'force-simple-view',
            name: '强制使用简单HTML视图',
            callback: () => {
                if (this.rootNode) {
                    this.createSimpleHTMLMindMap();
                } else {
                    this.createSimpleTestView();
                }
            }
        });

        this.addCommand({
            id: 'edit-root-node',
            name: '编辑根节点',
            callback: () => {
                if (this.rootNode) {
                    console.log('Forcing edit of root node');
                    this.startEditing(this.rootNode);
                } else {
                    console.log('No root node to edit');
                }
            }
        });

        this.addCommand({
            id: 'export-to-markdown',
            name: '导出为Markdown文档',
            callback: () => this.exportToMarkdown()
        });

        this.addCommand({
            id: 'import-from-markdown',
            name: '从Markdown文档导入',
            callback: () => this.importFromMarkdown()
        });

        this.addCommand({
            id: 'toggle-mindmap-markdown',
            name: '切换思维导图/Markdown视图',
            hotkeys: [{ modifiers: ['Ctrl'], key: 'M' }],
            callback: () => this.toggleMindMapMarkdown()
        });

        // 注册键盘事件
        this.registerDomEvent(document, 'keydown', (evt: KeyboardEvent) => {
            // 只在思维导图视图激活时处理键盘事件
            const activeView = this.app.workspace.getActiveViewOfType(MindMapView);
            if (!activeView || !this.selectedNode) return;

            switch(evt.key) {
                case 'Tab':
                    evt.preventDefault();
                    if (evt.shiftKey) {
                        this.createSiblingNode(this.selectedNode);
                    } else {
                        this.createChildNode(this.selectedNode);
                    }
                    break;
                case 'Enter':
                    evt.preventDefault();
                    if (this.editingNode) {
                        this.finishEditing().catch(console.error);
                    } else {
                        this.startEditing(this.selectedNode).catch(console.error);
                    }
                    break;
                case 'Delete':
                case 'Backspace':
                    if (!this.editingNode && evt.ctrlKey) {
                        evt.preventDefault();
                        this.deleteNode(this.selectedNode);
                    }
                    break;
                case 'ArrowUp':
                    evt.preventDefault();
                    this.selectPreviousSibling();
                    break;
                case 'ArrowDown':
                    evt.preventDefault();
                    this.selectNextSibling();
                    break;
                case 'ArrowLeft':
                    evt.preventDefault();
                    if (this.selectedNode && this.selectedNode.isExpanded) {
                        this.collapseNode(this.selectedNode);
                    } else {
                        this.selectParentNode();
                    }
                    break;
                case 'ArrowRight':
                    evt.preventDefault();
                    if (this.selectedNode && !this.selectedNode.isExpanded) {
                        this.expandNode(this.selectedNode);
                    } else {
                        this.selectFirstChild();
                    }
                    break;
                case 'Escape':
                    if (this.editingNode) {
                        evt.preventDefault();
                        this.cancelEditing();
                    }
                    break;
            }
        });
        
        // 添加设置标签页
        this.addSettingTab(new MindMapSettingTab(this.app, this));
        
        this.logger.info('插件加载完成');
    }
    
    // 保存插件设置到存储
    async saveSettings() {
        // 由于 saveData 方法可能不支持传入参数，我们需要检查正确的保存方式
        // 在 Obsidian 插件中，通常使用 savePluginData 或其他方法来保存设置
        // 这里我们直接调用 saveData 而不传入参数，或者使用替代方法
        await this.saveData(); // 尝试调用无参数的 saveData 方法
        // 如果需要保存设置数据，可以通过其他方式存储，例如直接写入文件或使用插件的存储接口
        // 暂时注释掉传入参数的方式以避免错误
        // await this.saveData(this.settings); // 原代码：将设置数据持久化保存
        this.logger.info('设置已保存'); // 记录保存成功的日志
    }

    // 调试思维导图状态，输出到控制台
    private debugMindMapState() {
        this.logger.info('=== 思维导图调试信息 ===');
        this.logger.debug('Root node:', this.rootNode);
        this.logger.debug('Selected node:', this.selectedNode);
        this.logger.debug('Editing node:', this.editingNode);
        this.logger.debug('Mindmap instance:', this.mindmap);

        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        this.logger.debug('Active view:', view);

        if (view) {
            const container = view.containerEl.querySelector('.mindmap-container');
            this.logger.debug('Container:', container);

            if (container) {
                const svg = container.querySelector('svg');
                this.logger.debug('SVG element:', svg);

                if (svg) {
                    const groups = svg.querySelectorAll('g');
                    this.logger.debug('Found groups:', groups.length);

                    const textElements = svg.querySelectorAll('text');
                    this.logger.debug('Found text elements:', textElements.length);

                    if (this.settings.logLevel >= LogLevel.TRACE) {
                        for (let i = 0; i < textElements.length; i++) {
                            const textEl = textElements[i];
                            this.logger.trace(`Text ${i}:`, textEl.textContent, textEl);
                        }
                    }
                }
            }
        }

        if (this.rootNode) {
            this.logger.debug('All nodes:', this.getAllNodes(this.rootNode));
        }
        
        // 显示一个通知，告知用户调试信息已记录
        new Notice('思维导图调试信息已记录到控制台');
        this.logger.info('=== 调试信息结束 ===');
    }

    // 创建简单测试视图
    async createSimpleTestView() {
        this.rootNode = {
            id: 'root',
            content: '中心主题',
            children: [
                {
                    id: 'child1',
                    content: '子节点1',
                    children: [],
                    parent: undefined,
                    isExpanded: true
                },
                {
                    id: 'child2',
                    content: '子节点2',
                    children: [],
                    parent: undefined,
                    isExpanded: true
                }
            ],
            isExpanded: true
        };

        // 设置父节点引用
        this.rootNode.children.forEach(child => {
            child.parent = this.rootNode!;
        });

        const leaf = this.app.workspace.getLeaf(true);
        await leaf.setViewState({
            type: MIND_MAP_VIEW_TYPE,
            state: { data: this.rootNode }
        });

        this.app.workspace.revealLeaf(leaf);

        // 等待视图加载后创建简单HTML版本
        setTimeout(() => {
            this.createSimpleHTMLMindMap();
        }, 200);
    }

    // 创建简单的HTML思维导图（不使用markmap）
    private createSimpleHTMLMindMap() {
        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        if (!view || !this.rootNode) return;

        const container = view.containerEl.querySelector('.mindmap-container') as HTMLElement;
        if (!container) return;

        container.innerHTML = '';
        container.style.cssText = `
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            background: var(--background-primary);
        `;

        // 创建根节点
        const rootElement = this.createNodeElement(this.rootNode, 0);
        container.appendChild(rootElement);

        // 创建子节点
        const childrenContainer = container.createDiv('children-container');
        childrenContainer.style.cssText = `
            display: flex;
            gap: 20px;
            margin-top: 20px;
        `;

        this.rootNode.children.forEach(child => {
            const childElement = this.createNodeElement(child, 1);
            childrenContainer.appendChild(childElement);
        });

        // 选中根节点
        this.selectNode(this.rootNode);
    }

    // 创建节点元素
    private createNodeElement(node: MindMapNode, level: number): HTMLElement {
        const nodeEl = document.createElement('div');
        nodeEl.className = 'simple-mindmap-node';
        nodeEl.setAttribute('data-node-id', node.id);
        nodeEl.setAttribute('tabindex', '0'); // 使元素可以获得焦点

        const isSelected = this.selectedNode === node;

        nodeEl.style.cssText = `
            padding: 10px 15px;
            border: 2px solid ${isSelected ? 'var(--text-accent)' : 'var(--background-modifier-border)'};
            border-radius: 8px;
            background: var(--background-primary);
            cursor: pointer;
            user-select: none;
            font-weight: ${level === 0 ? '600' : '400'};
            font-size: ${level === 0 ? '16px' : '14px'};
            color: ${isSelected ? 'var(--text-accent)' : 'var(--text-normal)'};
            transition: all 0.2s ease;
            min-width: 100px;
            text-align: center;
            outline: none;
        `;

        nodeEl.textContent = node.content;

        // 添加事件监听器
        nodeEl.addEventListener('click', (e) => {
            e.stopPropagation();
            console.log('Simple node clicked:', node.content);
            this.selectNode(node);
            this.updateSimpleNodeStyles();
        });

        nodeEl.addEventListener('dblclick', (e) => {
            e.stopPropagation();
            e.preventDefault();
            console.log('Simple node double-clicked:', node.content);
            this.startSimpleEditing(node, nodeEl);
        });

        // 添加键盘事件支持
        nodeEl.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && this.selectedNode === node) {
                e.preventDefault();
                console.log('Enter pressed on selected node:', node.content);
                this.startSimpleEditing(node, nodeEl);
            }
        });

        return nodeEl;
    }

    // 更新简单节点样式
    private updateSimpleNodeStyles() {
        const nodes = document.querySelectorAll('.simple-mindmap-node');
        nodes.forEach(nodeEl => {
            const nodeId = nodeEl.getAttribute('data-node-id');
            const isSelected = this.selectedNode?.id === nodeId;

            (nodeEl as HTMLElement).style.borderColor = isSelected ? 'var(--text-accent)' : 'var(--background-modifier-border)';
            (nodeEl as HTMLElement).style.color = isSelected ? 'var(--text-accent)' : 'var(--text-normal)';
        });
    }

    // 简单编辑功能
    private startSimpleEditing(node: MindMapNode, nodeEl: HTMLElement) {
        console.log('Starting simple editing for node:', node.content);

        const input = document.createElement('input');
        input.type = 'text';
        input.value = node.content;
        input.className = 'simple-mindmap-input';

        // 复制原有样式并添加编辑样式
        input.style.cssText = `
            padding: 10px 15px;
            border: 2px solid var(--text-accent);
            border-radius: 8px;
            background: var(--background-primary);
            font-weight: ${node === this.rootNode ? '600' : '400'};
            font-size: ${node === this.rootNode ? '16px' : '14px'};
            color: var(--text-accent);
            min-width: 100px;
            text-align: center;
            outline: none;
            box-shadow: 0 0 0 2px var(--background-modifier-border-hover);
        `;

        console.log('Replacing node element with input');
        nodeEl.parentNode?.replaceChild(input, nodeEl);

        // 确保输入框获得焦点
        setTimeout(() => {
            input.focus();
            input.select();
        }, 10);

        const finishEdit = () => {
            console.log('Finishing edit, new content:', input.value);
            node.content = input.value;

            // 重新创建整个简单思维导图以确保一致性
            this.createSimpleHTMLMindMap();
            this.saveData();
        };

        const cancelEdit = () => {
            console.log('Cancelling edit');
            // 重新创建整个简单思维导图
            this.createSimpleHTMLMindMap();
        };

        input.addEventListener('blur', finishEdit);
        input.addEventListener('keydown', (e) => {
            console.log('Key pressed in input:', e.key);
            if (e.key === 'Enter') {
                e.preventDefault();
                finishEdit();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });
    }

    // 处理节点选中
    private selectNode(node: MindMapNode | null) {
        if (this.selectedNode) {
            this.selectedNode.isSelected = false;
        }

        this.selectedNode = node;
        if (node) {
            node.isSelected = true;

            // 高亮选中的节点
            this.highlightSelectedNode();
        }
    }

    // 选择父节点
    private selectParentNode() {
        if (this.selectedNode?.parent) {
            this.selectNode(this.selectedNode.parent);
        }
    }

    // 选择第一个子节点
    private selectFirstChild() {
        if (this.selectedNode && this.selectedNode.children && this.selectedNode.children.length > 0) {
            this.selectNode(this.selectedNode.children[0]);
        }
    }

    // 选择下一个同级节点
    private selectNextSibling() {
        if (!this.selectedNode?.parent) return;
        
        const siblings = this.selectedNode.parent.children;
        const currentIndex = siblings.indexOf(this.selectedNode);
        if (currentIndex < siblings.length - 1) {
            this.selectNode(siblings[currentIndex + 1]);
        }
    }

    // 选择上一个同级节点
    private selectPreviousSibling() {
        if (!this.selectedNode?.parent) return;
        
        const siblings = this.selectedNode.parent.children;
        const currentIndex = siblings.indexOf(this.selectedNode);
        if (currentIndex > 0) {
            this.selectNode(siblings[currentIndex - 1]);
        }
    }

    // 展开节点
    private async expandNode(node: MindMapNode) {
        node.isExpanded = true;
        await this.renderMindMap();
    }

    // 折叠节点
    private async collapseNode(node: MindMapNode) {
        node.isExpanded = false;
        await this.renderMindMap();
    }

    // 删除节点
    private deleteNode(node: MindMapNode) {
        if (!node.parent || node === this.rootNode) return;
        
        const siblings = node.parent.children;
        const index = siblings.indexOf(node);
        siblings.splice(index, 1);
        
        // 选择相邻节点或父节点
        if (siblings.length > 0) {
            this.selectNode(siblings[Math.min(index, siblings.length - 1)]);
        } else {
            this.selectNode(node.parent);
        }
        
        this.saveData().catch(console.error);
    }



    // 从 DOM 元素中提取节点 ID
    private findNodeIdFromElement(element: Element): string | null {
        // markmap 会为每个节点生成唯一的 ID
        const transform = element.getAttribute('transform');
        if (!transform) return null;
        
        // 遍历所有节点找到匹配的
        const allNodes = this.getAllNodes(this.rootNode!);
        for (const node of allNodes) {
            if (element.textContent?.includes(node.content)) {
                return node.id;
            }
        }
        return null;
    }

    // 获取所有节点
    private getAllNodes(root: MindMapNode): MindMapNode[] {
        const nodes: MindMapNode[] = [root];
        for (const child of root.children) {
            nodes.push(...this.getAllNodes(child));
        }
        return nodes;
    }

    // 开始编辑节点
    private async startEditing(node: MindMapNode) {
        console.log('Starting editing for node:', node.content);

        if (this.editingNode === node) {
            console.log('Already editing this node');
            return;
        }

        await this.finishEditing();
        this.editingNode = node;

        // 检查是否在简单HTML模式
        const simpleNode = document.querySelector(`[data-node-id="${node.id}"]`) as HTMLElement;
        if (simpleNode) {
            console.log('Using simple HTML editing');
            this.startSimpleEditing(node, simpleNode);
            return;
        }

        // markmap模式的编辑
        console.log('Using markmap editing mode');
        this.startMarkmapEditing(node);
    }

    // markmap模式的编辑
    private startMarkmapEditing(node: MindMapNode) {
        this.logger.debug('开始markmap编辑模式');
        this.logger.debug('要编辑的节点:', node);
        
        // 设置当前编辑节点
        this.editingNode = node;
        
        // 查找包含该节点内容的文本元素
        const svg = document.querySelector('.mindmap-container svg');
        if (!svg) {
            this.logger.warn('未找到SVG元素，无法编辑');
            this.fallbackToSimpleEditing(node);
            return;
        }
        
        this.logger.debug('找到SVG元素');

        // 查找所有可能的文本元素
        const textElements = Array.from(svg.querySelectorAll('text, tspan'));
        this.logger.debug(`找到${textElements.length}个文本元素`);
        
        // 按照与节点内容的相似度对文本元素进行排序
        const sortedElements = this.sortElementsByContentSimilarity(textElements, node.content);
        
        // 取最匹配的元素
        let targetElement: Element | null = sortedElements.length > 0 ? sortedElements[0].element : null;
        
        if (targetElement) {
            this.logger.debug('找到最匹配的目标元素，匹配度:', sortedElements[0].similarity);
        } else {
            this.logger.debug('未找到匹配的目标元素');
        }

        // 如果没有找到匹配的元素，尝试其他方法
        if (!targetElement) {
            // 方法2：查找父级g元素中的文本
            this.logger.debug('尝试查找g元素中的文本...');
            const gElements = svg.querySelectorAll('g');
            this.logger.trace(`找到${gElements.length}个g元素`);
            
            for (let i = 0; i < gElements.length; i++) {
                const g = gElements[i];
                if (g.textContent?.includes(node.content)) {
                    const text = g.querySelector('text, tspan');
                    if (text) {
                        targetElement = text;
                        this.logger.debug(`在g元素中找到匹配的文本元素`);
                        break;
                    }
                }
            }
        }

        // 如果仍然没有找到匹配的元素，尝试使用简单HTML编辑模式
        if (!targetElement) {
            this.logger.warn('未找到目标元素，无法编辑');
            this.fallbackToSimpleEditing(node);
            return;
        }

        // 创建输入框
        const input = document.createElement('input');
        input.value = node.content;
        input.className = 'mindmap-node-input';
        
        // 如果内容包含换行符，使用textarea而不是input
        if (node.content.includes('\n')) {
            const textarea = document.createElement('textarea');
            textarea.value = node.content;
            textarea.className = 'mindmap-node-input';
            input.replaceWith(textarea);
            input.remove();
            input.value = '';
            return this.setupTextareaEditing(textarea, node, targetElement, svg);
        }

        // 获取元素位置
        const rect = targetElement.getBoundingClientRect();
        const svgRect = svg.getBoundingClientRect();
        
        this.logger.trace('目标元素位置:', rect);
        this.logger.trace('SVG元素位置:', svgRect);

        // 计算相对于页面的位置
        const pageLeft = rect.left;
        const pageTop = rect.top;
        
        this.logger.debug('计算编辑框位置:', { left: pageLeft, top: pageTop });

        // 设置输入框样式
        Object.assign(input.style, {
            position: 'fixed', // 使用fixed而不是absolute，以确保相对于视口定位
            left: `${pageLeft}px`,
            top: `${pageTop}px`,
            width: `${Math.max(rect.width + 40, 100)}px`,
            height: `${Math.max(rect.height + 8, 30)}px`,
            zIndex: '10000',
            fontSize: '14px',
            fontFamily: 'var(--font-text)',
            background: 'var(--background-primary)',
            border: '2px solid var(--text-accent)',
            borderRadius: '4px',
            padding: '4px 8px',
            color: 'var(--text-normal)',
            outline: 'none',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)'
        });

        // 将输入框添加到body
        document.body.appendChild(input);
        this.logger.debug('输入框已添加到文档');

        // 确保输入框获得焦点
        setTimeout(() => {
            input.focus();
            input.select();
            this.logger.debug('输入框已获得焦点');
        }, 10);

        // 添加事件监听器
        input.addEventListener('blur', () => {
            this.logger.debug('输入框失去焦点，完成编辑');
            this.finishEditing().catch(e => this.logger.error('编辑完成时出错:', e));
        });

        input.addEventListener('keydown', (e) => {
            this.logger.trace('输入框键盘事件:', e.key);
            if (e.key === 'Enter') {
                e.preventDefault();
                this.logger.debug('按下回车键，完成编辑');
                this.finishEditing().catch(e => this.logger.error('编辑完成时出错:', e));
            } else if (e.key === 'Escape') {
                e.preventDefault();
                this.logger.debug('按下ESC键，取消编辑');
                this.cancelEditing();
            }
        });
        
        this.logger.debug('markmap编辑模式设置完成');
    }
    
    // 设置textarea编辑
    private setupTextareaEditing(textarea: HTMLTextAreaElement, node: MindMapNode, targetElement: Element, svg: Element) {
        // 获取元素位置
        const rect = targetElement.getBoundingClientRect();
        
        // 设置textarea样式
        Object.assign(textarea.style, {
            position: 'fixed',
            left: `${rect.left}px`,
            top: `${rect.top}px`,
            width: `${Math.max(rect.width + 40, 200)}px`,
            height: `${Math.max(rect.height + 40, 100)}px`,
            zIndex: '10000',
            fontSize: '14px',
            fontFamily: 'var(--font-text)',
            background: 'var(--background-primary)',
            border: '2px solid var(--text-accent)',
            borderRadius: '4px',
            padding: '8px',
            color: 'var(--text-normal)',
            outline: 'none',
            resize: 'both',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)'
        });

        // 将textarea添加到body
        document.body.appendChild(textarea);
        console.log('textarea已添加到文档');

        // 确保textarea获得焦点
        setTimeout(() => {
            textarea.focus();
            textarea.select();
            console.log('textarea已获得焦点');
        }, 10);

        // 添加事件监听器
        textarea.addEventListener('blur', () => {
            console.log('textarea失去焦点，完成编辑');
            if (this.editingNode === node) {
                this.editingNode.content = textarea.value;
                textarea.remove();
                this.editingNode = null;
                this.renderMindMap().catch(console.error);
                this.saveData().catch(console.error);
            }
        });

        textarea.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                e.preventDefault();
                console.log('按下ESC键，取消编辑');
                textarea.remove();
                this.editingNode = null;
            } else if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                console.log('按下Ctrl+Enter，完成编辑');
                if (this.editingNode === node) {
                    this.editingNode.content = textarea.value;
                    textarea.remove();
                    this.editingNode = null;
                    this.renderMindMap().catch(console.error);
                    this.saveData().catch(console.error);
                }
            }
        });
    }
    
    // 按照内容相似度对元素进行排序
    private sortElementsByContentSimilarity(elements: Element[], content: string): {element: Element, similarity: number}[] {
        const result: {element: Element, similarity: number}[] = [];
        
        for (const el of elements) {
            if (!el.textContent) continue;
            
            const elContent = el.textContent.trim();
            const similarity = this.calculateSimilarity(elContent, content);
            
            result.push({
                element: el,
                similarity
            });
        }
        
        // 按相似度降序排序
        return result.sort((a, b) => b.similarity - a.similarity);
    }
    
    // 计算两个字符串的相似度（0-1之间，1表示完全相同）
    private calculateSimilarity(str1: string, str2: string): number {
        // 如果完全相同，返回1
        if (str1 === str2) return 1;
        
        // 如果一个包含另一个，返回较高的相似度
        if (str1.includes(str2)) return 0.9;
        if (str2.includes(str1)) return 0.8;
        
        // 计算编辑距离
        const longerStr = str1.length > str2.length ? str1 : str2;
        const shorterStr = str1.length > str2.length ? str2 : str1;
        
        // 如果较长的字符串为空，返回0
        if (longerStr.length === 0) return 0;
        
        // 计算相似度
        let matchCount = 0;
        for (let i = 0; i < shorterStr.length; i++) {
            if (shorterStr[i] === longerStr[i]) {
                matchCount++;
            }
        }
        
        return matchCount / longerStr.length;
    }
    
    // 回退到简单HTML编辑模式
    private fallbackToSimpleEditing(node: MindMapNode) {
        console.log('回退到简单HTML编辑模式...');
        this.createSimpleHTMLMindMap();
        
        // 延迟一下再尝试编辑
        setTimeout(() => {
            const simpleNode = document.querySelector(`[data-node-id="${node.id}"]`) as HTMLElement;
            if (simpleNode) {
                console.log('找到简单HTML节点，开始编辑');
                this.startSimpleEditing(node, simpleNode);
            } else {
                console.log('无法找到简单HTML节点，编辑失败');
            }
        }, 100);
    }

    // 获取 SVG 元素的页面坐标
    private getSVGElementPosition(element: SVGTextElement) {
        const svg = element.ownerSVGElement!;
        const point = svg.createSVGPoint();
        const bbox = element.getBBox();
        point.x = bbox.x;
        point.y = bbox.y + bbox.height;
        
        // 转换为页面坐标
        const ctm = element.getScreenCTM();
        if (ctm) {
            const globalPoint = point.matrixTransform(ctm);
            return { x: globalPoint.x, y: globalPoint.y };
        }
        return { x: 0, y: 0 };
    }

    // 完成编辑
    private async finishEditing() {
        console.log('===== 完成编辑 =====');
        
        // 查找输入元素（可能是input或textarea）
        const input = document.querySelector('.mindmap-node-input');
        if (!input) {
            console.log('未找到输入元素');
            return;
        }
        
        if (!this.editingNode) {
            console.log('没有正在编辑的节点');
            this.safelyRemoveElement(input);
            return;
        }
        
        console.log('正在完成节点编辑:', this.editingNode);
        console.log('节点编辑前属性:', {
            isHeading: this.editingNode.isHeading,
            nodeType: this.editingNode.nodeType,
            originalLevel: this.editingNode.originalLevel
        });

        // 获取输入值（处理input和textarea两种情况）
        let newContent = '';
        if (input instanceof HTMLInputElement) {
            newContent = input.value;
        } else if (input instanceof HTMLTextAreaElement) {
            newContent = input.value;
        } else {
            newContent = input.textContent || '';
        }
        
        // 更新节点内容
        this.editingNode.content = newContent;

        console.log('节点编辑后属性:', {
            isHeading: this.editingNode.isHeading,
            nodeType: this.editingNode.nodeType,
            originalLevel: this.editingNode.originalLevel,
            content: this.editingNode.content
        });

        // 安全地移除输入元素
        this.safelyRemoveElement(input);

        // 清除编辑状态
        const editingNode = this.editingNode;
        this.editingNode = null;
        
        // 清除节点内容缓存
        this.clearNodeContentCache();
        
        // 重新渲染思维导图
        try {
            await this.renderMindMap();
            console.log('思维导图重新渲染完成');
            
            // 重新选中编辑的节点
            if (editingNode) {
                this.selectNode(editingNode);
                this.highlightSelectedNode();
            }
        } catch (error) {
            console.error('重新渲染思维导图时出错:', error);
        }

        // 异步保存数据
        try {
            await this.saveData();
            console.log('编辑后数据保存成功');
        } catch (error) {
            console.error('编辑后保存数据时出错:', error);
        }
        
        console.log('===== 编辑完成 =====');
    }

    // 取消编辑
    private cancelEditing() {
        console.log('===== 取消编辑 =====');
        
        // 查找输入元素
        const input = document.querySelector('.mindmap-node-input');
        if (!input) {
            console.log('未找到输入元素');
            return;
        }
        
        if (!this.editingNode) {
            console.log('没有正在编辑的节点');
            this.safelyRemoveElement(input);
            return;
        }
        
        console.log('取消编辑节点:', this.editingNode);

        // 安全地移除输入元素
        this.safelyRemoveElement(input);

        // 清除编辑状态
        this.editingNode = null;
        
        console.log('===== 编辑已取消 =====');
    }
    
    // 安全地移除元素
    private safelyRemoveElement(element: Element) {
        try {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
                console.log('元素已安全移除');
            }
        } catch (error) {
            console.warn('移除元素时出错:', error);
        }
    }

    // 创建新的思维导图
    async createNewMindMap() {
        this.rootNode = {
            id: 'root',
            content: '中心主题',
            children: [],
            isExpanded: true
        };

        // 添加一些测试子节点
        const child1: MindMapNode = {
            id: 'child1',
            content: '子节点1',
            children: [],
            parent: this.rootNode,
            isExpanded: true
        };

        const child2: MindMapNode = {
            id: 'child2',
            content: '子节点2',
            children: [],
            parent: this.rootNode,
            isExpanded: true
        };

        this.rootNode.children.push(child1, child2);

        const leaf = this.app.workspace.getLeaf(true);
        await leaf.setViewState({
            type: MIND_MAP_VIEW_TYPE,
            state: { data: this.rootNode }
        });

        this.app.workspace.revealLeaf(leaf);

        // 选中根节点
        this.selectNode(this.rootNode);
        this.saveData().catch(console.error); // 保存数据
    }

    // 保存数据并同步到源文件
    async saveData(): Promise<void> {
        console.log('saveData called, rootNode:', this.rootNode);
        if (!this.rootNode) return Promise.resolve();

        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        console.log('Found MindMapView:', view);

        if (view) {
            // 获取当前视图状态中的源文件路径
            const currentState = view.leaf.getViewState().state as MindMapViewState;
            const sourceFilePath = currentState?.sourceFile || view.sourceFilePath;

            console.log('Current view state:', currentState);
            console.log('Source file path from state:', currentState?.sourceFile);
            console.log('Source file path from view:', view.sourceFilePath);
            console.log('Final source file path:', sourceFilePath);

            // 如果有源文件，先同步保存到Markdown文件
            if (sourceFilePath) {
                console.log('Attempting to sync to source file:', sourceFilePath);
                try {
                    await this.syncToSourceFile(sourceFilePath);
                } catch (error) {
                    console.error('Error syncing to source file:', error);
                    new Notice('同步到源文件失败: ' + error.message);
                }
            } else {
                console.log('No source file path found, skipping sync');
            }

            // 然后保存到视图状态
            return view.leaf.setViewState({
                type: MIND_MAP_VIEW_TYPE,
                state: {
                    data: this.rootNode,
                    sourceFile: sourceFilePath // 保持源文件路径
                }
            });
        } else {
            console.log('No MindMapView found');
        }
        return Promise.resolve();
    }

    // 防抖同步定时器
    private syncDebounceTimer: NodeJS.Timeout | null = null;

    // 同步到源文件（带防抖）
    private async syncToSourceFile(filePath: string, immediate: boolean = false) {
        if (!this.rootNode) {
            console.log('没有根节点，无法同步');
            return;
        }

        // 如果不是立即同步且启用了防抖，则使用防抖机制
        if (!immediate && this.settings.autoSyncInterval > 0) {
            if (this.syncDebounceTimer) {
                clearTimeout(this.syncDebounceTimer);
            }

            this.syncDebounceTimer = setTimeout(() => {
                this.performSync(filePath);
            }, this.settings.autoSyncInterval);
            return;
        }

        // 立即执行同步
        await this.performSync(filePath);
    }

    // 执行实际的同步操作
    private async performSync(filePath: string) {
        if (!this.rootNode) {
            console.log('没有根节点，无法同步');
            return;
        }

        try {
            console.log('===== 开始同步到源文件 =====');
            console.log('源文件路径:', filePath);
            console.log('根节点数据:', this.rootNode);

            // 设置同步标志
            this.isSyncing = true;

            // 生成保持原始格式的markdown内容
            const markdownContent = this.generateCleanMarkdownFromMindMap(this.rootNode);
            console.log('生成的Markdown内容长度:', markdownContent.length);

            // 获取文件对象
            const file = this.app.vault.getAbstractFileByPath(filePath);
            if (!file) {
                console.error('未找到源文件:', filePath);
                throw new Error(`未找到源文件: ${filePath}`);
            }
            
            console.log('找到源文件:', file);

            // 检查文件内容是否真的需要更新
            const currentContent = await this.app.vault.read(file as any);
            if (currentContent.trim() === markdownContent.trim()) {
                console.log('文件内容没有变化，跳过同步');
                return;
            }

            // 暂时禁用文件监听器以避免循环更新
            const view = this.app.workspace.getActiveViewOfType(MindMapView);
            let wasWatchingFile = false;
            if (view && view.fileWatcher && view.sourceFilePath === filePath) {
                console.log('暂时禁用文件监听器以避免循环更新');
                this.app.vault.offref(view.fileWatcher);
                view.fileWatcher = null;
                wasWatchingFile = true;
            }

            try {
                // 更新文件内容
                await this.app.vault.modify(file as any, markdownContent);
                console.log(`成功同步思维导图到源文件: ${filePath}`);
                
                // 如果启用了实时同步，显示同步状态
                if (this.settings.realTimeSync) {
                    this.showSyncStatus('已同步', 'success');
                }
            } catch (error) {
                console.error('修改文件时出错:', error);
                this.showSyncStatus('同步失败', 'error');
                throw new Error(`修改文件时出错: ${error.message}`);
            }

            // 重新启用文件监听器
            if (view && wasWatchingFile) {
                setTimeout(() => {
                    console.log('重新启用文件监听器');
                    try {
                        view.setupFileWatcher(filePath);
                        console.log('文件监听器已重新启用');
                    } catch (error) {
                        console.error('重新启用文件监听器时出错:', error);
                    }
                }, 300); // 稍长的延迟以确保文件写入完成
            }

            // 显示成功提示（可选，避免过于频繁的提示）
            // new Notice('已同步到源文件');
            
            console.log('===== 同步到源文件完成 =====');
        } catch (error) {
            console.error('同步到源文件时出错:', error);
            this.showSyncStatus('同步失败: ' + error.message, 'error');
            throw error;
        } finally {
            // 清除同步标志
            this.isSyncing = false;
        }
    }

    // 显示同步状态
    private showSyncStatus(message: string, type: 'success' | 'error' | 'info' = 'info') {
        if (!this.settings.realTimeSync) return;

        // 创建状态指示器
        const statusEl = document.querySelector('.mindmap-sync-status') as HTMLElement;
        if (statusEl) {
            statusEl.textContent = message;
            statusEl.className = `mindmap-sync-status ${type}`;
            
            // 3秒后隐藏状态
            setTimeout(() => {
                statusEl.textContent = '';
                statusEl.className = 'mindmap-sync-status';
            }, 3000);
        } else {
            // 如果没有状态指示器，使用通知
            if (type === 'error') {
                new Notice(message);
            }
        }
    }

    // 添加同步状态指示器到视图
    private addSyncStatusIndicator(container: HTMLElement) {
        if (!this.settings.realTimeSync) return;

        const statusEl = container.createDiv('mindmap-sync-status');
        statusEl.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            transition: all 0.3s ease;
        `;

        // 添加CSS样式
        if (!document.getElementById('mindmap-sync-styles')) {
            const style = document.createElement('style');
            style.id = 'mindmap-sync-styles';
            style.textContent = `
                .mindmap-sync-status.success {
                    background: var(--background-modifier-success);
                    color: var(--text-on-accent);
                }
                .mindmap-sync-status.error {
                    background: var(--background-modifier-error);
                    color: var(--text-on-accent);
                }
                .mindmap-sync-status.info {
                    background: var(--background-modifier-border);
                    color: var(--text-muted);
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    // 检查文件是否存在
    private async fileExists(filePath: string): Promise<boolean> {
        try {
            const file = this.app.vault.getAbstractFileByPath(filePath);
            return !!file;
        } catch (error) {
            console.error('检查文件是否存在时出错:', error);
            return false;
        }
    }

    // 加载数据
    loadData(): Promise<any>;
    loadData(data: MindMapNode): Promise<void>;
    async loadData(data?: MindMapNode): Promise<any> {
        if (data) {
            this.rootNode = this.reconstructNode(data);
            this.currentNode = this.rootNode;
            await this.renderMindMap();
        }
    }

    // 获取根节点数据
    getRootNode(): MindMapNode | null {
        return this.rootNode;
    }

    // 更新思维导图数据（用于文件监听器）
    updateMindMapData(data: MindMapNode) {
        console.log('Updating mind map data');
        this.rootNode = this.reconstructNode(data);
        this.currentNode = this.rootNode;
        this.selectedNode = this.rootNode;
        console.log('Mind map data updated');
    }

    // 重建节点树（恢复父节点引用）
    private reconstructNode(node: MindMapNode, parent: MindMapNode | undefined = undefined): MindMapNode {
        const newNode: MindMapNode = {
            id: node.id,
            content: node.content,
            children: [],
            parent: parent,
            isSelected: node.isSelected,
            isExpanded: node.isExpanded !== undefined ? node.isExpanded : true,
            isHeading: node.isHeading,
            originalLevel: node.originalLevel,
            nodeType: node.nodeType
        };

        console.log(`重建节点: "${node.content}", 保留属性:`, {
            isHeading: newNode.isHeading,
            nodeType: newNode.nodeType,
            originalLevel: newNode.originalLevel
        });

        for (const child of node.children) {
            newNode.children.push(this.reconstructNode(child, newNode));
        }

        return newNode;
    }

    // 强制从源文件重新解析思维导图
    async reloadFromSourceFile(sourceFilePath: string): Promise<void> {
        try {
            console.log('强制从源文件重新解析:', sourceFilePath);

            // 获取文件对象
            const file = this.app.vault.getAbstractFileByPath(sourceFilePath);
            if (!file) {
                console.error('源文件不存在:', sourceFilePath);
                return;
            }

            // 读取文件内容
            const content = await this.app.vault.read(file as any);
            console.log('读取文件内容长度:', content.length);

            // 重新解析为思维导图数据
            const newRootNode = this.parseMarkdownToMindMap(content, file.name);

            if (newRootNode) {
                console.log('重新解析成功，更新根节点');
                this.rootNode = newRootNode;
                this.currentNode = this.rootNode;
                this.selectedNode = this.rootNode;

                // 重新渲染思维导图
                await this.renderMindMap();

                // 保存新的状态
                await this.saveData();

                console.log('从源文件重新加载完成');
            } else {
                console.error('重新解析失败');
            }
        } catch (error) {
            console.error('从源文件重新加载时出错:', error);
        }
    }

    // 创建同级节点
    private createSiblingNode(node: MindMapNode) {
        if (!node.parent) return; // 根节点不能创建同级节点

        const newNode: MindMapNode = {
            id: Date.now().toString(),
            content: '新节点',
            children: [],
            parent: node.parent,
            isExpanded: true
        };

        const index = node.parent.children.indexOf(node);
        node.parent.children.splice(index + 1, 0, newNode);

        this.renderMindMap().catch(console.error);
        this.selectNode(newNode);
        this.saveData().catch(console.error); // 保存数据
    }

    // 创建父节点
    private createParentNode(node: MindMapNode) {
        const newNode: MindMapNode = {
            id: Date.now().toString(),
            content: '新父节点',
            children: [node]
        };

        if (node === this.rootNode) {
            this.rootNode = newNode;
        } else if (node.parent) {
            const parentChildren = node.parent.children;
            const index = parentChildren.indexOf(node);
            parentChildren[index] = newNode;
            newNode.parent = node.parent;
        }

        node.parent = newNode;
        this.currentNode = newNode;
        this.renderMindMap().catch(console.error);
        this.saveData().catch(console.error); // 保存数据
    }

    // 创建子节点
    private createChildNode(parentNode: MindMapNode) {
        const newNode: MindMapNode = {
            id: Date.now().toString(),
            content: '新节点',
            children: [],
            parent: parentNode,
            isExpanded: true
        };

        parentNode.children.push(newNode);
        parentNode.isExpanded = true; // 确保父节点展开

        this.renderMindMap().catch(console.error);
        this.selectNode(newNode);
        this.saveData().catch(console.error); // 保存数据
    }

    // 查找节点
    private findNode(root: MindMapNode, id: string): MindMapNode | null {
        if (root.id === id) return root;
        for (const child of root.children) {
            const found = this.findNode(child, id);
            if (found) return found;
        }
        return null;
    }

    // 渲染思维导图
    async renderMindMap() {
        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        if (!view || !this.rootNode) {
            return;
        }

        const container = view.containerEl.querySelector('.mindmap-container') as HTMLElement;
        if (!container) return;

        // 清空容器
        container.innerHTML = '';

        // 等待容器完全渲染后再获取尺寸
        await new Promise(resolve => setTimeout(resolve, 50));

        // 确保容器有明确的尺寸
        const containerRect = container.getBoundingClientRect();
        let width = containerRect.width;
        let height = containerRect.height;

        // 减去内边距（CSS中设置了20px padding）
        const padding = 40; // 左右各20px
        width = Math.max(width - padding, 600); // 最小宽度600px
        height = Math.max(height - padding, 400); // 最小高度400px

        // 如果容器尺寸仍然无效，使用默认值
        if (!width || width <= 0 || isNaN(width)) {
            width = 800;
        }
        if (!height || height <= 0 || isNaN(height)) {
            height = 600;
        }

        console.log('Container dimensions (after padding):', width, height);

        // 创建SVG元素，使用响应式尺寸
        const svg = d3.select(container)
            .append('svg')
            .attr('width', '100%')
            .attr('height', '100%')
            .attr('viewBox', `0 0 ${width} ${height}`)
            .attr('preserveAspectRatio', 'xMidYMid meet')
            .style('width', '100%')
            .style('height', '100%')
            .style('min-width', width + 'px')
            .style('min-height', height + 'px');

        // 等待下一帧再创建markmap，确保SVG已经渲染
        requestAnimationFrame(() => {
            try {
                // 使用markmap渲染，优化配置参数
                this.mindmap = Markmap.create(svg.node() as SVGSVGElement, {
                    autoFit: true,
                    duration: 300,
                    maxWidth: Math.max(width - 100, 200), // 确保最小宽度
                    initialExpandLevel: 3, // 增加初始展开层级
                    spacingVertical: 10, // 垂直间距
                    spacingHorizontal: 80, // 水平间距
                    paddingX: 20, // X轴内边距
                });

                // 转换数据格式并更新
                const markdown = this.mindmapNodeToMarkdown(this.rootNode!);
                const { root: data } = this.transformer.transform(markdown);

                console.log('Markmap data:', data);

                // 为每个节点添加自定义ID
                this.addCustomNodeIds(data, this.rootNode!);

                this.mindmap.setData(data);

                // 设置事件监听器
                this.setupEventListeners(container);

                // 如果有选中的节点，高亮显示
                if (this.selectedNode) {
                    setTimeout(() => this.highlightSelectedNode(), 100);
                }
            } catch (error) {
                console.error('Error creating markmap:', error);
                // 如果markmap失败，创建一个简单的备用显示
                this.createFallbackDisplay(container);
            }
        });
    }

    // 创建备用显示
    private createFallbackDisplay(container: HTMLElement) {
        container.innerHTML = '';
        const fallbackDiv = container.createDiv('mindmap-fallback');
        fallbackDiv.style.cssText = `
            padding: 20px;
            text-align: center;
            color: var(--text-muted);
            font-size: 14px;
        `;

        fallbackDiv.innerHTML = `
            <h3>思维导图渲染失败</h3>
            <p>请尝试以下操作：</p>
            <ul style="text-align: left; display: inline-block;">
                <li>重新加载插件</li>
                <li>检查浏览器控制台的错误信息</li>
                <li>使用调试命令查看详细信息</li>
            </ul>
            <button onclick="this.closest('.mindmap-container').dispatchEvent(new CustomEvent('retry-render'))">
                重试渲染
            </button>
        `;

        // 添加重试事件监听器
        container.addEventListener('retry-render', () => {
            setTimeout(() => this.renderMindMap().catch(console.error), 100);
        });
    }

    // 为markmap数据添加自定义ID
    private addCustomNodeIds(markmapNode: any, mindmapNode: MindMapNode) {
        markmapNode.customId = mindmapNode.id;
        if (markmapNode.children && mindmapNode.children) {
            for (let i = 0; i < markmapNode.children.length; i++) {
                if (mindmapNode.children[i]) {
                    this.addCustomNodeIds(markmapNode.children[i], mindmapNode.children[i]);
                }
            }
        }
    }

    // 设置事件监听器
    private setupEventListeners(container: Element) {
        // 移除旧的事件监听器
        const svg = container.querySelector('svg');
        if (!svg) {
            console.log('No SVG found in container');
            return;
        }

        console.log('===== 设置事件监听器 =====');
        console.log('SVG元素:', svg);
        console.log('SVG尺寸:', svg.getBoundingClientRect());

        // 清理旧的事件处理器
        if ((container as any)._mindmapHandlers) {
            console.log('清理旧的事件处理器');
            const handlers = (container as any)._mindmapHandlers;
            svg.removeEventListener('click', handlers.clickHandler, true);
            svg.removeEventListener('dblclick', handlers.dblClickHandler, true);
            container.removeEventListener('click', handlers.clickHandler, true);
            container.removeEventListener('dblclick', handlers.dblClickHandler, true);
            document.removeEventListener('dblclick', handlers.documentHandler, true);
        }

        // 清理旧的MutationObserver
        if ((container as any)._mindmapObserver) {
            console.log('清理旧的MutationObserver');
            (container as any)._mindmapObserver.disconnect();
            (container as any)._mindmapObserver = null;
        }

        // 使用更强的事件监听器，确保能捕获到所有事件
        const clickHandler = (evt: MouseEvent) => {
            console.log('Click event captured on:', evt.target);
            this.handleSVGClick(evt);
        };

        const dblClickHandler = (evt: MouseEvent) => {
            console.log('Double click event captured on:', evt.target);
            evt.preventDefault();
            evt.stopPropagation();
            this.handleSVGDoubleClick(evt);
        };

        // 在多个层级添加事件监听器
        svg.addEventListener('click', clickHandler, true);
        svg.addEventListener('dblclick', dblClickHandler, true);

        container.addEventListener('click', clickHandler, true);
        container.addEventListener('dblclick', dblClickHandler, true);

        // 为所有文本元素单独添加双击事件
        console.log('为文本元素添加双击事件');
        const textElements = svg.querySelectorAll('text, tspan');
        console.log(`找到${textElements.length}个文本元素`);
        
        textElements.forEach((el, index) => {
            console.log(`文本元素 ${index}:`, el.textContent);
            el.addEventListener('dblclick', dblClickHandler, true);
            // 添加标记以便识别已绑定事件的元素
            el.setAttribute('data-event-bound', 'true');
            
            // 添加样式以提高可点击性
            el.setAttribute('style', 'cursor: pointer; user-select: none;');
            
            // 添加title提示，提示用户可以双击编辑
            if (el.textContent && el.textContent.trim()) {
                el.setAttribute('title', '双击编辑');
            }
        });

        // 为所有g元素添加双击事件
        console.log('为g元素添加双击事件');
        const gElements = svg.querySelectorAll('g');
        console.log(`找到${gElements.length}个g元素`);
        
        gElements.forEach((el, index) => {
            if (el.querySelector('text')) {
                console.log(`g元素 ${index} 包含文本:`, el.querySelector('text')?.textContent);
                el.addEventListener('dblclick', dblClickHandler, true);
                // 添加标记以便识别已绑定事件的元素
                el.setAttribute('data-event-bound', 'true');
                
                // 添加样式以提高可点击性
                el.setAttribute('style', 'cursor: pointer;');
            }
        });

        // 添加到document级别作为最后的备用
        const documentHandler = (evt: MouseEvent) => {
            const target = evt.target as Element;
            if (target.closest('.mindmap-container')) {
                if (evt.type === 'dblclick') {
                    console.log('Document level double click captured');
                    evt.preventDefault();
                    evt.stopPropagation();
                    this.handleSVGDoubleClick(evt);
                }
            }
        };

        document.addEventListener('dblclick', documentHandler, true);

        // 保存处理器引用以便清理
        (container as any)._mindmapHandlers = {
            clickHandler,
            dblClickHandler,
            documentHandler
        };
        
        // 添加MutationObserver以处理动态添加的元素
        console.log('设置MutationObserver监听动态元素');
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    // 为新添加的文本元素绑定事件
                    const newTextElements = svg.querySelectorAll('text:not([data-event-bound]), tspan:not([data-event-bound])');
                    if (newTextElements.length > 0) {
                        console.log(`发现${newTextElements.length}个新文本元素，添加事件监听器`);
                        newTextElements.forEach(el => {
                            el.addEventListener('dblclick', dblClickHandler, true);
                            el.setAttribute('data-event-bound', 'true');
                            el.setAttribute('style', 'cursor: pointer; user-select: none;');
                            if (el.textContent && el.textContent.trim()) {
                                el.setAttribute('title', '双击编辑');
                            }
                        });
                    }
                    
                    // 为新添加的g元素绑定事件
                    const newGElements = svg.querySelectorAll('g:not([data-event-bound])');
                    if (newGElements.length > 0) {
                        console.log(`发现${newGElements.length}个新g元素，添加事件监听器`);
                        newGElements.forEach(el => {
                            if (el.querySelector('text')) {
                                el.addEventListener('dblclick', dblClickHandler, true);
                                el.setAttribute('data-event-bound', 'true');
                                el.setAttribute('style', 'cursor: pointer;');
                            }
                        });
                    }
                }
            });
        });
        
        observer.observe(svg, { childList: true, subtree: true });
        (container as any)._mindmapObserver = observer;
        
        // 添加CSS样式到文档，以提高编辑体验
        this.addEditingStyles();
        
        console.log('事件监听器设置完成');
    }
    
    // 添加编辑相关的CSS样式
    private addEditingStyles() {
        // 检查是否已经添加了样式
        if (document.getElementById('mindmap-editing-styles')) {
            return;
        }
        
        // 创建样式元素
        const style = document.createElement('style');
        style.id = 'mindmap-editing-styles';
        style.textContent = `
            .mindmap-node-input {
                font-family: var(--font-text);
                background: var(--background-primary);
                color: var(--text-normal);
                border: 2px solid var(--text-accent);
                border-radius: 4px;
                padding: 4px 8px;
                outline: none;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                transition: all 0.2s ease;
            }
            
            .mindmap-node-input:focus {
                border-color: var(--interactive-accent);
                box-shadow: 0 0 0 2px var(--background-modifier-border-focus);
            }
            
            .mindmap-container text,
            .mindmap-container tspan {
                cursor: pointer;
                user-select: none;
            }
            
            .mindmap-container g {
                cursor: pointer;
            }
            
            .mindmap-container .selected-node text,
            .mindmap-container .selected-node tspan {
                fill: var(--text-accent) !important;
                font-weight: bold;
            }
        `;
        
        // 添加到文档头部
        document.head.appendChild(style);
        console.log('添加了编辑样式');
    }

    // 处理SVG点击事件
    private handleSVGClick(evt: MouseEvent) {
        console.log('===== SVG Click event triggered =====');
        console.log('Event target:', evt.target);
        console.log('Event currentTarget:', evt.currentTarget);
        console.log('Event path:', evt.composedPath());

        const target = evt.target as Element;
        console.log('Target element:', target.tagName, target.className);

        // 尝试多种方式查找节点组和文本内容
        let nodeContent: string | null = null;
        let nodeGroup: Element | null = null;
        let textElement: Element | null = null;

        // 方法1：直接从目标元素获取文本
        if (target.textContent && target.textContent.trim()) {
            nodeContent = target.textContent.trim();
            nodeGroup = target.closest('g[data-depth]') || target.closest('g');
            textElement = target;
            console.log('方法1找到内容:', nodeContent);
        }

        // 方法2：查找父级节点组中的文本
        if (!nodeContent) {
            const parentGroup = target.parentElement?.closest('g') || null;
            nodeGroup = target.closest('g[data-depth]') || target.closest('g') || parentGroup;
            console.log('找到的节点组:', nodeGroup);
            
            if (nodeGroup) {
                // 查找文本元素（可能是text标签或div标签）
                textElement = nodeGroup.querySelector('text') || nodeGroup.querySelector('div[xmlns]') || nodeGroup.querySelector('div');
                if (textElement && textElement.textContent) {
                    nodeContent = textElement.textContent.trim();
                    console.log('方法2找到内容:', nodeContent);
                }
            }
        }

        // 方法3：遍历所有父元素查找文本
        if (!nodeContent) {
            let currentElement: Element | null = target;
            while (currentElement && !nodeContent) {
                if (currentElement.textContent && currentElement.textContent.trim()) {
                    nodeContent = currentElement.textContent.trim();
                    textElement = currentElement;
                    console.log('方法3找到内容:', nodeContent);
                    break;
                }
                currentElement = currentElement.parentElement;
            }
        }

        console.log('最终提取的内容:', nodeContent);
        console.log('找到的文本元素:', textElement);
        console.log('找到的节点组:', nodeGroup);

        if (nodeContent && this.rootNode) {
            console.log('尝试在思维导图数据中查找节点...');
            
            // 尝试精确匹配
            let node = this.findNodeByContent(this.rootNode, nodeContent);
            
            // 如果精确匹配失败，尝试部分匹配
            if (!node) {
                console.log('精确匹配失败，尝试部分匹配...');
                const allNodes = this.getAllNodes(this.rootNode);
                
                // 首先尝试包含匹配
                const partialMatch = allNodes.find(n => 
                    n.content.includes(nodeContent!) || 
                    nodeContent!.includes(n.content)
                );
                
                if (partialMatch) {
                    console.log('找到部分匹配的节点:', partialMatch);
                    node = partialMatch;
                }
            }
            
            if (node) {
                console.log('找到匹配的节点:', node);
                this.selectNode(node);
            } else {
                console.log('未找到匹配的节点，内容:', nodeContent);
                console.log('可用节点:', this.getAllNodes(this.rootNode).map(n => n.content));
            }
        } else {
            console.log('没有提取到内容或没有根节点');
            if (this.editingNode) {
                console.log('完成编辑');
                this.finishEditing().catch(console.error);
            }
        }
        
        console.log('===== 点击事件处理结束 =====');
    }

    // 处理SVG双击事件
    private handleSVGDoubleClick(evt: MouseEvent) {
        console.log('===== SVG Double click event triggered =====');
        console.log('Event target:', evt.target);
        console.log('Event currentTarget:', evt.currentTarget);
        
        evt.preventDefault();
        evt.stopPropagation();

        const target = evt.target as Element;
        console.log('Double click target tag:', target.tagName);
        console.log('Double click target class:', target.className);
        console.log('Double click target textContent:', target.textContent);

        // 尝试多种方式查找节点内容
        let nodeContent: string | null = null;
        let textElement: Element | null = null;
        let nodeId: string | null = null;

        // 方法1：检查目标元素是否有data-node-id属性（用于简单HTML模式）
        nodeId = target.getAttribute('data-node-id');
        if (nodeId) {
            console.log('方法1找到节点ID:', nodeId);
            // 直接通过ID查找节点
            if (this.rootNode) {
                const node = this.findNodeById(this.rootNode, nodeId);
                if (node) {
                    console.log('通过ID找到节点:', node);
                    this.selectNode(node);
                    this.startEditing(node).catch(console.error);
                    return;
                }
            }
        }

        // 方法2：直接从目标元素获取文本
        if (target.textContent && target.textContent.trim()) {
            nodeContent = target.textContent.trim();
            textElement = target;
            console.log('方法2找到内容:', nodeContent);
        }

        // 方法3：查找父级节点组中的文本
        if (!nodeContent) {
            const nodeGroup = target.closest('g[data-depth]') || target.closest('g');
            console.log('找到的节点组:', nodeGroup);
            
            if (nodeGroup) {
                textElement = nodeGroup.querySelector('text') || nodeGroup.querySelector('tspan') || 
                              nodeGroup.querySelector('div[xmlns]') || nodeGroup.querySelector('div');
                if (textElement && textElement.textContent) {
                    nodeContent = textElement.textContent.trim();
                    console.log('方法3找到内容:', nodeContent);
                }
            }
        }

        // 方法4：遍历所有父元素查找文本
        if (!nodeContent) {
            let currentElement: Element | null = target;
            while (currentElement && !nodeContent) {
                if (currentElement.textContent && currentElement.textContent.trim()) {
                    nodeContent = currentElement.textContent.trim();
                    textElement = currentElement;
                    console.log('方法4找到内容:', nodeContent);
                    break;
                }
                currentElement = currentElement.parentElement;
            }
        }

        // 方法5：如果目标是SVG元素，尝试查找最近的文本元素
        if (!nodeContent && target.namespaceURI === 'http://www.w3.org/2000/svg') {
            // 获取点击位置
            const svgElement = target.closest('svg');
            if (svgElement) {
                // 获取点击坐标
                const rect = svgElement.getBoundingClientRect();
                const x = evt.clientX - rect.left;
                const y = evt.clientY - rect.top;
                
                // 查找最近的文本元素
                const textElements = Array.from(svgElement.querySelectorAll('text, tspan'));
                let closestElement = null;
                let minDistance = Infinity;
                
                for (const el of textElements) {
                    const elRect = el.getBoundingClientRect();
                    const elX = elRect.left + elRect.width / 2 - rect.left;
                    const elY = elRect.top + elRect.height / 2 - rect.top;
                    const distance = Math.sqrt(Math.pow(x - elX, 2) + Math.pow(y - elY, 2));
                    
                    if (distance < minDistance) {
                        minDistance = distance;
                        closestElement = el;
                    }
                }
                
                if (closestElement && closestElement.textContent) {
                    nodeContent = closestElement.textContent.trim();
                    textElement = closestElement;
                    console.log('方法5找到最近的文本元素:', nodeContent);
                }
            }
        }

        console.log('最终提取的内容:', nodeContent);
        console.log('找到的文本元素:', textElement);

        if (nodeContent && this.rootNode) {
            console.log('尝试在思维导图数据中查找节点...');
            
            // 尝试精确匹配
            let node = this.findNodeByContent(this.rootNode, nodeContent);
            
            // 如果精确匹配失败，尝试部分匹配
            if (!node) {
                console.log('精确匹配失败，尝试部分匹配...');
                const allNodes = this.getAllNodes(this.rootNode);
                
                // 首先尝试包含匹配
                const partialMatch = allNodes.find(n => 
                    n.content.includes(nodeContent!) || 
                    nodeContent!.includes(n.content)
                );
                
                if (partialMatch) {
                    console.log('找到部分匹配的节点:', partialMatch);
                    node = partialMatch;
                } else {
                    // 如果仍然失败，尝试更宽松的匹配（忽略空格和特殊字符）
                    console.log('部分匹配失败，尝试宽松匹配...');
                    const normalizedContent = nodeContent.replace(/\s+/g, '').toLowerCase();
                    
                    const looseMatch = allNodes.find(n => {
                        const normalizedNodeContent = n.content.replace(/\s+/g, '').toLowerCase();
                        return normalizedNodeContent.includes(normalizedContent) || 
                               normalizedContent.includes(normalizedNodeContent);
                    });
                    
                    if (looseMatch) {
                        console.log('找到宽松匹配的节点:', looseMatch);
                        node = looseMatch;
                    }
                }
            }
            
            if (node) {
                console.log('找到匹配的节点:', node);
                console.log('开始编辑节点:', node.content);
                
                // 设置为当前选中的节点
                this.selectNode(node);
                
                // 开始编辑
                this.startEditing(node).catch(error => {
                    console.error('编辑节点时出错:', error);
                    
                    // 如果markmap编辑失败，尝试切换到简单HTML模式
                    console.log('尝试切换到简单HTML模式...');
                    this.createSimpleHTMLMindMap();
                    
                    // 延迟一下再尝试编辑
                    setTimeout(() => {
                        const simpleNode = document.querySelector(`[data-node-id="${node!.id}"]`) as HTMLElement;
                        if (simpleNode) {
                            console.log('找到简单HTML节点，开始编辑');
                            this.startSimpleEditing(node!, simpleNode);
                        }
                    }, 100);
                });
            } else {
                console.log('没有找到任何匹配的节点');
                
                // 如果无法找到匹配的节点，可能是markmap渲染的问题
                // 尝试切换到简单HTML模式
                console.log('无法找到匹配节点，尝试切换到简单HTML模式...');
                this.createSimpleHTMLMindMap();
            }
        } else {
            console.log('没有提取到内容或没有根节点');
        }
        
        console.log('===== 双击事件处理结束 =====');
    }
    
    // 根据ID查找节点
    private findNodeById(root: MindMapNode, id: string): MindMapNode | null {
        if (root.id === id) return root;
        for (const child of root.children) {
            const found = this.findNodeById(child, id);
            if (found) return found;
        }
        return null;
    }

    // 根据内容查找节点
    private findNodeByContent(root: MindMapNode, content: string): MindMapNode | null {
        // 缓存查找结果以提高性能
        if (!this._nodeContentCache) {
            this._nodeContentCache = new Map<string, MindMapNode>();
        }
        
        // 检查缓存
        if (this._nodeContentCache.has(content)) {
            return this._nodeContentCache.get(content)!;
        }
        
        // 精确匹配
        if (root.content === content) {
            this._nodeContentCache.set(content, root);
            return root;
        }
        
        // 如果内容包含换行符，尝试只匹配第一行
        if (root.content.includes('\n')) {
            const firstLine = root.content.split('\n')[0];
            if (firstLine === content) {
                this._nodeContentCache.set(content, root);
                return root;
            }
        }
        
        // 如果内容很长，可能在显示时被截断，尝试部分匹配
        if (content.length > 10 && (
            root.content.includes(content) || 
            content.includes(root.content)
        )) {
            this._nodeContentCache.set(content, root);
            return root;
        }
        
        // 递归检查子节点
        for (const child of root.children) {
            const found = this.findNodeByContent(child, content);
            if (found) {
                this._nodeContentCache.set(content, found);
                return found;
            }
        }
        
        return null;
    }
    
    // 清除节点内容缓存
    private clearNodeContentCache() {
        this._nodeContentCache = new Map<string, MindMapNode>();
    }
    
    // 节点内容缓存
    private _nodeContentCache: Map<string, MindMapNode> | null = null;

    // 高亮选中的节点
    private highlightSelectedNode() {
        if (!this.selectedNode || !this.mindmap) return;

        // 移除之前的高亮
        const svg = document.querySelector('.mindmap-container svg');
        if (svg) {
            const selectedElements = svg.querySelectorAll('.selected-node');
            for (let i = 0; i < selectedElements.length; i++) {
                selectedElements[i].classList.remove('selected-node');
            }

            // 查找并高亮当前选中的节点
            const textElements = svg.querySelectorAll('text');
            for (let i = 0; i < textElements.length; i++) {
                const textEl = textElements[i];
                if (textEl.textContent?.trim() === this.selectedNode!.content) {
                    const nodeGroup = textEl.closest('g');
                    if (nodeGroup) {
                        nodeGroup.classList.add('selected-node');
                    }
                }
            }
        }
    }

    // 将节点树转换为Markdown格式
    private mindmapNodeToMarkdown(node: MindMapNode, depth = 0): string {
        const indent = '  '.repeat(depth);
        let markdown = `${indent}- ${node.content}\n`;
        if (node.children) {
            for (const child of node.children) {
                markdown += this.mindmapNodeToMarkdown(child, depth + 1);
            }
        }
        return markdown;
    }

    // 导出为Markdown文档
    async exportToMarkdown() {
        if (!this.rootNode) {
            console.log('No mind map data to export');
            return;
        }

        try {
            // 生成markdown内容
            const markdownContent = this.generateMarkdownFromMindMap(this.rootNode);

            // 创建文件名（基于根节点内容）
            const fileName = this.sanitizeFileName(this.rootNode.content) + '.md';

            // 检查文件是否已存在
            const existingFile = this.app.vault.getAbstractFileByPath(fileName);
            if (existingFile) {
                // 如果文件存在，询问用户是否覆盖
                const shouldOverwrite = await this.confirmOverwrite(fileName);
                if (!shouldOverwrite) {
                    return;
                }
            }

            // 创建或更新文件
            await this.app.vault.create(fileName, markdownContent);

            console.log(`Mind map exported to: ${fileName}`);

            // 打开新创建的文件
            const file = this.app.vault.getAbstractFileByPath(fileName);
            if (file) {
                const leaf = this.app.workspace.getLeaf(false);
                await leaf.openFile(file as any);
            }

        } catch (error) {
            console.error('Error exporting to markdown:', error);
        }
    }

    // 生成完整的Markdown内容
    private generateMarkdownFromMindMap(rootNode: MindMapNode): string {
        console.log('开始生成Markdown，根节点:', rootNode);

        // 处理根节点内容
        const lines = rootNode.content.split('\n');
        const title = lines[0]; // 第一行作为标题
        const description = lines.slice(1).join('\n').trim(); // 其余行作为描述

        let markdown = `# ${title}\n\n`;

        // 如果有描述内容，添加为段落
        if (description) {
            markdown += `${description}\n\n`;
        }

        // 如果根节点有子节点，生成层级结构
        if (rootNode.children && rootNode.children.length > 0) {
            for (const child of rootNode.children) {
                markdown += this.nodeToMarkdownContent(child, 2);
            }
        }

        return markdown;
    }

    // 生成干净的Markdown内容（用于双向同步，不添加导出元数据）
    private generateCleanMarkdownFromMindMap(rootNode: MindMapNode): string {
        console.log('生成干净的Markdown，根节点:', rootNode);

        // 处理根节点内容
        const lines = rootNode.content.split('\n');
        const title = lines[0]; // 第一行作为标题
        const description = lines.slice(1).join('\n').trim(); // 其余行作为描述

        let markdown = `# ${title}\n\n`;

        // 如果有描述内容，添加为段落
        if (description) {
            markdown += `${description}\n\n`;
        }

        // 如果根节点有子节点，生成层级结构
        if (rootNode.children && rootNode.children.length > 0) {
            for (const child of rootNode.children) {
                markdown += this.nodeToCleanMarkdown(child, 2);
            }
        }

        return markdown.trim(); // 移除末尾多余的换行
    }

    // 将节点转换为干净的Markdown格式（保持原始结构）
    private nodeToCleanMarkdown(node: MindMapNode, level: number): string {
        console.log(`转换节点为干净格式: "${node.content}", isHeading: ${node.isHeading}, level: ${level}`);

        let markdown = '';

        // 检查节点是否为原始标题节点
        if (node.isHeading) {
            // 使用原始标题级别或当前级别
            const headingLevel = node.originalLevel || level;
            const headingPrefix = '#'.repeat(Math.min(headingLevel, 6));

            // 处理包含换行符的标题内容
            const lines = node.content.split('\n');
            const title = lines[0]; // 第一行作为标题
            const description = lines.slice(1).join('\n').trim(); // 其余行作为描述

            markdown += `${headingPrefix} ${title}\n\n`;

            // 如果有描述内容，添加为段落
            if (description) {
                markdown += `${description}\n\n`;
            }
        } else {
            // 非标题节点（如列表项）使用列表格式
            markdown += `- ${node.content}\n`;

            // 处理列表项的子节点（嵌套列表）
            if (node.children && node.children.length > 0) {
                for (const child of node.children) {
                    markdown += this.nodeToNestedList(child, 1);
                }
            }
            return markdown;
        }

        // 处理标题节点的子节点
        if (node.children && node.children.length > 0) {
            for (const child of node.children) {
                if (child.isHeading && level < 6) {
                    // 子标题节点
                    markdown += this.nodeToCleanMarkdown(child, level + 1);
                } else {
                    // 列表项
                    markdown += `- ${child.content}\n`;

                    // 处理列表项的子节点
                    if (child.children && child.children.length > 0) {
                        for (const grandChild of child.children) {
                            markdown += this.nodeToNestedList(grandChild, 1);
                        }
                    }
                }
            }
            markdown += '\n'; // 在章节后添加空行
        }

        return markdown;
    }

    // 处理嵌套列表
    private nodeToNestedList(node: MindMapNode, depth: number): string {
        const indent = '  '.repeat(depth);
        let markdown = `${indent}- ${node.content}\n`;

        if (node.children && node.children.length > 0) {
            for (const child of node.children) {
                markdown += this.nodeToNestedList(child, depth + 1);
            }
        }

        return markdown;
    }

    // 智能转换节点为Markdown内容
    private nodeToMarkdownContent(node: MindMapNode, level: number): string {
        console.log(`转换节点: "${node.content}", isHeading: ${node.isHeading}, level: ${level}`);

        let markdown = '';

        // 检查节点是否为原始标题节点
        if (node.isHeading) {
            // 使用原始标题级别或当前级别
            const headingLevel = node.originalLevel || level;
            const headingPrefix = '#'.repeat(Math.min(headingLevel, 6));

            // 处理包含换行符的标题内容
            const lines = node.content.split('\n');
            const title = lines[0]; // 第一行作为标题
            const description = lines.slice(1).join('\n').trim(); // 其余行作为描述

            markdown += `${headingPrefix} ${title}\n\n`;

            // 如果有描述内容，添加为段落
            if (description) {
                markdown += `${description}\n\n`;
            }
        } else {
            // 非标题节点（如列表项）使用列表格式
            markdown += this.nodeToMarkdownList(node, 0);
            return markdown; // 列表项直接返回，不处理子节点
        }

        // 处理子节点
        if (node.children && node.children.length > 0) {
            for (const child of node.children) {
                if (child.isHeading && level < 6) {
                    // 子标题节点
                    markdown += this.nodeToMarkdownContent(child, level + 1);
                } else {
                    // 列表项或超过6级的节点
                    markdown += this.nodeToMarkdownList(child, 0);
                }
            }
        }

        return markdown;
    }

    // 将节点转换为Markdown列表
    private nodeToMarkdownList(node: MindMapNode, depth: number): string {
        const indent = '  '.repeat(depth);
        let markdown = `${indent}- ${node.content}\n`;

        if (node.children && node.children.length > 0) {
            for (const child of node.children) {
                markdown += this.nodeToMarkdownList(child, depth + 1);
            }
        }

        return markdown;
    }

    // 清理文件名
    private sanitizeFileName(name: string): string {
        // 移除或替换不允许的字符
        return name
            .replace(/[<>:"/\\|?*]/g, '-')
            .replace(/\s+/g, '_')
            .substring(0, 100); // 限制长度
    }

    // 确认覆盖文件
    private async confirmOverwrite(fileName: string): Promise<boolean> {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal-container';
            modal.innerHTML = `
                <div class="modal">
                    <div class="modal-title">文件已存在</div>
                    <div class="modal-content">
                        <p>文件 "${fileName}" 已存在。是否要覆盖它？</p>
                    </div>
                    <div class="modal-button-container">
                        <button class="mod-cta" id="confirm-overwrite">覆盖</button>
                        <button id="cancel-overwrite">取消</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            modal.querySelector('#confirm-overwrite')?.addEventListener('click', () => {
                document.body.removeChild(modal);
                resolve(true);
            });

            modal.querySelector('#cancel-overwrite')?.addEventListener('click', () => {
                document.body.removeChild(modal);
                resolve(false);
            });
        });
    }

    // 从Markdown文档导入
    async importFromMarkdown() {
        try {
            // 获取当前活动的文件
            const activeFile = this.app.workspace.getActiveFile();
            if (!activeFile || !activeFile.path.endsWith('.md')) {
                console.log('Please open a markdown file to import');
                new Notice('请先打开一个Markdown文件');
                return;
            }

            console.log('正在导入文件:', activeFile.path);

            // 读取文件内容
            const content = await this.app.vault.read(activeFile);
            console.log('读取到的文件内容:', content);

            // 解析Markdown内容为思维导图结构
            const mindMapData = this.parseMarkdownToMindMap(content, activeFile.basename);
            console.log('解析后的思维导图数据:', mindMapData);

            if (mindMapData) {
                // 设置为当前的思维导图数据
                this.rootNode = mindMapData;
                this.selectedNode = this.rootNode;

                // 创建新的思维导图视图
                const leaf = this.app.workspace.getLeaf(true);
                await leaf.setViewState({
                    type: MIND_MAP_VIEW_TYPE,
                    state: { data: this.rootNode }
                });

                this.app.workspace.revealLeaf(leaf);

                console.log(`Imported mind map from: ${activeFile.path}`);
                new Notice(`成功从 ${activeFile.basename} 导入思维导图`);
            } else {
                console.log('Failed to parse markdown content');
                new Notice('解析Markdown内容失败');
            }

        } catch (error) {
            console.error('Error importing from markdown:', error);
            new Notice('导入失败: ' + error.message);
        }
    }

    // 解析Markdown内容为思维导图结构
    parseMarkdownToMindMap(content: string, fileName: string): MindMapNode | null {
        try {
            console.log('开始解析Markdown内容:', content);

            // 移除前置元数据
            const cleanContent = content.replace(/^---[\s\S]*?---\n/, '');

            // 按行分割
            const lines = cleanContent.split('\n');
            console.log('分割后的行数:', lines.length);

            // 创建根节点 - 使用第一个一级标题作为根节点，如果没有则使用文件名
            let rootNode: MindMapNode | null = null;
            const nodeStack: { node: MindMapNode; level: number; nodeType: 'heading' | 'list' | 'root' }[] = [];
            let nodeIdCounter = 1;
            let pendingParagraphs: string[] = []; // 存储待处理的段落内容

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                const trimmedLine = line.trim();

                console.log(`处理第${i+1}行: "${trimmedLine}"`);

                // 跳过空行和特殊格式行
                if (!trimmedLine ||
                    trimmedLine.startsWith('>') ||
                    trimmedLine.startsWith('---') ||
                    trimmedLine.startsWith('```') ||
                    trimmedLine.match(/^-\s*\*.*\*\s*$/) || // 跳过 "- *由 Mind Map 插件生成*" 这样的行
                    trimmedLine.match(/^\*.*\*$/) || // 跳过斜体注释行
                    trimmedLine.match(/^\|.*\|$/)) { // 表格行
                    console.log('跳过特殊行');
                    continue;
                }

                // 解析标题 (# ## ### 等)
                const headingMatch = trimmedLine.match(/^(#{1,6})\s+(.+)$/);
                if (headingMatch) {
                    const level = headingMatch[1].length;
                    const content = headingMatch[2];

                    console.log(`发现标题: 级别${level}, 内容"${content}"`);

                    // 处理待处理的段落内容
                    if (pendingParagraphs.length > 0) {
                        // 将段落内容添加到最近的标题节点
                        if (nodeStack.length > 0) {
                            const lastHeading = nodeStack[nodeStack.length - 1].node;
                            if (lastHeading.nodeType === 'heading') {
                                lastHeading.content += '\n\n' + pendingParagraphs.join('\n');
                            }
                        }
                        pendingParagraphs = [];
                    }

                    const newNode: MindMapNode = {
                        id: `node_${nodeIdCounter++}`,
                        content: content,
                        children: [],
                        isExpanded: true,
                        isHeading: true,
                        originalLevel: level,
                        nodeType: 'heading'
                    };

                    // 如果是第一个节点或者是一级标题，设为根节点
                    if (!rootNode || level === 1) {
                        if (!rootNode) {
                            rootNode = newNode;
                            rootNode.nodeType = 'root';
                            nodeStack.push({ node: rootNode, level, nodeType: 'heading' });
                            console.log('设置根节点:', content);
                            continue;
                        } else if (level === 1) {
                            // 如果已经有根节点，新的一级标题作为根节点的子节点
                            newNode.parent = rootNode;
                            rootNode.children.push(newNode);
                            // 清理栈，只保留根节点
                            nodeStack.splice(1);
                            nodeStack.push({ node: newNode, level, nodeType: 'heading' });
                            console.log('添加一级子节点:', content);
                            continue;
                        }
                    }

                    // 找到合适的父节点
                    while (nodeStack.length > 0 && nodeStack[nodeStack.length - 1].level >= level) {
                        nodeStack.pop();
                    }

                    if (nodeStack.length === 0) {
                        // 如果栈为空，说明这是一个新的顶级节点
                        if (!rootNode) {
                            rootNode = newNode;
                            rootNode.nodeType = 'root';
                            nodeStack.push({ node: rootNode, level, nodeType: 'heading' });
                        } else {
                            newNode.parent = rootNode;
                            rootNode.children.push(newNode);
                            nodeStack.push({ node: rootNode, level: 0, nodeType: 'root' });
                            nodeStack.push({ node: newNode, level, nodeType: 'heading' });
                        }
                    } else {
                        const parentNode = nodeStack[nodeStack.length - 1].node;
                        newNode.parent = parentNode;
                        parentNode.children.push(newNode);
                        nodeStack.push({ node: newNode, level, nodeType: 'heading' });
                    }

                    console.log(`添加节点"${content}"到父节点"${newNode.parent?.content || 'root'}"`);
                    continue;
                }

                // 解析列表项 (- * + 或数字列表)
                const listMatch = trimmedLine.match(/^(\s*)([-*+]|\d+\.)\s+(.+)$/);
                if (listMatch) {
                    const indent = listMatch[1].length;
                    const content = listMatch[3];

                    // 跳过插件生成的注释
                    if (content.includes('Mind Map 插件生成')) {
                        console.log('跳过插件注释');
                        continue;
                    }

                    // 列表项的级别应该比最近的标题高一级
                    let level = Math.floor(indent / 2) + 1;

                    // 找到最近的标题节点，列表项应该比它高一级
                    for (let j = nodeStack.length - 1; j >= 0; j--) {
                        const stackItem = nodeStack[j];
                        if (stackItem.nodeType === 'heading') {
                            level = stackItem.level + 1 + Math.floor(indent / 2);
                            break;
                        }
                    }

                    console.log(`发现列表项: 缩进${indent}, 级别${level}, 内容"${content}"`);
                    console.log('列表项处理前栈状态:', nodeStack.map(item => `${item.node.content}(level:${item.level}, nodeType:${item.nodeType})`));

                    const newNode: MindMapNode = {
                        id: `node_${nodeIdCounter++}`,
                        content: content,
                        children: [],
                        isExpanded: true,
                        isHeading: false, // 列表项不是标题
                        nodeType: 'list'
                    };

                    // 确保有根节点
                    if (!rootNode) {
                        rootNode = {
                            id: 'root',
                            content: fileName || '导入的思维导图',
                            children: [],
                            isExpanded: true,
                            nodeType: 'root'
                        };
                        nodeStack.push({ node: rootNode, level: 0, nodeType: 'root' });
                    }

                    // 找到合适的父节点
                    console.log(`寻找级别小于${level}的父节点`);
                    while (nodeStack.length > 0 && nodeStack[nodeStack.length - 1].level >= level) {
                        const removed = nodeStack.pop();
                        console.log(`从栈中移除: ${removed?.node.content}(level:${removed?.level})`);
                    }

                    const parentNode = nodeStack.length > 0 ? nodeStack[nodeStack.length - 1].node : rootNode;
                    newNode.parent = parentNode;
                    parentNode.children.push(newNode);

                    nodeStack.push({ node: newNode, level, nodeType: 'list' });
                    console.log(`添加列表项"${content}"到父节点"${parentNode.content}"`);
                    continue;
                }

                // 解析普通段落文本（正文内容）
                if (trimmedLine.length > 0) {
                    console.log(`发现正文段落: "${trimmedLine}"`);

                    // 将段落内容添加到待处理列表
                    pendingParagraphs.push(trimmedLine);
                    continue;
                }
            }

            // 处理剩余的段落内容
            if (pendingParagraphs.length > 0) {
                if (nodeStack.length > 0) {
                    const lastHeading = nodeStack[nodeStack.length - 1].node;
                    if (lastHeading.nodeType === 'heading' || lastHeading.nodeType === 'root') {
                        lastHeading.content += '\n\n' + pendingParagraphs.join('\n');
                        console.log(`将剩余段落内容添加到节点"${lastHeading.content.split('\n')[0]}"`);
                    }
                } else if (rootNode) {
                    rootNode.content += '\n\n' + pendingParagraphs.join('\n');
                    console.log(`将剩余段落内容添加到根节点`);
                }
            }

            // 如果没有根节点，创建一个默认根节点
            if (!rootNode) {
                rootNode = {
                    id: 'root',
                    content: fileName || '导入的思维导图',
                    children: [],
                    isExpanded: true
                };

                const defaultChild: MindMapNode = {
                    id: 'default_child',
                    content: '从Markdown导入的内容',
                    children: [],
                    parent: rootNode,
                    isExpanded: true
                };
                rootNode.children.push(defaultChild);
            }

            console.log('解析完成，根节点:', rootNode);
            console.log('根节点子节点数量:', rootNode.children.length);

            return rootNode;

        } catch (error) {
            console.error('Error parsing markdown:', error);
            return null;
        }
    }

    // 切换思维导图和Markdown视图 - 增强版本，支持更好的同步
    async toggleMindMapMarkdown() {
        const activeLeaf = this.app.workspace.getActiveViewOfType(ItemView)?.leaf || this.app.workspace.getMostRecentLeaf();
        if (!activeLeaf) return;

        const currentView = activeLeaf.view;

        if (currentView.getViewType() === MIND_MAP_VIEW_TYPE) {
            // 当前是思维导图视图，切换到对应的Markdown文件
            await this.switchToMarkdownView(activeLeaf);
        } else if (currentView.getViewType() === 'markdown') {
            // 当前是Markdown视图，创建或切换到思维导图预览
            await this.createMindMapPreview();
        } else {
            // 其他视图类型，创建新的思维导图
            await this.createNewMindMap();
        }
    }

    // 从思维导图视图切换到Markdown视图
    private async switchToMarkdownView(mindMapLeaf: any) {
        try {
            const mindMapView = mindMapLeaf.view as MindMapView;
            const sourceFilePath = mindMapView.sourceFilePath;

            if (sourceFilePath) {
                // 如果有源文件，打开源文件
                const file = this.app.vault.getAbstractFileByPath(sourceFilePath);
                if (file) {
                    // 在当前leaf中打开Markdown文件
                    await mindMapLeaf.openFile(file);
                    new Notice(`切换到源文件: ${file.basename}`);
                } else {
                    new Notice('源文件不存在');
                }
            } else {
                // 如果没有源文件，提示用户导出
                new Notice('此思维导图没有关联的源文件，请使用"导出为Markdown文档"功能');
            }
        } catch (error) {
            console.error('切换到Markdown视图时出错:', error);
            new Notice('切换失败');
        }
    }

    // 创建思维导图预览（增强版本，支持更好的同步和样式保持）
    private async createMindMapPreview() {
        try {
            const currentFile = this.app.workspace.getActiveFile();

            if (!currentFile || currentFile.extension !== 'md') {
                new Notice('请先打开一个Markdown文件');
                return;
            }

            // 检查是否已经有相同文件的思维导图视图打开
            const existingMindMapLeaves = this.app.workspace.getLeavesOfType(MIND_MAP_VIEW_TYPE);
            const existingLeaf = existingMindMapLeaves.find(leaf => {
                const view = leaf.view as MindMapView;
                return view.sourceFilePath === currentFile.path;
            });

            if (existingLeaf) {
                // 如果已经有相同文件的思维导图视图，激活它并更新内容
                console.log('找到现有的思维导图视图，更新内容');
                
                // 重新解析当前文件，保持原有的节点属性
                const content = await this.app.vault.read(currentFile);
                const mindMapData = this.parseMarkdownToMindMap(content, currentFile.basename);

                if (mindMapData) {
                    // 如果有现有的根节点，尝试保持选中状态和展开状态
                    if (this.rootNode) {
                        this.preserveNodeStates(this.rootNode, mindMapData);
                    }

                    this.rootNode = mindMapData;
                    if (!this.selectedNode) {
                        this.selectedNode = this.rootNode;
                    }

                    // 更新现有视图
                    await existingLeaf.setViewState({
                        type: MIND_MAP_VIEW_TYPE,
                        state: {
                            data: this.rootNode,
                            sourceFile: currentFile.path
                        }
                    });
                }

                this.app.workspace.revealLeaf(existingLeaf);
                new Notice(`已更新 ${currentFile.basename} 的思维导图`);
                return;
            }

            // 读取当前文件内容
            const content = await this.app.vault.read(currentFile);
            const mindMapData = this.parseMarkdownToMindMap(content, currentFile.basename);

            if (mindMapData) {
                // 设置思维导图数据
                this.rootNode = mindMapData;
                this.selectedNode = this.rootNode;

                // 根据用户设置决定是分割视图还是新标签页
                const splitMode = this.settings.defaultSplitMode || 'split';
                const newLeaf = this.app.workspace.getLeaf(splitMode, 'vertical');

                // 在新leaf中打开思维导图视图
                await newLeaf.setViewState({
                    type: MIND_MAP_VIEW_TYPE,
                    state: {
                        data: this.rootNode,
                        sourceFile: currentFile.path
                    }
                });

                this.app.workspace.revealLeaf(newLeaf);

                console.log(`Created mind map preview for: ${currentFile.path}`);
                new Notice(`为 ${currentFile.basename} 创建了思维导图预览`);
            } else {
                console.log('Failed to parse markdown content');
                new Notice('解析Markdown内容失败，请检查文件格式');
            }

        } catch (error) {
            console.error('Error creating mind map preview:', error);
            new Notice('创建思维导图预览失败: ' + error.message);
        }
    }

    // 保持节点状态（展开/折叠、选中状态等）
    private preserveNodeStates(oldRoot: MindMapNode, newRoot: MindMapNode) {
        const oldNodeMap = new Map<string, MindMapNode>();
        this.buildNodeMap(oldRoot, oldNodeMap);

        this.applyNodeStates(newRoot, oldNodeMap);
    }

    // 构建节点映射（基于内容）
    private buildNodeMap(node: MindMapNode, map: Map<string, MindMapNode>) {
        map.set(node.content, node);
        for (const child of node.children) {
            this.buildNodeMap(child, map);
        }
    }

    // 应用节点状态
    private applyNodeStates(node: MindMapNode, oldNodeMap: Map<string, MindMapNode>) {
        const oldNode = oldNodeMap.get(node.content);
        if (oldNode) {
            node.isExpanded = oldNode.isExpanded;
            node.isSelected = oldNode.isSelected;
        }

        for (const child of node.children) {
            this.applyNodeStates(child, oldNodeMap);
        }
    }

    // 关闭思维导图视图
    private async closeMindMapView(leaf: any) {
        try {
            // 简单地关闭当前leaf
            leaf.detach();
            console.log('Closed mind map view');
        } catch (error) {
            console.error('Error closing mind map view:', error);
        }
    }



    onunload() {
        this.mindmap = null;
    }
}

// 思维导图视图类
class MindMapView extends ItemView {
    plugin: MindMapPlugin;
    sourceFilePath: string | null = null;
    fileWatcher: any = null;
    private logger: Logger;

    constructor(leaf: WorkspaceLeaf, plugin: MindMapPlugin) {
        super(leaf);
        this.plugin = plugin;
        this.logger = createLogger('MindMapView');
    }

    getViewType() {
        return MIND_MAP_VIEW_TYPE;
    }

    getDisplayText() {
        return "思维导图";
    }

    getState(): Record<string, unknown> {
        return {
            type: MIND_MAP_VIEW_TYPE,
            data: this.plugin.getRootNode()
        };
    }

    async setState(state: Record<string, unknown>, _result: ViewStateResult) {
        const data = state.data as unknown;
        const sourceFile = state.sourceFile as string;

        // 如果有源文件，优先从源文件重新解析以确保节点属性正确
        if (sourceFile && !this.plugin.isSyncing) {
            console.log('setState - 检测到源文件，强制重新解析:', sourceFile);
            await this.plugin.reloadFromSourceFile(sourceFile);

            // 设置源文件监听
            if (sourceFile !== this.sourceFilePath) {
                console.log('Setting up file watcher because source file changed');
                this.setupFileWatcher(sourceFile);
            } else {
                console.log('Source file unchanged, keeping existing watcher');
            }
        } else if (sourceFile && this.plugin.isSyncing) {
            console.log('setState - 正在同步中，跳过重新解析');
            // 设置源文件监听
            if (sourceFile !== this.sourceFilePath) {
                console.log('Setting up file watcher because source file changed');
                this.setupFileWatcher(sourceFile);
            } else {
                console.log('Source file unchanged, keeping existing watcher');
            }
        } else if (this.isMindMapNode(data)) {
            // 如果没有源文件，使用保存的数据
            console.log('setState - 没有源文件，使用保存的数据');
            await this.plugin.loadData(data);
        }
    }

    // 设置文件监听器
    setupFileWatcher(filePath: string) {
        console.log('Setting up file watcher for:', filePath);

        // 清除之前的监听器
        if (this.fileWatcher) {
            console.log('Clearing previous file watcher');
            this.app.vault.offref(this.fileWatcher);
        }

        this.sourceFilePath = filePath;

        // 监听文件修改
        this.fileWatcher = this.app.vault.on('modify', async (file) => {
            console.log('File modified event:', file.path, 'watching:', this.sourceFilePath);
            if (file.path === this.sourceFilePath) {
                console.log('Source file modified, updating mind map:', file.path);
                await this.updateFromSourceFile(file);
            }
        });

        console.log('Mind map view linked to source file:', filePath);
        console.log('File watcher active:', !!this.fileWatcher);
    }

    // 从源文件更新思维导图
    private async updateFromSourceFile(file: any) {
        try {
            console.log('Reading source file content:', file.path);
            const content = await this.app.vault.read(file);
            console.log('File content length:', content.length);

            const mindMapData = this.plugin.parseMarkdownToMindMap(content, file.basename);
            console.log('Parsed mind map data:', mindMapData);

            if (mindMapData) {
                console.log('Updating mind map view with new data');

                // 使用public方法更新数据
                this.plugin.updateMindMapData(mindMapData);

                // 触发视图重新渲染
                this.refreshView();

                console.log('Mind map view updated from source file successfully');
            } else {
                console.log('Failed to parse mind map data from source file');
            }
        } catch (error) {
            console.error('Error updating mind map from source file:', error);
        }
    }

    private isMindMapNode(data: unknown): data is MindMapNode {
        if (!data || typeof data !== 'object') return false;
        
        const node = data as Partial<MindMapNode>;
        return (
            typeof node.id === 'string' &&
            typeof node.content === 'string' &&
            Array.isArray(node.children) &&
            node.children.every(child => this.isMindMapNode(child))
        );
    }

    async onOpen() {
        const container = this.containerEl.children[1];
        container.empty();
        const mindmapContainer = container.createDiv('mindmap-container');

        // 设置容器样式，确保有明确的尺寸
        mindmapContainer.style.width = '100%';
        mindmapContainer.style.height = '100%';
        mindmapContainer.style.minHeight = '400px';
        mindmapContainer.style.position = 'relative';
        mindmapContainer.style.overflow = 'hidden';

        console.log('MindMap view opened, container created');

        // 等待容器渲染完成
        setTimeout(async () => {
            // 从状态中加载数据
            const state = this.getState();
            if (state.data && this.isMindMapNode(state.data)) {
                await this.plugin.loadData(state.data);
            } else if (this.plugin.getRootNode()) {
                // 如果插件已有根节点，渲染它
                this.plugin.renderMindMap().catch(console.error);
            } else {
                // 创建默认的思维导图
                await this.plugin.createNewMindMap();
            }
        }, 100);
    }

    // 刷新视图
    async refreshView() {
        console.log('Refreshing mind map view');
        const container = this.containerEl.querySelector('.mindmap-container') as HTMLElement;
        if (container && this.plugin.getRootNode()) {
            console.log('Container found, triggering render');
            // 清空容器并重新渲染
            container.innerHTML = '';
            await this.plugin.renderMindMap();
        } else {
            console.log('Container or root node not found for refresh');
        }
    }

    async onClose() {
        console.log('===== 关闭思维导图视图 =====');
        
        // 清理文件监听器
        if (this.fileWatcher) {
            console.log('清理文件监听器');
            this.app.vault.offref(this.fileWatcher);
            this.fileWatcher = null;
        }
        
        // 清理事件监听器
        const container = this.containerEl.querySelector('.mindmap-container');
        if (container && (container as any)._mindmapHandlers) {
            console.log('清理事件监听器');
            const handlers = (container as any)._mindmapHandlers;
            const svg = container.querySelector('svg');
            if (svg) {
                svg.removeEventListener('click', handlers.clickHandler, true);
                svg.removeEventListener('dblclick', handlers.dblClickHandler, true);
            }
            container.removeEventListener('click', handlers.clickHandler, true);
            container.removeEventListener('dblclick', handlers.dblClickHandler, true);
            document.removeEventListener('dblclick', handlers.documentHandler, true);
        }
        
        // 清理MutationObserver
        if (container && (container as any)._mindmapObserver) {
            console.log('清理MutationObserver');
            (container as any)._mindmapObserver.disconnect();
            (container as any)._mindmapObserver = null;
        }
        
        // 清理编辑状态
        if (this.editingNode) {
            console.log('清理编辑状态');
            const input = document.querySelector('.mindmap-node-input');
            if (input) {
                this.safelyRemoveElement(input);
            }
            this.editingNode = null;
        }
        
        this.sourceFilePath = null;
        console.log('===== 思维导图视图已关闭 =====');
    }
}
