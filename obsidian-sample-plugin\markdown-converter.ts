import { MindMapNode, generateUniqueId } from './models';

/**
 * Markdown 转换器类
 * 负责在 Markdown 和思维导图数据结构之间进行转换
 */
export class MarkdownConverter {
    
    /**
     * 解析 Markdown 内容为思维导图结构
     * @param content Markdown 内容
     * @param fileName 文件名（用于设置根节点标题）
     * @returns 思维导图根节点
     */
    parseMarkdownToMindMap(content: string, fileName: string): MindMapNode {
        // 创建根节点
        const rootNode: MindMapNode = {
            id: generateUniqueId(),
            content: fileName.replace(/\.md$/, '') || '思维导图',
            children: [],
            isExpanded: true,
            nodeType: 'root'
        };
        
        // 按行分割内容
        const lines = content.split('\n');
        
        // 当前处理的节点栈
        const nodeStack: { node: MindMapNode, level: number }[] = [{ node: rootNode, level: 0 }];
        
        // 处理每一行
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // 跳过空行
            if (!line) continue;
            
            // 处理标题行
            const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
            if (headingMatch) {
                const level = headingMatch[1].length;
                const content = headingMatch[2].trim();
                
                // 创建新节点
                const newNode: MindMapNode = {
                    id: generateUniqueId(),
                    content: content,
                    children: [],
                    isExpanded: true,
                    isHeading: true,
                    originalLevel: level,
                    nodeType: 'heading'
                };
                
                // 找到合适的父节点
                while (nodeStack.length > 1 && nodeStack[nodeStack.length - 1].level >= level) {
                    nodeStack.pop();
                }
                
                // 添加到父节点
                const parent = nodeStack[nodeStack.length - 1].node;
                parent.children.push(newNode);
                newNode.parent = parent;
                
                // 将新节点入栈
                nodeStack.push({ node: newNode, level: level });
                continue;
            }
            
            // 处理列表项
            const listMatch = line.match(/^(\s*)[-*+]\s+(.+)$/);
            if (listMatch) {
                const indentation = listMatch[1].length;
                const content = listMatch[2].trim();
                const level = Math.floor(indentation / 2) + 1; // 每两个空格算一级缩进
                
                // 创建新节点
                const newNode: MindMapNode = {
                    id: generateUniqueId(),
                    content: content,
                    children: [],
                    isExpanded: true,
                    nodeType: 'list'
                };
                
                // 找到合适的父节点
                while (nodeStack.length > 1 && nodeStack[nodeStack.length - 1].level >= level) {
                    nodeStack.pop();
                }
                
                // 添加到父节点
                const parent = nodeStack[nodeStack.length - 1].node;
                parent.children.push(newNode);
                newNode.parent = parent;
                
                // 将新节点入栈
                nodeStack.push({ node: newNode, level: level });
                continue;
            }
            
            // 处理普通段落
            if (line) {
                // 创建新节点
                const newNode: MindMapNode = {
                    id: generateUniqueId(),
                    content: line,
                    children: [],
                    isExpanded: true,
                    nodeType: 'paragraph'
                };
                
                // 添加到当前节点
                const parent = nodeStack[nodeStack.length - 1].node;
                parent.children.push(newNode);
                newNode.parent = parent;
            }
        }
        
        return rootNode;
    }
    
    /**
     * 将思维导图转换为 Markdown 内容
     * @param rootNode 思维导图根节点
     * @returns Markdown 内容
     */
    generateMarkdownFromMindMap(rootNode: MindMapNode): string {
        let markdown = '';
        
        // 添加标题
        markdown += `# ${rootNode.content}\n\n`;
        
        // 处理子节点
        for (const child of rootNode.children) {
            markdown += this.nodeToMarkdownContent(child, 1);
        }
        
        return markdown;
    }
    
    /**
     * 将节点转换为 Markdown 内容
     * @param node 节点
     * @param level 当前级别
     * @returns Markdown 内容
     */
    private nodeToMarkdownContent(node: MindMapNode, level: number): string {
        let markdown = '';
        
        // 根据节点类型生成不同的 Markdown 内容
        if (node.nodeType === 'heading' || node.isHeading) {
            // 使用原始标题级别或当前级别
            const headingLevel = node.originalLevel || Math.min(level, 6);
            const headingMarks = '#'.repeat(headingLevel);
            markdown += `${headingMarks} ${node.content}\n\n`;
        } else if (node.nodeType === 'list') {
            // 列表项缩进
            const indent = '  '.repeat(level - 1);
            markdown += `${indent}- ${node.content}\n`;
        } else {
            // 普通段落
            markdown += `${node.content}\n\n`;
        }
        
        // 处理子节点
        for (const child of node.children) {
            markdown += this.nodeToMarkdownContent(child, level + 1);
        }
        
        return markdown;
    }
    
    /**
     * 生成干净的 Markdown 内容（用于双向同步）
     * @param rootNode 思维导图根节点
     * @returns Markdown 内容
     */
    generateCleanMarkdownFromMindMap(rootNode: MindMapNode): string {
        let markdown = '';
        
        // 处理所有节点
        for (const child of rootNode.children) {
            markdown += this.nodeToCleanMarkdown(child, 1);
        }
        
        return markdown;
    }
    
    /**
     * 将节点转换为干净的 Markdown 格式
     * @param node 节点
     * @param level 当前级别
     * @returns Markdown 内容
     */
    private nodeToCleanMarkdown(node: MindMapNode, level: number): string {
        let markdown = '';
        
        // 根据节点类型生成不同的 Markdown 内容
        if (node.nodeType === 'heading' || node.isHeading) {
            // 使用原始标题级别或当前级别
            const headingLevel = node.originalLevel || Math.min(level, 6);
            const headingMarks = '#'.repeat(headingLevel);
            markdown += `${headingMarks} ${node.content}\n\n`;
        } else if (node.nodeType === 'list') {
            // 列表项缩进
            const indent = '  '.repeat(level - 1);
            markdown += `${indent}- ${node.content}\n`;
        } else {
            // 普通段落
            markdown += `${node.content}\n\n`;
        }
        
        // 处理子节点
        for (const child of node.children) {
            markdown += this.nodeToCleanMarkdown(child, level + 1);
        }
        
        return markdown;
    }
}