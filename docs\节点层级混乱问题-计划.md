# 节点层级混乱问题 - 修复计划

## 项目概述
修复思维导图节点层级混乱问题：虽然有连接线，但节点布局不合理，缺乏清晰的层级结构。根本原因是Vis.js层次布局配置不当。

## 执行步骤

### 1.1 优化Vis.js层次布局配置
**文件路径**: `ob-sample/src/core/VisJsRenderer.ts`
**修改范围**: 第52-61行的layout配置
**预期结果**: 清晰的从左到右层级布局

**具体操作**:
- 修改`sortMethod`为`'defined'`
- 调整层级间距和节点间距
- 启用布局优化选项
- 添加父节点居中配置

**修改内容**:
```typescript
layout: {
  hierarchical: {
    enabled: true,
    direction: 'LR',
    sortMethod: 'defined',          // 关键改动
    shakeTowards: 'roots',
    levelSeparation: 150,           // 优化间距
    nodeSpacing: 100,               // 优化间距
    treeSpacing: 200,
    blockShifting: true,            // 启用优化
    edgeMinimization: true,         // 启用优化
    parentCentralization: true      // 启用优化
  }
}
```

**验证标准**:
- 节点按层级从左到右排列
- 同级节点垂直对齐
- 布局美观合理

### 1.2 修正节点level属性传递
**文件路径**: `ob-sample/src/core/VisJsRenderer.ts`
**修改范围**: 第277-284行的节点创建逻辑
**预期结果**: Vis.js正确识别节点层级

**具体操作**:
- 修正level值计算（Vis.js从0开始）
- 确保根节点level为0
- 添加节点位置提示

**修改内容**:
```typescript
const visNode: VisNode = {
  id: node.id,
  label: this.formatNodeLabel(node),
  level: node.level - 1,            // 修正level值
  type: node.type,
  group: this.getNodeGroup(node),
  originalData: node
};
```

**验证标准**:
- 根节点level为0
- 子节点level递增
- 层级关系正确

### 1.3 增强节点样式分层
**文件路径**: `ob-sample/src/core/VisJsRenderer.ts`
**修改范围**: 第100-130行的groups配置
**预期结果**: 不同层级节点有明显的视觉区分

**具体操作**:
- 为不同层级设置不同的节点形状
- 调整节点大小和颜色
- 优化字体大小和边距

**修改内容**:
```typescript
groups: {
  heading1: {
    color: { background: '#e3f2fd', border: '#1976d2' },
    font: { size: 18, face: 'var(--font-text)', color: '#1976d2' },
    shape: 'box',
    margin: { top: 15, right: 15, bottom: 15, left: 15 }
  },
  heading2: {
    color: { background: '#f3e5f5', border: '#7b1fa2' },
    font: { size: 16, face: 'var(--font-text)', color: '#7b1fa2' },
    shape: 'box'
  },
  heading3: {
    color: { background: '#e8f5e8', border: '#388e3c' },
    font: { size: 14, face: 'var(--font-text)', color: '#388e3c' },
    shape: 'box'
  },
  list: {
    color: { background: 'var(--background-secondary)', border: 'var(--text-muted)' },
    font: { size: 12, face: 'var(--font-text)', color: 'var(--text-normal)' },
    shape: 'ellipse'
  }
}
```

**验证标准**:
- 标题节点使用方形
- 列表节点使用椭圆形
- 颜色层次分明
- 字体大小递减

### 1.4 优化连接线样式
**文件路径**: `ob-sample/src/core/VisJsRenderer.ts`
**修改范围**: 第87-99行的edges配置
**预期结果**: 更清晰美观的连接线

**具体操作**:
- 调整连接线透明度
- 优化曲线参数
- 设置理想边长度

**修改内容**:
```typescript
edges: {
  arrows: {
    to: { enabled: false }
  },
  color: {
    color: 'var(--text-muted)',
    opacity: 0.8                     // 提高透明度
  },
  width: 2,
  smooth: {
    enabled: true,
    type: 'cubicBezier',
    roundness: 0.4                   // 调整曲线
  },
  length: 150                        // 设置理想长度
}
```

**验证标准**:
- 连接线清晰可见
- 曲线自然美观
- 不遮挡节点内容

### 1.5 添加布局调试信息
**文件路径**: `ob-sample/src/core/VisJsRenderer.ts`
**修改范围**: 第200-210行的render方法
**预期结果**: 详细的布局调试信息

**具体操作**:
- 添加节点层级分布统计
- 记录根节点信息
- 输出布局配置详情

**修改内容**:
```typescript
// 在render方法中添加
this.logger.debug('节点层级分布:', this.analyzeNodeLevels(visNodes));
this.logger.debug('根节点数量:', visNodes.filter(n => n.level === 0).length);
this.logger.debug('最大层级:', Math.max(...visNodes.map(n => n.level)));
```

**验证标准**:
- 控制台显示层级统计
- 可以清楚看到布局信息
- 便于问题排查

### 1.6 项目编译和测试
**文件路径**: 整个项目
**修改范围**: 编译和运行时测试
**预期结果**: 布局问题完全解决

**具体操作**:
- 编译项目检查错误
- 重新加载插件测试
- 验证各种Markdown格式
- 检查交互功能正常

**测试用例**:
```markdown
# 测试文档

## ✨ 核心特性
• 🧠 智能解析: 自动解析 Markdown 标题结构生成思维导图
• 📱 双向同步: Markdown 文件与思维导图实时同步
• ✏️ 交互编辑: 支持节点编辑、添加、删除等操作

## 🚀 快速开始
### 安装
1. 相对文件夹复制到 .obsidian/plugins/ 目录
2. 在 Obsidian 设置中启用插件
3. 重新加载 Obsidian

### 使用
• 打开任意 Markdown 文档
• 点击思维导图图标
```

**验证标准**:
- "测试文档"在最左侧作为根节点
- "核心特性"和"快速开始"在第二层
- 列表项和"安装"、"使用"在第三层
- 数字列表在第四层
- 所有节点按层级整齐排列

## 时间安排

| 步骤 | 预计时间 | 累计时间 |
|------|----------|----------|
| 1.1 优化Vis.js层次布局配置 | 8分钟 | 8分钟 |
| 1.2 修正节点level属性传递 | 4分钟 | 12分钟 |
| 1.3 增强节点样式分层 | 5分钟 | 17分钟 |
| 1.4 优化连接线样式 | 2分钟 | 19分钟 |
| 1.5 添加布局调试信息 | 3分钟 | 22分钟 |
| 1.6 项目编译和测试 | 8分钟 | 30分钟 |

**总计**: 30分钟

## 风险控制

### 技术风险
- **配置风险**: Vis.js布局参数可能需要多次调试
- **兼容风险**: 确保现有功能不受影响
- **性能风险**: 布局优化不能影响渲染性能

### 质量保证
- **渐进修改**: 每次只修改一个配置，立即测试
- **回退方案**: 保留原始配置作为备份
- **全面测试**: 测试各种Markdown格式和文档大小

## 成功标准

### 布局标准
- ✅ 清晰的从左到右层级结构
- ✅ 根节点在最左侧
- ✅ 子节点按层级递进排列
- ✅ 同级节点垂直对齐
- ✅ 节点间距合理美观

### 视觉标准
- ✅ 不同层级节点有明显区分
- ✅ 连接线清晰美观
- ✅ 整体布局协调统一
- ✅ 适配Obsidian主题

### 功能标准
- ✅ 所有交互功能正常
- ✅ 性能不降低
- ✅ 兼容各种Markdown格式
- ✅ 调试信息完善

## 预期效果

修复后的思维导图应该显示为：
```
测试文档
├── 核心特性
│   ├── 智能解析: 自动解析...
│   ├── 双向同步: Markdown文件...
│   └── 交互编辑: 支持节点编辑...
└── 快速开始
    ├── 安装
    │   ├── 1. 相对文件夹复制到...
    │   ├── 2. 在Obsidian设置中...
    │   └── 3. 重新加载Obsidian
    └── 使用
        ├── 打开任意Markdown文档
        └── 点击思维导图图标
```

## 后续优化

完成基础布局修复后，可以考虑：
1. 动态调整节点大小
2. 添加节点折叠/展开功能
3. 支持自定义布局参数
4. 优化大文档的布局性能

该计划确保在30分钟内完全解决节点层级混乱问题，提供清晰美观的思维导图布局。
