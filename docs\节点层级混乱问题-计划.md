# 节点层级混乱问题 - 修复计划（修订版）

## 项目概述
重构Markdown解析器，精准识别文档的层级关系（包括文字内容+文字样式），然后准确转换成思维导图。根本原因是现有解析器对Markdown结构和样式信息的识别不够精准。

## 执行步骤

### 1.1 创建增强的数据结构接口
**文件路径**: `ob-sample/src/core/VisJsRenderer.ts`
**修改范围**: 接口定义部分（第1-30行）
**预期结果**: 支持精准层级和样式信息的数据结构

**具体操作**:
- 创建EnhancedNodeData接口
- 添加StyleInfo接口
- 扩展LineStructure接口
- 定义层级分析结果类型

**修改内容**:
```typescript
interface EnhancedNodeData {
  id: string;
  content: string;
  rawContent: string;        // 原始内容（含样式标记）
  level: number;             // 精确层级
  type: 'heading' | 'list' | 'code' | 'quote' | 'text';
  headingLevel?: number;     // H1-H6层级
  listLevel?: number;        // 列表嵌套层级
  indentLevel?: number;      // 缩进层级
  styles: StyleInfo;         // 样式信息
  children: EnhancedNodeData[];
  parent?: EnhancedNodeData;
  position: { line: number; ch: number };
}

interface StyleInfo {
  bold: boolean;
  italic: boolean;
  code: boolean;
  link?: string;
  emoji: string[];
  strikethrough: boolean;
  highlight: boolean;
}

interface LineStructure {
  level: number;
  type: 'heading' | 'list' | 'code' | 'quote' | 'text';
  headingLevel: number;
  listLevel: number;
  indentLevel: number;
  content: string;
  rawContent: string;
  styles: StyleInfo;
}
```

**验证标准**:
- 接口定义完整准确
- 支持所有需要的样式信息
- 层级信息精确

### 1.2 重构行结构分析方法
**文件路径**: `ob-sample/src/core/VisJsRenderer.ts`
**修改范围**: parseMarkdownToNodes方法内部
**预期结果**: 精准识别每行的结构和层级

**具体操作**:
- 创建analyzeLineStructure方法
- 精确识别标题层级（H1-H6）
- 准确计算列表嵌套层级
- 识别代码块、引用等特殊结构
- 提取样式信息

**修改内容**:
```typescript
private analyzeLineStructure(line: string, lineIndex: number): LineStructure {
  const structure: LineStructure = {
    level: 1,
    type: 'text',
    headingLevel: 0,
    listLevel: 0,
    indentLevel: 0,
    content: line.trim(),
    rawContent: line,
    styles: this.extractStyles(line)
  };

  // 标题识别 (H1-H6)
  const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
  if (headingMatch) {
    structure.type = 'heading';
    structure.headingLevel = headingMatch[1].length;
    structure.level = headingMatch[1].length;
    structure.content = headingMatch[2];
    return structure;
  }

  // 列表识别 (支持多种bullet符号和精确嵌套)
  const listMatch = line.match(/^(\s*)([•\-*+]|\d+\.)\s+(.+)$/);
  if (listMatch) {
    structure.type = 'list';
    structure.indentLevel = listMatch[1].length;
    structure.listLevel = Math.floor(listMatch[1].length / 2) + 1;
    structure.level = structure.listLevel + this.getParentHeadingLevel();
    structure.content = listMatch[3];
    return structure;
  }

  return structure;
}
```

**验证标准**:
- 标题层级识别准确（H1=1, H2=2...）
- 列表嵌套层级计算正确
- 样式信息完整提取

### 1.3 创建样式信息提取方法
**文件路径**: `ob-sample/src/core/VisJsRenderer.ts`
**修改范围**: 新增方法
**预期结果**: 完整提取和保留Markdown样式信息

**具体操作**:
- 创建extractStyles方法
- 识别粗体、斜体、代码等样式
- 提取emoji表情符号
- 识别链接、删除线、高亮等
- 保留原始样式标记

**修改内容**:
```typescript
private extractStyles(content: string): StyleInfo {
  const styles: StyleInfo = {
    bold: false,
    italic: false,
    code: false,
    link: null,
    emoji: [],
    strikethrough: false,
    highlight: false
  };

  // 提取emoji
  const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
  const emojiMatches = content.match(emojiRegex);
  if (emojiMatches) {
    styles.emoji = emojiMatches;
  }

  // 检测粗体 **text** 或 __text__
  styles.bold = /\*\*.*?\*\*|__.*?__/.test(content);

  // 检测斜体 *text* 或 _text_
  styles.italic = /\*.*?\*|_.*?_/.test(content);

  // 检测代码 `code`
  styles.code = /`.*?`/.test(content);

  // 检测链接 [text](url)
  const linkMatch = content.match(/\[([^\]]+)\]\(([^)]+)\)/);
  if (linkMatch) {
    styles.link = linkMatch[2];
  }

  return styles;
}
```

**验证标准**:
- 正确识别所有样式标记
- emoji提取完整
- 链接信息保留
- 样式信息结构化存储

### 1.4 重构层级关系构建逻辑
**文件路径**: `ob-sample/src/core/VisJsRenderer.ts`
**修改范围**: parseMarkdownToNodes方法核心逻辑
**预期结果**: 精准的父子关系构建

**具体操作**:
- 重写层级关系判断逻辑
- 使用栈结构管理层级上下文
- 精确计算相对层级关系
- 处理复杂嵌套情况

**修改内容**:
```typescript
private buildHierarchicalStructure(lines: string[]): EnhancedNodeData[] {
  const rootNodes: EnhancedNodeData[] = [];
  const nodeStack: EnhancedNodeData[] = [];

  lines.forEach((line, index) => {
    if (!line.trim()) return;

    const structure = this.analyzeLineStructure(line, index);
    const node = this.createNodeFromStructure(structure, index);

    // 找到正确的父节点
    const parentNode = this.findParentNode(node, nodeStack);

    if (parentNode) {
      parentNode.children.push(node);
      node.parent = parentNode;
    } else {
      rootNodes.push(node);
    }

    // 更新节点栈
    this.updateNodeStack(node, nodeStack);
  });

  return rootNodes;
}

private findParentNode(currentNode: EnhancedNodeData, nodeStack: EnhancedNodeData[]): EnhancedNodeData | null {
  // 从栈顶向下查找合适的父节点
  for (let i = nodeStack.length - 1; i >= 0; i--) {
    const candidate = nodeStack[i];
    if (candidate.level < currentNode.level) {
      return candidate;
    }
  }
  return null;
}
```

**验证标准**:
- 父子关系准确无误
- 支持复杂嵌套结构
- 层级计算精确
- 处理边界情况正确

### 1.5 增强节点标签格式化
**文件路径**: `ob-sample/src/core/VisJsRenderer.ts`
**修改范围**: formatNodeLabel方法
**预期结果**: 保留样式信息的节点显示

**具体操作**:
- 重写formatNodeLabel方法
- 保留emoji和重要样式
- 根据样式信息调整显示格式
- 优化长文本处理

**修改内容**:
```typescript
private formatNodeLabel(node: EnhancedNodeData): string {
  let label = node.content;

  // 保留emoji
  if (node.styles.emoji.length > 0) {
    // emoji已经在content中，保持原样
  }

  // 处理粗体（在Vis.js中用HTML标签）
  if (node.styles.bold) {
    label = `<b>${label}</b>`;
  }

  // 处理斜体
  if (node.styles.italic) {
    label = `<i>${label}</i>`;
  }

  // 处理代码
  if (node.styles.code) {
    label = `<code>${label}</code>`;
  }

  // 长文本截断
  if (label.length > 50) {
    label = label.substring(0, 47) + '...';
  }

  return label;
}
```

**验证标准**:
- emoji正确显示
- 样式信息保留
- 长文本合理截断
- HTML标签正确应用

### 1.6 优化Vis.js布局配置适配新数据结构
**文件路径**: `ob-sample/src/core/VisJsRenderer.ts`
**修改范围**: initializeNetwork方法的布局配置
**预期结果**: 布局算法与精准数据结构完美配合

**具体操作**:
- 调整层次布局参数
- 优化节点间距和层级间距
- 配置适合的排序方法
- 启用布局优化选项

**修改内容**:
```typescript
layout: {
  hierarchical: {
    enabled: true,
    direction: 'LR',
    sortMethod: 'defined',          // 使用预定义顺序
    shakeTowards: 'roots',
    levelSeparation: 150,           // 层级间距
    nodeSpacing: 100,               // 节点间距
    treeSpacing: 200,               // 树间距
    blockShifting: true,            // 启用块移动优化
    edgeMinimization: true,         // 启用边最小化
    parentCentralization: true      // 父节点居中
  }
},
physics: {
  enabled: false                    // 禁用物理引擎
}
```

**验证标准**:
- 层级结构清晰
- 节点排列整齐
- 视觉效果美观
- 性能表现良好

### 1.7 项目编译和全面测试
**文件路径**: 整个项目
**修改范围**: 编译和运行时测试
**预期结果**: 精准的层级识别和美观的思维导图显示

**具体操作**:
- 编译项目检查错误
- 重新加载插件测试
- 验证复杂Markdown格式
- 测试样式信息保留
- 检查层级关系准确性

**测试用例**:
```markdown
# 📚 **测试文档**

## ✨ 核心特性
• 🧠 **智能解析**: 自动解析 *Markdown* 标题结构生成思维导图
• 📱 双向同步: `Markdown` 文件与思维导图实时同步
  • 子功能1: 实时更新
  • 子功能2: 双向编辑
• ✏️ 交互编辑: 支持节点编辑、添加、删除等操作

## 🚀 快速开始
### 安装步骤
1. 相对文件夹复制到 `.obsidian/plugins/` 目录
2. 在 Obsidian 设置中启用插件
3. 重新加载 Obsidian

### 使用方法
• 打开任意 Markdown 文档
• 点击思维导图图标
  • 查看思维导图
  • 编辑节点内容

## 📝 注意事项
> 这是一个引用块
> 包含重要信息

```code
这是代码块
包含示例代码
```
```

**验证标准**:
- 标题层级准确识别（H1、H2、H3）
- 列表嵌套层级正确
- emoji、粗体、斜体、代码样式保留
- 引用块和代码块正确处理
- 层级关系清晰准确
- 思维导图布局美观

## 时间安排

| 步骤 | 预计时间 | 累计时间 |
|------|----------|----------|
| 1.1 创建增强的数据结构接口 | 15分钟 | 15分钟 |
| 1.2 重构行结构分析方法 | 20分钟 | 35分钟 |
| 1.3 创建样式信息提取方法 | 15分钟 | 50分钟 |
| 1.4 重构层级关系构建逻辑 | 25分钟 | 75分钟 |
| 1.5 增强节点标签格式化 | 10分钟 | 85分钟 |
| 1.6 优化Vis.js布局配置 | 10分钟 | 95分钟 |
| 1.7 项目编译和全面测试 | 25分钟 | 120分钟 |

**总计**: 120分钟（2小时）

## 风险控制

### 技术风险
- **配置风险**: Vis.js布局参数可能需要多次调试
- **兼容风险**: 确保现有功能不受影响
- **性能风险**: 布局优化不能影响渲染性能

### 质量保证
- **渐进修改**: 每次只修改一个配置，立即测试
- **回退方案**: 保留原始配置作为备份
- **全面测试**: 测试各种Markdown格式和文档大小

## 成功标准

### 布局标准
- ✅ 清晰的从左到右层级结构
- ✅ 根节点在最左侧
- ✅ 子节点按层级递进排列
- ✅ 同级节点垂直对齐
- ✅ 节点间距合理美观

### 视觉标准
- ✅ 不同层级节点有明显区分
- ✅ 连接线清晰美观
- ✅ 整体布局协调统一
- ✅ 适配Obsidian主题

### 功能标准
- ✅ 所有交互功能正常
- ✅ 性能不降低
- ✅ 兼容各种Markdown格式
- ✅ 调试信息完善

## 预期效果

重构后的思维导图应该精准显示为：
```
📚 测试文档 (H1, 粗体样式)
├── ✨ 核心特性 (H2)
│   ├── 🧠 智能解析: 自动解析 Markdown 标题结构... (列表项, 粗体+斜体样式)
│   ├── 📱 双向同步: Markdown 文件与思维导图... (列表项, 代码样式)
│   │   ├── 子功能1: 实时更新 (嵌套列表项)
│   │   └── 子功能2: 双向编辑 (嵌套列表项)
│   └── ✏️ 交互编辑: 支持节点编辑、添加... (列表项)
├── 🚀 快速开始 (H2)
│   ├── 安装步骤 (H3)
│   │   ├── 1. 相对文件夹复制到 .obsidian/plugins/ 目录 (数字列表, 代码样式)
│   │   ├── 2. 在Obsidian设置中启用插件 (数字列表)
│   │   └── 3. 重新加载Obsidian (数字列表)
│   └── 使用方法 (H3)
│       ├── 打开任意Markdown文档 (列表项)
│       └── 点击思维导图图标 (列表项)
│           ├── 查看思维导图 (嵌套列表项)
│           └── 编辑节点内容 (嵌套列表项)
├── 📝 注意事项 (H2)
│   ├── 这是一个引用块 (引用类型)
│   └── 包含重要信息 (引用类型)
└── 代码块示例 (代码块类型)
```

**关键改进**:
- ✅ 精准的层级识别（H1-H6, 列表嵌套）
- ✅ 完整的样式信息保留（emoji, 粗体, 斜体, 代码）
- ✅ 准确的父子关系构建
- ✅ 清晰的视觉层次区分
- ✅ 支持复杂Markdown结构

## 后续扩展计划

完成核心解析器重构后，可以考虑：
1. **高级样式支持**: 链接、图片、表格等
2. **交互功能增强**: 样式编辑、节点类型转换
3. **性能优化**: 大文档的增量解析
4. **扩展语法支持**: 数学公式、图表等
5. **自定义样式**: 用户自定义节点样式和布局

该计划确保在2小时内完全重构Markdown解析器，实现精准的层级识别和样式保留，从根本上解决思维导图显示问题。
