# 双击编辑功能测试

本文档描述了测试思维导图插件中双击编辑功能的步骤和预期结果。

## 测试环境准备

1. 确保Obsidian已安装并运行
2. 确保思维导图插件已安装并启用
3. 创建一个测试Markdown文件，包含以下内容：

```markdown
# 测试思维导图

## 一级标题1
- 列表项1
- 列表项2

## 一级标题2
- 列表项3
- 列表项4
```

## 测试用例1：双击编辑标题节点

### 步骤
1. 打开测试Markdown文件
2. 使用命令面板执行"从Markdown文档导入"命令，创建思维导图视图
3. 双击"一级标题1"节点

### 预期结果
- 应该出现一个输入框，其中包含"一级标题1"文本
- 输入框应该位于节点位置
- 输入框应该获得焦点

### 验证点
- 输入框是否正确显示
- 输入框位置是否正确
- 输入框是否获得焦点

## 测试用例2：编辑并保存节点内容

### 步骤
1. 按照测试用例1的步骤，双击"一级标题1"节点
2. 将文本修改为"修改后的标题1"
3. 按回车键保存修改

### 预期结果
- 输入框应该消失
- 节点文本应该更新为"修改后的标题1"
- 思维导图应该重新渲染
- 源Markdown文件应该更新，"一级标题1"应该变为"修改后的标题1"

### 验证点
- 节点文本是否正确更新
- 思维导图是否重新渲染
- 源Markdown文件是否正确更新

## 测试用例3：双击编辑列表节点

### 步骤
1. 打开测试Markdown文件的思维导图视图
2. 双击"列表项1"节点

### 预期结果
- 应该出现一个输入框，其中包含"列表项1"文本
- 输入框应该位于节点位置
- 输入框应该获得焦点

### 验证点
- 输入框是否正确显示
- 输入框位置是否正确
- 输入框是否获得焦点

## 测试用例4：取消编辑

### 步骤
1. 按照测试用例1的步骤，双击"一级标题1"节点
2. 将文本修改为"不应保存的文本"
3. 按Esc键取消修改

### 预期结果
- 输入框应该消失
- 节点文本应该保持不变
- 思维导图应该保持不变
- 源Markdown文件应该保持不变

### 验证点
- 节点文本是否保持不变
- 思维导图是否保持不变
- 源Markdown文件是否保持不变

## 测试用例5：点击外部区域完成编辑

### 步骤
1. 按照测试用例1的步骤，双击"一级标题1"节点
2. 将文本修改为"通过点击外部完成的修改"
3. 点击思维导图的空白区域

### 预期结果
- 输入框应该消失
- 节点文本应该更新为"通过点击外部完成的修改"
- 思维导图应该重新渲染
- 源Markdown文件应该更新

### 验证点
- 节点文本是否正确更新
- 思维导图是否重新渲染
- 源Markdown文件是否正确更新

## 测试用例6：编辑包含换行符的内容

### 步骤
1. 创建一个包含多行内容的节点（可以通过编辑源Markdown文件实现）
2. 打开思维导图视图
3. 双击包含多行内容的节点

### 预期结果
- 应该出现一个文本区域（textarea），而不是单行输入框
- 文本区域应该包含所有行
- 文本区域应该获得焦点

### 验证点
- 文本区域是否正确显示
- 文本区域是否包含所有行
- 文本区域是否获得焦点

## 测试用例7：在不同缩放级别下编辑

### 步骤
1. 打开思维导图视图
2. 使用浏览器缩放功能或Obsidian的缩放功能，将页面缩放到150%
3. 双击任意节点

### 预期结果
- 输入框应该出现在正确的位置
- 输入框大小应该适应缩放级别
- 编辑功能应该正常工作

### 验证点
- 输入框位置是否正确
- 输入框大小是否适应缩放级别
- 编辑功能是否正常工作

## 测试用例8：快速连续编辑多个节点

### 步骤
1. 打开思维导图视图
2. 双击"一级标题1"节点，修改内容并按回车保存
3. 立即双击"一级标题2"节点，修改内容并按回车保存
4. 立即双击"列表项1"节点，修改内容并按回车保存

### 预期结果
- 每个节点的编辑操作应该独立进行
- 每个节点的内容应该正确更新
- 思维导图应该在每次编辑后重新渲染
- 源Markdown文件应该正确更新所有修改

### 验证点
- 每个节点的内容是否正确更新
- 思维导图是否在每次编辑后重新渲染
- 源Markdown文件是否正确更新所有修改