# 思维导图显示问题 - 修复计划

## 项目概述
修复思维导图显示问题：节点没有连接线，布局混乱。根本原因是正则表达式不支持用户使用的 `•` bullet符号。

## 执行步骤

### 1.1 修复正则表达式支持更多bullet符号
**文件路径**: `ob-sample/src/core/VisJsRenderer.ts`
**修改范围**: 第440-464行的解析方法
**预期结果**: 支持 `•` 符号的列表项解析

**具体操作**:
- 修改 `getLineLevel()` 方法的正则表达式
- 修改 `cleanLineContent()` 方法的正则表达式  
- 修改 `getNodeType()` 方法的正则表达式
- 确保所有方法都支持 `•` 符号

**修改内容**:
```typescript
// 第444行: 扩展列表匹配
const listMatch = line.match(/^(\s*)([•\-*+]|\d+\.)\s/);

// 第453行: 扩展内容清理
.replace(/^\s*[•\-*+]\s+/, '')

// 第460-461行: 扩展类型检测
if (line.match(/^\s*[•\-*+]\s/)) return 'list';
```

**验证标准**:
- 正则表达式正确匹配 `•` 符号
- 层级计算准确
- 内容清理正确

### 1.2 增强调试日志输出
**文件路径**: `ob-sample/src/core/VisJsRenderer.ts`
**修改范围**: 第188-219行的render方法
**预期结果**: 提供详细的解析和渲染调试信息

**具体操作**:
- 在render方法中添加输入数据日志
- 记录解析得到的节点数量和结构
- 记录生成的边数量和详情
- 添加关键步骤的状态日志

**验证标准**:
- 控制台显示详细的调试信息
- 可以清楚看到解析过程
- 便于问题排查

### 1.3 完善边生成逻辑
**文件路径**: `ob-sample/src/core/VisJsRenderer.ts`
**修改范围**: 第261-294行的convertToVisFormat方法
**预期结果**: 确保边正确生成并包含必要属性

**具体操作**:
- 为每个边添加唯一ID
- 增加边创建的调试日志
- 验证from和to节点ID的正确性
- 确保边数据完整

**修改内容**:
```typescript
// 添加边ID和调试日志
const edge: VisEdge = {
    id: `edge-${parentId}-${node.id}`,
    from: parentId,
    to: node.id
};
visEdges.push(edge);
this.logger.debug(`创建边: ${parentId} -> ${node.id}`);
```

**验证标准**:
- 每个边都有唯一ID
- 边的from/to关系正确
- 调试日志显示边创建过程

### 1.4 项目编译和构建
**文件路径**: 整个项目
**修改范围**: 编译配置和构建流程
**预期结果**: 项目成功编译，生成可用的插件文件

**具体操作**:
- 运行TypeScript编译检查
- 执行npm run build命令
- 检查生成的main.js文件
- 验证无编译错误

**验证标准**:
- 无TypeScript编译错误
- main.js文件正确生成
- 文件大小合理

### 1.5 功能测试和验证
**文件路径**: Obsidian插件环境
**修改范围**: 插件运行时测试
**预期结果**: 用户的Markdown正确显示为思维导图

**具体操作**:
- 重新加载Obsidian插件
- 使用用户提供的Markdown内容测试
- 验证节点正确显示
- 检查连接线是否出现
- 确认布局效果

**测试用例**:
```markdown
# ✨ 核心特性wewrewewew554

• 🧠 智能解析rwr: 自动解析 Markdown 标题结构生成思维导图
• 📱 双向同步: Markdown 文件与思维导图实时同步
• ✏️ 交互编辑: 支持节点编辑、添加、删除等操作
• 🔄 快捷切换: Ctrl+M 快速在 Markdown 和思维导图视图间切换
• 🎨 响应式设计: 自适应不同屏幕大小，支持缩放和拖拽
• 🎯 主题适配: 完美适配 Obsidian 明暗主题
```

**验证标准**:
- 主标题显示为根节点
- 所有列表项显示为子节点
- 有连接线连接父子节点
- 布局呈现树状结构
- 节点样式美观

### 1.6 兼容性测试
**文件路径**: 测试环境
**修改范围**: 多种Markdown格式测试
**预期结果**: 确保现有格式仍然正常工作

**具体操作**:
- 测试标准的 `-` bullet符号
- 测试 `*` 和 `+` bullet符号
- 测试数字列表格式
- 测试混合格式
- 测试嵌套层级

**测试用例**:
```markdown
# 标准格式测试
- 项目1
- 项目2
  - 子项目1
  - 子项目2

# 混合格式测试  
• 特殊符号项目
- 标准符号项目
* 星号项目
+ 加号项目
```

**验证标准**:
- 所有格式都正确解析
- 层级关系正确
- 连接线正常显示
- 无功能回归

## 时间安排

| 步骤 | 预计时间 | 累计时间 |
|------|----------|----------|
| 1.1 修复正则表达式支持 | 8分钟 | 8分钟 |
| 1.2 增强调试日志输出 | 3分钟 | 11分钟 |
| 1.3 完善边生成逻辑 | 4分钟 | 15分钟 |
| 1.4 项目编译和构建 | 2分钟 | 17分钟 |
| 1.5 功能测试和验证 | 5分钟 | 22分钟 |
| 1.6 兼容性测试 | 3分钟 | 25分钟 |

**总计**: 25分钟

## 风险控制

### 技术风险
- **备份策略**: 保留原有代码作为备份
- **渐进修改**: 每次只修改一个方法，立即测试
- **回退方案**: 可以快速恢复到修改前状态

### 质量保证
- **代码审查**: 确保正则表达式正确性
- **功能测试**: 全面测试各种Markdown格式
- **性能测试**: 验证修改不影响性能

## 成功标准

### 功能标准
- ✅ 用户的Markdown正确显示
- ✅ 节点有连接线连接
- ✅ 布局呈现树状结构
- ✅ 现有功能不受影响

### 技术标准
- ✅ 正则表达式支持 `•` 符号
- ✅ 边生成逻辑完善
- ✅ 调试信息详细
- ✅ 代码质量良好

### 用户体验标准
- ✅ 立即解决显示问题
- ✅ 视觉效果美观
- ✅ 操作流程不变
- ✅ 性能不降低

## 预期效果

修复后的思维导图应该显示为：
```
核心特性wewrewewew554
├── 智能解析rwr: 自动解析 Markdown 标题结构生成思维导图
├── 双向同步: Markdown 文件与思维导图实时同步  
├── 交互编辑: 支持节点编辑、添加、删除等操作
├── 快捷切换: Ctrl+M 快速在 Markdown 和思维导图视图间切换
├── 响应式设计: 自适应不同屏幕大小，支持缩放和拖拽
└── 主题适配: 完美适配 Obsidian 明暗主题
```

## 后续优化

完成基础修复后，可以考虑：
1. 支持更多Unicode bullet符号
2. 智能检测bullet符号类型
3. 提供格式转换功能
4. 增强错误提示信息

该计划确保在25分钟内快速解决用户的显示问题，恢复思维导图的正常功能。
