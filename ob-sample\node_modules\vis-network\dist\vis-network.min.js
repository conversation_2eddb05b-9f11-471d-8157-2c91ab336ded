/**
 * vis-network
 * https://visjs.github.io/vis-network/
 *
 * A dynamic, browser-based visualization library.
 *
 * @version 10.0.1
 * @date    2025-07-13T08:16:41.402Z
 *
 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
 *
 * @license
 * vis.js is dual licensed under both
 *
 *   1. The Apache 2.0 License
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *   and
 *
 *   2. The MIT License
 *      http://opensource.org/licenses/MIT
 *
 * vis.js may be distributed under either license.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).vis=t.vis||{})}(this,function(t){"use strict";var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function i(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var o,n,s,r,a,h,d,l,c,u,p,f,g,m,v,y,b={};function w(){if(n)return o;n=1;var t=function(t){return t&&t.Math===Math&&t};return o=t("object"==typeof globalThis&&globalThis)||t("object"==typeof window&&window)||t("object"==typeof self&&self)||t("object"==typeof e&&e)||t("object"==typeof o&&o)||function(){return this}()||Function("return this")()}function _(){return r?s:(r=1,s=function(t){try{return!!t()}catch(t){return!0}})}function x(){return h?a:(h=1,a=!_()(function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))}function E(){if(l)return d;l=1;var t=x(),e=Function.prototype,i=e.apply,o=e.call;return d="object"==typeof Reflect&&Reflect.apply||(t?o.bind(i):function(){return o.apply(i,arguments)}),d}function O(){if(u)return c;u=1;var t=x(),e=Function.prototype,i=e.call,o=t&&e.bind.bind(i,i);return c=t?o:function(t){return function(){return i.apply(t,arguments)}},c}function C(){if(f)return p;f=1;var t=O(),e=t({}.toString),i=t("".slice);return p=function(t){return i(e(t),8,-1)},p}function k(){if(m)return g;m=1;var t=C(),e=O();return g=function(i){if("Function"===t(i))return e(i)}}function S(){if(y)return v;y=1;var t="object"==typeof document&&document.all;return v=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(t){return"function"==typeof t}}var T,D,M,I,P={};function N(){return D?T:(D=1,T=!_()(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))}function B(){if(I)return M;I=1;var t=x(),e=Function.prototype.call;return M=t?e.bind(e):function(){return e.apply(e,arguments)},M}var z,F,A,j,R,L,H,W,q,V,U,Y,X,G,K,Z,Q,$,J,tt,et,it,ot,nt,st,rt,at,ht,dt,lt,ct,ut,pt,ft,gt,mt,vt,yt={};function bt(){if(z)return yt;z=1;var t={}.propertyIsEnumerable,e=Object.getOwnPropertyDescriptor,i=e&&!t.call({1:2},1);return yt.f=i?function(t){var i=e(this,t);return!!i&&i.enumerable}:t,yt}function wt(){return A?F:(A=1,F=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}})}function _t(){if(R)return j;R=1;var t=O(),e=_(),i=C(),o=Object,n=t("".split);return j=e(function(){return!o("z").propertyIsEnumerable(0)})?function(t){return"String"===i(t)?n(t,""):o(t)}:o,j}function xt(){return H||(H=1,L=function(t){return null==t}),L}function Et(){if(q)return W;q=1;var t=xt(),e=TypeError;return W=function(i){if(t(i))throw new e("Can't call method on "+i);return i},W}function Ot(){if(U)return V;U=1;var t=_t(),e=Et();return V=function(i){return t(e(i))},V}function Ct(){if(X)return Y;X=1;var t=S();return Y=function(e){return"object"==typeof e?null!==e:t(e)},Y}function kt(){return K?G:(K=1,G={})}function St(){if(Q)return Z;Q=1;var t=kt(),e=w(),i=S(),o=function(t){return i(t)?t:void 0};return Z=function(i,n){return arguments.length<2?o(t[i])||o(e[i]):t[i]&&t[i][n]||e[i]&&e[i][n]},Z}function Tt(){return J?$:(J=1,$=O()({}.isPrototypeOf))}function Dt(){if(et)return tt;et=1;var t=w().navigator,e=t&&t.userAgent;return tt=e?String(e):""}function Mt(){if(ot)return it;ot=1;var t,e,i=w(),o=Dt(),n=i.process,s=i.Deno,r=n&&n.versions||s&&s.version,a=r&&r.v8;return a&&(e=(t=a.split("."))[0]>0&&t[0]<4?1:+(t[0]+t[1])),!e&&o&&(!(t=o.match(/Edge\/(\d+)/))||t[1]>=74)&&(t=o.match(/Chrome\/(\d+)/))&&(e=+t[1]),it=e}function It(){if(st)return nt;st=1;var t=Mt(),e=_(),i=w().String;return nt=!!Object.getOwnPropertySymbols&&!e(function(){var e=Symbol("symbol detection");return!i(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&t&&t<41}),nt}function Pt(){return at?rt:(at=1,rt=It()&&!Symbol.sham&&"symbol"==typeof Symbol.iterator)}function Nt(){if(dt)return ht;dt=1;var t=St(),e=S(),i=Tt(),o=Object;return ht=Pt()?function(t){return"symbol"==typeof t}:function(n){var s=t("Symbol");return e(s)&&i(s.prototype,o(n))},ht}function Bt(){if(ct)return lt;ct=1;var t=String;return lt=function(e){try{return t(e)}catch(t){return"Object"}}}function zt(){if(pt)return ut;pt=1;var t=S(),e=Bt(),i=TypeError;return ut=function(o){if(t(o))return o;throw new i(e(o)+" is not a function")}}function Ft(){if(gt)return ft;gt=1;var t=zt(),e=xt();return ft=function(i,o){var n=i[o];return e(n)?void 0:t(n)}}function At(){if(vt)return mt;vt=1;var t=B(),e=S(),i=Ct(),o=TypeError;return mt=function(n,s){var r,a;if("string"===s&&e(r=n.toString)&&!i(a=t(r,n)))return a;if(e(r=n.valueOf)&&!i(a=t(r,n)))return a;if("string"!==s&&e(r=n.toString)&&!i(a=t(r,n)))return a;throw new o("Can't convert object to primitive value")}}var jt,Rt,Lt,Ht,Wt,qt,Vt,Ut,Yt,Xt,Gt,Kt,Zt,Qt,$t,Jt,te,ee,ie,oe,ne,se,re,ae,he,de,le,ce,ue={exports:{}};function pe(){return Rt?jt:(Rt=1,jt=!0)}function fe(){if(Ht)return Lt;Ht=1;var t=w(),e=Object.defineProperty;return Lt=function(i,o){try{e(t,i,{value:o,configurable:!0,writable:!0})}catch(e){t[i]=o}return o}}function ge(){if(Wt)return ue.exports;Wt=1;var t=pe(),e=w(),i=fe(),o="__core-js_shared__",n=ue.exports=e[o]||i(o,{});return(n.versions||(n.versions=[])).push({version:"3.44.0",mode:t?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.44.0/LICENSE",source:"https://github.com/zloirock/core-js"}),ue.exports}function me(){if(Vt)return qt;Vt=1;var t=ge();return qt=function(e,i){return t[e]||(t[e]=i||{})}}function ve(){if(Yt)return Ut;Yt=1;var t=Et(),e=Object;return Ut=function(i){return e(t(i))}}function ye(){if(Gt)return Xt;Gt=1;var t=O(),e=ve(),i=t({}.hasOwnProperty);return Xt=Object.hasOwn||function(t,o){return i(e(t),o)},Xt}function be(){if(Zt)return Kt;Zt=1;var t=O(),e=0,i=Math.random(),o=t(1.1.toString);return Kt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+o(++e+i,36)}}function we(){if($t)return Qt;$t=1;var t=w(),e=me(),i=ye(),o=be(),n=It(),s=Pt(),r=t.Symbol,a=e("wks"),h=s?r.for||r:r&&r.withoutSetter||o;return Qt=function(t){return i(a,t)||(a[t]=n&&i(r,t)?r[t]:h("Symbol."+t)),a[t]}}function _e(){if(te)return Jt;te=1;var t=B(),e=Ct(),i=Nt(),o=Ft(),n=At(),s=TypeError,r=we()("toPrimitive");return Jt=function(a,h){if(!e(a)||i(a))return a;var d,l=o(a,r);if(l){if(void 0===h&&(h="default"),d=t(l,a,h),!e(d)||i(d))return d;throw new s("Can't convert object to primitive value")}return void 0===h&&(h="number"),n(a,h)}}function xe(){if(ie)return ee;ie=1;var t=_e(),e=Nt();return ee=function(i){var o=t(i,"string");return e(o)?o:o+""}}function Ee(){if(ne)return oe;ne=1;var t=w(),e=Ct(),i=t.document,o=e(i)&&e(i.createElement);return oe=function(t){return o?i.createElement(t):{}},oe}function Oe(){if(re)return se;re=1;var t=N(),e=_(),i=Ee();return se=!t&&!e(function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a})}function Ce(){if(ae)return P;ae=1;var t=N(),e=B(),i=bt(),o=wt(),n=Ot(),s=xe(),r=ye(),a=Oe(),h=Object.getOwnPropertyDescriptor;return P.f=t?h:function(t,d){if(t=n(t),d=s(d),a)try{return h(t,d)}catch(t){}if(r(t,d))return o(!e(i.f,t,d),t[d])},P}function ke(){if(de)return he;de=1;var t=_(),e=S(),i=/#|\.prototype\./,o=function(i,o){var h=s[n(i)];return h===a||h!==r&&(e(o)?t(o):!!o)},n=o.normalize=function(t){return String(t).replace(i,".").toLowerCase()},s=o.data={},r=o.NATIVE="N",a=o.POLYFILL="P";return he=o}function Se(){if(ce)return le;ce=1;var t=k(),e=zt(),i=x(),o=t(t.bind);return le=function(t,n){return e(t),void 0===n?t:i?o(t,n):function(){return t.apply(n,arguments)}},le}var Te,De,Me,Ie,Pe,Ne,Be,ze,Fe,Ae,je,Re,Le,He,We,qe,Ve,Ue,Ye,Xe,Ge,Ke,Ze,Qe,$e,Je,ti,ei,ii,oi={};function ni(){return De?Te:(De=1,Te=N()&&_()(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype}))}function si(){if(Ie)return Me;Ie=1;var t=Ct(),e=String,i=TypeError;return Me=function(o){if(t(o))return o;throw new i(e(o)+" is not an object")}}function ri(){if(Pe)return oi;Pe=1;var t=N(),e=Oe(),i=ni(),o=si(),n=xe(),s=TypeError,r=Object.defineProperty,a=Object.getOwnPropertyDescriptor,h="enumerable",d="configurable",l="writable";return oi.f=t?i?function(t,e,i){if(o(t),e=n(e),o(i),"function"==typeof t&&"prototype"===e&&"value"in i&&l in i&&!i[l]){var s=a(t,e);s&&s[l]&&(t[e]=i.value,i={configurable:d in i?i[d]:s[d],enumerable:h in i?i[h]:s[h],writable:!1})}return r(t,e,i)}:r:function(t,i,a){if(o(t),i=n(i),o(a),e)try{return r(t,i,a)}catch(t){}if("get"in a||"set"in a)throw new s("Accessors not supported");return"value"in a&&(t[i]=a.value),t},oi}function ai(){if(Be)return Ne;Be=1;var t=N(),e=ri(),i=wt();return Ne=t?function(t,o,n){return e.f(t,o,i(1,n))}:function(t,e,i){return t[e]=i,t},Ne}function hi(){if(Fe)return ze;Fe=1;var t=w(),e=E(),i=k(),o=S(),n=Ce().f,s=ke(),r=kt(),a=Se(),h=ai(),d=ye(),l=function(t){var i=function(o,n,s){if(this instanceof i){switch(arguments.length){case 0:return new t;case 1:return new t(o);case 2:return new t(o,n)}return new t(o,n,s)}return e(t,this,arguments)};return i.prototype=t.prototype,i};return ze=function(e,c){var u,p,f,g,m,v,y,b,w,_=e.target,x=e.global,E=e.stat,O=e.proto,C=x?t:E?t[_]:t[_]&&t[_].prototype,k=x?r:r[_]||h(r,_,{})[_],S=k.prototype;for(g in c)p=!(u=s(x?g:_+(E?".":"#")+g,e.forced))&&C&&d(C,g),v=k[g],p&&(y=e.dontCallGetSet?(w=n(C,g))&&w.value:C[g]),m=p&&y?y:c[g],(u||O||typeof v!=typeof m)&&(b=e.bind&&p?a(m,t):e.wrap&&p?l(m):O&&o(m)?i(m):m,(e.sham||m&&m.sham||v&&v.sham)&&h(b,"sham",!0),h(k,g,b),O&&(d(r,f=_+"Prototype")||h(r,f,{}),h(r[f],g,m),e.real&&S&&(u||!S[g])&&h(S,g,m)))}}function di(){if(je)return Ae;je=1;var t=Math.ceil,e=Math.floor;return Ae=Math.trunc||function(i){var o=+i;return(o>0?e:t)(o)}}function li(){if(Le)return Re;Le=1;var t=di();return Re=function(e){var i=+e;return i!=i||0===i?0:t(i)},Re}function ci(){if(We)return He;We=1;var t=li(),e=Math.max,i=Math.min;return He=function(o,n){var s=t(o);return s<0?e(s+n,0):i(s,n)},He}function ui(){if(Ve)return qe;Ve=1;var t=li(),e=Math.min;return qe=function(i){var o=t(i);return o>0?e(o,9007199254740991):0}}function pi(){if(Ye)return Ue;Ye=1;var t=ui();return Ue=function(e){return t(e.length)}}function fi(){if(Ge)return Xe;Ge=1;var t=Ot(),e=ci(),i=pi(),o=function(o){return function(n,s,r){var a=t(n),h=i(a);if(0===h)return!o&&-1;var d,l=e(r,h);if(o&&s!=s){for(;h>l;)if((d=a[l++])!=d)return!0}else for(;h>l;l++)if((o||l in a)&&a[l]===s)return o||l||0;return!o&&-1}};return Xe={includes:o(!0),indexOf:o(!1)}}function gi(){return Ze?Ke:(Ze=1,Ke={})}function mi(){if($e)return Qe;$e=1;var t=O(),e=ye(),i=Ot(),o=fi().indexOf,n=gi(),s=t([].push);return Qe=function(t,r){var a,h=i(t),d=0,l=[];for(a in h)!e(n,a)&&e(h,a)&&s(l,a);for(;r.length>d;)e(h,a=r[d++])&&(~o(l,a)||s(l,a));return l},Qe}function vi(){return ti?Je:(ti=1,Je=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"])}function yi(){if(ii)return ei;ii=1;var t=mi(),e=vi();return ei=Object.keys||function(i){return t(i,e)}}var bi,wi,_i,xi,Ei,Oi,Ci,ki,Si,Ti,Di={};function Mi(){return bi||(bi=1,Di.f=Object.getOwnPropertySymbols),Di}function Ii(){if(_i)return wi;_i=1;var t=N(),e=O(),i=B(),o=_(),n=yi(),s=Mi(),r=bt(),a=ve(),h=_t(),d=Object.assign,l=Object.defineProperty,c=e([].concat);return wi=!d||o(function(){if(t&&1!==d({b:1},d(l({},"a",{enumerable:!0,get:function(){l(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},i={},o=Symbol("assign detection"),s="abcdefghijklmnopqrst";return e[o]=7,s.split("").forEach(function(t){i[t]=t}),7!==d({},e)[o]||n(d({},i)).join("")!==s})?function(e,o){for(var d=a(e),l=arguments.length,u=1,p=s.f,f=r.f;l>u;)for(var g,m=h(arguments[u++]),v=p?c(n(m),p(m)):n(m),y=v.length,b=0;y>b;)g=v[b++],t&&!i(f,m,g)||(d[g]=m[g]);return d}:d,wi}function Pi(){return Oi?Ei:(Oi=1,function(){if(xi)return b;xi=1;var t=hi(),e=Ii();t({target:"Object",stat:!0,arity:2,forced:Object.assign!==e},{assign:e})}(),Ei=kt().Object.assign)}function Ni(){return ki?Ci:(ki=1,Ci=Pi())}var Bi,zi,Fi,Ai,ji,Ri,Li,Hi,Wi,qi,Vi,Ui,Yi,Xi,Gi,Ki=i(Ti?Si:(Ti=1,Si=Ni())),Zi={};function Qi(){return zi?Bi:(zi=1,Bi=O()([].slice))}function $i(){if(Ai)return Fi;Ai=1;var t=O(),e=zt(),i=Ct(),o=ye(),n=Qi(),s=x(),r=Function,a=t([].concat),h=t([].join),d={};return Fi=s?r.bind:function(t){var s=e(this),l=s.prototype,c=n(arguments,1),u=function(){var e=a(c,n(arguments));return this instanceof u?function(t,e,i){if(!o(d,e)){for(var n=[],s=0;s<e;s++)n[s]="a["+s+"]";d[e]=r("C,a","return new C("+h(n,",")+")")}return d[e](t,i)}(s,e.length,e):s.apply(t,e)};return i(l)&&(u.prototype=l),u},Fi}function Ji(){if(Li)return Ri;Li=1;var t=w(),e=kt();return Ri=function(i,o){var n=e[i+"Prototype"],s=n&&n[o];if(s)return s;var r=t[i],a=r&&r.prototype;return a&&a[o]}}function to(){return Wi?Hi:(Wi=1,function(){if(ji)return Zi;ji=1;var t=hi(),e=$i();t({target:"Function",proto:!0,forced:Function.bind!==e},{bind:e})}(),Hi=Ji()("Function","bind"))}function eo(){if(Vi)return qi;Vi=1;var t=Tt(),e=to(),i=Function.prototype;return qi=function(o){var n=o.bind;return o===i||t(i,o)&&n===i.bind?e:n},qi}function io(){return Yi?Ui:(Yi=1,Ui=eo())}var oo=i(Gi?Xi:(Gi=1,Xi=io()));function no(t,e,i,o){t.beginPath(),t.arc(e,i,o,0,2*Math.PI,!1),t.closePath()}function so(t,e,i,o,n,s){const r=Math.PI/180;o-2*s<0&&(s=o/2),n-2*s<0&&(s=n/2),t.beginPath(),t.moveTo(e+s,i),t.lineTo(e+o-s,i),t.arc(e+o-s,i+s,s,270*r,360*r,!1),t.lineTo(e+o,i+n-s),t.arc(e+o-s,i+n-s,s,0,90*r,!1),t.lineTo(e+s,i+n),t.arc(e+s,i+n-s,s,90*r,180*r,!1),t.lineTo(e,i+s),t.arc(e+s,i+s,s,180*r,270*r,!1),t.closePath()}function ro(t,e,i,o,n){const s=.5522848,r=o/2*s,a=n/2*s,h=e+o,d=i+n,l=e+o/2,c=i+n/2;t.beginPath(),t.moveTo(e,c),t.bezierCurveTo(e,c-a,l-r,i,l,i),t.bezierCurveTo(l+r,i,h,c-a,h,c),t.bezierCurveTo(h,c+a,l+r,d,l,d),t.bezierCurveTo(l-r,d,e,c+a,e,c),t.closePath()}function ao(t,e,i,o,n){const s=n*(1/3),r=.5522848,a=o/2*r,h=s/2*r,d=e+o,l=i+s,c=e+o/2,u=i+s/2,p=i+(n-s/2),f=i+n;t.beginPath(),t.moveTo(d,u),t.bezierCurveTo(d,u+h,c+a,l,c,l),t.bezierCurveTo(c-a,l,e,u+h,e,u),t.bezierCurveTo(e,u-h,c-a,i,c,i),t.bezierCurveTo(c+a,i,d,u-h,d,u),t.lineTo(d,p),t.bezierCurveTo(d,p+h,c+a,f,c,f),t.bezierCurveTo(c-a,f,e,p+h,e,p),t.lineTo(e,u)}function ho(t,e,i,o,n,s){t.beginPath(),t.moveTo(e,i);const r=s.length,a=o-e,h=n-i,d=h/a;let l=Math.sqrt(a*a+h*h),c=0,u=!0,p=0,f=+s[0];for(;l>=.1;)f=+s[c++%r],f>l&&(f=l),p=Math.sqrt(f*f/(1+d*d)),p=a<0?-p:p,e+=p,i+=d*p,!0===u?t.lineTo(e,i):t.moveTo(e,i),l-=f,u=!u}const lo={circle:no,dashedLine:ho,database:ao,diamond:function(t,e,i,o){t.beginPath(),t.lineTo(e,i+o),t.lineTo(e+o,i),t.lineTo(e,i-o),t.lineTo(e-o,i),t.closePath()},ellipse:ro,ellipse_vis:ro,hexagon:function(t,e,i,o){t.beginPath();const n=2*Math.PI/6;t.moveTo(e+o,i);for(let s=1;s<6;s++)t.lineTo(e+o*Math.cos(n*s),i+o*Math.sin(n*s));t.closePath()},roundRect:so,square:function(t,e,i,o){t.beginPath(),t.rect(e-o,i-o,2*o,2*o),t.closePath()},star:function(t,e,i,o){t.beginPath(),i+=.1*(o*=.82);for(let n=0;n<10;n++){const s=n%2==0?1.3*o:.5*o;t.lineTo(e+s*Math.sin(2*n*Math.PI/10),i-s*Math.cos(2*n*Math.PI/10))}t.closePath()},triangle:function(t,e,i,o){t.beginPath(),i+=.275*(o*=1.15);const n=2*o,s=n/2,r=Math.sqrt(3)/6*n,a=Math.sqrt(n*n-s*s);t.moveTo(e,i-(a-r)),t.lineTo(e+s,i+r),t.lineTo(e-s,i+r),t.lineTo(e,i-(a-r)),t.closePath()},triangleDown:function(t,e,i,o){t.beginPath(),i-=.275*(o*=1.15);const n=2*o,s=n/2,r=Math.sqrt(3)/6*n,a=Math.sqrt(n*n-s*s);t.moveTo(e,i+(a-r)),t.lineTo(e+s,i-r),t.lineTo(e-s,i-r),t.lineTo(e,i+(a-r)),t.closePath()}};var co,uo={exports:{}};var po,fo=(co||(co=1,function(t){function e(t){if(t)return function(t){for(var i in e.prototype)t[i]=e.prototype[i];return t}(t)}t.exports=e,e.prototype.on=e.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},e.prototype.once=function(t,e){function i(){this.off(t,i),e.apply(this,arguments)}return i.fn=e,this.on(t,i),this},e.prototype.off=e.prototype.removeListener=e.prototype.removeAllListeners=e.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var i,o=this._callbacks["$"+t];if(!o)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var n=0;n<o.length;n++)if((i=o[n])===e||i.fn===e){o.splice(n,1);break}return 0===o.length&&delete this._callbacks["$"+t],this},e.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),i=this._callbacks["$"+t],o=1;o<arguments.length;o++)e[o-1]=arguments[o];if(i){o=0;for(var n=(i=i.slice(0)).length;o<n;++o)i[o].apply(this,e)}return this},e.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},e.prototype.hasListeners=function(t){return!!this.listeners(t).length}}(uo)),uo.exports),go=i(fo);function mo(){return mo=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o])}return t},mo.apply(this,arguments)}function vo(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}function yo(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}po="function"!=typeof Object.assign?function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),i=1;i<arguments.length;i++){var o=arguments[i];if(null!=o)for(var n in o)o.hasOwnProperty(n)&&(e[n]=o[n])}return e}:Object.assign;var bo,wo=po,_o=["","webkit","Moz","MS","ms","o"],xo="undefined"==typeof document?{style:{}}:document.createElement("div"),Eo=Math.round,Oo=Math.abs,Co=Date.now;function ko(t,e){for(var i,o,n=e[0].toUpperCase()+e.slice(1),s=0;s<_o.length;){if((o=(i=_o[s])?i+n:e)in t)return o;s++}}bo="undefined"==typeof window?{}:window;var So=ko(xo.style,"touchAction"),To=void 0!==So;var Do="compute",Mo="auto",Io="manipulation",Po="none",No="pan-x",Bo="pan-y",zo=function(){if(!To)return!1;var t={},e=bo.CSS&&bo.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach(function(i){return t[i]=!e||bo.CSS.supports("touch-action",i)}),t}(),Fo="ontouchstart"in bo,Ao=void 0!==ko(bo,"PointerEvent"),jo=Fo&&/mobile|tablet|ip(ad|hone|od)|android/i.test(navigator.userAgent),Ro="touch",Lo="mouse",Ho=16,Wo=24,qo=["x","y"],Vo=["clientX","clientY"];function Uo(t,e,i){var o;if(t)if(t.forEach)t.forEach(e,i);else if(void 0!==t.length)for(o=0;o<t.length;)e.call(i,t[o],o,t),o++;else for(o in t)t.hasOwnProperty(o)&&e.call(i,t[o],o,t)}function Yo(t,e){return"function"==typeof t?t.apply(e&&e[0]||void 0,e):t}function Xo(t,e){return t.indexOf(e)>-1}var Go=function(){function t(t,e){this.manager=t,this.set(e)}var e=t.prototype;return e.set=function(t){t===Do&&(t=this.compute()),To&&this.manager.element.style&&zo[t]&&(this.manager.element.style[So]=t),this.actions=t.toLowerCase().trim()},e.update=function(){this.set(this.manager.options.touchAction)},e.compute=function(){var t=[];return Uo(this.manager.recognizers,function(e){Yo(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))}),function(t){if(Xo(t,Po))return Po;var e=Xo(t,No),i=Xo(t,Bo);return e&&i?Po:e||i?e?No:Bo:Xo(t,Io)?Io:Mo}(t.join(" "))},e.preventDefaults=function(t){var e=t.srcEvent,i=t.offsetDirection;if(this.manager.session.prevented)e.preventDefault();else{var o=this.actions,n=Xo(o,Po)&&!zo[Po],s=Xo(o,Bo)&&!zo[Bo],r=Xo(o,No)&&!zo[No];if(n){var a=1===t.pointers.length,h=t.distance<2,d=t.deltaTime<250;if(a&&h&&d)return}if(!r||!s)return n||s&&6&i||r&&i&Wo?this.preventSrc(e):void 0}},e.preventSrc=function(t){this.manager.session.prevented=!0,t.preventDefault()},t}();function Ko(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1}function Zo(t){var e=t.length;if(1===e)return{x:Eo(t[0].clientX),y:Eo(t[0].clientY)};for(var i=0,o=0,n=0;n<e;)i+=t[n].clientX,o+=t[n].clientY,n++;return{x:Eo(i/e),y:Eo(o/e)}}function Qo(t){for(var e=[],i=0;i<t.pointers.length;)e[i]={clientX:Eo(t.pointers[i].clientX),clientY:Eo(t.pointers[i].clientY)},i++;return{timeStamp:Co(),pointers:e,center:Zo(e),deltaX:t.deltaX,deltaY:t.deltaY}}function $o(t,e,i){i||(i=qo);var o=e[i[0]]-t[i[0]],n=e[i[1]]-t[i[1]];return Math.sqrt(o*o+n*n)}function Jo(t,e,i){i||(i=qo);var o=e[i[0]]-t[i[0]],n=e[i[1]]-t[i[1]];return 180*Math.atan2(n,o)/Math.PI}function tn(t,e){return t===e?1:Oo(t)>=Oo(e)?t<0?2:4:e<0?8:Ho}function en(t,e,i){return{x:e/t||0,y:i/t||0}}function on(t,e){var i=t.session,o=e.pointers,n=o.length;i.firstInput||(i.firstInput=Qo(e)),n>1&&!i.firstMultiple?i.firstMultiple=Qo(e):1===n&&(i.firstMultiple=!1);var s=i.firstInput,r=i.firstMultiple,a=r?r.center:s.center,h=e.center=Zo(o);e.timeStamp=Co(),e.deltaTime=e.timeStamp-s.timeStamp,e.angle=Jo(a,h),e.distance=$o(a,h),function(t,e){var i=e.center,o=t.offsetDelta||{},n=t.prevDelta||{},s=t.prevInput||{};1!==e.eventType&&4!==s.eventType||(n=t.prevDelta={x:s.deltaX||0,y:s.deltaY||0},o=t.offsetDelta={x:i.x,y:i.y}),e.deltaX=n.x+(i.x-o.x),e.deltaY=n.y+(i.y-o.y)}(i,e),e.offsetDirection=tn(e.deltaX,e.deltaY);var d,l,c=en(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=c.x,e.overallVelocityY=c.y,e.overallVelocity=Oo(c.x)>Oo(c.y)?c.x:c.y,e.scale=r?(d=r.pointers,$o((l=o)[0],l[1],Vo)/$o(d[0],d[1],Vo)):1,e.rotation=r?function(t,e){return Jo(e[1],e[0],Vo)+Jo(t[1],t[0],Vo)}(r.pointers,o):0,e.maxPointers=i.prevInput?e.pointers.length>i.prevInput.maxPointers?e.pointers.length:i.prevInput.maxPointers:e.pointers.length,function(t,e){var i,o,n,s,r=t.lastInterval||e,a=e.timeStamp-r.timeStamp;if(8!==e.eventType&&(a>25||void 0===r.velocity)){var h=e.deltaX-r.deltaX,d=e.deltaY-r.deltaY,l=en(a,h,d);o=l.x,n=l.y,i=Oo(l.x)>Oo(l.y)?l.x:l.y,s=tn(h,d),t.lastInterval=e}else i=r.velocity,o=r.velocityX,n=r.velocityY,s=r.direction;e.velocity=i,e.velocityX=o,e.velocityY=n,e.direction=s}(i,e);var u,p=t.element,f=e.srcEvent;Ko(u=f.composedPath?f.composedPath()[0]:f.path?f.path[0]:f.target,p)&&(p=u),e.target=p}function nn(t,e,i){var o=i.pointers.length,n=i.changedPointers.length,s=1&e&&o-n===0,r=12&e&&o-n===0;i.isFirst=!!s,i.isFinal=!!r,s&&(t.session={}),i.eventType=e,on(t,i),t.emit("hammer.input",i),t.recognize(i),t.session.prevInput=i}function sn(t){return t.trim().split(/\s+/g)}function rn(t,e,i){Uo(sn(e),function(e){t.addEventListener(e,i,!1)})}function an(t,e,i){Uo(sn(e),function(e){t.removeEventListener(e,i,!1)})}function hn(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||window}var dn=function(){function t(t,e){var i=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){Yo(t.options.enable,[t])&&i.handler(e)},this.init()}var e=t.prototype;return e.handler=function(){},e.init=function(){this.evEl&&rn(this.element,this.evEl,this.domHandler),this.evTarget&&rn(this.target,this.evTarget,this.domHandler),this.evWin&&rn(hn(this.element),this.evWin,this.domHandler)},e.destroy=function(){this.evEl&&an(this.element,this.evEl,this.domHandler),this.evTarget&&an(this.target,this.evTarget,this.domHandler),this.evWin&&an(hn(this.element),this.evWin,this.domHandler)},t}();function ln(t,e,i){if(t.indexOf&&!i)return t.indexOf(e);for(var o=0;o<t.length;){if(i&&t[o][i]==e||!i&&t[o]===e)return o;o++}return-1}var cn={pointerdown:1,pointermove:2,pointerup:4,pointercancel:8,pointerout:8},un={2:Ro,3:"pen",4:Lo,5:"kinect"},pn="pointerdown",fn="pointermove pointerup pointercancel";bo.MSPointerEvent&&!bo.PointerEvent&&(pn="MSPointerDown",fn="MSPointerMove MSPointerUp MSPointerCancel");var gn=function(t){function e(){var i,o=e.prototype;return o.evEl=pn,o.evWin=fn,(i=t.apply(this,arguments)||this).store=i.manager.session.pointerEvents=[],i}return vo(e,t),e.prototype.handler=function(t){var e=this.store,i=!1,o=t.type.toLowerCase().replace("ms",""),n=cn[o],s=un[t.pointerType]||t.pointerType,r=s===Ro,a=ln(e,t.pointerId,"pointerId");1&n&&(0===t.button||r)?a<0&&(e.push(t),a=e.length-1):12&n&&(i=!0),a<0||(e[a]=t,this.callback(this.manager,n,{pointers:e,changedPointers:[t],pointerType:s,srcEvent:t}),i&&e.splice(a,1))},e}(dn);function mn(t){return Array.prototype.slice.call(t,0)}function vn(t,e,i){for(var o=[],n=[],s=0;s<t.length;){var r=e?t[s][e]:t[s];ln(n,r)<0&&o.push(t[s]),n[s]=r,s++}return i&&(o=e?o.sort(function(t,i){return t[e]>i[e]}):o.sort()),o}var yn={touchstart:1,touchmove:2,touchend:4,touchcancel:8},bn=function(t){function e(){var i;return e.prototype.evTarget="touchstart touchmove touchend touchcancel",(i=t.apply(this,arguments)||this).targetIds={},i}return vo(e,t),e.prototype.handler=function(t){var e=yn[t.type],i=wn.call(this,t,e);i&&this.callback(this.manager,e,{pointers:i[0],changedPointers:i[1],pointerType:Ro,srcEvent:t})},e}(dn);function wn(t,e){var i,o,n=mn(t.touches),s=this.targetIds;if(3&e&&1===n.length)return s[n[0].identifier]=!0,[n,n];var r=mn(t.changedTouches),a=[],h=this.target;if(o=n.filter(function(t){return Ko(t.target,h)}),1===e)for(i=0;i<o.length;)s[o[i].identifier]=!0,i++;for(i=0;i<r.length;)s[r[i].identifier]&&a.push(r[i]),12&e&&delete s[r[i].identifier],i++;return a.length?[vn(o.concat(a),"identifier",!0),a]:void 0}var _n={mousedown:1,mousemove:2,mouseup:4},xn=function(t){function e(){var i,o=e.prototype;return o.evEl="mousedown",o.evWin="mousemove mouseup",(i=t.apply(this,arguments)||this).pressed=!1,i}return vo(e,t),e.prototype.handler=function(t){var e=_n[t.type];1&e&&0===t.button&&(this.pressed=!0),2&e&&1!==t.which&&(e=4),this.pressed&&(4&e&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:Lo,srcEvent:t}))},e}(dn);function En(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var i={x:e.clientX,y:e.clientY},o=this.lastTouches;this.lastTouches.push(i);setTimeout(function(){var t=o.indexOf(i);t>-1&&o.splice(t,1)},2500)}}function On(t,e){1&t?(this.primaryTouch=e.changedPointers[0].identifier,En.call(this,e)):12&t&&En.call(this,e)}function Cn(t){for(var e=t.srcEvent.clientX,i=t.srcEvent.clientY,o=0;o<this.lastTouches.length;o++){var n=this.lastTouches[o],s=Math.abs(e-n.x),r=Math.abs(i-n.y);if(s<=25&&r<=25)return!0}return!1}var kn=function(){return function(t){function e(e,i){var o;return(o=t.call(this,e,i)||this).handler=function(t,e,i){var n=i.pointerType===Ro,s=i.pointerType===Lo;if(!(s&&i.sourceCapabilities&&i.sourceCapabilities.firesTouchEvents)){if(n)On.call(yo(yo(o)),e,i);else if(s&&Cn.call(yo(yo(o)),i))return;o.callback(t,e,i)}},o.touch=new bn(o.manager,o.handler),o.mouse=new xn(o.manager,o.handler),o.primaryTouch=null,o.lastTouches=[],o}return vo(e,t),e.prototype.destroy=function(){this.touch.destroy(),this.mouse.destroy()},e}(dn)}();function Sn(t,e,i){return!!Array.isArray(t)&&(Uo(t,i[e],i),!0)}var Tn=32,Dn=1;function Mn(t,e){var i=e.manager;return i?i.get(t):t}function In(t){return 16&t?"cancel":8&t?"end":4&t?"move":2&t?"start":""}var Pn=function(){function t(t){void 0===t&&(t={}),this.options=mo({enable:!0},t),this.id=Dn++,this.manager=null,this.state=1,this.simultaneous={},this.requireFail=[]}var e=t.prototype;return e.set=function(t){return wo(this.options,t),this.manager&&this.manager.touchAction.update(),this},e.recognizeWith=function(t){if(Sn(t,"recognizeWith",this))return this;var e=this.simultaneous;return e[(t=Mn(t,this)).id]||(e[t.id]=t,t.recognizeWith(this)),this},e.dropRecognizeWith=function(t){return Sn(t,"dropRecognizeWith",this)||(t=Mn(t,this),delete this.simultaneous[t.id]),this},e.requireFailure=function(t){if(Sn(t,"requireFailure",this))return this;var e=this.requireFail;return-1===ln(e,t=Mn(t,this))&&(e.push(t),t.requireFailure(this)),this},e.dropRequireFailure=function(t){if(Sn(t,"dropRequireFailure",this))return this;t=Mn(t,this);var e=ln(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},e.hasRequireFailures=function(){return this.requireFail.length>0},e.canRecognizeWith=function(t){return!!this.simultaneous[t.id]},e.emit=function(t){var e=this,i=this.state;function o(i){e.manager.emit(i,t)}i<8&&o(e.options.event+In(i)),o(e.options.event),t.additionalEvent&&o(t.additionalEvent),i>=8&&o(e.options.event+In(i))},e.tryEmit=function(t){if(this.canEmit())return this.emit(t);this.state=Tn},e.canEmit=function(){for(var t=0;t<this.requireFail.length;){if(!(33&this.requireFail[t].state))return!1;t++}return!0},e.recognize=function(t){var e=wo({},t);if(!Yo(this.options.enable,[this,e]))return this.reset(),void(this.state=Tn);56&this.state&&(this.state=1),this.state=this.process(e),30&this.state&&this.tryEmit(e)},e.process=function(t){},e.getTouchAction=function(){},e.reset=function(){},t}(),Nn=function(t){function e(e){var i;return void 0===e&&(e={}),(i=t.call(this,mo({event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},e))||this).pTime=!1,i.pCenter=!1,i._timer=null,i._input=null,i.count=0,i}vo(e,t);var i=e.prototype;return i.getTouchAction=function(){return[Io]},i.process=function(t){var e=this,i=this.options,o=t.pointers.length===i.pointers,n=t.distance<i.threshold,s=t.deltaTime<i.time;if(this.reset(),1&t.eventType&&0===this.count)return this.failTimeout();if(n&&s&&o){if(4!==t.eventType)return this.failTimeout();var r=!this.pTime||t.timeStamp-this.pTime<i.interval,a=!this.pCenter||$o(this.pCenter,t.center)<i.posThreshold;if(this.pTime=t.timeStamp,this.pCenter=t.center,a&&r?this.count+=1:this.count=1,this._input=t,0===this.count%i.taps)return this.hasRequireFailures()?(this._timer=setTimeout(function(){e.state=8,e.tryEmit()},i.interval),2):8}return Tn},i.failTimeout=function(){var t=this;return this._timer=setTimeout(function(){t.state=Tn},this.options.interval),Tn},i.reset=function(){clearTimeout(this._timer)},i.emit=function(){8===this.state&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))},e}(Pn),Bn=function(t){function e(e){return void 0===e&&(e={}),t.call(this,mo({pointers:1},e))||this}vo(e,t);var i=e.prototype;return i.attrTest=function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},i.process=function(t){var e=this.state,i=t.eventType,o=6&e,n=this.attrTest(t);return o&&(8&i||!n)?16|e:o||n?4&i?8|e:2&e?4|e:2:Tn},e}(Pn);function zn(t){return t===Ho?"down":8===t?"up":2===t?"left":4===t?"right":""}var Fn=function(t){function e(e){var i;return void 0===e&&(e={}),(i=t.call(this,mo({event:"pan",threshold:10,pointers:1,direction:30},e))||this).pX=null,i.pY=null,i}vo(e,t);var i=e.prototype;return i.getTouchAction=function(){var t=this.options.direction,e=[];return 6&t&&e.push(Bo),t&Wo&&e.push(No),e},i.directionTest=function(t){var e=this.options,i=!0,o=t.distance,n=t.direction,s=t.deltaX,r=t.deltaY;return n&e.direction||(6&e.direction?(n=0===s?1:s<0?2:4,i=s!==this.pX,o=Math.abs(t.deltaX)):(n=0===r?1:r<0?8:Ho,i=r!==this.pY,o=Math.abs(t.deltaY))),t.direction=n,i&&o>e.threshold&&n&e.direction},i.attrTest=function(t){return Bn.prototype.attrTest.call(this,t)&&(2&this.state||!(2&this.state)&&this.directionTest(t))},i.emit=function(e){this.pX=e.deltaX,this.pY=e.deltaY;var i=zn(e.direction);i&&(e.additionalEvent=this.options.event+i),t.prototype.emit.call(this,e)},e}(Bn),An=function(t){function e(e){return void 0===e&&(e={}),t.call(this,mo({event:"swipe",threshold:10,velocity:.3,direction:30,pointers:1},e))||this}vo(e,t);var i=e.prototype;return i.getTouchAction=function(){return Fn.prototype.getTouchAction.call(this)},i.attrTest=function(e){var i,o=this.options.direction;return 30&o?i=e.overallVelocity:6&o?i=e.overallVelocityX:o&Wo&&(i=e.overallVelocityY),t.prototype.attrTest.call(this,e)&&o&e.offsetDirection&&e.distance>this.options.threshold&&e.maxPointers===this.options.pointers&&Oo(i)>this.options.velocity&&4&e.eventType},i.emit=function(t){var e=zn(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)},e}(Bn),jn=function(t){function e(e){return void 0===e&&(e={}),t.call(this,mo({event:"pinch",threshold:0,pointers:2},e))||this}vo(e,t);var i=e.prototype;return i.getTouchAction=function(){return[Po]},i.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.scale-1)>this.options.threshold||2&this.state)},i.emit=function(e){if(1!==e.scale){var i=e.scale<1?"in":"out";e.additionalEvent=this.options.event+i}t.prototype.emit.call(this,e)},e}(Bn),Rn=function(t){function e(e){return void 0===e&&(e={}),t.call(this,mo({event:"rotate",threshold:0,pointers:2},e))||this}vo(e,t);var i=e.prototype;return i.getTouchAction=function(){return[Po]},i.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.rotation)>this.options.threshold||2&this.state)},e}(Bn),Ln=function(t){function e(e){var i;return void 0===e&&(e={}),(i=t.call(this,mo({event:"press",pointers:1,time:251,threshold:9},e))||this)._timer=null,i._input=null,i}vo(e,t);var i=e.prototype;return i.getTouchAction=function(){return[Mo]},i.process=function(t){var e=this,i=this.options,o=t.pointers.length===i.pointers,n=t.distance<i.threshold,s=t.deltaTime>i.time;if(this._input=t,!n||!o||12&t.eventType&&!s)this.reset();else if(1&t.eventType)this.reset(),this._timer=setTimeout(function(){e.state=8,e.tryEmit()},i.time);else if(4&t.eventType)return 8;return Tn},i.reset=function(){clearTimeout(this._timer)},i.emit=function(t){8===this.state&&(t&&4&t.eventType?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=Co(),this.manager.emit(this.options.event,this._input)))},e}(Pn),Hn={domEvents:!1,touchAction:Do,enable:!0,inputTarget:null,inputClass:null,cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}},Wn=[[Rn,{enable:!1}],[jn,{enable:!1},["rotate"]],[An,{direction:6}],[Fn,{direction:6},["swipe"]],[Nn],[Nn,{event:"doubletap",taps:2},["tap"]],[Ln]];function qn(t,e){var i,o=t.element;o.style&&(Uo(t.options.cssProps,function(n,s){i=ko(o.style,s),e?(t.oldCssProps[i]=o.style[i],o.style[i]=n):o.style[i]=t.oldCssProps[i]||""}),e||(t.oldCssProps={}))}var Vn=function(){function t(t,e){var i,o=this;this.options=wo({},Hn,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=new((i=this).options.inputClass||(Ao?gn:jo?bn:Fo?kn:xn))(i,nn),this.touchAction=new Go(this,this.options.touchAction),qn(this,!0),Uo(this.options.recognizers,function(t){var e=o.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])},this)}var e=t.prototype;return e.set=function(t){return wo(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},e.stop=function(t){this.session.stopped=t?2:1},e.recognize=function(t){var e=this.session;if(!e.stopped){var i;this.touchAction.preventDefaults(t);var o=this.recognizers,n=e.curRecognizer;(!n||n&&8&n.state)&&(e.curRecognizer=null,n=null);for(var s=0;s<o.length;)i=o[s],2===e.stopped||n&&i!==n&&!i.canRecognizeWith(n)?i.reset():i.recognize(t),!n&&14&i.state&&(e.curRecognizer=i,n=i),s++}},e.get=function(t){if(t instanceof Pn)return t;for(var e=this.recognizers,i=0;i<e.length;i++)if(e[i].options.event===t)return e[i];return null},e.add=function(t){if(Sn(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},e.remove=function(t){if(Sn(t,"remove",this))return this;var e=this.get(t);if(t){var i=this.recognizers,o=ln(i,e);-1!==o&&(i.splice(o,1),this.touchAction.update())}return this},e.on=function(t,e){if(void 0===t||void 0===e)return this;var i=this.handlers;return Uo(sn(t),function(t){i[t]=i[t]||[],i[t].push(e)}),this},e.off=function(t,e){if(void 0===t)return this;var i=this.handlers;return Uo(sn(t),function(t){e?i[t]&&i[t].splice(ln(i[t],e),1):delete i[t]}),this},e.emit=function(t,e){this.options.domEvents&&function(t,e){var i=document.createEvent("Event");i.initEvent(t,!0,!0),i.gesture=e,e.target.dispatchEvent(i)}(t,e);var i=this.handlers[t]&&this.handlers[t].slice();if(i&&i.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var o=0;o<i.length;)i[o](e),o++}},e.destroy=function(){this.element&&qn(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null},t}(),Un={touchstart:1,touchmove:2,touchend:4,touchcancel:8},Yn=function(t){function e(){var i,o=e.prototype;return o.evTarget="touchstart",o.evWin="touchstart touchmove touchend touchcancel",(i=t.apply(this,arguments)||this).started=!1,i}return vo(e,t),e.prototype.handler=function(t){var e=Un[t.type];if(1===e&&(this.started=!0),this.started){var i=Xn.call(this,t,e);12&e&&i[0].length-i[1].length===0&&(this.started=!1),this.callback(this.manager,e,{pointers:i[0],changedPointers:i[1],pointerType:Ro,srcEvent:t})}},e}(dn);function Xn(t,e){var i=mn(t.touches),o=mn(t.changedTouches);return 12&e&&(i=vn(i.concat(o),"identifier",!0)),[i,o]}function Gn(t,e,i){var o="DEPRECATED METHOD: "+e+"\n"+i+" AT \n";return function(){var e=new Error("get-stack-trace"),i=e&&e.stack?e.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",n=window.console&&(window.console.warn||window.console.log);return n&&n.call(window.console,o,i),t.apply(this,arguments)}}var Kn=Gn(function(t,e,i){for(var o=Object.keys(e),n=0;n<o.length;)(!i||i&&void 0===t[o[n]])&&(t[o[n]]=e[o[n]]),n++;return t},"extend","Use `assign`."),Zn=Gn(function(t,e){return Kn(t,e,!0)},"merge","Use `assign`.");function Qn(t,e,i){var o,n=e.prototype;(o=t.prototype=Object.create(n)).constructor=t,o._super=n,i&&wo(o,i)}function $n(t,e){return function(){return t.apply(e,arguments)}}var Jn=function(){var t=function(t,e){return void 0===e&&(e={}),new Vn(t,mo({recognizers:Wn.concat()},e))};return t.VERSION="2.0.17-rc",t.DIRECTION_ALL=30,t.DIRECTION_DOWN=Ho,t.DIRECTION_LEFT=2,t.DIRECTION_RIGHT=4,t.DIRECTION_UP=8,t.DIRECTION_HORIZONTAL=6,t.DIRECTION_VERTICAL=Wo,t.DIRECTION_NONE=1,t.DIRECTION_DOWN=Ho,t.INPUT_START=1,t.INPUT_MOVE=2,t.INPUT_END=4,t.INPUT_CANCEL=8,t.STATE_POSSIBLE=1,t.STATE_BEGAN=2,t.STATE_CHANGED=4,t.STATE_ENDED=8,t.STATE_RECOGNIZED=8,t.STATE_CANCELLED=16,t.STATE_FAILED=Tn,t.Manager=Vn,t.Input=dn,t.TouchAction=Go,t.TouchInput=bn,t.MouseInput=xn,t.PointerEventInput=gn,t.TouchMouseInput=kn,t.SingleTouchInput=Yn,t.Recognizer=Pn,t.AttrRecognizer=Bn,t.Tap=Nn,t.Pan=Fn,t.Swipe=An,t.Pinch=jn,t.Rotate=Rn,t.Press=Ln,t.on=rn,t.off=an,t.each=Uo,t.merge=Zn,t.extend=Kn,t.bindFn=$n,t.assign=wo,t.inherit=Qn,t.bindFn=$n,t.prefixed=ko,t.toArray=mn,t.inArray=ln,t.uniqueArray=vn,t.splitStr=sn,t.boolOrFn=Yo,t.hasParent=Ko,t.addEventListeners=rn,t.removeEventListeners=an,t.defaults=wo({},Hn,{preset:Wn}),t}();Jn.defaults;
/**
	 * vis-util
	 * https://github.com/visjs/vis-util
	 *
	 * utilitie collection for visjs
	 *
	 * @version 6.0.0
	 * @date    2025-07-12T18:02:43.836Z
	 *
	 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
	 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
	 *
	 * @license
	 * vis.js is dual licensed under both
	 *
	 *   1. The Apache 2.0 License
	 *      http://www.apache.org/licenses/LICENSE-2.0
	 *
	 *   and
	 *
	 *   2. The MIT License
	 *      http://opensource.org/licenses/MIT
	 *
	 * vis.js may be distributed under either license.
	 */
const ts=Symbol("DELETE");function es(t,...e){return is({},t,...e)}function is(...t){const e=os(...t);return ss(e),e}function os(...t){if(t.length<2)return t[0];if(t.length>2)return os(is(t[0],t[1]),...t.slice(2));const e=t[0],i=t[1];if(e instanceof Date&&i instanceof Date)return e.setTime(i.getTime()),e;for(const t of Reflect.ownKeys(i))Object.prototype.propertyIsEnumerable.call(i,t)&&(i[t]===ts?delete e[t]:null===e[t]||null===i[t]||"object"!=typeof e[t]||"object"!=typeof i[t]||Array.isArray(e[t])||Array.isArray(i[t])?e[t]=ns(i[t]):e[t]=os(e[t],i[t]));return e}function ns(t){return Array.isArray(t)?t.map(t=>ns(t)):"object"==typeof t&&null!==t?t instanceof Date?new Date(t.getTime()):os({},t):t}function ss(t){for(const e of Object.keys(t))t[e]===ts?delete t[e]:"object"==typeof t[e]&&null!==t[e]&&ss(t[e])}function rs(...t){return function(t){let[e,i,o]=function(...t){const e=function(){let t=**********;return function(e){const i=e.toString();for(let e=0;e<i.length;e++){t+=i.charCodeAt(e);let o=.02519603282416938*t;t=o>>>0,o-=t,o*=t,t=o>>>0,o-=t,t+=4294967296*o}return 2.3283064365386963e-10*(t>>>0)}}();let i=e(" "),o=e(" "),n=e(" ");for(let s=0;s<t.length;s++)i-=e(t[s]),i<0&&(i+=1),o-=e(t[s]),o<0&&(o+=1),n-=e(t[s]),n<0&&(n+=1);return[i,o,n]}(t),n=1;const s=()=>{const t=2091639*e+2.3283064365386963e-10*n;return e=i,i=o,o=t-(n=0|t)};return s.uint32=()=>4294967296*s(),s.fract53=()=>s()+11102230246251565e-32*(2097152*s()|0),s.algorithm="Alea",s.seed=t,s.version="0.9",s}(t.length?t:[Date.now()])}const as="undefined"!=typeof window?window.Hammer||Jn:function(){return function(){const t=()=>{};return{on:t,off:t,destroy:t,emit:t,get:()=>({set:t})}}()};function hs(t){this._cleanupQueue=[],this.active=!1,this._dom={container:t,overlay:document.createElement("div")},this._dom.overlay.classList.add("vis-overlay"),this._dom.container.appendChild(this._dom.overlay),this._cleanupQueue.push(()=>{this._dom.overlay.parentNode.removeChild(this._dom.overlay)});const e=as(this._dom.overlay);e.on("tap",this._onTapOverlay.bind(this)),this._cleanupQueue.push(()=>{e.destroy()});["tap","doubletap","press","pinch","pan","panstart","panmove","panend"].forEach(t=>{e.on(t,t=>{t.srcEvent.stopPropagation()})}),document&&document.body&&(this._onClick=e=>{(function(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1})(e.target,t)||this.deactivate()},document.body.addEventListener("click",this._onClick),this._cleanupQueue.push(()=>{document.body.removeEventListener("click",this._onClick)})),this._escListener=t=>{("key"in t?"Escape"===t.key:27===t.keyCode)&&this.deactivate()}}go(hs.prototype),hs.current=null,hs.prototype.destroy=function(){this.deactivate();for(const t of this._cleanupQueue.splice(0).reverse())t()},hs.prototype.activate=function(){hs.current&&hs.current.deactivate(),hs.current=this,this.active=!0,this._dom.overlay.style.display="none",this._dom.container.classList.add("vis-active"),this.emit("change"),this.emit("activate"),document.body.addEventListener("keydown",this._escListener)},hs.prototype.deactivate=function(){this.active=!1,this._dom.overlay.style.display="block",this._dom.container.classList.remove("vis-active"),document.body.removeEventListener("keydown",this._escListener),this.emit("change"),this.emit("deactivate")},hs.prototype._onTapOverlay=function(t){this.activate(),t.srcEvent.stopPropagation()};const ds=/^\/?Date\((-?\d+)/i,ls=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,cs=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,us=/^rgb\( *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *\)$/i,ps=/^rgba\( *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *([01]|0?\.\d+) *\)$/i;function fs(t){return t instanceof Number||"number"==typeof t}function gs(t){if(t)for(;!0===t.hasChildNodes();){const e=t.firstChild;e&&(gs(e),t.removeChild(e))}}function ms(t){return t instanceof String||"string"==typeof t}function vs(t){return"object"==typeof t&&null!==t}function ys(t,e,i,o){let n=!1;!0===o&&(n=null===e[i]&&void 0!==t[i]),n?delete t[i]:t[i]=e[i]}function bs(t,e,i=!1){for(const o in t)if(void 0!==e[o])if(null===e[o]||"object"!=typeof e[o])ys(t,e,o,i);else{const n=t[o],s=e[o];vs(n)&&vs(s)&&bs(n,s,i)}}const ws=Object.assign;function _s(t,e,i,o=!1){if(Array.isArray(i))throw new TypeError("Arrays are not supported by deepExtend");for(let n=0;n<t.length;n++){const s=t[n];if(Object.prototype.hasOwnProperty.call(i,s))if(i[s]&&i[s].constructor===Object)void 0===e[s]&&(e[s]={}),e[s].constructor===Object?Es(e[s],i[s],!1,o):ys(e,i,s,o);else{if(Array.isArray(i[s]))throw new TypeError("Arrays are not supported by deepExtend");ys(e,i,s,o)}}return e}function xs(t,e,i,o=!1){if(Array.isArray(i))throw new TypeError("Arrays are not supported by deepExtend");for(const n in i)if(Object.prototype.hasOwnProperty.call(i,n)&&!t.includes(n))if(i[n]&&i[n].constructor===Object)void 0===e[n]&&(e[n]={}),e[n].constructor===Object?Es(e[n],i[n]):ys(e,i,n,o);else if(Array.isArray(i[n])){e[n]=[];for(let t=0;t<i[n].length;t++)e[n].push(i[n][t])}else ys(e,i,n,o);return e}function Es(t,e,i=!1,o=!1){for(const n in e)(Object.prototype.hasOwnProperty.call(e,n)||!0===i)&&("object"==typeof e[n]&&null!==e[n]&&Object.getPrototypeOf(e[n])===Object.prototype?void 0===t[n]?t[n]=Es({},e[n],i):"object"==typeof t[n]&&null!==t[n]&&Object.getPrototypeOf(t[n])===Object.prototype?Es(t[n],e[n],i):ys(t,e,n,o):Array.isArray(e[n])?t[n]=e[n].slice():ys(t,e,n,o));return t}function Os(t,e){return[...t,e]}function Cs(t){return t.slice()}function ks(t){return t.getBoundingClientRect().left}function Ss(t){return t.getBoundingClientRect().top}function Ts(t,e){if(Array.isArray(t)){const i=t.length;for(let o=0;o<i;o++)e(t[o],o,t)}else for(const i in t)Object.prototype.hasOwnProperty.call(t,i)&&e(t[i],i,t)}const Ds=Object.values;const Ms={asBoolean:(t,e)=>("function"==typeof t&&(t=t()),null!=t?0!=t:e||null),asNumber:(t,e)=>("function"==typeof t&&(t=t()),null!=t?Number(t)||e||null:e||null),asString:(t,e)=>("function"==typeof t&&(t=t()),null!=t?String(t):e||null),asSize:(t,e)=>("function"==typeof t&&(t=t()),ms(t)?t:fs(t)?t+"px":e||null),asElement:(t,e)=>("function"==typeof t&&(t=t()),t||e||null)};function Is(t){let e;switch(t.length){case 3:case 4:return e=cs.exec(t),e?{r:parseInt(e[1]+e[1],16),g:parseInt(e[2]+e[2],16),b:parseInt(e[3]+e[3],16)}:null;case 6:case 7:return e=ls.exec(t),e?{r:parseInt(e[1],16),g:parseInt(e[2],16),b:parseInt(e[3],16)}:null;default:return null}}function Ps(t,e){if(t.includes("rgba"))return t;if(t.includes("rgb")){const i=t.substr(t.indexOf("(")+1).replace(")","").split(",");return"rgba("+i[0]+","+i[1]+","+i[2]+","+e+")"}{const i=Is(t);return null==i?t:"rgba("+i.r+","+i.g+","+i.b+","+e+")"}}function Ns(t,e,i){return"#"+((1<<24)+(t<<16)+(e<<8)+i).toString(16).slice(1)}function Bs(t,e){if(ms(t)){let e=t;if(Hs(e)){const t=e.substr(4).substr(0,e.length-5).split(",").map(function(t){return parseInt(t)});e=Ns(t[0],t[1],t[2])}if(!0===Ls(e)){const t=Rs(e),i={h:t.h,s:.8*t.s,v:Math.min(1,1.02*t.v)},o={h:t.h,s:Math.min(1,1.25*t.s),v:.8*t.v},n=js(o.h,o.s,o.v),s=js(i.h,i.s,i.v);return{background:e,border:n,highlight:{background:s,border:n},hover:{background:s,border:n}}}return{background:e,border:e,highlight:{background:e,border:e},hover:{background:e,border:e}}}if(e){return{background:t.background||e.background,border:t.border||e.border,highlight:ms(t.highlight)?{border:t.highlight,background:t.highlight}:{background:t.highlight&&t.highlight.background||e.highlight.background,border:t.highlight&&t.highlight.border||e.highlight.border},hover:ms(t.hover)?{border:t.hover,background:t.hover}:{border:t.hover&&t.hover.border||e.hover.border,background:t.hover&&t.hover.background||e.hover.background}}}return{background:t.background||void 0,border:t.border||void 0,highlight:ms(t.highlight)?{border:t.highlight,background:t.highlight}:{background:t.highlight&&t.highlight.background||void 0,border:t.highlight&&t.highlight.border||void 0},hover:ms(t.hover)?{border:t.hover,background:t.hover}:{border:t.hover&&t.hover.border||void 0,background:t.hover&&t.hover.background||void 0}}}function zs(t,e,i){t/=255,e/=255,i/=255;const o=Math.min(t,Math.min(e,i)),n=Math.max(t,Math.max(e,i));if(o===n)return{h:0,s:0,v:o};return{h:60*((t===o?3:i===o?1:5)-(t===o?e-i:i===o?t-e:i-t)/(n-o))/360,s:(n-o)/n,v:n}}function Fs(t){const e=document.createElement("div"),i={};e.style.cssText=t;for(let t=0;t<e.style.length;++t)i[e.style[t]]=e.style.getPropertyValue(e.style[t]);return i}function As(t,e,i){let o,n,s;const r=Math.floor(6*t),a=6*t-r,h=i*(1-e),d=i*(1-a*e),l=i*(1-(1-a)*e);switch(r%6){case 0:o=i,n=l,s=h;break;case 1:o=d,n=i,s=h;break;case 2:o=h,n=i,s=l;break;case 3:o=h,n=d,s=i;break;case 4:o=l,n=h,s=i;break;case 5:o=i,n=h,s=d}return{r:Math.floor(255*o),g:Math.floor(255*n),b:Math.floor(255*s)}}function js(t,e,i){const o=As(t,e,i);return Ns(o.r,o.g,o.b)}function Rs(t){const e=Is(t);if(!e)throw new TypeError(`'${t}' is not a valid color.`);return zs(e.r,e.g,e.b)}function Ls(t){return/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(t)}function Hs(t){return us.test(t)}function Ws(t){return ps.test(t)}function qs(t){if(null===t||"object"!=typeof t)return null;if(t instanceof Element)return t;const e=Object.create(t);for(const i in t)Object.prototype.hasOwnProperty.call(t,i)&&"object"==typeof t[i]&&(e[i]=qs(t[i]));return e}function Vs(t,e,i,o={}){const n=function(t){return null!=t},s=function(t){return null!==t&&"object"==typeof t};if(!s(t))throw new Error("Parameter mergeTarget must be an object");if(!s(e))throw new Error("Parameter options must be an object");if(!n(i))throw new Error("Parameter option must have a value");if(!s(o))throw new Error("Parameter globalOptions must be an object");const r=e[i],a=s(o)&&!function(t){for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e))return!1;return!0}(o)?o[i]:void 0,h=a?a.enabled:void 0;if(void 0===r)return;if("boolean"==typeof r)return s(t[i])||(t[i]={}),void(t[i].enabled=r);if(null===r&&!s(t[i])){if(!n(a))return;t[i]=Object.create(a)}if(!s(r))return;let d=!0;void 0!==r.enabled?d=r.enabled:void 0!==h&&(d=a.enabled),function(t,e,i){s(t[i])||(t[i]={});const o=e[i],n=t[i];for(const t in o)Object.prototype.hasOwnProperty.call(o,t)&&(n[t]=o[t])}(t,e,i),t[i].enabled=d}const Us={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>t*(2-t),easeInOutQuad:t=>t<.5?2*t*t:(4-2*t)*t-1,easeInCubic:t=>t*t*t,easeOutCubic:t=>--t*t*t+1,easeInOutCubic:t=>t<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1,easeInQuart:t=>t*t*t*t,easeOutQuart:t=>1- --t*t*t*t,easeInOutQuart:t=>t<.5?8*t*t*t*t:1-8*--t*t*t*t,easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>1+--t*t*t*t*t,easeInOutQuint:t=>t<.5?16*t*t*t*t*t:1+16*--t*t*t*t*t};function Ys(t,e){let i;Array.isArray(e)||(e=[e]);for(const o of t)if(o){i=o[e[0]];for(let t=1;t<e.length;t++)i&&(i=i[e[t]]);if(void 0!==i)break}return i}const Xs={black:"#000000",navy:"#000080",darkblue:"#00008B",mediumblue:"#0000CD",blue:"#0000FF",darkgreen:"#006400",green:"#008000",teal:"#008080",darkcyan:"#008B8B",deepskyblue:"#00BFFF",darkturquoise:"#00CED1",mediumspringgreen:"#00FA9A",lime:"#00FF00",springgreen:"#00FF7F",aqua:"#00FFFF",cyan:"#00FFFF",midnightblue:"#191970",dodgerblue:"#1E90FF",lightseagreen:"#20B2AA",forestgreen:"#228B22",seagreen:"#2E8B57",darkslategray:"#2F4F4F",limegreen:"#32CD32",mediumseagreen:"#3CB371",turquoise:"#40E0D0",royalblue:"#4169E1",steelblue:"#4682B4",darkslateblue:"#483D8B",mediumturquoise:"#48D1CC",indigo:"#4B0082",darkolivegreen:"#556B2F",cadetblue:"#5F9EA0",cornflowerblue:"#6495ED",mediumaquamarine:"#66CDAA",dimgray:"#696969",slateblue:"#6A5ACD",olivedrab:"#6B8E23",slategray:"#708090",lightslategray:"#778899",mediumslateblue:"#7B68EE",lawngreen:"#7CFC00",chartreuse:"#7FFF00",aquamarine:"#7FFFD4",maroon:"#800000",purple:"#800080",olive:"#808000",gray:"#808080",skyblue:"#87CEEB",lightskyblue:"#87CEFA",blueviolet:"#8A2BE2",darkred:"#8B0000",darkmagenta:"#8B008B",saddlebrown:"#8B4513",darkseagreen:"#8FBC8F",lightgreen:"#90EE90",mediumpurple:"#9370D8",darkviolet:"#9400D3",palegreen:"#98FB98",darkorchid:"#9932CC",yellowgreen:"#9ACD32",sienna:"#A0522D",brown:"#A52A2A",darkgray:"#A9A9A9",lightblue:"#ADD8E6",greenyellow:"#ADFF2F",paleturquoise:"#AFEEEE",lightsteelblue:"#B0C4DE",powderblue:"#B0E0E6",firebrick:"#B22222",darkgoldenrod:"#B8860B",mediumorchid:"#BA55D3",rosybrown:"#BC8F8F",darkkhaki:"#BDB76B",silver:"#C0C0C0",mediumvioletred:"#C71585",indianred:"#CD5C5C",peru:"#CD853F",chocolate:"#D2691E",tan:"#D2B48C",lightgrey:"#D3D3D3",palevioletred:"#D87093",thistle:"#D8BFD8",orchid:"#DA70D6",goldenrod:"#DAA520",crimson:"#DC143C",gainsboro:"#DCDCDC",plum:"#DDA0DD",burlywood:"#DEB887",lightcyan:"#E0FFFF",lavender:"#E6E6FA",darksalmon:"#E9967A",violet:"#EE82EE",palegoldenrod:"#EEE8AA",lightcoral:"#F08080",khaki:"#F0E68C",aliceblue:"#F0F8FF",honeydew:"#F0FFF0",azure:"#F0FFFF",sandybrown:"#F4A460",wheat:"#F5DEB3",beige:"#F5F5DC",whitesmoke:"#F5F5F5",mintcream:"#F5FFFA",ghostwhite:"#F8F8FF",salmon:"#FA8072",antiquewhite:"#FAEBD7",linen:"#FAF0E6",lightgoldenrodyellow:"#FAFAD2",oldlace:"#FDF5E6",red:"#FF0000",fuchsia:"#FF00FF",magenta:"#FF00FF",deeppink:"#FF1493",orangered:"#FF4500",tomato:"#FF6347",hotpink:"#FF69B4",coral:"#FF7F50",darkorange:"#FF8C00",lightsalmon:"#FFA07A",orange:"#FFA500",lightpink:"#FFB6C1",pink:"#FFC0CB",gold:"#FFD700",peachpuff:"#FFDAB9",navajowhite:"#FFDEAD",moccasin:"#FFE4B5",bisque:"#FFE4C4",mistyrose:"#FFE4E1",blanchedalmond:"#FFEBCD",papayawhip:"#FFEFD5",lavenderblush:"#FFF0F5",seashell:"#FFF5EE",cornsilk:"#FFF8DC",lemonchiffon:"#FFFACD",floralwhite:"#FFFAF0",snow:"#FFFAFA",yellow:"#FFFF00",lightyellow:"#FFFFE0",ivory:"#FFFFF0",white:"#FFFFFF"};let Gs=class{constructor(t=1){this.pixelRatio=t,this.generated=!1,this.centerCoordinates={x:144.5,y:144.5},this.r=289*.49,this.color={r:255,g:255,b:255,a:1},this.hueCircle=void 0,this.initialColor={r:255,g:255,b:255,a:1},this.previousColor=void 0,this.applied=!1,this.updateCallback=()=>{},this.closeCallback=()=>{},this._create()}insertTo(t){void 0!==this.hammer&&(this.hammer.destroy(),this.hammer=void 0),this.container=t,this.container.appendChild(this.frame),this._bindHammer(),this._setSize()}setUpdateCallback(t){if("function"!=typeof t)throw new Error("Function attempted to set as colorPicker update callback is not a function.");this.updateCallback=t}setCloseCallback(t){if("function"!=typeof t)throw new Error("Function attempted to set as colorPicker closing callback is not a function.");this.closeCallback=t}_isColorString(t){if("string"==typeof t)return Xs[t]}setColor(t,e=!0){if("none"===t)return;let i;const o=this._isColorString(t);if(void 0!==o&&(t=o),!0===ms(t)){if(!0===Hs(t)){const e=t.substr(4).substr(0,t.length-5).split(",");i={r:e[0],g:e[1],b:e[2],a:1}}else if(!0===Ws(t)){const e=t.substr(5).substr(0,t.length-6).split(",");i={r:e[0],g:e[1],b:e[2],a:e[3]}}else if(!0===Ls(t)){const e=Is(t);i={r:e.r,g:e.g,b:e.b,a:1}}}else if(t instanceof Object&&void 0!==t.r&&void 0!==t.g&&void 0!==t.b){const e=void 0!==t.a?t.a:"1.0";i={r:t.r,g:t.g,b:t.b,a:e}}if(void 0===i)throw new Error("Unknown color passed to the colorPicker. Supported are strings: rgb, hex, rgba. Object: rgb ({r:r,g:g,b:b,[a:a]}). Supplied: "+JSON.stringify(t));this._setColor(i,e)}show(){void 0!==this.closeCallback&&(this.closeCallback(),this.closeCallback=void 0),this.applied=!1,this.frame.style.display="block",this._generateHueCircle()}_hide(t=!0){!0===t&&(this.previousColor=Object.assign({},this.color)),!0===this.applied&&this.updateCallback(this.initialColor),this.frame.style.display="none",setTimeout(()=>{void 0!==this.closeCallback&&(this.closeCallback(),this.closeCallback=void 0)},0)}_save(){this.updateCallback(this.color),this.applied=!1,this._hide()}_apply(){this.applied=!0,this.updateCallback(this.color),this._updatePicker(this.color)}_loadLast(){void 0!==this.previousColor?this.setColor(this.previousColor,!1):alert("There is no last color to load...")}_setColor(t,e=!0){!0===e&&(this.initialColor=Object.assign({},t)),this.color=t;const i=zs(t.r,t.g,t.b),o=2*Math.PI,n=this.r*i.s,s=this.centerCoordinates.x+n*Math.sin(o*i.h),r=this.centerCoordinates.y+n*Math.cos(o*i.h);this.colorPickerSelector.style.left=s-.5*this.colorPickerSelector.clientWidth+"px",this.colorPickerSelector.style.top=r-.5*this.colorPickerSelector.clientHeight+"px",this._updatePicker(t)}_setOpacity(t){this.color.a=t/100,this._updatePicker(this.color)}_setBrightness(t){const e=zs(this.color.r,this.color.g,this.color.b);e.v=t/100;const i=As(e.h,e.s,e.v);i.a=this.color.a,this.color=i,this._updatePicker()}_updatePicker(t=this.color){const e=zs(t.r,t.g,t.b),i=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(i.webkitBackingStorePixelRatio||i.mozBackingStorePixelRatio||i.msBackingStorePixelRatio||i.oBackingStorePixelRatio||i.backingStorePixelRatio||1)),i.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);const o=this.colorPickerCanvas.clientWidth,n=this.colorPickerCanvas.clientHeight;i.clearRect(0,0,o,n),i.putImageData(this.hueCircle,0,0),i.fillStyle="rgba(0,0,0,"+(1-e.v)+")",i.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),i.fill(),this.brightnessRange.value=100*e.v,this.opacityRange.value=100*t.a,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}_setSize(){this.colorPickerCanvas.style.width="100%",this.colorPickerCanvas.style.height="100%",this.colorPickerCanvas.width=289*this.pixelRatio,this.colorPickerCanvas.height=289*this.pixelRatio}_create(){if(this.frame=document.createElement("div"),this.frame.className="vis-color-picker",this.colorPickerDiv=document.createElement("div"),this.colorPickerSelector=document.createElement("div"),this.colorPickerSelector.className="vis-selector",this.colorPickerDiv.appendChild(this.colorPickerSelector),this.colorPickerCanvas=document.createElement("canvas"),this.colorPickerDiv.appendChild(this.colorPickerCanvas),this.colorPickerCanvas.getContext){const t=this.colorPickerCanvas.getContext("2d");this.pixelRatio=(window.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1),this.colorPickerCanvas.getContext("2d").setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0)}else{const t=document.createElement("DIV");t.style.color="red",t.style.fontWeight="bold",t.style.padding="10px",t.innerText="Error: your browser does not support HTML canvas",this.colorPickerCanvas.appendChild(t)}this.colorPickerDiv.className="vis-color",this.opacityDiv=document.createElement("div"),this.opacityDiv.className="vis-opacity",this.brightnessDiv=document.createElement("div"),this.brightnessDiv.className="vis-brightness",this.arrowDiv=document.createElement("div"),this.arrowDiv.className="vis-arrow",this.opacityRange=document.createElement("input");try{this.opacityRange.type="range",this.opacityRange.min="0",this.opacityRange.max="100"}catch(t){}this.opacityRange.value="100",this.opacityRange.className="vis-range",this.brightnessRange=document.createElement("input");try{this.brightnessRange.type="range",this.brightnessRange.min="0",this.brightnessRange.max="100"}catch(t){}this.brightnessRange.value="100",this.brightnessRange.className="vis-range",this.opacityDiv.appendChild(this.opacityRange),this.brightnessDiv.appendChild(this.brightnessRange);const t=this;this.opacityRange.onchange=function(){t._setOpacity(this.value)},this.opacityRange.oninput=function(){t._setOpacity(this.value)},this.brightnessRange.onchange=function(){t._setBrightness(this.value)},this.brightnessRange.oninput=function(){t._setBrightness(this.value)},this.brightnessLabel=document.createElement("div"),this.brightnessLabel.className="vis-label vis-brightness",this.brightnessLabel.innerText="brightness:",this.opacityLabel=document.createElement("div"),this.opacityLabel.className="vis-label vis-opacity",this.opacityLabel.innerText="opacity:",this.newColorDiv=document.createElement("div"),this.newColorDiv.className="vis-new-color",this.newColorDiv.innerText="new",this.initialColorDiv=document.createElement("div"),this.initialColorDiv.className="vis-initial-color",this.initialColorDiv.innerText="initial",this.cancelButton=document.createElement("div"),this.cancelButton.className="vis-button vis-cancel",this.cancelButton.innerText="cancel",this.cancelButton.onclick=this._hide.bind(this,!1),this.applyButton=document.createElement("div"),this.applyButton.className="vis-button vis-apply",this.applyButton.innerText="apply",this.applyButton.onclick=this._apply.bind(this),this.saveButton=document.createElement("div"),this.saveButton.className="vis-button vis-save",this.saveButton.innerText="save",this.saveButton.onclick=this._save.bind(this),this.loadButton=document.createElement("div"),this.loadButton.className="vis-button vis-load",this.loadButton.innerText="load last",this.loadButton.onclick=this._loadLast.bind(this),this.frame.appendChild(this.colorPickerDiv),this.frame.appendChild(this.arrowDiv),this.frame.appendChild(this.brightnessLabel),this.frame.appendChild(this.brightnessDiv),this.frame.appendChild(this.opacityLabel),this.frame.appendChild(this.opacityDiv),this.frame.appendChild(this.newColorDiv),this.frame.appendChild(this.initialColorDiv),this.frame.appendChild(this.cancelButton),this.frame.appendChild(this.applyButton),this.frame.appendChild(this.saveButton),this.frame.appendChild(this.loadButton)}_bindHammer(){this.drag={},this.pinch={},this.hammer=new as(this.colorPickerCanvas),this.hammer.get("pinch").set({enable:!0}),this.hammer.on("hammer.input",t=>{t.isFirst&&this._moveSelector(t)}),this.hammer.on("tap",t=>{this._moveSelector(t)}),this.hammer.on("panstart",t=>{this._moveSelector(t)}),this.hammer.on("panmove",t=>{this._moveSelector(t)}),this.hammer.on("panend",t=>{this._moveSelector(t)})}_generateHueCircle(){if(!1===this.generated){const t=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1)),t.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);const e=this.colorPickerCanvas.clientWidth,i=this.colorPickerCanvas.clientHeight;let o,n,s,r;t.clearRect(0,0,e,i),this.centerCoordinates={x:.5*e,y:.5*i},this.r=.49*e;const a=2*Math.PI/360,h=1/360,d=1/this.r;let l;for(s=0;s<360;s++)for(r=0;r<this.r;r++)o=this.centerCoordinates.x+r*Math.sin(a*s),n=this.centerCoordinates.y+r*Math.cos(a*s),l=As(s*h,r*d,1),t.fillStyle="rgb("+l.r+","+l.g+","+l.b+")",t.fillRect(o-.5,n-.5,2,2);t.strokeStyle="rgba(0,0,0,1)",t.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),t.stroke(),this.hueCircle=t.getImageData(0,0,e,i)}this.generated=!0}_moveSelector(t){const e=this.colorPickerDiv.getBoundingClientRect(),i=t.center.x-e.left,o=t.center.y-e.top,n=.5*this.colorPickerDiv.clientHeight,s=.5*this.colorPickerDiv.clientWidth,r=i-s,a=o-n,h=Math.atan2(r,a),d=.98*Math.min(Math.sqrt(r*r+a*a),s),l=Math.cos(h)*d+n,c=Math.sin(h)*d+s;this.colorPickerSelector.style.top=l-.5*this.colorPickerSelector.clientHeight+"px",this.colorPickerSelector.style.left=c-.5*this.colorPickerSelector.clientWidth+"px";let u=h/(2*Math.PI);u=u<0?u+1:u;const p=d/this.r,f=zs(this.color.r,this.color.g,this.color.b);f.h=u,f.s=p;const g=As(f.h,f.s,f.v);g.a=this.color.a,this.color=g,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}};function Ks(...t){if(t.length<1)throw new TypeError("Invalid arguments.");if(1===t.length)return document.createTextNode(t[0]);{const e=document.createElement(t[0]);return e.appendChild(Ks(...t.slice(1))),e}}let Zs,Qs=!1;const $s="background: #FFeeee; color: #dd0000";const Js=hs,tr=Gs,er=class{constructor(t,e,i,o=1,n=()=>!1){this.parent=t,this.changedOptions=[],this.container=e,this.allowCreation=!1,this.hideOption=n,this.options={},this.initialized=!1,this.popupCounter=0,this.defaultOptions={enabled:!1,filter:!0,container:void 0,showButton:!0},Object.assign(this.options,this.defaultOptions),this.configureOptions=i,this.moduleOptions={},this.domElements=[],this.popupDiv={},this.popupLimit=5,this.popupHistory={},this.colorPicker=new Gs(o),this.wrapper=void 0}setOptions(t){if(void 0!==t){this.popupHistory={},this._removePopup();let e=!0;if("string"==typeof t)this.options.filter=t;else if(Array.isArray(t))this.options.filter=t.join();else if("object"==typeof t){if(null==t)throw new TypeError("options cannot be null");void 0!==t.container&&(this.options.container=t.container),void 0!==t.filter&&(this.options.filter=t.filter),void 0!==t.showButton&&(this.options.showButton=t.showButton),void 0!==t.enabled&&(e=t.enabled)}else"boolean"==typeof t?(this.options.filter=!0,e=t):"function"==typeof t&&(this.options.filter=t,e=!0);!1===this.options.filter&&(e=!1),this.options.enabled=e}this._clean()}setModuleOptions(t){this.moduleOptions=t,!0===this.options.enabled&&(this._clean(),void 0!==this.options.container&&(this.container=this.options.container),this._create())}_create(){this._clean(),this.changedOptions=[];const t=this.options.filter;let e=0,i=!1;for(const o in this.configureOptions)Object.prototype.hasOwnProperty.call(this.configureOptions,o)&&(this.allowCreation=!1,i=!1,"function"==typeof t?(i=t(o,[]),i=i||this._handleObject(this.configureOptions[o],[o],!0)):!0!==t&&-1===t.indexOf(o)||(i=!0),!1!==i&&(this.allowCreation=!0,e>0&&this._makeItem([]),this._makeHeader(o),this._handleObject(this.configureOptions[o],[o])),e++);this._makeButton(),this._push()}_push(){this.wrapper=document.createElement("div"),this.wrapper.className="vis-configuration-wrapper",this.container.appendChild(this.wrapper);for(let t=0;t<this.domElements.length;t++)this.wrapper.appendChild(this.domElements[t]);this._showPopupIfNeeded()}_clean(){for(let t=0;t<this.domElements.length;t++)this.wrapper.removeChild(this.domElements[t]);void 0!==this.wrapper&&(this.container.removeChild(this.wrapper),this.wrapper=void 0),this.domElements=[],this._removePopup()}_getValue(t){let e=this.moduleOptions;for(let i=0;i<t.length;i++){if(void 0===e[t[i]]){e=void 0;break}e=e[t[i]]}return e}_makeItem(t,...e){if(!0===this.allowCreation){const i=document.createElement("div");return i.className="vis-configuration vis-config-item vis-config-s"+t.length,e.forEach(t=>{i.appendChild(t)}),this.domElements.push(i),this.domElements.length}return 0}_makeHeader(t){const e=document.createElement("div");e.className="vis-configuration vis-config-header",e.innerText=t,this._makeItem([],e)}_makeLabel(t,e,i=!1){const o=document.createElement("div");if(o.className="vis-configuration vis-config-label vis-config-s"+e.length,!0===i){for(;o.firstChild;)o.removeChild(o.firstChild);o.appendChild(Ks("i","b",t))}else o.innerText=t+":";return o}_makeDropdown(t,e,i){const o=document.createElement("select");o.className="vis-configuration vis-config-select";let n=0;void 0!==e&&-1!==t.indexOf(e)&&(n=t.indexOf(e));for(let e=0;e<t.length;e++){const i=document.createElement("option");i.value=t[e],e===n&&(i.selected="selected"),i.innerText=t[e],o.appendChild(i)}const s=this;o.onchange=function(){s._update(this.value,i)};const r=this._makeLabel(i[i.length-1],i);this._makeItem(i,r,o)}_makeRange(t,e,i){const o=t[0],n=t[1],s=t[2],r=t[3],a=document.createElement("input");a.className="vis-configuration vis-config-range";try{a.type="range",a.min=n,a.max=s}catch(t){}a.step=r;let h="",d=0;if(void 0!==e){const t=1.2;e<0&&e*t<n?(a.min=Math.ceil(e*t),d=a.min,h="range increased"):e/t<n&&(a.min=Math.ceil(e/t),d=a.min,h="range increased"),e*t>s&&1!==s&&(a.max=Math.ceil(e*t),d=a.max,h="range increased"),a.value=e}else a.value=o;const l=document.createElement("input");l.className="vis-configuration vis-config-rangeinput",l.value=a.value;const c=this;a.onchange=function(){l.value=this.value,c._update(Number(this.value),i)},a.oninput=function(){l.value=this.value};const u=this._makeLabel(i[i.length-1],i),p=this._makeItem(i,u,a,l);""!==h&&this.popupHistory[p]!==d&&(this.popupHistory[p]=d,this._setupPopup(h,p))}_makeButton(){if(!0===this.options.showButton){const t=document.createElement("div");t.className="vis-configuration vis-config-button",t.innerText="generate options",t.onclick=()=>{this._printOptions()},t.onmouseover=()=>{t.className="vis-configuration vis-config-button hover"},t.onmouseout=()=>{t.className="vis-configuration vis-config-button"},this.optionsContainer=document.createElement("div"),this.optionsContainer.className="vis-configuration vis-config-option-container",this.domElements.push(this.optionsContainer),this.domElements.push(t)}}_setupPopup(t,e){if(!0===this.initialized&&!0===this.allowCreation&&this.popupCounter<this.popupLimit){const i=document.createElement("div");i.id="vis-configuration-popup",i.className="vis-configuration-popup",i.innerText=t,i.onclick=()=>{this._removePopup()},this.popupCounter+=1,this.popupDiv={html:i,index:e}}}_removePopup(){void 0!==this.popupDiv.html&&(this.popupDiv.html.parentNode.removeChild(this.popupDiv.html),clearTimeout(this.popupDiv.hideTimeout),clearTimeout(this.popupDiv.deleteTimeout),this.popupDiv={})}_showPopupIfNeeded(){if(void 0!==this.popupDiv.html){const t=this.domElements[this.popupDiv.index].getBoundingClientRect();this.popupDiv.html.style.left=t.left+"px",this.popupDiv.html.style.top=t.top-30+"px",document.body.appendChild(this.popupDiv.html),this.popupDiv.hideTimeout=setTimeout(()=>{this.popupDiv.html.style.opacity=0},1500),this.popupDiv.deleteTimeout=setTimeout(()=>{this._removePopup()},1800)}}_makeCheckbox(t,e,i){const o=document.createElement("input");o.type="checkbox",o.className="vis-configuration vis-config-checkbox",o.checked=t,void 0!==e&&(o.checked=e,e!==t&&("object"==typeof t?e!==t.enabled&&this.changedOptions.push({path:i,value:e}):this.changedOptions.push({path:i,value:e})));const n=this;o.onchange=function(){n._update(this.checked,i)};const s=this._makeLabel(i[i.length-1],i);this._makeItem(i,s,o)}_makeTextInput(t,e,i){const o=document.createElement("input");o.type="text",o.className="vis-configuration vis-config-text",o.value=e,e!==t&&this.changedOptions.push({path:i,value:e});const n=this;o.onchange=function(){n._update(this.value,i)};const s=this._makeLabel(i[i.length-1],i);this._makeItem(i,s,o)}_makeColorField(t,e,i){const o=t[1],n=document.createElement("div");"none"!==(e=void 0===e?o:e)?(n.className="vis-configuration vis-config-colorBlock",n.style.backgroundColor=e):n.className="vis-configuration vis-config-colorBlock none",e=void 0===e?o:e,n.onclick=()=>{this._showColorPicker(e,n,i)};const s=this._makeLabel(i[i.length-1],i);this._makeItem(i,s,n)}_showColorPicker(t,e,i){e.onclick=function(){},this.colorPicker.insertTo(e),this.colorPicker.show(),this.colorPicker.setColor(t),this.colorPicker.setUpdateCallback(t=>{const o="rgba("+t.r+","+t.g+","+t.b+","+t.a+")";e.style.backgroundColor=o,this._update(o,i)}),this.colorPicker.setCloseCallback(()=>{e.onclick=()=>{this._showColorPicker(t,e,i)}})}_handleObject(t,e=[],i=!1){let o=!1;const n=this.options.filter;let s=!1;for(const r in t)if(Object.prototype.hasOwnProperty.call(t,r)){o=!0;const a=t[r],h=Os(e,r);if("function"==typeof n&&(o=n(r,e),!1===o&&!Array.isArray(a)&&"string"!=typeof a&&"boolean"!=typeof a&&a instanceof Object&&(this.allowCreation=!1,o=this._handleObject(a,h,!0),this.allowCreation=!1===i)),!1!==o){s=!0;const t=this._getValue(h);if(Array.isArray(a))this._handleArray(a,t,h);else if("string"==typeof a)this._makeTextInput(a,t,h);else if("boolean"==typeof a)this._makeCheckbox(a,t,h);else if(a instanceof Object){if(!this.hideOption(e,r,this.moduleOptions))if(void 0!==a.enabled){const t=Os(h,"enabled"),e=this._getValue(t);if(!0===e){const t=this._makeLabel(r,h,!0);this._makeItem(h,t),s=this._handleObject(a,h)||s}else this._makeCheckbox(a,e,h)}else{const t=this._makeLabel(r,h,!0);this._makeItem(h,t),s=this._handleObject(a,h)||s}}else console.error("dont know how to handle",a,r,h)}}return s}_handleArray(t,e,i){"string"==typeof t[0]&&"color"===t[0]?(this._makeColorField(t,e,i),t[1]!==e&&this.changedOptions.push({path:i,value:e})):"string"==typeof t[0]?(this._makeDropdown(t,e,i),t[0]!==e&&this.changedOptions.push({path:i,value:e})):"number"==typeof t[0]&&(this._makeRange(t,e,i),t[0]!==e&&this.changedOptions.push({path:i,value:Number(e)}))}_update(t,e){const i=this._constructOptions(t,e);this.parent.body&&this.parent.body.emitter&&this.parent.body.emitter.emit&&this.parent.body.emitter.emit("configChange",i),this.initialized=!0,this.parent.setOptions(i)}_constructOptions(t,e,i={}){let o=i;t="false"!==(t="true"===t||t)&&t;for(let i=0;i<e.length;i++)"global"!==e[i]&&(void 0===o[e[i]]&&(o[e[i]]={}),i!==e.length-1?o=o[e[i]]:o[e[i]]=t);return i}_printOptions(){const t=this.getOptions();for(;this.optionsContainer.firstChild;)this.optionsContainer.removeChild(this.optionsContainer.firstChild);this.optionsContainer.appendChild(Ks("pre","const options = "+JSON.stringify(t,null,2)))}getOptions(){const t={};for(let e=0;e<this.changedOptions.length;e++)this._constructOptions(this.changedOptions[e].value,this.changedOptions[e].path,t);return t}},ir=as,or=class{constructor(t,e){this.container=t,this.overflowMethod=e||"cap",this.x=0,this.y=0,this.padding=5,this.hidden=!1,this.frame=document.createElement("div"),this.frame.className="vis-tooltip",this.container.appendChild(this.frame)}setPosition(t,e){this.x=parseInt(t),this.y=parseInt(e)}setText(t){if(t instanceof Element){for(;this.frame.firstChild;)this.frame.removeChild(this.frame.firstChild);this.frame.appendChild(t)}else this.frame.innerText=t}show(t){if(void 0===t&&(t=!0),!0===t){const t=this.frame.clientHeight,e=this.frame.clientWidth,i=this.frame.parentNode.clientHeight,o=this.frame.parentNode.clientWidth;let n=0,s=0;if("flip"==this.overflowMethod){let i=!1,r=!0;this.y-t<this.padding&&(r=!1),this.x+e>o-this.padding&&(i=!0),n=i?this.x-e:this.x,s=r?this.y-t:this.y}else s=this.y-t,s+t+this.padding>i&&(s=i-t-this.padding),s<this.padding&&(s=this.padding),n=this.x,n+e+this.padding>o&&(n=o-e-this.padding),n<this.padding&&(n=this.padding);this.frame.style.left=n+"px",this.frame.style.top=s+"px",this.frame.style.visibility="visible",this.hidden=!1}else this.hide()}hide(){this.hidden=!0,this.frame.style.left="0",this.frame.style.top="0",this.frame.style.visibility="hidden"}destroy(){this.frame.parentNode.removeChild(this.frame)}},nr=$s,sr=class t{static validate(e,i,o){Qs=!1,Zs=i;let n=i;return void 0!==o&&(n=i[o]),t.parse(e,n,[]),Qs}static parse(e,i,o){for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.check(n,e,i,o)}static check(e,i,o,n){if(void 0===o[e]&&void 0===o.__any__)return void t.getSuggestion(e,o,n);let s=e,r=!0;void 0===o[e]&&void 0!==o.__any__&&(s="__any__",r="object"===t.getType(i[e]));let a=o[s];r&&void 0!==a.__type__&&(a=a.__type__),t.checkFields(e,i,o,s,a,n)}static checkFields(e,i,o,n,s,r){const a=function(i){console.error("%c"+i+t.printLocation(r,e),$s)},h=t.getType(i[e]),d=s[h];void 0!==d?"array"===t.getType(d)&&-1===d.indexOf(i[e])?(a('Invalid option detected in "'+e+'". Allowed values are:'+t.print(d)+' not "'+i[e]+'". '),Qs=!0):"object"===h&&"__any__"!==n&&(r=Os(r,e),t.parse(i[e],o[n],r)):void 0===s.any&&(a('Invalid type received for "'+e+'". Expected: '+t.print(Object.keys(s))+". Received ["+h+'] "'+i[e]+'"'),Qs=!0)}static getType(t){const e=typeof t;return"object"===e?null===t?"null":t instanceof Boolean?"boolean":t instanceof Number?"number":t instanceof String?"string":Array.isArray(t)?"array":t instanceof Date?"date":void 0!==t.nodeType?"dom":!0===t._isAMomentObject?"moment":"object":"number"===e?"number":"boolean"===e?"boolean":"string"===e?"string":void 0===e?"undefined":e}static getSuggestion(e,i,o){const n=t.findInOptions(e,i,o,!1),s=t.findInOptions(e,Zs,[],!0);let r;r=void 0!==n.indexMatch?" in "+t.printLocation(n.path,e,"")+'Perhaps it was incomplete? Did you mean: "'+n.indexMatch+'"?\n\n':s.distance<=4&&n.distance>s.distance?" in "+t.printLocation(n.path,e,"")+"Perhaps it was misplaced? Matching option found at: "+t.printLocation(s.path,s.closestMatch,""):n.distance<=8?'. Did you mean "'+n.closestMatch+'"?'+t.printLocation(n.path,e):". Did you mean one of these: "+t.print(Object.keys(i))+t.printLocation(o,e),console.error('%cUnknown option detected: "'+e+'"'+r,$s),Qs=!0}static findInOptions(e,i,o,n=!1){let s=1e9,r="",a=[];const h=e.toLowerCase();let d;for(const l in i){let c;if(void 0!==i[l].__type__&&!0===n){const n=t.findInOptions(e,i[l],Os(o,l));s>n.distance&&(r=n.closestMatch,a=n.path,s=n.distance,d=n.indexMatch)}else-1!==l.toLowerCase().indexOf(h)&&(d=l),c=t.levenshteinDistance(e,l),s>c&&(r=l,a=Cs(o),s=c)}return{closestMatch:r,path:a,distance:s,indexMatch:d}}static printLocation(t,e,i="Problem value found at: \n"){let o="\n\n"+i+"options = {\n";for(let e=0;e<t.length;e++){for(let t=0;t<e+1;t++)o+="  ";o+=t[e]+": {\n"}for(let e=0;e<t.length+1;e++)o+="  ";o+=e+"\n";for(let e=0;e<t.length+1;e++){for(let i=0;i<t.length-e;i++)o+="  ";o+="}\n"}return o+"\n\n"}static print(t){return JSON.stringify(t).replace(/(")|(\[)|(\])|(,"__type__")/g,"").replace(/(,)/g,", ")}static levenshteinDistance(t,e){if(0===t.length)return e.length;if(0===e.length)return t.length;const i=[];let o,n;for(o=0;o<=e.length;o++)i[o]=[o];for(n=0;n<=t.length;n++)i[0][n]=n;for(o=1;o<=e.length;o++)for(n=1;n<=t.length;n++)e.charAt(o-1)==t.charAt(n-1)?i[o][n]=i[o-1][n-1]:i[o][n]=Math.min(i[o-1][n-1]+1,Math.min(i[o][n-1]+1,i[o-1][n]+1));return i[e.length][t.length]}};var rr,ar,hr,dr,lr,cr,ur,pr,fr,gr,mr,vr,yr,br,wr=Object.freeze({__proto__:null,Activator:Js,Alea:rs,ColorPicker:tr,Configurator:er,DELETE:ts,HSVToHex:js,HSVToRGB:As,Hammer:ir,Popup:or,RGBToHSV:zs,RGBToHex:Ns,VALIDATOR_PRINT_STYLE:nr,Validator:sr,addClassName:function(t,e){let i=t.className.split(" ");const o=e.split(" ");i=i.concat(o.filter(function(t){return!i.includes(t)})),t.className=i.join(" ")},addCssText:function(t,e){const i=Fs(e);for(const[e,o]of Object.entries(i))t.style.setProperty(e,o)},binarySearchCustom:function(t,e,i,o){let n=0,s=0,r=t.length-1;for(;s<=r&&n<1e4;){const a=Math.floor((s+r)/2),h=t[a],d=e(void 0===o?h[i]:h[i][o]);if(0==d)return a;-1==d?s=a+1:r=a-1,n++}return-1},binarySearchValue:function(t,e,i,o,n){let s,r,a,h,d=0,l=0,c=t.length-1;for(n=null!=n?n:function(t,e){return t==e?0:t<e?-1:1};l<=c&&d<1e4;){if(h=Math.floor(.5*(c+l)),s=t[Math.max(0,h-1)][i],r=t[h][i],a=t[Math.min(t.length-1,h+1)][i],0==n(r,e))return h;if(n(s,e)<0&&n(r,e)>0)return"before"==o?Math.max(0,h-1):h;if(n(r,e)<0&&n(a,e)>0)return"before"==o?h:Math.min(t.length-1,h+1);n(r,e)<0?l=h+1:c=h-1,d++}return-1},bridgeObject:qs,copyAndExtendArray:Os,copyArray:Cs,deepExtend:Es,deepObjectAssign:is,easingFunctions:Us,equalArray:function(t,e){if(t.length!==e.length)return!1;for(let i=0,o=t.length;i<o;i++)if(t[i]!=e[i])return!1;return!0},extend:ws,fillIfDefined:bs,forEach:Ts,getAbsoluteLeft:ks,getAbsoluteRight:function(t){return t.getBoundingClientRect().right},getAbsoluteTop:Ss,getScrollBarWidth:function(){const t=document.createElement("p");t.style.width="100%",t.style.height="200px";const e=document.createElement("div");e.style.position="absolute",e.style.top="0px",e.style.left="0px",e.style.visibility="hidden",e.style.width="200px",e.style.height="150px",e.style.overflow="hidden",e.appendChild(t),document.body.appendChild(e);const i=t.offsetWidth;e.style.overflow="scroll";let o=t.offsetWidth;return i==o&&(o=e.clientWidth),document.body.removeChild(e),i-o},getTarget:function(t=window.event){let e=null;return t&&(t.target?e=t.target:t.srcElement&&(e=t.srcElement)),e instanceof Element&&(null==e.nodeType||3!=e.nodeType||(e=e.parentNode,e instanceof Element))?e:null},getType:function(t){const e=typeof t;return"object"===e?null===t?"null":t instanceof Boolean?"Boolean":t instanceof Number?"Number":t instanceof String?"String":Array.isArray(t)?"Array":t instanceof Date?"Date":"Object":"number"===e?"Number":"boolean"===e?"Boolean":"string"===e?"String":void 0===e?"undefined":e},hasParent:function(t,e){let i=t;for(;i;){if(i===e)return!0;if(!i.parentNode)return!1;i=i.parentNode}return!1},hexToHSV:Rs,hexToRGB:Is,insertSort:function(t,e){for(let i=0;i<t.length;i++){const o=t[i];let n;for(n=i;n>0&&e(o,t[n-1])<0;n--)t[n]=t[n-1];t[n]=o}return t},isDate:function(t){if(t instanceof Date)return!0;if(ms(t)){if(ds.exec(t))return!0;if(!isNaN(Date.parse(t)))return!0}return!1},isNumber:fs,isObject:vs,isString:ms,isValidHex:Ls,isValidRGB:Hs,isValidRGBA:Ws,mergeOptions:Vs,option:Ms,overrideOpacity:Ps,parseColor:Bs,preventDefault:function(t){t||(t=window.event),t&&(t.preventDefault?t.preventDefault():t.returnValue=!1)},pureDeepObjectAssign:es,recursiveDOMDelete:gs,removeClassName:function(t,e){let i=t.className.split(" ");const o=e.split(" ");i=i.filter(function(t){return!o.includes(t)}),t.className=i.join(" ")},removeCssText:function(t,e){const i=Fs(e);for(const e of Object.keys(i))t.style.removeProperty(e)},selectiveBridgeObject:function(t,e){if(null!==e&&"object"==typeof e){const i=Object.create(e);for(let o=0;o<t.length;o++)Object.prototype.hasOwnProperty.call(e,t[o])&&"object"==typeof e[t[o]]&&(i[t[o]]=qs(e[t[o]]));return i}return null},selectiveDeepExtend:_s,selectiveExtend:function(t,e,...i){if(!Array.isArray(t))throw new Error("Array with property names expected as first argument");for(const o of i)for(let i=0;i<t.length;i++){const n=t[i];o&&Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e},selectiveNotDeepExtend:xs,throttle:function(t){let e=!1;return()=>{e||(e=!0,requestAnimationFrame(()=>{e=!1,t()}))}},toArray:Ds,topMost:Ys,updateProperty:function(t,e,i){return t[e]!==i&&(t[e]=i,!0)}}),_r={};function xr(){if(rr)return _r;rr=1;var t=N(),e=ni(),i=ri(),o=si(),n=Ot(),s=yi();return _r.f=t&&!e?Object.defineProperties:function(t,e){o(t);for(var r,a=n(e),h=s(e),d=h.length,l=0;d>l;)i.f(t,r=h[l++],a[r]);return t},_r}function Er(){return hr?ar:(hr=1,ar=St()("document","documentElement"))}function Or(){if(lr)return dr;lr=1;var t=me(),e=be(),i=t("keys");return dr=function(t){return i[t]||(i[t]=e(t))}}function Cr(){if(ur)return cr;ur=1;var t,e=si(),i=xr(),o=vi(),n=gi(),s=Er(),r=Ee(),a="prototype",h="script",d=Or()("IE_PROTO"),l=function(){},c=function(t){return"<"+h+">"+t+"</"+h+">"},u=function(t){t.write(c("")),t.close();var e=t.parentWindow.Object;return t=null,e},p=function(){try{t=new ActiveXObject("htmlfile")}catch(t){}var e,i,n;p="undefined"!=typeof document?document.domain&&t?u(t):(i=r("iframe"),n="java"+h+":",i.style.display="none",s.appendChild(i),i.src=String(n),(e=i.contentWindow.document).open(),e.write(c("document.F=Object")),e.close(),e.F):u(t);for(var d=o.length;d--;)delete p[a][o[d]];return p()};return n[d]=!0,cr=Object.create||function(t,o){var n;return null!==t?(l[a]=e(t),n=new l,l[a]=null,n[d]=t):n=p(),void 0===o?n:i.f(n,o)}}function kr(){if(gr)return fr;gr=1,pr||(pr=1,hi()({target:"Object",stat:!0,sham:!N()},{create:Cr()}));var t=kt().Object;return fr=function(e,i){return t.create(e,i)}}function Sr(){return vr?mr:(vr=1,mr=kr())}var Tr,Dr,Mr,Ir,Pr,Nr,Br,zr,Fr,Ar,jr,Rr=i(br?yr:(br=1,yr=Sr())),Lr={};function Hr(){if(Dr)return Tr;Dr=1;var t=_();return Tr=function(e,i){var o=[][e];return!!o&&t(function(){o.call(null,i||function(){return 1},1)})}}function Wr(){return Pr?Ir:(Pr=1,function(){if(Mr)return Lr;Mr=1;var t=hi(),e=k(),i=fi().indexOf,o=Hr(),n=e([].indexOf),s=!!n&&1/n([1],1,-0)<0;t({target:"Array",proto:!0,forced:s||!o("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return s?n(this,t,e)||0:i(this,t,e)}})}(),Ir=Ji()("Array","indexOf"))}function qr(){if(Br)return Nr;Br=1;var t=Tt(),e=Wr(),i=Array.prototype;return Nr=function(o){var n=o.indexOf;return o===i||t(i,o)&&n===i.indexOf?e:n},Nr}function Vr(){return Fr?zr:(Fr=1,zr=qr())}var Ur,Yr,Xr,Gr,Kr,Zr=i(jr?Ar:(jr=1,Ar=Vr())),Qr={};function $r(){return Yr?Ur:(Yr=1,Ur=function(){})}function Jr(){return Kr?Gr:(Kr=1,function(){if(Xr)return Qr;Xr=1;var t=hi(),e=fi().includes,i=_(),o=$r();t({target:"Array",proto:!0,forced:i(function(){return!Array(1).includes()})},{includes:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0)}}),o("includes")}(),Gr=Ji()("Array","includes"))}var ta,ea,ia,oa,na,sa,ra,aa,ha,da,la,ca,ua,pa,fa,ga,ma,va,ya,ba,wa,_a={};function xa(){if(ea)return ta;ea=1;var t=Ct(),e=C(),i=we()("match");return ta=function(o){var n;return t(o)&&(void 0!==(n=o[i])?!!n:"RegExp"===e(o))},ta}function Ea(){if(oa)return ia;oa=1;var t=xa(),e=TypeError;return ia=function(i){if(t(i))throw new e("The method doesn't accept regular expressions");return i},ia}function Oa(){if(sa)return na;sa=1;var t={};return t[we()("toStringTag")]="z",na="[object z]"===String(t)}function Ca(){if(aa)return ra;aa=1;var t=Oa(),e=S(),i=C(),o=we()("toStringTag"),n=Object,s="Arguments"===i(function(){return arguments}());return ra=t?i:function(t){var r,a,h;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(a=function(t,e){try{return t[e]}catch(t){}}(r=n(t),o))?a:s?i(r):"Object"===(h=i(r))&&e(r.callee)?"Arguments":h},ra}function ka(){if(da)return ha;da=1;var t=Ca(),e=String;return ha=function(i){if("Symbol"===t(i))throw new TypeError("Cannot convert a Symbol value to a string");return e(i)}}function Sa(){if(ca)return la;ca=1;var t=we()("match");return la=function(e){var i=/./;try{"/./"[e](i)}catch(o){try{return i[t]=!1,"/./"[e](i)}catch(t){}}return!1}}function Ta(){return fa?pa:(fa=1,function(){if(ua)return _a;ua=1;var t=hi(),e=O(),i=Ea(),o=Et(),n=ka(),s=Sa(),r=e("".indexOf);t({target:"String",proto:!0,forced:!s("includes")},{includes:function(t){return!!~r(n(o(this)),n(i(t)),arguments.length>1?arguments[1]:void 0)}})}(),pa=Ji()("String","includes"))}function Da(){if(ma)return ga;ma=1;var t=Tt(),e=Jr(),i=Ta(),o=Array.prototype,n=String.prototype;return ga=function(s){var r=s.includes;return s===o||t(o,s)&&r===o.includes?e:"string"==typeof s||s===n||t(n,s)&&r===n.includes?i:r},ga}function Ma(){return ya?va:(ya=1,va=Da())}var Ia,Pa,Na,Ba,za,Fa,Aa,ja,Ra,La,Ha,Wa,qa,Va,Ua,Ya,Xa,Ga,Ka,Za,Qa,$a,Ja,th,eh,ih,oh,nh,sh,rh=i(wa?ba:(wa=1,ba=Ma())),ah={};function hh(){if(Pa)return Ia;Pa=1;var t=C();return Ia=Array.isArray||function(e){return"Array"===t(e)}}function dh(){if(Ba)return Na;Ba=1;var t=N(),e=hh(),i=TypeError,o=Object.getOwnPropertyDescriptor,n=t&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();return Na=n?function(t,n){if(e(t)&&!o(t,"length").writable)throw new i("Cannot set read only .length");return t.length=n}:function(t,e){return t.length=e}}function lh(){if(Fa)return za;Fa=1;var t=TypeError;return za=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e},za}function ch(){if(ja)return Aa;ja=1;var t=O(),e=S(),i=ge(),o=t(Function.toString);return e(i.inspectSource)||(i.inspectSource=function(t){return o(t)}),Aa=i.inspectSource}function uh(){if(La)return Ra;La=1;var t=O(),e=_(),i=S(),o=Ca(),n=St(),s=ch(),r=function(){},a=n("Reflect","construct"),h=/^\s*(?:class|function)\b/,d=t(h.exec),l=!h.test(r),c=function(t){if(!i(t))return!1;try{return a(r,[],t),!0}catch(t){return!1}},u=function(t){if(!i(t))return!1;switch(o(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return l||!!d(h,s(t))}catch(t){return!0}};return u.sham=!0,Ra=!a||e(function(){var t;return c(c.call)||!c(Object)||!c(function(){t=!0})||t})?u:c}function ph(){if(Wa)return Ha;Wa=1;var t=hh(),e=uh(),i=Ct(),o=we()("species"),n=Array;return Ha=function(s){var r;return t(s)&&(r=s.constructor,(e(r)&&(r===n||t(r.prototype))||i(r)&&null===(r=r[o]))&&(r=void 0)),void 0===r?n:r}}function fh(){if(Va)return qa;Va=1;var t=ph();return qa=function(e,i){return new(t(e))(0===i?0:i)}}function gh(){if(Ya)return Ua;Ya=1;var t=N(),e=ri(),i=wt();return Ua=function(o,n,s){t?e.f(o,n,i(0,s)):o[n]=s},Ua}function mh(){if(Ga)return Xa;Ga=1;var t=Bt(),e=TypeError;return Xa=function(i,o){if(!delete i[o])throw new e("Cannot delete property "+t(o)+" of "+t(i))}}function vh(){if(Za)return Ka;Za=1;var t=_(),e=we(),i=Mt(),o=e("species");return Ka=function(e){return i>=51||!t(function(){var t=[];return(t.constructor={})[o]=function(){return{foo:1}},1!==t[e](Boolean).foo})},Ka}function yh(){return Ja?$a:(Ja=1,function(){if(Qa)return ah;Qa=1;var t=hi(),e=ve(),i=ci(),o=li(),n=pi(),s=dh(),r=lh(),a=fh(),h=gh(),d=mh(),l=vh()("splice"),c=Math.max,u=Math.min;t({target:"Array",proto:!0,forced:!l},{splice:function(t,l){var p,f,g,m,v,y,b=e(this),w=n(b),_=i(t,w),x=arguments.length;for(0===x?p=f=0:1===x?(p=0,f=w-_):(p=x-2,f=u(c(o(l),0),w-_)),r(w+p-f),g=a(b,f),m=0;m<f;m++)(v=_+m)in b&&h(g,m,b[v]);if(g.length=f,p<f){for(m=_;m<w-f;m++)y=m+p,(v=m+f)in b?b[y]=b[v]:d(b,y);for(m=w;m>w-f+p;m--)d(b,m-1)}else if(p>f)for(m=w-f;m>_;m--)y=m+p-1,(v=m+f-1)in b?b[y]=b[v]:d(b,y);for(m=0;m<p;m++)b[m+_]=arguments[m+2];return s(b,w-f+p),g}})}(),$a=Ji()("Array","splice"))}function bh(){if(eh)return th;eh=1;var t=Tt(),e=yh(),i=Array.prototype;return th=function(o){var n=o.splice;return o===i||t(i,o)&&n===i.splice?e:n},th}function wh(){return oh?ih:(oh=1,ih=bh())}var _h,xh,Eh,Oh,Ch,kh,Sh,Th=i(sh?nh:(sh=1,nh=wh()));function Dh(){return Eh?xh:(Eh=1,_h||(_h=1,hi()({target:"Array",stat:!0},{isArray:hh()})),xh=kt().Array.isArray)}function Mh(){return Ch?Oh:(Ch=1,Oh=Dh())}var Ih,Ph,Nh,Bh,zh,Fh,Ah,jh,Rh,Lh,Hh,Wh,qh,Vh=i(Sh?kh:(Sh=1,kh=Mh())),Uh={};function Yh(){if(Ph)return Ih;Ph=1;var t=Se(),e=O(),i=_t(),o=ve(),n=pi(),s=fh(),r=e([].push),a=function(e){var a=1===e,h=2===e,d=3===e,l=4===e,c=6===e,u=7===e,p=5===e||c;return function(f,g,m,v){for(var y,b,w=o(f),_=i(w),x=n(_),E=t(g,m),O=0,C=v||s,k=a?C(f,x):h||u?C(f,0):void 0;x>O;O++)if((p||O in _)&&(b=E(y=_[O],O,w),e))if(a)k[O]=b;else if(b)switch(e){case 3:return!0;case 5:return y;case 6:return O;case 2:r(k,y)}else switch(e){case 4:return!1;case 7:r(k,y)}return c?-1:d||l?l:k}};return Ih={forEach:a(0),map:a(1),filter:a(2),some:a(3),every:a(4),find:a(5),findIndex:a(6),filterReject:a(7)}}function Xh(){if(Bh)return Nh;Bh=1;var t=Yh().forEach,e=Hr()("forEach");return Nh=e?[].forEach:function(e){return t(this,e,arguments.length>1?arguments[1]:void 0)},Nh}function Gh(){return Ah?Fh:(Ah=1,function(){if(zh)return Uh;zh=1;var t=hi(),e=Xh();t({target:"Array",proto:!0,forced:[].forEach!==e},{forEach:e})}(),Fh=Ji()("Array","forEach"))}function Kh(){return Rh?jh:(Rh=1,jh=Gh())}function Zh(){if(Hh)return Lh;Hh=1;var t=Ca(),e=ye(),i=Tt(),o=Kh(),n=Array.prototype,s={DOMTokenList:!0,NodeList:!0};return Lh=function(r){var a=r.forEach;return r===n||i(n,r)&&a===n.forEach||e(s,t(r))?o:a},Lh}var Qh=i(qh?Wh:(qh=1,Wh=Zh()));function $h(t){return od=t,function(){var t={};nd=0,void(sd=od.charAt(0)),md(),"strict"===rd&&(t.strict=!0,md());"graph"!==rd&&"digraph"!==rd||(t.type=rd,md());ad===ed.IDENTIFIER&&(t.id=rd,md());if("{"!=rd)throw xd("Angle bracket { expected");if(md(),vd(t),"}"!=rd)throw xd("Angle bracket } expected");if(md(),""!==rd)throw xd("End of file expected");return md(),delete t.node,delete t.edge,delete t.graph,t}()}var Jh={fontsize:"font.size",fontcolor:"font.color",labelfontcolor:"font.color",fontname:"font.face",color:["color.border","color.background"],fillcolor:"color.background",tooltip:"title",labeltooltip:"title"},td=Rr(Jh);td.color="color.color",td.style="dashes";var ed={NULL:0,DELIMITER:1,IDENTIFIER:2,UNKNOWN:3},id={"{":!0,"}":!0,"[":!0,"]":!0,";":!0,"=":!0,",":!0,"->":!0,"--":!0},od="",nd=0,sd="",rd="",ad=ed.NULL;function hd(){nd++,sd=od.charAt(nd)}function dd(){return od.charAt(nd+1)}function ld(t){var e=t.charCodeAt(0);return e<47?35===e||46===e:e<59?e>47:e<91?e>64:e<96?95===e:e<123&&e>96}function cd(t,e){if(t||(t={}),e)for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function ud(t,e,i){for(var o=e.split("."),n=t;o.length;){var s=o.shift();o.length?(n[s]||(n[s]={}),n=n[s]):n[s]=i}}function pd(t,e){for(var i,o,n=null,s=[t],r=t;r.parent;)s.push(r.parent),r=r.parent;if(r.nodes)for(i=0,o=r.nodes.length;i<o;i++)if(e.id===r.nodes[i].id){n=r.nodes[i];break}for(n||(n={id:e.id},t.node&&(n.attr=cd(n.attr,t.node))),i=s.length-1;i>=0;i--){var a,h=s[i];h.nodes||(h.nodes=[]),-1===Zr(a=h.nodes).call(a,n)&&h.nodes.push(n)}e.attr&&(n.attr=cd(n.attr,e.attr))}function fd(t,e){if(t.edges||(t.edges=[]),t.edges.push(e),t.edge){var i=cd({},t.edge);e.attr=cd(i,e.attr)}}function gd(t,e,i,o,n){var s={from:e,to:i,type:o};return t.edge&&(s.attr=cd({},t.edge)),s.attr=cd(s.attr||{},n),null!=n&&n.hasOwnProperty("arrows")&&null!=n.arrows&&(s.arrows={to:{enabled:!0,type:n.arrows.type}},n.arrows=null),s}function md(){for(ad=ed.NULL,rd="";" "===sd||"\t"===sd||"\n"===sd||"\r"===sd;)hd();do{var t=!1;if("#"===sd){for(var e=nd-1;" "===od.charAt(e)||"\t"===od.charAt(e);)e--;if("\n"===od.charAt(e)||""===od.charAt(e)){for(;""!=sd&&"\n"!=sd;)hd();t=!0}}if("/"===sd&&"/"===dd()){for(;""!=sd&&"\n"!=sd;)hd();t=!0}if("/"===sd&&"*"===dd()){for(;""!=sd;){if("*"===sd&&"/"===dd()){hd(),hd();break}hd()}t=!0}for(;" "===sd||"\t"===sd||"\n"===sd||"\r"===sd;)hd()}while(t);if(""!==sd){var i=sd+dd();if(id[i])return ad=ed.DELIMITER,rd=i,hd(),void hd();if(id[sd])return ad=ed.DELIMITER,rd=sd,void hd();if(ld(sd)||"-"===sd){for(rd+=sd,hd();ld(sd);)rd+=sd,hd();return"false"===rd?rd=!1:"true"===rd?rd=!0:isNaN(Number(rd))||(rd=Number(rd)),void(ad=ed.IDENTIFIER)}if('"'===sd){for(hd();""!=sd&&('"'!=sd||'"'===sd&&'"'===dd());)'"'===sd?(rd+=sd,hd()):"\\"===sd&&"n"===dd()?(rd+="\n",hd()):rd+=sd,hd();if('"'!=sd)throw xd('End of string " expected');return hd(),void(ad=ed.IDENTIFIER)}for(ad=ed.UNKNOWN;""!=sd;)rd+=sd,hd();throw new SyntaxError('Syntax error in part "'+Ed(rd,30)+'"')}ad=ed.DELIMITER}function vd(t){for(;""!==rd&&"}"!=rd;)yd(t),";"===rd&&md()}function yd(t){var e=bd(t);if(e)wd(t,e);else{var i=function(t){if("node"===rd)return md(),t.node=_d(),"node";if("edge"===rd)return md(),t.edge=_d(),"edge";if("graph"===rd)return md(),t.graph=_d(),"graph";return null}(t);if(!i){if(ad!=ed.IDENTIFIER)throw xd("Identifier expected");var o=rd;if(md(),"="===rd){if(md(),ad!=ed.IDENTIFIER)throw xd("Identifier expected");t[o]=rd,md()}else!function(t,e){var i={id:e},o=_d();o&&(i.attr=o);pd(t,i),wd(t,e)}(t,o)}}}function bd(t){var e=null;if("subgraph"===rd&&((e={}).type="subgraph",md(),ad===ed.IDENTIFIER&&(e.id=rd,md())),"{"===rd){if(md(),e||(e={}),e.parent=t,e.node=t.node,e.edge=t.edge,e.graph=t.graph,vd(e),"}"!=rd)throw xd("Angle bracket } expected");md(),delete e.node,delete e.edge,delete e.graph,delete e.parent,t.subgraphs||(t.subgraphs=[]),t.subgraphs.push(e)}return e}function wd(t,e){for(;"->"===rd||"--"===rd;){var i,o=rd;md();var n=bd(t);if(n)i=n;else{if(ad!=ed.IDENTIFIER)throw xd("Identifier or subgraph expected");pd(t,{id:i=rd}),md()}fd(t,gd(t,e,i,o,_d())),e=i}}function _d(){for(var t,e,i=null,o={dashed:!0,solid:!1,dotted:[1,5]},n={dot:"circle",box:"box",crow:"crow",curve:"curve",icurve:"inv_curve",normal:"triangle",inv:"inv_triangle",diamond:"diamond",tee:"bar",vee:"vee"},s=new Array,r=new Array;"["===rd;){for(md(),i={};""!==rd&&"]"!=rd;){if(ad!=ed.IDENTIFIER)throw xd("Attribute name expected");var a=rd;if(md(),"="!=rd)throw xd("Equal sign = expected");if(md(),ad!=ed.IDENTIFIER)throw xd("Attribute value expected");var h=rd;"style"===a&&(h=o[h]),"arrowhead"===a&&(a="arrows",h={to:{enabled:!0,type:n[h]}}),"arrowtail"===a&&(a="arrows",h={from:{enabled:!0,type:n[h]}}),s.push({attr:i,name:a,value:h}),r.push(a),md(),","==rd&&md()}if("]"!=rd)throw xd("Bracket ] expected");md()}if(rh(r).call(r,"dir")){var d={arrows:{}};for(t=0;t<s.length;t++)if("arrows"===s[t].name)if(null!=s[t].value.to)d.arrows.to=t;else{if(null==s[t].value.from)throw xd("Invalid value of arrows");d.arrows.from=t}else"dir"===s[t].name&&(d.dir=t);var l,c,u=s[d.dir].value;if(!rh(r).call(r,"arrows"))if("both"===u)s.push({attr:s[d.dir].attr,name:"arrows",value:{to:{enabled:!0}}}),d.arrows.to=s.length-1,s.push({attr:s[d.dir].attr,name:"arrows",value:{from:{enabled:!0}}}),d.arrows.from=s.length-1;else if("forward"===u)s.push({attr:s[d.dir].attr,name:"arrows",value:{to:{enabled:!0}}}),d.arrows.to=s.length-1;else if("back"===u)s.push({attr:s[d.dir].attr,name:"arrows",value:{from:{enabled:!0}}}),d.arrows.from=s.length-1;else{if("none"!==u)throw xd('Invalid dir type "'+u+'"');s.push({attr:s[d.dir].attr,name:"arrows",value:""}),d.arrows.to=s.length-1}if("both"===u)d.arrows.to&&d.arrows.from?(c=s[d.arrows.to].value.to.type,l=s[d.arrows.from].value.from.type,s[d.arrows.to]={attr:s[d.arrows.to].attr,name:s[d.arrows.to].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}},Th(s).call(s,d.arrows.from,1)):d.arrows.to?(c=s[d.arrows.to].value.to.type,l="arrow",s[d.arrows.to]={attr:s[d.arrows.to].attr,name:s[d.arrows.to].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}):d.arrows.from&&(c="arrow",l=s[d.arrows.from].value.from.type,s[d.arrows.from]={attr:s[d.arrows.from].attr,name:s[d.arrows.from].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}});else if("back"===u)d.arrows.to&&d.arrows.from?(c="",l=s[d.arrows.from].value.from.type,s[d.arrows.from]={attr:s[d.arrows.from].attr,name:s[d.arrows.from].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}):d.arrows.to?(c="",l="arrow",d.arrows.from=d.arrows.to,s[d.arrows.from]={attr:s[d.arrows.from].attr,name:s[d.arrows.from].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}):d.arrows.from&&(c="",l=s[d.arrows.from].value.from.type,s[d.arrows.to]={attr:s[d.arrows.from].attr,name:s[d.arrows.from].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}),s[d.arrows.from]={attr:s[d.arrows.from].attr,name:s[d.arrows.from].name,value:{from:{enabled:!0,type:s[d.arrows.from].value.from.type}}};else if("none"===u){var p;s[p=d.arrows.to?d.arrows.to:d.arrows.from]={attr:s[p].attr,name:s[p].name,value:""}}else{if("forward"!==u)throw xd('Invalid dir type "'+u+'"');d.arrows.to&&d.arrows.from||d.arrows.to?(c=s[d.arrows.to].value.to.type,l="",s[d.arrows.to]={attr:s[d.arrows.to].attr,name:s[d.arrows.to].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}):d.arrows.from&&(c="arrow",l="",d.arrows.to=d.arrows.from,s[d.arrows.to]={attr:s[d.arrows.to].attr,name:s[d.arrows.to].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}),s[d.arrows.to]={attr:s[d.arrows.to].attr,name:s[d.arrows.to].name,value:{to:{enabled:!0,type:s[d.arrows.to].value.to.type}}}}Th(s).call(s,d.dir,1)}if(rh(r).call(r,"penwidth")){var f=[];for(e=s.length,t=0;t<e;t++)"width"!==s[t].name&&("penwidth"===s[t].name&&(s[t].name="width"),f.push(s[t]));s=f}for(e=s.length,t=0;t<e;t++)ud(s[t].attr,s[t].name,s[t].value);return i}function xd(t){return new SyntaxError(t+', got "'+Ed(rd,30)+'" (char '+nd+")")}function Ed(t,e){return t.length<=e?t:t.substr(0,27)+"..."}function Od(t,e,i){for(var o=e.split("."),n=o.pop(),s=t,r=0;r<o.length;r++){var a=o[r];a in s||(s[a]={}),s=s[a]}return s[n]=i,t}function Cd(t,e){var i={};for(var o in t)if(t.hasOwnProperty(o)){var n=e[o];Vh(n)?Qh(n).call(n,function(e){Od(i,e,t[o])}):Od(i,"string"==typeof n?n:o,t[o])}return i}function kd(t){var e,i=$h(t),o={nodes:[],edges:[],options:{}};i.nodes&&Qh(e=i.nodes).call(e,function(t){var e={id:t.id,label:String(t.label||t.id)};cd(e,Cd(t.attr,Jh)),e.image&&(e.shape="image"),o.nodes.push(e)});if(i.edges){var n,s=function(t){var e={from:t.from,to:t.to};return cd(e,Cd(t.attr,td)),null==e.arrows&&"->"===t.type&&(e.arrows="to"),e};Qh(n=i.edges).call(n,function(t){var e,i,n,r,a,h,d;(e=t.from instanceof Object?t.from.nodes:{id:t.from},i=t.to instanceof Object?t.to.nodes:{id:t.to},t.from instanceof Object&&t.from.edges)&&Qh(n=t.from.edges).call(n,function(t){var e=s(t);o.edges.push(e)});(a=i,h=function(e,i){var n=gd(o,e.id,i.id,t.type,t.attr),r=s(n);o.edges.push(r)},Vh(r=e)?Qh(r).call(r,function(t){Vh(a)?Qh(a).call(a,function(e){h(t,e)}):h(t,a)}):Vh(a)?Qh(a).call(a,function(t){h(r,t)}):h(r,a),t.to instanceof Object&&t.to.edges)&&Qh(d=t.to.edges).call(d,function(t){var e=s(t);o.edges.push(e)})})}return i.attr&&(o.options=i.attr),o}var Sd,Td,Dd,Md,Id,Pd,Nd,Bd,zd,Fd=Object.freeze({__proto__:null,DOTToGraph:kd,parseDOT:$h}),Ad={};function jd(){return Dd?Td:(Dd=1,function(){if(Sd)return Ad;Sd=1;var t=hi(),e=Yh().map;t({target:"Array",proto:!0,forced:!vh()("map")},{map:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0)}})}(),Td=Ji()("Array","map"))}function Rd(){if(Id)return Md;Id=1;var t=Tt(),e=jd(),i=Array.prototype;return Md=function(o){var n=o.map;return o===i||t(i,o)&&n===i.map?e:n},Md}function Ld(){return Nd?Pd:(Nd=1,Pd=Rd())}var Hd=i(zd?Bd:(zd=1,Bd=Ld()));function Wd(t,e){var i;const o={edges:{inheritColor:!1},nodes:{fixed:!1,parseColor:!1}};null!=e&&(null!=e.fixed&&(o.nodes.fixed=e.fixed),null!=e.parseColor&&(o.nodes.parseColor=e.parseColor),null!=e.inheritColor&&(o.edges.inheritColor=e.inheritColor));const n=t.edges,s=Hd(n).call(n,t=>{const e={from:t.source,id:t.id,to:t.target};return null!=t.attributes&&(e.attributes=t.attributes),null!=t.label&&(e.label=t.label),null!=t.attributes&&null!=t.attributes.title&&(e.title=t.attributes.title),"Directed"===t.type&&(e.arrows="to"),t.color&&!1===o.edges.inheritColor&&(e.color=t.color),e});return{nodes:Hd(i=t.nodes).call(i,t=>{const e={id:t.id,fixed:o.nodes.fixed&&null!=t.x&&null!=t.y};return null!=t.attributes&&(e.attributes=t.attributes),null!=t.label&&(e.label=t.label),null!=t.size&&(e.size=t.size),null!=t.attributes&&null!=t.attributes.title&&(e.title=t.attributes.title),null!=t.title&&(e.title=t.title),null!=t.x&&(e.x=t.x),null!=t.y&&(e.y=t.y),null!=t.color&&(!0===o.nodes.parseColor?e.color=t.color:e.color={background:t.color,border:t.color,highlight:{background:t.color,border:t.color},hover:{background:t.color,border:t.color}}),e}),edges:s}}var qd=Object.freeze({__proto__:null,parseGephi:Wd});var Vd,Ud,Yd,Xd,Gd,Kd,Zd,Qd,$d,Jd=Object.freeze({__proto__:null,cn:{addDescription:"单击空白处放置新节点。",addEdge:"添加连接线",addNode:"添加节点",back:"返回",close:"關閉",createEdgeError:"无法将连接线连接到群集。",del:"删除选定",deleteClusterError:"无法删除群集。",edgeDescription:"单击某个节点并将该连接线拖动到另一个节点以连接它们。",edit:"编辑",editClusterError:"无法编辑群集。",editEdge:"编辑连接线",editEdgeDescription:"单击控制节点并将它们拖到节点上连接。",editNode:"编辑节点"},cs:{addDescription:"Kluknutím do prázdného prostoru můžete přidat nový vrchol.",addEdge:"Přidat hranu",addNode:"Přidat vrchol",back:"Zpět",close:"Zavřít",createEdgeError:"Nelze připojit hranu ke shluku.",del:"Smazat výběr",deleteClusterError:"Nelze mazat shluky.",edgeDescription:"Přetažením z jednoho vrcholu do druhého můžete spojit tyto vrcholy novou hranou.",edit:"Upravit",editClusterError:"Nelze upravovat shluky.",editEdge:"Upravit hranu",editEdgeDescription:"Přetažením kontrolního vrcholu hrany ji můžete připojit k jinému vrcholu.",editNode:"Upravit vrchol"},de:{addDescription:"Klicke auf eine freie Stelle, um einen neuen Knoten zu plazieren.",addEdge:"Kante hinzufügen",addNode:"Knoten hinzufügen",back:"Zurück",close:"Schließen",createEdgeError:"Es ist nicht möglich, Kanten mit Clustern zu verbinden.",del:"Lösche Auswahl",deleteClusterError:"Cluster können nicht gelöscht werden.",edgeDescription:"Klicke auf einen Knoten und ziehe die Kante zu einem anderen Knoten, um diese zu verbinden.",edit:"Editieren",editClusterError:"Cluster können nicht editiert werden.",editEdge:"Kante editieren",editEdgeDescription:"Klicke auf die Verbindungspunkte und ziehe diese auf einen Knoten, um sie zu verbinden.",editNode:"Knoten editieren"},en:{addDescription:"Click in an empty space to place a new node.",addEdge:"Add Edge",addNode:"Add Node",back:"Back",close:"Close",createEdgeError:"Cannot link edges to a cluster.",del:"Delete selected",deleteClusterError:"Clusters cannot be deleted.",edgeDescription:"Click on a node and drag the edge to another node to connect them.",edit:"Edit",editClusterError:"Clusters cannot be edited.",editEdge:"Edit Edge",editEdgeDescription:"Click on the control points and drag them to a node to connect to it.",editNode:"Edit Node"},es:{addDescription:"Haga clic en un lugar vacío para colocar un nuevo nodo.",addEdge:"Añadir arista",addNode:"Añadir nodo",back:"Atrás",close:"Cerrar",createEdgeError:"No se puede conectar una arista a un grupo.",del:"Eliminar selección",deleteClusterError:"No es posible eliminar grupos.",edgeDescription:"Haga clic en un nodo y arrastre la arista hacia otro nodo para conectarlos.",edit:"Editar",editClusterError:"No es posible editar grupos.",editEdge:"Editar arista",editEdgeDescription:"Haga clic en un punto de control y arrastrelo a un nodo para conectarlo.",editNode:"Editar nodo"},fr:{addDescription:"Cliquez dans un endroit vide pour placer un nœud.",addEdge:"Ajouter un lien",addNode:"Ajouter un nœud",back:"Retour",close:"Fermer",createEdgeError:"Impossible de créer un lien vers un cluster.",del:"Effacer la sélection",deleteClusterError:"Les clusters ne peuvent pas être effacés.",edgeDescription:"Cliquez sur un nœud et glissez le lien vers un autre nœud pour les connecter.",edit:"Éditer",editClusterError:"Les clusters ne peuvent pas être édités.",editEdge:"Éditer le lien",editEdgeDescription:"Cliquez sur les points de contrôle et glissez-les pour connecter un nœud.",editNode:"Éditer le nœud"},it:{addDescription:"Clicca per aggiungere un nuovo nodo",addEdge:"Aggiungi un vertice",addNode:"Aggiungi un nodo",back:"Indietro",close:"Chiudere",createEdgeError:"Non si possono collegare vertici ad un cluster",del:"Cancella la selezione",deleteClusterError:"I cluster non possono essere cancellati",edgeDescription:"Clicca su un nodo e trascinalo ad un altro nodo per connetterli.",edit:"Modifica",editClusterError:"I clusters non possono essere modificati.",editEdge:"Modifica il vertice",editEdgeDescription:"Clicca sui Punti di controllo e trascinali ad un nodo per connetterli.",editNode:"Modifica il nodo"},nl:{addDescription:"Klik op een leeg gebied om een nieuwe node te maken.",addEdge:"Link toevoegen",addNode:"Node toevoegen",back:"Terug",close:"Sluiten",createEdgeError:"Kan geen link maken naar een cluster.",del:"Selectie verwijderen",deleteClusterError:"Clusters kunnen niet worden verwijderd.",edgeDescription:"Klik op een node en sleep de link naar een andere node om ze te verbinden.",edit:"Wijzigen",editClusterError:"Clusters kunnen niet worden aangepast.",editEdge:"Link wijzigen",editEdgeDescription:"Klik op de verbindingspunten en sleep ze naar een node om daarmee te verbinden.",editNode:"Node wijzigen"},pt:{addDescription:"Clique em um espaço em branco para adicionar um novo nó",addEdge:"Adicionar aresta",addNode:"Adicionar nó",back:"Voltar",close:"Fechar",createEdgeError:"Não foi possível linkar arestas a um cluster.",del:"Remover selecionado",deleteClusterError:"Clusters não puderam ser removidos.",edgeDescription:"Clique em um nó e arraste a aresta até outro nó para conectá-los",edit:"Editar",editClusterError:"Clusters não puderam ser editados.",editEdge:"Editar aresta",editEdgeDescription:"Clique nos pontos de controle e os arraste para um nó para conectá-los",editNode:"Editar nó"},ru:{addDescription:"Кликните в свободное место, чтобы добавить новый узел.",addEdge:"Добавить ребро",addNode:"Добавить узел",back:"Назад",close:"Закрывать",createEdgeError:"Невозможно соединить ребра в кластер.",del:"Удалить выбранное",deleteClusterError:"Кластеры не могут быть удалены",edgeDescription:"Кликните на узел и протяните ребро к другому узлу, чтобы соединить их.",edit:"Редактировать",editClusterError:"Кластеры недоступны для редактирования.",editEdge:"Редактировать ребро",editEdgeDescription:"Кликните на контрольные точки и перетащите их в узел, чтобы подключиться к нему.",editNode:"Редактировать узел"},uk:{addDescription:"Kлікніть на вільне місце, щоб додати новий вузол.",addEdge:"Додати край",addNode:"Додати вузол",back:"Назад",close:"Закрити",createEdgeError:"Не можливо об'єднати краї в групу.",del:"Видалити обране",deleteClusterError:"Групи не можуть бути видалені.",edgeDescription:"Клікніть на вузол і перетягніть край до іншого вузла, щоб їх з'єднати.",edit:"Редагувати",editClusterError:"Групи недоступні для редагування.",editEdge:"Редагувати край",editEdgeDescription:"Клікніть на контрольні точки і перетягніть їх у вузол, щоб підключитися до нього.",editNode:"Редагувати вузол"}}),tl={};function el(){if(Vd)return tl;Vd=1;var t=hi(),e=_(),i=hh(),o=Ct(),n=ve(),s=pi(),r=lh(),a=gh(),h=fh(),d=vh(),l=we(),c=Mt(),u=l("isConcatSpreadable"),p=c>=51||!e(function(){var t=[];return t[u]=!1,t.concat()[0]!==t}),f=function(t){if(!o(t))return!1;var e=t[u];return void 0!==e?!!e:i(t)};return t({target:"Array",proto:!0,arity:1,forced:!p||!d("concat")},{concat:function(t){var e,i,o,d,l,c=n(this),u=h(c,0),p=0;for(e=-1,o=arguments.length;e<o;e++)if(f(l=-1===e?c:arguments[e]))for(d=s(l),r(p+d),i=0;i<d;i++,p++)i in l&&a(u,p,l[i]);else r(p+1),a(u,p++,l);return u.length=p,u}}),tl}function il(){return Yd?Ud:(Yd=1,el(),Ud=Ji()("Array","concat"))}function ol(){if(Gd)return Xd;Gd=1;var t=Tt(),e=il(),i=Array.prototype;return Xd=function(o){var n=o.concat;return o===i||t(i,o)&&n===i.concat?e:n},Xd}function nl(){return Zd?Kd:(Zd=1,Kd=ol())}var sl,rl,al,hl,dl,ll,cl,ul,pl,fl,gl,ml,vl,yl,bl,wl,_l,xl,El,Ol,Cl,kl,Sl,Tl,Dl,Ml,Il,Pl,Nl,Bl,zl,Fl,Al,jl,Rl,Ll,Hl=i($d?Qd:($d=1,Qd=nl()));class Wl{constructor(){this.NUM_ITERATIONS=4,this.image=new Image,this.canvas=document.createElement("canvas")}init(){if(this.initialized())return;this.src=this.image.src;const t=this.image.width,e=this.image.height;this.width=t,this.height=e;const i=Math.floor(e/2),o=Math.floor(e/4),n=Math.floor(e/8),s=Math.floor(e/16),r=Math.floor(t/2),a=Math.floor(t/4),h=Math.floor(t/8),d=Math.floor(t/16);this.canvas.width=3*a,this.canvas.height=i,this.coordinates=[[0,0,r,i],[r,0,a,o],[r,o,h,n],[5*h,o,d,s]],this._fillMipMap()}initialized(){return void 0!==this.coordinates}_fillMipMap(){const t=this.canvas.getContext("2d"),e=this.coordinates[0];t.drawImage(this.image,e[0],e[1],e[2],e[3]);for(let e=1;e<this.NUM_ITERATIONS;e++){const i=this.coordinates[e-1],o=this.coordinates[e];t.drawImage(this.canvas,i[0],i[1],i[2],i[3],o[0],o[1],o[2],o[3])}}drawImageAtPosition(t,e,i,o,n,s){if(this.initialized())if(e>2){e*=.5;let r=0;for(;e>2&&r<this.NUM_ITERATIONS;)e*=.5,r+=1;r>=this.NUM_ITERATIONS&&(r=this.NUM_ITERATIONS-1);const a=this.coordinates[r];t.drawImage(this.canvas,a[0],a[1],a[2],a[3],i,o,n,s)}else t.drawImage(this.image,i,o,n,s)}}class ql{constructor(t){this.images={},this.imageBroken={},this.callback=t}_tryloadBrokenUrl(t,e,i){void 0!==t&&void 0!==i&&(void 0!==e?(i.image.onerror=()=>{console.error("Could not load brokenImage:",e)},i.image.src=e):console.warn("No broken url image defined"))}_redrawWithImage(t){this.callback&&this.callback(t)}load(t,e){const i=this.images[t];if(i)return i;const o=new Wl;return this.images[t]=o,o.image.onload=()=>{this._fixImageCoordinates(o.image),o.init(),this._redrawWithImage(o)},o.image.onerror=()=>{console.error("Could not load image:",t),this._tryloadBrokenUrl(t,e,o)},o.image.src=t,o}_fixImageCoordinates(t){0===t.width&&(document.body.appendChild(t),t.width=t.offsetWidth,t.height=t.offsetHeight,document.body.removeChild(t))}}function Vl(){return rl?sl:(rl=1,sl={})}function Ul(){if(hl)return al;hl=1;var t=w(),e=S(),i=t.WeakMap;return al=e(i)&&/native code/.test(String(i))}function Yl(){if(ll)return dl;ll=1;var t,e,i,o=Ul(),n=w(),s=Ct(),r=ai(),a=ye(),h=ge(),d=Or(),l=gi(),c="Object already initialized",u=n.TypeError,p=n.WeakMap;if(o||h.state){var f=h.state||(h.state=new p);f.get=f.get,f.has=f.has,f.set=f.set,t=function(t,e){if(f.has(t))throw new u(c);return e.facade=t,f.set(t,e),e},e=function(t){return f.get(t)||{}},i=function(t){return f.has(t)}}else{var g=d("state");l[g]=!0,t=function(t,e){if(a(t,g))throw new u(c);return e.facade=t,r(t,g,e),e},e=function(t){return a(t,g)?t[g]:{}},i=function(t){return a(t,g)}}return dl={set:t,get:e,has:i,enforce:function(o){return i(o)?e(o):t(o,{})},getterFor:function(t){return function(i){var o;if(!s(i)||(o=e(i)).type!==t)throw new u("Incompatible receiver, "+t+" required");return o}}},dl}function Xl(){if(ul)return cl;ul=1;var t=N(),e=ye(),i=Function.prototype,o=t&&Object.getOwnPropertyDescriptor,n=e(i,"name"),s=n&&"something"===function(){}.name,r=n&&(!t||t&&o(i,"name").configurable);return cl={EXISTS:n,PROPER:s,CONFIGURABLE:r}}function Gl(){return fl?pl:(fl=1,pl=!_()(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))}function Kl(){if(ml)return gl;ml=1;var t=ye(),e=S(),i=ve(),o=Or(),n=Gl(),s=o("IE_PROTO"),r=Object,a=r.prototype;return gl=n?r.getPrototypeOf:function(o){var n=i(o);if(t(n,s))return n[s];var h=n.constructor;return e(h)&&n instanceof h?h.prototype:n instanceof r?a:null},gl}function Zl(){if(yl)return vl;yl=1;var t=ai();return vl=function(e,i,o,n){return n&&n.enumerable?e[i]=o:t(e,i,o),e}}function Ql(){if(wl)return bl;wl=1;var t,e,i,o=_(),n=S(),s=Ct(),r=Cr(),a=Kl(),h=Zl(),d=we(),l=pe(),c=d("iterator"),u=!1;return[].keys&&("next"in(i=[].keys())?(e=a(a(i)))!==Object.prototype&&(t=e):u=!0),!s(t)||o(function(){var e={};return t[c].call(e)!==e})?t={}:l&&(t=r(t)),n(t[c])||h(t,c,function(){return this}),bl={IteratorPrototype:t,BUGGY_SAFARI_ITERATORS:u}}function $l(){if(xl)return _l;xl=1;var t=Oa(),e=Ca();return _l=t?{}.toString:function(){return"[object "+e(this)+"]"}}function Jl(){if(Ol)return El;Ol=1;var t=Oa(),e=ri().f,i=ai(),o=ye(),n=$l(),s=we()("toStringTag");return El=function(r,a,h,d){var l=h?r:r&&r.prototype;l&&(o(l,s)||e(l,s,{configurable:!0,value:a}),d&&!t&&i(l,"toString",n))},El}function tc(){if(kl)return Cl;kl=1;var t=Ql().IteratorPrototype,e=Cr(),i=wt(),o=Jl(),n=Vl(),s=function(){return this};return Cl=function(r,a,h,d){var l=a+" Iterator";return r.prototype=e(t,{next:i(+!d,h)}),o(r,l,!1,!0),n[l]=s,r},Cl}function ec(){if(Tl)return Sl;Tl=1;var t=O(),e=zt();return Sl=function(i,o,n){try{return t(e(Object.getOwnPropertyDescriptor(i,o)[n]))}catch(t){}},Sl}function ic(){if(Ml)return Dl;Ml=1;var t=Ct();return Dl=function(e){return t(e)||null===e}}function oc(){if(Pl)return Il;Pl=1;var t=ic(),e=String,i=TypeError;return Il=function(o){if(t(o))return o;throw new i("Can't set "+e(o)+" as a prototype")}}function nc(){if(Bl)return Nl;Bl=1;var t=ec(),e=Ct(),i=Et(),o=oc();return Nl=Object.setPrototypeOf||("__proto__"in{}?function(){var n,s=!1,r={};try{(n=t(Object.prototype,"__proto__","set"))(r,[]),s=r instanceof Array}catch(t){}return function(t,r){return i(t),o(r),e(t)?(s?n(t,r):t.__proto__=r,t):t}}():void 0)}function sc(){if(Fl)return zl;Fl=1;var t=hi(),e=B(),i=pe(),o=Xl(),n=S(),s=tc(),r=Kl(),a=nc(),h=Jl(),d=ai(),l=Zl(),c=we(),u=Vl(),p=Ql(),f=o.PROPER,g=o.CONFIGURABLE,m=p.IteratorPrototype,v=p.BUGGY_SAFARI_ITERATORS,y=c("iterator"),b="keys",w="values",_="entries",x=function(){return this};return zl=function(o,c,p,E,O,C,k){s(p,c,E);var S,T,D,M=function(t){if(t===O&&z)return z;if(!v&&t&&t in N)return N[t];switch(t){case b:case w:case _:return function(){return new p(this,t)}}return function(){return new p(this)}},I=c+" Iterator",P=!1,N=o.prototype,B=N[y]||N["@@iterator"]||O&&N[O],z=!v&&B||M(O),F="Array"===c&&N.entries||B;if(F&&(S=r(F.call(new o)))!==Object.prototype&&S.next&&(i||r(S)===m||(a?a(S,m):n(S[y])||l(S,y,x)),h(S,I,!0,!0),i&&(u[I]=x)),f&&O===w&&B&&B.name!==w&&(!i&&g?d(N,"name",w):(P=!0,z=function(){return e(B,this)})),O)if(T={values:M(w),keys:C?z:M(b),entries:M(_)},k)for(D in T)(v||P||!(D in N))&&l(N,D,T[D]);else t({target:c,proto:!0,forced:v||P},T);return i&&!k||N[y]===z||l(N,y,z,{name:O}),u[c]=z,T},zl}function rc(){return jl?Al:(jl=1,Al=function(t,e){return{value:t,done:e}})}function ac(){if(Ll)return Rl;Ll=1;var t=Ot(),e=$r(),i=Vl(),o=Yl(),n=ri().f,s=sc(),r=rc(),a=pe(),h=N(),d="Array Iterator",l=o.set,c=o.getterFor(d);Rl=s(Array,"Array",function(e,i){l(this,{type:d,target:t(e),index:0,kind:i})},function(){var t=c(this),e=t.target,i=t.index++;if(!e||i>=e.length)return t.target=null,r(void 0,!0);switch(t.kind){case"keys":return r(i,!1);case"values":return r(e[i],!1)}return r([i,e[i]],!1)},"values");var u=i.Arguments=i.Array;if(e("keys"),e("values"),e("entries"),!a&&h&&"values"!==u.name)try{n(u,"name",{value:"values"})}catch(t){}return Rl}var hc,dc={},lc={exports:{}},cc={};function uc(){if(hc)return cc;hc=1;var t=mi(),e=vi().concat("length","prototype");return cc.f=Object.getOwnPropertyNames||function(i){return t(i,e)},cc}var pc,fc,gc,mc,vc,yc,bc,wc,_c,xc,Ec,Oc,Cc,kc,Sc,Tc,Dc,Mc,Ic,Pc,Nc,Bc,zc,Fc,Ac,jc,Rc,Lc,Hc,Wc,qc,Vc,Uc={};function Yc(){if(pc)return Uc;pc=1;var t=C(),e=Ot(),i=uc().f,o=Qi(),n="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];return Uc.f=function(s){return n&&"Window"===t(s)?function(t){try{return i(t)}catch(t){return o(n)}}(s):i(e(s))},Uc}function Xc(){return gc?fc:(gc=1,fc=_()(function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}}))}function Gc(){if(vc)return mc;vc=1;var t=_(),e=Ct(),i=C(),o=Xc(),n=Object.isExtensible,s=t(function(){});return mc=s||o?function(t){return!!e(t)&&((!o||"ArrayBuffer"!==i(t))&&(!n||n(t)))}:n,mc}function Kc(){return bc?yc:(bc=1,yc=!_()(function(){return Object.isExtensible(Object.preventExtensions({}))}))}function Zc(){if(wc)return lc.exports;wc=1;var t=hi(),e=O(),i=gi(),o=Ct(),n=ye(),s=ri().f,r=uc(),a=Yc(),h=Gc(),d=be(),l=Kc(),c=!1,u=d("meta"),p=0,f=function(t){s(t,u,{value:{objectID:"O"+p++,weakData:{}}})},g=lc.exports={enable:function(){g.enable=function(){},c=!0;var i=r.f,o=e([].splice),n={};n[u]=1,i(n).length&&(r.f=function(t){for(var e=i(t),n=0,s=e.length;n<s;n++)if(e[n]===u){o(e,n,1);break}return e},t({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:a.f}))},fastKey:function(t,e){if(!o(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!n(t,u)){if(!h(t))return"F";if(!e)return"E";f(t)}return t[u].objectID},getWeakData:function(t,e){if(!n(t,u)){if(!h(t))return!0;if(!e)return!1;f(t)}return t[u].weakData},onFreeze:function(t){return l&&c&&h(t)&&!n(t,u)&&f(t),t}};return i[u]=!0,lc.exports}function Qc(){if(xc)return _c;xc=1;var t=we(),e=Vl(),i=t("iterator"),o=Array.prototype;return _c=function(t){return void 0!==t&&(e.Array===t||o[i]===t)},_c}function $c(){if(Oc)return Ec;Oc=1;var t=Ca(),e=Ft(),i=xt(),o=Vl(),n=we()("iterator");return Ec=function(s){if(!i(s))return e(s,n)||e(s,"@@iterator")||o[t(s)]},Ec}function Jc(){if(kc)return Cc;kc=1;var t=B(),e=zt(),i=si(),o=Bt(),n=$c(),s=TypeError;return Cc=function(r,a){var h=arguments.length<2?n(r):a;if(e(h))return i(t(h,r));throw new s(o(r)+" is not iterable")},Cc}function tu(){if(Tc)return Sc;Tc=1;var t=B(),e=si(),i=Ft();return Sc=function(o,n,s){var r,a;e(o);try{if(!(r=i(o,"return"))){if("throw"===n)throw s;return s}r=t(r,o)}catch(t){a=!0,r=t}if("throw"===n)throw s;if(a)throw r;return e(r),s},Sc}function eu(){if(Mc)return Dc;Mc=1;var t=Se(),e=B(),i=si(),o=Bt(),n=Qc(),s=pi(),r=Tt(),a=Jc(),h=$c(),d=tu(),l=TypeError,c=function(t,e){this.stopped=t,this.result=e},u=c.prototype;return Dc=function(p,f,g){var m,v,y,b,w,_,x,E=g&&g.that,O=!(!g||!g.AS_ENTRIES),C=!(!g||!g.IS_RECORD),k=!(!g||!g.IS_ITERATOR),S=!(!g||!g.INTERRUPTED),T=t(f,E),D=function(t){return m&&d(m,"normal"),new c(!0,t)},M=function(t){return O?(i(t),S?T(t[0],t[1],D):T(t[0],t[1])):S?T(t,D):T(t)};if(C)m=p.iterator;else if(k)m=p;else{if(!(v=h(p)))throw new l(o(p)+" is not iterable");if(n(v)){for(y=0,b=s(p);b>y;y++)if((w=M(p[y]))&&r(u,w))return w;return new c(!1)}m=a(p,v)}for(_=C?p.next:m.next;!(x=e(_,m)).done;){try{w=M(x.value)}catch(t){d(m,"throw",t)}if("object"==typeof w&&w&&r(u,w))return w}return new c(!1)},Dc}function iu(){if(Pc)return Ic;Pc=1;var t=Tt(),e=TypeError;return Ic=function(i,o){if(t(o,i))return i;throw new e("Incorrect invocation")},Ic}function ou(){if(Bc)return Nc;Bc=1;var t=hi(),e=w(),i=Zc(),o=_(),n=ai(),s=eu(),r=iu(),a=S(),h=Ct(),d=xt(),l=Jl(),c=ri().f,u=Yh().forEach,p=N(),f=Yl(),g=f.set,m=f.getterFor;return Nc=function(f,v,y){var b,w=-1!==f.indexOf("Map"),_=-1!==f.indexOf("Weak"),x=w?"set":"add",E=e[f],O=E&&E.prototype,C={};if(p&&a(E)&&(_||O.forEach&&!o(function(){(new E).entries().next()}))){var k=(b=v(function(t,e){g(r(t,k),{type:f,collection:new E}),d(e)||s(e,t[x],{that:t,AS_ENTRIES:w})})).prototype,S=m(f);u(["add","clear","delete","forEach","get","has","set","keys","values","entries"],function(t){var e="add"===t||"set"===t;!(t in O)||_&&"clear"===t||n(k,t,function(i,o){var n=S(this).collection;if(!e&&_&&!h(i))return"get"===t&&void 0;var s=n[t](0===i?0:i,o);return e?this:s})}),_||c(k,"size",{configurable:!0,get:function(){return S(this).collection.size}})}else b=y.getConstructor(v,f,w,x),i.enable();return l(b,f,!1,!0),C[f]=b,t({global:!0,forced:!0},C),_||y.setStrong(b,f,w),b}}function nu(){if(Fc)return zc;Fc=1;var t=ri();return zc=function(e,i,o){return t.f(e,i,o)}}function su(){if(jc)return Ac;jc=1;var t=Zl();return Ac=function(e,i,o){for(var n in i)o&&o.unsafe&&e[n]?e[n]=i[n]:t(e,n,i[n],o);return e}}function ru(){if(Lc)return Rc;Lc=1;var t=St(),e=nu(),i=we(),o=N(),n=i("species");return Rc=function(i){var s=t(i);o&&s&&!s[n]&&e(s,n,{configurable:!0,get:function(){return this}})}}function au(){if(Wc)return Hc;Wc=1;var t=Cr(),e=nu(),i=su(),o=Se(),n=iu(),s=xt(),r=eu(),a=sc(),h=rc(),d=ru(),l=N(),c=Zc().fastKey,u=Yl(),p=u.set,f=u.getterFor;return Hc={getConstructor:function(a,h,d,u){var g=a(function(e,i){n(e,m),p(e,{type:h,index:t(null),first:null,last:null,size:0}),l||(e.size=0),s(i)||r(i,e[u],{that:e,AS_ENTRIES:d})}),m=g.prototype,v=f(h),y=function(t,e,i){var o,n,s=v(t),r=b(t,e);return r?r.value=i:(s.last=r={index:n=c(e,!0),key:e,value:i,previous:o=s.last,next:null,removed:!1},s.first||(s.first=r),o&&(o.next=r),l?s.size++:t.size++,"F"!==n&&(s.index[n]=r)),t},b=function(t,e){var i,o=v(t),n=c(e);if("F"!==n)return o.index[n];for(i=o.first;i;i=i.next)if(i.key===e)return i};return i(m,{clear:function(){for(var e=v(this),i=e.first;i;)i.removed=!0,i.previous&&(i.previous=i.previous.next=null),i=i.next;e.first=e.last=null,e.index=t(null),l?e.size=0:this.size=0},delete:function(t){var e=this,i=v(e),o=b(e,t);if(o){var n=o.next,s=o.previous;delete i.index[o.index],o.removed=!0,s&&(s.next=n),n&&(n.previous=s),i.first===o&&(i.first=n),i.last===o&&(i.last=s),l?i.size--:e.size--}return!!o},forEach:function(t){for(var e,i=v(this),n=o(t,arguments.length>1?arguments[1]:void 0);e=e?e.next:i.first;)for(n(e.value,e.key,this);e&&e.removed;)e=e.previous},has:function(t){return!!b(this,t)}}),i(m,d?{get:function(t){var e=b(this,t);return e&&e.value},set:function(t,e){return y(this,0===t?0:t,e)}}:{add:function(t){return y(this,t=0===t?0:t,t)}}),l&&e(m,"size",{configurable:!0,get:function(){return v(this).size}}),g},setStrong:function(t,e,i){var o=e+" Iterator",n=f(e),s=f(o);a(t,e,function(t,e){p(this,{type:o,target:t,state:n(t),kind:e,last:null})},function(){for(var t=s(this),e=t.kind,i=t.last;i&&i.removed;)i=i.previous;return t.target&&(t.last=i=i?i.next:t.state.first)?h("keys"===e?i.key:"values"===e?i.value:[i.key,i.value],!1):(t.target=null,h(void 0,!0))},i?"entries":"values",!i,!0),d(e)}},Hc}function hu(){return Vc||(Vc=1,qc||(qc=1,ou()("Map",function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},au()))),dc}var du,lu,cu,uu,pu,fu={};function gu(){return lu||(lu=1,du=function(t,e){return 1===e?function(e,i){return e[t](i)}:function(e,i,o){return e[t](i,o)}}),du}function mu(){if(uu)return cu;uu=1;var t=St(),e=gu(),i=t("Map");return cu={Map:i,set:e("set",2),get:e("get",1),has:e("has",1),remove:e("delete",1),proto:i.prototype}}var vu,yu,bu,wu,_u,xu={};function Eu(){if(yu)return vu;yu=1;var t=O(),e=li(),i=ka(),o=Et(),n=t("".charAt),s=t("".charCodeAt),r=t("".slice),a=function(t){return function(a,h){var d,l,c=i(o(a)),u=e(h),p=c.length;return u<0||u>=p?t?"":void 0:(d=s(c,u))<55296||d>56319||u+1===p||(l=s(c,u+1))<56320||l>57343?t?n(c,u):d:t?r(c,u,u+2):l-56320+(d-55296<<10)+65536}};return vu={codeAt:a(!1),charAt:a(!0)}}function Ou(){if(bu)return xu;bu=1;var t=Eu().charAt,e=ka(),i=Yl(),o=sc(),n=rc(),s="String Iterator",r=i.set,a=i.getterFor(s);return o(String,"String",function(t){r(this,{type:s,string:e(t),index:0})},function(){var e,i=a(this),o=i.string,s=i.index;return s>=o.length?n(void 0,!0):(e=t(o,s),i.index+=e.length,n(e,!1))}),xu}function Cu(){return _u?wu:(_u=1,ac(),hu(),function(){if(pu)return fu;pu=1;var t=hi(),e=O(),i=zt(),o=Et(),n=eu(),s=mu(),r=pe(),a=_(),h=s.Map,d=s.has,l=s.get,c=s.set,u=e([].push),p=r||a(function(){return 1!==h.groupBy("ab",function(t){return t}).get("a").length});t({target:"Map",stat:!0,forced:r||p},{groupBy:function(t,e){o(t),i(e);var s=new h,r=0;return n(t,function(t){var i=e(t,r++);d(s,i)?u(l(s,i),t):c(s,i,[t])}),s}})}(),Ou(),wu=kt().Map)}var ku,Su,Tu,Du,Mu,Iu,Pu,Nu={};function Bu(){return Su?ku:(Su=1,ku={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0})}function zu(){if(Tu)return Nu;Tu=1,ac();var t=Bu(),e=w(),i=Jl(),o=Vl();for(var n in t)i(e[n],n),o[n]=o.Array;return Nu}function Fu(){if(Mu)return Du;Mu=1;var t=Cu();return zu(),Du=t}var Au=i(Pu?Iu:(Pu=1,Iu=Fu()));class ju{constructor(){this.clear(),this._defaultIndex=0,this._groupIndex=0,this._defaultGroups=[{border:"#2B7CE9",background:"#97C2FC",highlight:{border:"#2B7CE9",background:"#D2E5FF"},hover:{border:"#2B7CE9",background:"#D2E5FF"}},{border:"#FFA500",background:"#FFFF00",highlight:{border:"#FFA500",background:"#FFFFA3"},hover:{border:"#FFA500",background:"#FFFFA3"}},{border:"#FA0A10",background:"#FB7E81",highlight:{border:"#FA0A10",background:"#FFAFB1"},hover:{border:"#FA0A10",background:"#FFAFB1"}},{border:"#41A906",background:"#7BE141",highlight:{border:"#41A906",background:"#A1EC76"},hover:{border:"#41A906",background:"#A1EC76"}},{border:"#E129F0",background:"#EB7DF4",highlight:{border:"#E129F0",background:"#F0B3F5"},hover:{border:"#E129F0",background:"#F0B3F5"}},{border:"#7C29F0",background:"#AD85E4",highlight:{border:"#7C29F0",background:"#D3BDF0"},hover:{border:"#7C29F0",background:"#D3BDF0"}},{border:"#C37F00",background:"#FFA807",highlight:{border:"#C37F00",background:"#FFCA66"},hover:{border:"#C37F00",background:"#FFCA66"}},{border:"#4220FB",background:"#6E6EFD",highlight:{border:"#4220FB",background:"#9B9BFD"},hover:{border:"#4220FB",background:"#9B9BFD"}},{border:"#FD5A77",background:"#FFC0CB",highlight:{border:"#FD5A77",background:"#FFD1D9"},hover:{border:"#FD5A77",background:"#FFD1D9"}},{border:"#4AD63A",background:"#C2FABC",highlight:{border:"#4AD63A",background:"#E6FFE3"},hover:{border:"#4AD63A",background:"#E6FFE3"}},{border:"#990000",background:"#EE0000",highlight:{border:"#BB0000",background:"#FF3333"},hover:{border:"#BB0000",background:"#FF3333"}},{border:"#FF6000",background:"#FF6000",highlight:{border:"#FF6000",background:"#FF6000"},hover:{border:"#FF6000",background:"#FF6000"}},{border:"#97C2FC",background:"#2B7CE9",highlight:{border:"#D2E5FF",background:"#2B7CE9"},hover:{border:"#D2E5FF",background:"#2B7CE9"}},{border:"#399605",background:"#255C03",highlight:{border:"#399605",background:"#255C03"},hover:{border:"#399605",background:"#255C03"}},{border:"#B70054",background:"#FF007E",highlight:{border:"#B70054",background:"#FF007E"},hover:{border:"#B70054",background:"#FF007E"}},{border:"#AD85E4",background:"#7C29F0",highlight:{border:"#D3BDF0",background:"#7C29F0"},hover:{border:"#D3BDF0",background:"#7C29F0"}},{border:"#4557FA",background:"#000EA1",highlight:{border:"#6E6EFD",background:"#000EA1"},hover:{border:"#6E6EFD",background:"#000EA1"}},{border:"#FFC0CB",background:"#FD5A77",highlight:{border:"#FFD1D9",background:"#FD5A77"},hover:{border:"#FFD1D9",background:"#FD5A77"}},{border:"#C2FABC",background:"#74D66A",highlight:{border:"#E6FFE3",background:"#74D66A"},hover:{border:"#E6FFE3",background:"#74D66A"}},{border:"#EE0000",background:"#990000",highlight:{border:"#FF3333",background:"#BB0000"},hover:{border:"#FF3333",background:"#BB0000"}}],this.options={},this.defaultOptions={useDefaultGroups:!0},Ki(this.options,this.defaultOptions)}setOptions(t){const e=["useDefaultGroups"];if(void 0!==t)for(const i in t)if(Object.prototype.hasOwnProperty.call(t,i)&&-1===Zr(e).call(e,i)){const e=t[i];this.add(i,e)}}clear(){this._groups=new Au,this._groupNames=[]}get(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this._groups.get(t);if(void 0===i&&e)if(!1===this.options.useDefaultGroups&&this._groupNames.length>0){const e=this._groupIndex%this._groupNames.length;++this._groupIndex,i={},i.color=this._groups.get(this._groupNames[e]),this._groups.set(t,i)}else{const e=this._defaultIndex%this._defaultGroups.length;this._defaultIndex++,i={},i.color=this._defaultGroups[e],this._groups.set(t,i)}return i}add(t,e){return this._groups.has(t)||this._groupNames.push(t),this._groups.set(t,e),e}}var Ru,Lu,Hu,Wu,qu,Vu,Uu;function Yu(){return Hu?Lu:(Hu=1,Ru||(Ru=1,hi()({target:"Number",stat:!0},{isNaN:function(t){return t!=t}})),Lu=kt().Number.isNaN)}function Xu(){return qu?Wu:(qu=1,Wu=Yu())}var Gu,Ku,Zu,Qu,$u,Ju,tp,ep,ip,op=i(Uu?Vu:(Uu=1,Vu=Xu()));function np(){if(Ku)return Gu;Ku=1;var t=w().isFinite;return Gu=Number.isFinite||function(e){return"number"==typeof e&&t(e)},Gu}function sp(){return $u?Qu:($u=1,Zu||(Zu=1,hi()({target:"Number",stat:!0},{isFinite:np()})),Qu=kt().Number.isFinite)}function rp(){return tp?Ju:(tp=1,Ju=sp())}var ap,hp,dp,lp,cp,up,pp,fp=i(ip?ep:(ip=1,ep=rp())),gp={};function mp(){return dp?hp:(dp=1,function(){if(ap)return gp;ap=1;var t=hi(),e=ve(),i=yi();t({target:"Object",stat:!0,forced:_()(function(){i(1)})},{keys:function(t){return i(e(t))}})}(),hp=kt().Object.keys)}function vp(){return cp?lp:(cp=1,lp=mp())}var yp,bp,wp,_p,xp,Ep,Op,Cp,kp,Sp=i(pp?up:(pp=1,up=vp())),Tp={};function Dp(){return wp?bp:(wp=1,function(){if(yp)return Tp;yp=1;var t=hi(),e=Yh().some;t({target:"Array",proto:!0,forced:!Hr()("some")},{some:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0)}})}(),bp=Ji()("Array","some"))}function Mp(){if(xp)return _p;xp=1;var t=Tt(),e=Dp(),i=Array.prototype;return _p=function(o){var n=o.some;return o===i||t(i,o)&&n===i.some?e:n},_p}function Ip(){return Op?Ep:(Op=1,Ep=Mp())}var Pp,Np,Bp,zp,Fp,Ap,jp,Rp=i(kp?Cp:(kp=1,Cp=Ip())),Lp={},Hp={};function Wp(){if(Np)return Pp;Np=1;var t=w(),e=Dt(),i=C(),o=function(t){return e.slice(0,t.length)===t};return Pp=o("Bun/")?"BUN":o("Cloudflare-Workers")?"CLOUDFLARE":o("Deno/")?"DENO":o("Node.js/")?"NODE":t.Bun&&"string"==typeof Bun.version?"BUN":t.Deno&&"object"==typeof Deno.version?"DENO":"process"===i(t.process)?"NODE":t.window&&t.document?"BROWSER":"REST"}function qp(){if(zp)return Bp;zp=1;var t=TypeError;return Bp=function(e,i){if(e<i)throw new t("Not enough arguments");return e}}function Vp(){if(Ap)return Fp;Ap=1;var t,e=w(),i=E(),o=S(),n=Wp(),s=Dt(),r=Qi(),a=qp(),h=e.Function,d=/MSIE .\./.test(s)||"BUN"===n&&((t=e.Bun.version.split(".")).length<3||"0"===t[0]&&(t[1]<3||"3"===t[1]&&"0"===t[2]));return Fp=function(t,e){var n=e?2:1;return d?function(s,d){var l=a(arguments.length,1)>n,c=o(s)?s:h(s),u=l?r(arguments,n):[],p=l?function(){i(c,this,u)}:c;return e?t(p,d):t(p)}:t},Fp}var Up,Yp,Xp,Gp,Kp,Zp,Qp={};function $p(){return Yp||(Yp=1,function(){if(jp)return Hp;jp=1;var t=hi(),e=w(),i=Vp()(e.setInterval,!0);t({global:!0,bind:!0,forced:e.setInterval!==i},{setInterval:i})}(),function(){if(Up)return Qp;Up=1;var t=hi(),e=w(),i=Vp()(e.setTimeout,!0);t({global:!0,bind:!0,forced:e.setTimeout!==i},{setTimeout:i})}()),Lp}function Jp(){return Gp?Xp:(Gp=1,$p(),Xp=kt().setTimeout)}var tf=i(Zp?Kp:(Zp=1,Kp=Jp()));const ef=[];for(let t=0;t<256;++t)ef.push((t+256).toString(16).slice(1));let of;const nf=new Uint8Array(16);var sf={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function rf(t,e,i){if(sf.randomUUID&&!t)return sf.randomUUID();const o=(t=t||{}).random??t.rng?.()??function(){if(!of){if("undefined"==typeof crypto||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");of=crypto.getRandomValues.bind(crypto)}return of(nf)}();if(o.length<16)throw new Error("Random bytes length must be >= 16");return o[6]=15&o[6]|64,o[8]=63&o[8]|128,function(t,e=0){return(ef[t[e+0]]+ef[t[e+1]]+ef[t[e+2]]+ef[t[e+3]]+"-"+ef[t[e+4]]+ef[t[e+5]]+"-"+ef[t[e+6]]+ef[t[e+7]]+"-"+ef[t[e+8]]+ef[t[e+9]]+"-"+ef[t[e+10]]+ef[t[e+11]]+ef[t[e+12]]+ef[t[e+13]]+ef[t[e+14]]+ef[t[e+15]]).toLowerCase()}(o)}
/**
	 * vis-data
	 * http://visjs.org/
	 *
	 * Manage unstructured data using DataSet. Add, update, and remove data, and listen for changes in the data.
	 *
	 * @version 8.0.0
	 * @date    2025-07-12T18:33:36.028Z
	 *
	 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
	 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
	 *
	 * @license
	 * vis.js is dual licensed under both
	 *
	 *   1. The Apache 2.0 License
	 *      http://www.apache.org/licenses/LICENSE-2.0
	 *
	 *   and
	 *
	 *   2. The MIT License
	 *      http://opensource.org/licenses/MIT
	 *
	 * vis.js may be distributed under either license.
	 */class af{_source;_transformers;_target;_listeners={add:this._add.bind(this),remove:this._remove.bind(this),update:this._update.bind(this)};constructor(t,e,i){this._source=t,this._transformers=e,this._target=i}all(){return this._target.update(this._transformItems(this._source.get())),this}start(){return this._source.on("add",this._listeners.add),this._source.on("remove",this._listeners.remove),this._source.on("update",this._listeners.update),this}stop(){return this._source.off("add",this._listeners.add),this._source.off("remove",this._listeners.remove),this._source.off("update",this._listeners.update),this}_transformItems(t){return this._transformers.reduce((t,e)=>e(t),t)}_add(t,e){null!=e&&this._target.add(this._transformItems(this._source.get(e.items)))}_update(t,e){null!=e&&this._target.update(this._transformItems(this._source.get(e.items)))}_remove(t,e){null!=e&&this._target.remove(this._transformItems(e.oldData))}}class hf{_source;_transformers=[];constructor(t){this._source=t}filter(t){return this._transformers.push(e=>e.filter(t)),this}map(t){return this._transformers.push(e=>e.map(t)),this}flatMap(t){return this._transformers.push(e=>e.flatMap(t)),this}to(t){return new af(this._source,this._transformers,t)}}function df(t){return"string"==typeof t||"number"==typeof t}class lf{delay;max;_queue=[];_timeout=null;_extended=null;constructor(t){this.delay=null,this.max=1/0,this.setOptions(t)}setOptions(t){t&&void 0!==t.delay&&(this.delay=t.delay),t&&void 0!==t.max&&(this.max=t.max),this._flushIfNeeded()}static extend(t,e){const i=new lf(e);if(void 0!==t.flush)throw new Error("Target object already has a property flush");t.flush=()=>{i.flush()};const o=[{name:"flush",original:void 0}];if(e&&e.replace)for(let n=0;n<e.replace.length;n++){const s=e.replace[n];o.push({name:s,original:t[s]}),i.replace(t,s)}return i._extended={object:t,methods:o},i}destroy(){if(this.flush(),this._extended){const t=this._extended.object,e=this._extended.methods;for(let i=0;i<e.length;i++){const o=e[i];o.original?t[o.name]=o.original:delete t[o.name]}this._extended=null}}replace(t,e){const i=this,o=t[e];if(!o)throw new Error("Method "+e+" undefined");t[e]=function(...t){i.queue({args:t,fn:o,context:this})}}queue(t){"function"==typeof t?this._queue.push({fn:t}):this._queue.push(t),this._flushIfNeeded()}_flushIfNeeded(){this._queue.length>this.max&&this.flush(),null!=this._timeout&&(clearTimeout(this._timeout),this._timeout=null),this.queue.length>0&&"number"==typeof this.delay&&(this._timeout=setTimeout(()=>{this.flush()},this.delay))}flush(){this._queue.splice(0).forEach(t=>{t.fn.apply(t.context||t.fn,t.args||[])})}}class cf{_subscribers={"*":[],add:[],remove:[],update:[]};_trigger(t,e,i){if("*"===t)throw new Error("Cannot trigger event *");[...this._subscribers[t],...this._subscribers["*"]].forEach(o=>{o(t,e,null!=i?i:null)})}on(t,e){"function"==typeof e&&this._subscribers[t].push(e)}off(t,e){this._subscribers[t]=this._subscribers[t].filter(t=>t!==e)}subscribe=cf.prototype.on;unsubscribe=cf.prototype.off;get testLeakSubscribers(){return this._subscribers}}class uf{_pairs;constructor(t){this._pairs=t}*[Symbol.iterator](){for(const[t,e]of this._pairs)yield[t,e]}*entries(){for(const[t,e]of this._pairs)yield[t,e]}*keys(){for(const[t]of this._pairs)yield t}*values(){for(const[,t]of this._pairs)yield t}toIdArray(){return[...this._pairs].map(t=>t[0])}toItemArray(){return[...this._pairs].map(t=>t[1])}toEntryArray(){return[...this._pairs]}toObjectMap(){const t=Object.create(null);for(const[e,i]of this._pairs)t[e]=i;return t}toMap(){return new Map(this._pairs)}toIdSet(){return new Set(this.toIdArray())}toItemSet(){return new Set(this.toItemArray())}cache(){return new uf([...this._pairs])}distinct(t){const e=new Set;for(const[i,o]of this._pairs)e.add(t(o,i));return e}filter(t){const e=this._pairs;return new uf({*[Symbol.iterator](){for(const[i,o]of e)t(o,i)&&(yield[i,o])}})}forEach(t){for(const[e,i]of this._pairs)t(i,e)}map(t){const e=this._pairs;return new uf({*[Symbol.iterator](){for(const[i,o]of e)yield[i,t(o,i)]}})}max(t){const e=this._pairs[Symbol.iterator]();let i=e.next();if(i.done)return null;let o=i.value[1],n=t(i.value[1],i.value[0]);for(;!(i=e.next()).done;){const[e,s]=i.value,r=t(s,e);r>n&&(n=r,o=s)}return o}min(t){const e=this._pairs[Symbol.iterator]();let i=e.next();if(i.done)return null;let o=i.value[1],n=t(i.value[1],i.value[0]);for(;!(i=e.next()).done;){const[e,s]=i.value,r=t(s,e);r<n&&(n=r,o=s)}return o}reduce(t,e){for(const[i,o]of this._pairs)e=t(e,o,i);return e}sort(t){return new uf({[Symbol.iterator]:()=>[...this._pairs].sort(([e,i],[o,n])=>t(i,n,e,o))[Symbol.iterator]()})}}class pf extends cf{flush;length;get idProp(){return this._idProp}_options;_data;_idProp;_queue=null;constructor(t,e){super(),t&&!Array.isArray(t)&&(e=t,t=[]),this._options=e||{},this._data=new Map,this.length=0,this._idProp=this._options.fieldId||"id",t&&t.length&&this.add(t),this.setOptions(e)}setOptions(t){t&&void 0!==t.queue&&(!1===t.queue?this._queue&&(this._queue.destroy(),this._queue=null):(this._queue||(this._queue=lf.extend(this,{replace:["add","update","remove"]})),t.queue&&"object"==typeof t.queue&&this._queue.setOptions(t.queue)))}add(t,e){const i=[];let o;if(Array.isArray(t)){if(t.map(t=>t[this._idProp]).some(t=>this._data.has(t)))throw new Error("A duplicate id was found in the parameter array.");for(let e=0,n=t.length;e<n;e++)o=this._addItem(t[e]),i.push(o)}else{if(!t||"object"!=typeof t)throw new Error("Unknown dataType");o=this._addItem(t),i.push(o)}return i.length&&this._trigger("add",{items:i},e),i}update(t,e){const i=[],o=[],n=[],s=[],r=this._idProp,a=t=>{const e=t[r];if(null!=e&&this._data.has(e)){const i=t,r=Object.assign({},this._data.get(e)),a=this._updateItem(i);o.push(a),s.push(i),n.push(r)}else{const e=this._addItem(t);i.push(e)}};if(Array.isArray(t))for(let e=0,i=t.length;e<i;e++)t[e]&&"object"==typeof t[e]?a(t[e]):console.warn("Ignoring input item, which is not an object at index "+e);else{if(!t||"object"!=typeof t)throw new Error("Unknown dataType");a(t)}if(i.length&&this._trigger("add",{items:i},e),o.length){const t={items:o,oldData:n,data:s};this._trigger("update",t,e)}return i.concat(o)}updateOnly(t,e){Array.isArray(t)||(t=[t]);const i=t.map(t=>{const e=this._data.get(t[this._idProp]);if(null==e)throw new Error("Updating non-existent items is not allowed.");return{oldData:e,update:t}}).map(({oldData:t,update:e})=>{const i=t[this._idProp],o=es(t,e);return this._data.set(i,o),{id:i,oldData:t,updatedData:o}});if(i.length){const t={items:i.map(t=>t.id),oldData:i.map(t=>t.oldData),data:i.map(t=>t.updatedData)};return this._trigger("update",t,e),t.items}return[]}get(t,e){let i,o,n;df(t)?(i=t,n=e):Array.isArray(t)?(o=t,n=e):n=t;const s=n&&"Object"===n.returnType?"Object":"Array",r=n&&n.filter,a=[];let h,d,l;if(null!=i)h=this._data.get(i),h&&r&&!r(h)&&(h=void 0);else if(null!=o)for(let t=0,e=o.length;t<e;t++)h=this._data.get(o[t]),null==h||r&&!r(h)||a.push(h);else{d=[...this._data.keys()];for(let t=0,e=d.length;t<e;t++)l=d[t],h=this._data.get(l),null==h||r&&!r(h)||a.push(h)}if(n&&n.order&&null==i&&this._sort(a,n.order),n&&n.fields){const t=n.fields;if(null!=i&&null!=h)h=this._filterFields(h,t);else for(let e=0,i=a.length;e<i;e++)a[e]=this._filterFields(a[e],t)}if("Object"==s){const t={};for(let e=0,i=a.length;e<i;e++){const i=a[e];t[i[this._idProp]]=i}return t}return null!=i?h??null:a}getIds(t){const e=this._data,i=t&&t.filter,o=t&&t.order,n=[...e.keys()],s=[];if(i)if(o){const t=[];for(let e=0,o=n.length;e<o;e++){const o=n[e],s=this._data.get(o);null!=s&&i(s)&&t.push(s)}this._sort(t,o);for(let e=0,i=t.length;e<i;e++)s.push(t[e][this._idProp])}else for(let t=0,e=n.length;t<e;t++){const e=n[t],o=this._data.get(e);null!=o&&i(o)&&s.push(o[this._idProp])}else if(o){const t=[];for(let i=0,o=n.length;i<o;i++){const o=n[i];t.push(e.get(o))}this._sort(t,o);for(let e=0,i=t.length;e<i;e++)s.push(t[e][this._idProp])}else for(let t=0,i=n.length;t<i;t++){const i=n[t],o=e.get(i);null!=o&&s.push(o[this._idProp])}return s}getDataSet(){return this}forEach(t,e){const i=e&&e.filter,o=[...this._data.keys()];if(e&&e.order){const i=this.get(e);for(let e=0,o=i.length;e<o;e++){const o=i[e];t(o,o[this._idProp])}}else for(let e=0,n=o.length;e<n;e++){const n=o[e],s=this._data.get(n);null==s||i&&!i(s)||t(s,n)}}map(t,e){const i=e&&e.filter,o=[],n=[...this._data.keys()];for(let e=0,s=n.length;e<s;e++){const s=n[e],r=this._data.get(s);null==r||i&&!i(r)||o.push(t(r,s))}return e&&e.order&&this._sort(o,e.order),o}_filterFields(t,e){return t?(Array.isArray(e)?e:Object.keys(e)).reduce((e,i)=>(e[i]=t[i],e),{}):t}_sort(t,e){if("string"==typeof e){const i=e;t.sort((t,e)=>{const o=t[i],n=e[i];return o>n?1:o<n?-1:0})}else{if("function"!=typeof e)throw new TypeError("Order must be a function or a string");t.sort(e)}}remove(t,e){const i=[],o=[],n=Array.isArray(t)?t:[t];for(let t=0,e=n.length;t<e;t++){const e=this._remove(n[t]);if(e){const t=e[this._idProp];null!=t&&(i.push(t),o.push(e))}}return i.length&&this._trigger("remove",{items:i,oldData:o},e),i}_remove(t){let e;if(df(t)?e=t:t&&"object"==typeof t&&(e=t[this._idProp]),null!=e&&this._data.has(e)){const t=this._data.get(e)||null;return this._data.delete(e),--this.length,t}return null}clear(t){const e=[...this._data.keys()],i=[];for(let t=0,o=e.length;t<o;t++)i.push(this._data.get(e[t]));return this._data.clear(),this.length=0,this._trigger("remove",{items:e,oldData:i},t),e}max(t){let e=null,i=null;for(const o of this._data.values()){const n=o[t];"number"==typeof n&&(null==i||n>i)&&(e=o,i=n)}return e||null}min(t){let e=null,i=null;for(const o of this._data.values()){const n=o[t];"number"==typeof n&&(null==i||n<i)&&(e=o,i=n)}return e||null}distinct(t){const e=this._data,i=[...e.keys()],o=[];let n=0;for(let s=0,r=i.length;s<r;s++){const r=i[s],a=e.get(r)[t];let h=!1;for(let t=0;t<n;t++)if(o[t]==a){h=!0;break}h||void 0===a||(o[n]=a,n++)}return o}_addItem(t){const e=function(t,e){return null==t[e]&&(t[e]=rf()),t}(t,this._idProp),i=e[this._idProp];if(this._data.has(i))throw new Error("Cannot add item: item with id "+i+" already exists");return this._data.set(i,e),++this.length,i}_updateItem(t){const e=t[this._idProp];if(null==e)throw new Error("Cannot update item: item has no id (item: "+JSON.stringify(t)+")");const i=this._data.get(e);if(!i)throw new Error("Cannot update item: no item with id "+e+" found");return this._data.set(e,{...i,...t}),e}stream(t){if(t){const e=this._data;return new uf({*[Symbol.iterator](){for(const i of t){const t=e.get(i);null!=t&&(yield[i,t])}}})}return new uf({[Symbol.iterator]:this._data.entries.bind(this._data)})}get testLeakData(){return this._data}get testLeakIdProp(){return this._idProp}get testLeakOptions(){return this._options}get testLeakQueue(){return this._queue}set testLeakQueue(t){this._queue=t}}class ff extends cf{length=0;get idProp(){return this.getDataSet().idProp}_listener;_data;_ids=new Set;_options;constructor(t,e){super(),this._options=e||{},this._listener=this._onEvent.bind(this),this.setData(t)}setData(t){if(this._data){this._data.off&&this._data.off("*",this._listener);const t=this._data.getIds({filter:this._options.filter}),e=this._data.get(t);this._ids.clear(),this.length=0,this._trigger("remove",{items:t,oldData:e})}if(null!=t){this._data=t;const e=this._data.getIds({filter:this._options.filter});for(let t=0,i=e.length;t<i;t++){const i=e[t];this._ids.add(i)}this.length=e.length,this._trigger("add",{items:e})}else this._data=new pf;this._data.on&&this._data.on("*",this._listener)}refresh(){const t=this._data.getIds({filter:this._options.filter}),e=[...this._ids],i={},o=[],n=[],s=[];for(let e=0,n=t.length;e<n;e++){const n=t[e];i[n]=!0,this._ids.has(n)||(o.push(n),this._ids.add(n))}for(let t=0,o=e.length;t<o;t++){const o=e[t],r=this._data.get(o);null==r?console.error("If you see this, report it please."):i[o]||(n.push(o),s.push(r),this._ids.delete(o))}this.length+=o.length-n.length,o.length&&this._trigger("add",{items:o}),n.length&&this._trigger("remove",{items:n,oldData:s})}get(t,e){if(null==this._data)return null;let i,o=null;df(t)||Array.isArray(t)?(o=t,i=e):i=t;const n=Object.assign({},this._options,i),s=this._options.filter,r=i&&i.filter;return s&&r&&(n.filter=t=>s(t)&&r(t)),null==o?this._data.get(n):this._data.get(o,n)}getIds(t){if(this._data.length){const e=this._options.filter,i=null!=t?t.filter:null;let o;return o=i?e?t=>e(t)&&i(t):i:e,this._data.getIds({filter:o,order:t&&t.order})}return[]}forEach(t,e){if(this._data){const i=this._options.filter,o=e&&e.filter;let n;n=o?i?function(t){return i(t)&&o(t)}:o:i,this._data.forEach(t,{filter:n,order:e&&e.order})}}map(t,e){if(this._data){const i=this._options.filter,o=e&&e.filter;let n;return n=o?i?t=>i(t)&&o(t):o:i,this._data.map(t,{filter:n,order:e&&e.order})}return[]}getDataSet(){return this._data.getDataSet()}stream(t){return this._data.stream(t||{[Symbol.iterator]:this._ids.keys.bind(this._ids)})}dispose(){this._data?.off&&this._data.off("*",this._listener);const t="This data view has already been disposed of.",e={get:()=>{throw new Error(t)},set:()=>{throw new Error(t)},configurable:!1};for(const t of Reflect.ownKeys(ff.prototype))Object.defineProperty(this,t,e)}_onEvent(t,e,i){if(!e||!e.items||!this._data)return;const o=e.items,n=[],s=[],r=[],a=[],h=[],d=[];switch(t){case"add":for(let t=0,e=o.length;t<e;t++){const e=o[t];this.get(e)&&(this._ids.add(e),n.push(e))}break;case"update":for(let t=0,i=o.length;t<i;t++){const i=o[t];this.get(i)?this._ids.has(i)?(s.push(i),h.push(e.data[t]),a.push(e.oldData[t])):(this._ids.add(i),n.push(i)):this._ids.has(i)&&(this._ids.delete(i),r.push(i),d.push(e.oldData[t]))}break;case"remove":for(let t=0,i=o.length;t<i;t++){const i=o[t];this._ids.has(i)&&(this._ids.delete(i),r.push(i),d.push(e.oldData[t]))}}this.length+=n.length-r.length,n.length&&this._trigger("add",{items:n},i),s.length&&this._trigger("update",{items:s,oldData:a,data:h},i),r.length&&this._trigger("remove",{items:r,oldData:d},i)}}function gf(t,e){return"object"==typeof e&&null!==e&&t===e.idProp&&"function"==typeof e.add&&"function"==typeof e.clear&&"function"==typeof e.distinct&&"function"==typeof e.forEach&&"function"==typeof e.get&&"function"==typeof e.getDataSet&&"function"==typeof e.getIds&&"number"==typeof e.length&&"function"==typeof e.map&&"function"==typeof e.max&&"function"==typeof e.min&&"function"==typeof e.off&&"function"==typeof e.on&&"function"==typeof e.remove&&"function"==typeof e.setOptions&&"function"==typeof e.stream&&"function"==typeof e.update&&"function"==typeof e.updateOnly}function mf(t,e){return"object"==typeof e&&null!==e&&t===e.idProp&&"function"==typeof e.forEach&&"function"==typeof e.get&&"function"==typeof e.getDataSet&&"function"==typeof e.getIds&&"number"==typeof e.length&&"function"==typeof e.map&&"function"==typeof e.off&&"function"==typeof e.on&&"function"==typeof e.stream&&gf(t,e.getDataSet())}console.warn("You're running a development build.");var vf,yf,bf,wf,_f,xf,Ef,Of,Cf,kf,Sf=Object.freeze({__proto__:null,DELETE:ts,DataSet:pf,DataStream:uf,DataView:ff,Queue:lf,createNewDataPipeFrom:function(t){return new hf(t)},isDataSetLike:gf,isDataViewLike:mf}),Tf={exports:{}},Df={};function Mf(){if(vf)return Df;vf=1;var t=hi(),e=N(),i=ri().f;return t({target:"Object",stat:!0,forced:Object.defineProperty!==i,sham:!e},{defineProperty:i}),Df}function If(){if(yf)return Tf.exports;yf=1,Mf();var t=kt().Object,e=Tf.exports=function(e,i,o){return t.defineProperty(e,i,o)};return t.defineProperty.sham&&(e.sham=!0),Tf.exports}function Pf(){return wf?bf:(wf=1,bf=If())}function Nf(){return xf?_f:(xf=1,_f=Pf())}function Bf(){return Of?Ef:(Of=1,Ef=Nf())}function zf(){return kf?Cf:(kf=1,Cf=Bf())}var Ff,Af,jf,Rf,Lf,Hf,Wf=i(zf()),qf={},Vf={},Uf={};function Yf(){if(Ff)return Uf;Ff=1;var t=we();return Uf.f=t,Uf}function Xf(){if(jf)return Af;jf=1;var t=kt(),e=ye(),i=Yf(),o=ri().f;return Af=function(n){var s=t.Symbol||(t.Symbol={});e(s,n)||o(s,n,{value:i.f(n)})}}function Gf(){if(Lf)return Rf;Lf=1;var t=B(),e=St(),i=we(),o=Zl();return Rf=function(){var n=e("Symbol"),s=n&&n.prototype,r=s&&s.valueOf,a=i("toPrimitive");s&&!s[a]&&o(s,a,function(e){return t(r,this)},{arity:1})}}var Kf,Zf,Qf,$f={};function Jf(){return Zf?Kf:(Zf=1,Kf=It()&&!!Symbol.for&&!!Symbol.keyFor)}var tg,eg={};var ig,og,ng,sg={};function rg(){if(og)return ig;og=1;var t=O(),e=hh(),i=S(),o=C(),n=ka(),s=t([].push);return ig=function(t){if(i(t))return t;if(e(t)){for(var r=t.length,a=[],h=0;h<r;h++){var d=t[h];"string"==typeof d?s(a,d):"number"!=typeof d&&"Number"!==o(d)&&"String"!==o(d)||s(a,n(d))}var l=a.length,c=!0;return function(t,i){if(c)return c=!1,i;if(e(this))return i;for(var o=0;o<l;o++)if(a[o]===t)return i}}},ig}function ag(){if(ng)return sg;ng=1;var t=hi(),e=St(),i=E(),o=B(),n=O(),s=_(),r=S(),a=Nt(),h=Qi(),d=rg(),l=It(),c=String,u=e("JSON","stringify"),p=n(/./.exec),f=n("".charAt),g=n("".charCodeAt),m=n("".replace),v=n(1.1.toString),y=/[\uD800-\uDFFF]/g,b=/^[\uD800-\uDBFF]$/,w=/^[\uDC00-\uDFFF]$/,x=!l||s(function(){var t=e("Symbol")("stringify detection");return"[null]"!==u([t])||"{}"!==u({a:t})||"{}"!==u(Object(t))}),C=s(function(){return'"\\udf06\\ud834"'!==u("\udf06\ud834")||'"\\udead"'!==u("\udead")}),k=function(t,e){var n=h(arguments),s=d(e);if(r(s)||void 0!==t&&!a(t))return n[1]=function(t,e){if(r(s)&&(e=o(s,this,c(t),e)),!a(e))return e},i(u,null,n)},T=function(t,e,i){var o=f(i,e-1),n=f(i,e+1);return p(b,t)&&!p(w,n)||p(w,t)&&!p(b,o)?"\\u"+v(g(t,0),16):t};return u&&t({target:"JSON",stat:!0,arity:3,forced:x||C},{stringify:function(t,e,o){var n=h(arguments),s=i(x?k:u,null,n);return C&&"string"==typeof s?m(s,y,T):s}}),sg}var hg,dg,lg={};function cg(){return dg||(dg=1,function(){if(Hf)return Vf;Hf=1;var t=hi(),e=w(),i=B(),o=O(),n=pe(),s=N(),r=It(),a=_(),h=ye(),d=Tt(),l=si(),c=Ot(),u=xe(),p=ka(),f=wt(),g=Cr(),m=yi(),v=uc(),y=Yc(),b=Mi(),x=Ce(),E=ri(),C=xr(),k=bt(),S=Zl(),T=nu(),D=me(),M=Or(),I=gi(),P=be(),z=we(),F=Yf(),A=Xf(),j=Gf(),R=Jl(),L=Yl(),H=Yh().forEach,W=M("hidden"),q="Symbol",V="prototype",U=L.set,Y=L.getterFor(q),X=Object[V],G=e.Symbol,K=G&&G[V],Z=e.RangeError,Q=e.TypeError,$=e.QObject,J=x.f,tt=E.f,et=y.f,it=k.f,ot=o([].push),nt=D("symbols"),st=D("op-symbols"),rt=D("wks"),at=!$||!$[V]||!$[V].findChild,ht=function(t,e,i){var o=J(X,e);o&&delete X[e],tt(t,e,i),o&&t!==X&&tt(X,e,o)},dt=s&&a(function(){return 7!==g(tt({},"a",{get:function(){return tt(this,"a",{value:7}).a}})).a})?ht:tt,lt=function(t,e){var i=nt[t]=g(K);return U(i,{type:q,tag:t,description:e}),s||(i.description=e),i},ct=function(t,e,i){t===X&&ct(st,e,i),l(t);var o=u(e);return l(i),h(nt,o)?(i.enumerable?(h(t,W)&&t[W][o]&&(t[W][o]=!1),i=g(i,{enumerable:f(0,!1)})):(h(t,W)||tt(t,W,f(1,g(null))),t[W][o]=!0),dt(t,o,i)):tt(t,o,i)},ut=function(t,e){l(t);var o=c(e),n=m(o).concat(mt(o));return H(n,function(e){s&&!i(pt,o,e)||ct(t,e,o[e])}),t},pt=function(t){var e=u(t),o=i(it,this,e);return!(this===X&&h(nt,e)&&!h(st,e))&&(!(o||!h(this,e)||!h(nt,e)||h(this,W)&&this[W][e])||o)},ft=function(t,e){var i=c(t),o=u(e);if(i!==X||!h(nt,o)||h(st,o)){var n=J(i,o);return!n||!h(nt,o)||h(i,W)&&i[W][o]||(n.enumerable=!0),n}},gt=function(t){var e=et(c(t)),i=[];return H(e,function(t){h(nt,t)||h(I,t)||ot(i,t)}),i},mt=function(t){var e=t===X,i=et(e?st:c(t)),o=[];return H(i,function(t){!h(nt,t)||e&&!h(X,t)||ot(o,nt[t])}),o};r||(G=function(){if(d(K,this))throw new Q("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?p(arguments[0]):void 0,o=P(t),n=function(t){var s=void 0===this?e:this;s===X&&i(n,st,t),h(s,W)&&h(s[W],o)&&(s[W][o]=!1);var r=f(1,t);try{dt(s,o,r)}catch(t){if(!(t instanceof Z))throw t;ht(s,o,r)}};return s&&at&&dt(X,o,{configurable:!0,set:n}),lt(o,t)},S(K=G[V],"toString",function(){return Y(this).tag}),S(G,"withoutSetter",function(t){return lt(P(t),t)}),k.f=pt,E.f=ct,C.f=ut,x.f=ft,v.f=y.f=gt,b.f=mt,F.f=function(t){return lt(z(t),t)},s&&(T(K,"description",{configurable:!0,get:function(){return Y(this).description}}),n||S(X,"propertyIsEnumerable",pt,{unsafe:!0}))),t({global:!0,constructor:!0,wrap:!0,forced:!r,sham:!r},{Symbol:G}),H(m(rt),function(t){A(t)}),t({target:q,stat:!0,forced:!r},{useSetter:function(){at=!0},useSimple:function(){at=!1}}),t({target:"Object",stat:!0,forced:!r,sham:!s},{create:function(t,e){return void 0===e?g(t):ut(g(t),e)},defineProperty:ct,defineProperties:ut,getOwnPropertyDescriptor:ft}),t({target:"Object",stat:!0,forced:!r},{getOwnPropertyNames:gt}),j(),R(G,q),I[W]=!0}(),function(){if(Qf)return $f;Qf=1;var t=hi(),e=St(),i=ye(),o=ka(),n=me(),s=Jf(),r=n("string-to-symbol-registry"),a=n("symbol-to-string-registry");t({target:"Symbol",stat:!0,forced:!s},{for:function(t){var n=o(t);if(i(r,n))return r[n];var s=e("Symbol")(n);return r[n]=s,a[s]=n,s}})}(),function(){if(tg)return eg;tg=1;var t=hi(),e=ye(),i=Nt(),o=Bt(),n=me(),s=Jf(),r=n("symbol-to-string-registry");t({target:"Symbol",stat:!0,forced:!s},{keyFor:function(t){if(!i(t))throw new TypeError(o(t)+" is not a symbol");if(e(r,t))return r[t]}})}(),ag(),function(){if(hg)return lg;hg=1;var t=hi(),e=It(),i=_(),o=Mi(),n=ve();t({target:"Object",stat:!0,forced:!e||i(function(){o.f(1)})},{getOwnPropertySymbols:function(t){var e=o.f;return e?e(n(t)):[]}})}()),qf}var ug,pg={};function fg(){return ug||(ug=1,Xf()("asyncDispose")),pg}var gg;var mg,vg={};function yg(){return mg||(mg=1,Xf()("dispose")),vg}var bg;var wg;var _g,xg={};function Eg(){return _g||(_g=1,Xf()("iterator")),xg}var Og;var Cg;var kg;var Sg;var Tg;var Dg;var Mg,Ig={};function Pg(){if(Mg)return Ig;Mg=1;var t=Xf(),e=Gf();return t("toPrimitive"),e(),Ig}var Ng,Bg={};var zg;var Fg,Ag,jg,Rg,Lg,Hg={};function Wg(){return jg?Ag:(jg=1,el(),cg(),fg(),gg||(gg=1,Xf()("asyncIterator")),yg(),bg||(bg=1,Xf()("hasInstance")),wg||(wg=1,Xf()("isConcatSpreadable")),Eg(),Og||(Og=1,Xf()("match")),Cg||(Cg=1,Xf()("matchAll")),kg||(kg=1,Xf()("replace")),Sg||(Sg=1,Xf()("search")),Tg||(Tg=1,Xf()("species")),Dg||(Dg=1,Xf()("split")),Pg(),function(){if(Ng)return Bg;Ng=1;var t=St(),e=Xf(),i=Jl();e("toStringTag"),i(t("Symbol"),"Symbol")}(),zg||(zg=1,Xf()("unscopables")),function(){if(Fg)return Hg;Fg=1;var t=w();Jl()(t.JSON,"JSON",!0)}(),Ag=kt().Symbol)}function qg(){if(Lg)return Rg;Lg=1;var t=Wg();return zu(),Rg=t}var Vg,Ug={};var Yg;var Xg;var Gg,Kg,Zg;function Qg(){if(Zg)return Kg;Zg=1;var t=qg();return function(){if(Vg)return Ug;Vg=1;var t=we(),e=ri().f,i=t("metadata"),o=Function.prototype;void 0===o[i]&&e(o,i,{value:null})}(),Yg||(Yg=1,fg()),Xg||(Xg=1,yg()),Gg||(Gg=1,Xf()("metadata")),Kg=t}var $g,Jg,tm;function em(){if(Jg)return $g;Jg=1;var t=St(),e=O(),i=t("Symbol"),o=i.keyFor,n=e(i.prototype.valueOf);return $g=i.isRegisteredSymbol||function(t){try{return void 0!==o(n(t))}catch(t){return!1}}}var im,om,nm;function sm(){if(om)return im;om=1;for(var t=me(),e=St(),i=O(),o=Nt(),n=we(),s=e("Symbol"),r=s.isWellKnownSymbol,a=e("Object","getOwnPropertyNames"),h=i(s.prototype.valueOf),d=t("wks"),l=0,c=a(s),u=c.length;l<u;l++)try{var p=c[l];o(s[p])&&n(p)}catch(t){}return im=function(t){if(r&&r(t))return!0;try{for(var e=h(t),i=0,o=a(d),n=o.length;i<n;i++)if(d[o[i]]==e)return!0}catch(t){}return!1},im}var rm;var am;var hm;var dm;var lm;var cm;var um;var pm,fm,gm,mm,vm;function ym(){if(gm)return fm;gm=1;var t=Qg();return tm||(tm=1,hi()({target:"Symbol",stat:!0},{isRegisteredSymbol:em()})),nm||(nm=1,hi()({target:"Symbol",stat:!0,forced:!0},{isWellKnownSymbol:sm()})),rm||(rm=1,Xf()("customMatcher")),am||(am=1,Xf()("observable")),hm||(hm=1,hi()({target:"Symbol",stat:!0,name:"isRegisteredSymbol"},{isRegistered:em()})),dm||(dm=1,hi()({target:"Symbol",stat:!0,name:"isWellKnownSymbol",forced:!0},{isWellKnown:sm()})),lm||(lm=1,Xf()("matcher")),cm||(cm=1,Xf()("metadataKey")),um||(um=1,Xf()("patternMatch")),pm||(pm=1,Xf()("replaceAll")),fm=t}function bm(){return vm?mm:(vm=1,mm=ym())}var wm,_m,xm,Em,Om,Cm,km,Sm,Tm,Dm,Mm=i(bm());function Im(){return _m?wm:(_m=1,ac(),Ou(),Eg(),wm=Yf().f("iterator"))}function Pm(){if(Em)return xm;Em=1;var t=Im();return zu(),xm=t}function Nm(){return Cm?Om:(Cm=1,Om=Pm())}function Bm(){return Sm?km:(Sm=1,km=Nm())}function zm(){return Dm?Tm:(Dm=1,Tm=Bm())}var Fm,Am,jm,Rm,Lm,Hm,Wm,qm,Vm,Um,Ym=i(zm());function Xm(t){return Xm="function"==typeof Mm&&"symbol"==typeof Ym?function(t){return typeof t}:function(t){return t&&"function"==typeof Mm&&t.constructor===Mm&&t!==Mm.prototype?"symbol":typeof t},Xm(t)}function Gm(){return Am?Fm:(Am=1,Pg(),Fm=Yf().f("toPrimitive"))}function Km(){return Rm?jm:(Rm=1,jm=Gm())}function Zm(){return Hm?Lm:(Hm=1,Lm=Km())}function Qm(){return qm?Wm:(qm=1,Wm=Zm())}function $m(){return Um?Vm:(Um=1,Vm=Qm())}var Jm=i($m());function tv(t){var e=function(t,e){if("object"!=Xm(t)||!t)return t;var i=t[Jm];if(void 0!==i){var o=i.call(t,e);if("object"!=Xm(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Xm(e)?e:e+""}function ev(t,e,i){return(e=tv(e))in t?Wf(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var iv,ov,nv,sv,rv,av,hv,dv,lv,cv,uv,pv,fv,gv={};function mv(){return ov?iv:(ov=1,iv="\t\n\v\f\r                　\u2028\u2029\ufeff")}function vv(){if(sv)return nv;sv=1;var t=O(),e=Et(),i=ka(),o=mv(),n=t("".replace),s=RegExp("^["+o+"]+"),r=RegExp("(^|[^"+o+"])["+o+"]+$"),a=function(t){return function(o){var a=i(e(o));return 1&t&&(a=n(a,s,"")),2&t&&(a=n(a,r,"$1")),a}};return nv={start:a(1),end:a(2),trim:a(3)}}function yv(){if(av)return rv;av=1;var t=w(),e=_(),i=O(),o=ka(),n=vv().trim,s=mv(),r=t.parseInt,a=t.Symbol,h=a&&a.iterator,d=/^[+-]?0x/i,l=i(d.exec),c=8!==r(s+"08")||22!==r(s+"0x16")||h&&!e(function(){r(Object(h))});return rv=c?function(t,e){var i=n(o(t));return r(i,e>>>0||(l(d,i)?16:10))}:r,rv}function bv(){return lv?dv:(lv=1,function(){if(hv)return gv;hv=1;var t=hi(),e=yv();t({global:!0,forced:parseInt!==e},{parseInt:e})}(),dv=kt().parseInt)}function wv(){return uv?cv:(uv=1,cv=bv())}var _v,xv,Ev,Ov,Cv,kv,Sv,Tv,Dv,Mv=i(fv?pv:(fv=1,pv=wv())),Iv={};function Pv(){if(xv)return _v;xv=1;var t=w(),e=_(),i=O(),o=ka(),n=vv().trim,s=mv(),r=i("".charAt),a=t.parseFloat,h=t.Symbol,d=h&&h.iterator,l=1/a(s+"-0")!=-1/0||d&&!e(function(){a(Object(d))});return _v=l?function(t){var e=n(o(t)),i=a(e);return 0===i&&"-"===r(e,0)?-0:i}:a,_v}function Nv(){return Cv?Ov:(Cv=1,function(){if(Ev)return Iv;Ev=1;var t=hi(),e=Pv();t({global:!0,forced:parseFloat!==e},{parseFloat:e})}(),Ov=kt().parseFloat)}function Bv(){return Sv?kv:(Sv=1,kv=Nv())}var zv,Fv,Av,jv,Rv,Lv,Hv,Wv,qv,Vv=i(Dv?Tv:(Dv=1,Tv=Bv())),Uv={};function Yv(){return Av?Fv:(Av=1,function(){if(zv)return Uv;zv=1;var t=hi(),e=Yh().filter;t({target:"Array",proto:!0,forced:!vh()("filter")},{filter:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0)}})}(),Fv=Ji()("Array","filter"))}function Xv(){if(Rv)return jv;Rv=1;var t=Tt(),e=Yv(),i=Array.prototype;return jv=function(o){var n=o.filter;return o===i||t(i,o)&&n===i.filter?e:n},jv}function Gv(){return Hv?Lv:(Hv=1,Lv=Xv())}var Kv,Zv,Qv,$v,Jv,ty,ey,iy=i(qv?Wv:(qv=1,Wv=Gv())),oy={};function ny(){if(Kv)return oy;Kv=1;var t=hi(),e=_(),i=Yc().f;return t({target:"Object",stat:!0,forced:e(function(){return!Object.getOwnPropertyNames(1)})},{getOwnPropertyNames:i}),oy}function sy(){if(Qv)return Zv;Qv=1,ny();var t=kt().Object;return Zv=function(e){return t.getOwnPropertyNames(e)},Zv}function ry(){return Jv?$v:(Jv=1,$v=sy())}var ay,hy,dy,ly,cy,uy,py=i(ey?ty:(ey=1,ty=ry()));function fy(){return hy?ay:(hy=1,cg(),ay=kt().Object.getOwnPropertySymbols)}function gy(){return ly?dy:(ly=1,dy=fy())}var my,vy,yy,by,wy,_y,xy=i(uy?cy:(uy=1,cy=gy())),Ey={exports:{}},Oy={};function Cy(){if(vy)return Ey.exports;vy=1,function(){if(my)return Oy;my=1;var t=hi(),e=_(),i=Ot(),o=Ce().f,n=N();t({target:"Object",stat:!0,forced:!n||e(function(){o(1)}),sham:!n},{getOwnPropertyDescriptor:function(t,e){return o(i(t),e)}})}();var t=kt().Object,e=Ey.exports=function(e,i){return t.getOwnPropertyDescriptor(e,i)};return t.getOwnPropertyDescriptor.sham&&(e.sham=!0),Ey.exports}function ky(){return by?yy:(by=1,yy=Cy())}var Sy,Ty,Dy,My,Iy,Py,Ny,By,zy,Fy=i(_y?wy:(_y=1,wy=ky())),Ay={};function jy(){if(Ty)return Sy;Ty=1;var t=St(),e=O(),i=uc(),o=Mi(),n=si(),s=e([].concat);return Sy=t("Reflect","ownKeys")||function(t){var e=i.f(n(t)),r=o.f;return r?s(e,r(t)):e},Sy}function Ry(){return Iy?My:(Iy=1,function(){if(Dy)return Ay;Dy=1;var t=hi(),e=N(),i=jy(),o=Ot(),n=Ce(),s=gh();t({target:"Object",stat:!0,sham:!e},{getOwnPropertyDescriptors:function(t){for(var e,r,a=o(t),h=n.f,d=i(a),l={},c=0;d.length>c;)void 0!==(r=h(a,e=d[c++]))&&s(l,e,r);return l}})}(),My=kt().Object.getOwnPropertyDescriptors)}function Ly(){return Ny?Py:(Ny=1,Py=Ry())}var Hy,Wy,qy,Vy,Uy,Yy,Xy=i(zy?By:(zy=1,By=Ly())),Gy={exports:{}},Ky={};function Zy(){if(Hy)return Ky;Hy=1;var t=hi(),e=N(),i=xr().f;return t({target:"Object",stat:!0,forced:Object.defineProperties!==i,sham:!e},{defineProperties:i}),Ky}function Qy(){if(Wy)return Gy.exports;Wy=1,Zy();var t=kt().Object,e=Gy.exports=function(e,i){return t.defineProperties(e,i)};return t.defineProperties.sham&&(e.sham=!0),Gy.exports}function $y(){return Vy?qy:(Vy=1,qy=Qy())}var Jy,tb,eb=i(Yy?Uy:(Yy=1,Uy=$y()));var ib=i(tb?Jy:(tb=1,Jy=Pf()));function ob(t,e){const i=["node","edge","label"];let o=!0;const n=Ys(e,"chosen");if("boolean"==typeof n)o=n;else if("object"==typeof n){if(-1===Zr(i).call(i,t))throw new Error("choosify: subOption '"+t+"' should be one of '"+i.join("', '")+"'");const n=Ys(e,["chosen",t]);"boolean"!=typeof n&&"function"!=typeof n||(o=n)}return o}function nb(t,e,i){if(t.width<=0||t.height<=0)return!1;if(void 0!==i){const t={x:e.x-i.x,y:e.y-i.y};if(0!==i.angle){const o=-i.angle;e={x:Math.cos(o)*t.x-Math.sin(o)*t.y,y:Math.sin(o)*t.x+Math.cos(o)*t.y}}else e=t}const o=t.x+t.width,n=t.y+t.width;return t.left<e.x&&o>e.x&&t.top<e.y&&n>e.y}function sb(t){return"string"==typeof t&&""!==t}function rb(t,e,i,o){let n=o.x,s=o.y;if("function"==typeof o.distanceToBorder){const i=o.distanceToBorder(t,e),r=Math.sin(e)*i,a=Math.cos(e)*i;a===i?(n+=i,s=o.y):r===i?(n=o.x,s-=i):(n+=a,s-=r)}else o.shape.width>o.shape.height?(n=o.x+.5*o.shape.width,s=o.y-i):(n=o.x+i,s=o.y-.5*o.shape.height);return{x:n,y:s}}var ab,hb,db,lb,cb,ub,pb,fb,gb,mb={};function vb(){return db?hb:(db=1,function(){if(ab)return mb;ab=1;var t=hi(),e=hh(),i=uh(),o=Ct(),n=ci(),s=pi(),r=Ot(),a=gh(),h=we(),d=vh(),l=Qi(),c=d("slice"),u=h("species"),p=Array,f=Math.max;t({target:"Array",proto:!0,forced:!c},{slice:function(t,h){var d,c,g,m=r(this),v=s(m),y=n(t,v),b=n(void 0===h?v:h,v);if(e(m)&&(d=m.constructor,(i(d)&&(d===p||e(d.prototype))||o(d)&&null===(d=d[u]))&&(d=void 0),d===p||void 0===d))return l(m,y,b);for(c=new(void 0===d?p:d)(f(b-y,0)),g=0;y<b;y++,g++)y in m&&a(c,g,m[y]);return c.length=g,c}})}(),hb=Ji()("Array","slice"))}function yb(){if(cb)return lb;cb=1;var t=Tt(),e=vb(),i=Array.prototype;return lb=function(o){var n=o.slice;return o===i||t(i,o)&&n===i.slice?e:n},lb}function bb(){return pb?ub:(pb=1,ub=yb())}var wb,_b,xb,Eb,Ob,Cb,kb,Sb,Tb=i(gb?fb:(gb=1,fb=bb()));function Db(){return _b?wb:(_b=1,ac(),wb=Ji()("Array","values"))}function Mb(){return Eb?xb:(Eb=1,xb=Db())}function Ib(){if(Cb)return Ob;Cb=1,zu();var t=Ca(),e=ye(),i=Tt(),o=Mb(),n=Array.prototype,s={DOMTokenList:!0,NodeList:!0};return Ob=function(r){var a=r.values;return r===n||i(n,r)&&a===n.values||e(s,t(r))?o:a},Ob}var Pb=i(Sb?kb:(Sb=1,kb=Ib()));class Nb{constructor(t){this.measureText=t,this.current=0,this.width=0,this.height=0,this.lines=[]}_add(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"normal";void 0===this.lines[t]&&(this.lines[t]={width:0,height:0,blocks:[]});let o=e;void 0!==e&&""!==e||(o=" ");const n=this.measureText(o,i),s=Ki({},Pb(n));s.text=e,s.width=n.width,s.mod=i,void 0!==e&&""!==e||(s.width=0),this.lines[t].blocks.push(s),this.lines[t].width+=s.width}curWidth(){const t=this.lines[this.current];return void 0===t?0:t.width}append(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"normal";this._add(this.current,t,e)}newLine(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"normal";this._add(this.current,t,e),this.current++}determineLineHeights(){for(let t=0;t<this.lines.length;t++){const e=this.lines[t];let i=0;if(void 0!==e.blocks)for(let t=0;t<e.blocks.length;t++){const o=e.blocks[t];i<o.height&&(i=o.height)}e.height=i}}determineLabelSize(){let t=0,e=0;for(let i=0;i<this.lines.length;i++){const o=this.lines[i];o.width>t&&(t=o.width),e+=o.height}this.width=t,this.height=e}removeEmptyBlocks(){const t=[];for(let e=0;e<this.lines.length;e++){const i=this.lines[e];if(0===i.blocks.length)continue;if(e===this.lines.length-1&&0===i.width)continue;const o={};let n;Ki(o,i),o.blocks=[];const s=[];for(let t=0;t<i.blocks.length;t++){const e=i.blocks[t];0!==e.width?s.push(e):void 0===n&&(n=e)}0===s.length&&void 0!==n&&s.push(n),o.blocks=s,t.push(o)}return t}finalize(){this.determineLineHeights(),this.determineLabelSize();const t=this.removeEmptyBlocks();return{width:this.width,height:this.height,lines:t}}}const Bb={"<b>":/<b>/,"<i>":/<i>/,"<code>":/<code>/,"</b>":/<\/b>/,"</i>":/<\/i>/,"</code>":/<\/code>/,"*":/\*/,_:/_/,"`":/`/,afterBold:/[^*]/,afterItal:/[^_]/,afterMono:/[^`]/};class zb{constructor(t){this.text=t,this.bold=!1,this.ital=!1,this.mono=!1,this.spacing=!1,this.position=0,this.buffer="",this.modStack=[],this.blocks=[]}mod(){return 0===this.modStack.length?"normal":this.modStack[0]}modName(){return 0===this.modStack.length?"normal":"mono"===this.modStack[0]?"mono":this.bold&&this.ital?"boldital":this.bold?"bold":this.ital?"ital":void 0}emitBlock(){this.spacing&&(this.add(" "),this.spacing=!1),this.buffer.length>0&&(this.blocks.push({text:this.buffer,mod:this.modName()}),this.buffer="")}add(t){" "===t&&(this.spacing=!0),this.spacing&&(this.buffer+=" ",this.spacing=!1)," "!=t&&(this.buffer+=t)}parseWS(t){return!!/[ \t]/.test(t)&&(this.mono?this.add(t):this.spacing=!0,!0)}setTag(t){this.emitBlock(),this[t]=!0,this.modStack.unshift(t)}unsetTag(t){this.emitBlock(),this[t]=!1,this.modStack.shift()}parseStartTag(t,e){return!(this.mono||this[t]||!this.match(e))&&(this.setTag(t),!0)}match(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const[i,o]=this.prepareRegExp(t),n=i.test(this.text.substr(this.position,o));return n&&e&&(this.position+=o-1),n}parseEndTag(t,e,i){let o=this.mod()===t;return o="mono"===t?o&&this.mono:o&&!this.mono,!(!o||!this.match(e))&&(void 0!==i?(this.position===this.text.length-1||this.match(i,!1))&&this.unsetTag(t):this.unsetTag(t),!0)}replace(t,e){return!!this.match(t)&&(this.add(e),this.position+=length-1,!0)}prepareRegExp(t){let e,i;if(t instanceof RegExp)i=t,e=1;else{const o=Bb[t];i=void 0!==o?o:new RegExp(t),e=t.length}return[i,e]}}class Fb{constructor(t,e,i,o){this.ctx=t,this.parent=e,this.selected=i,this.hover=o;this.lines=new Nb((e,n)=>{if(void 0===e)return 0;const s=this.parent.getFormattingValues(t,i,o,n);let r=0;if(""!==e){r=this.ctx.measureText(e).width}return{width:r,values:s}})}process(t){if(!sb(t))return this.lines.finalize();const e=this.parent.fontOptions;t=(t=t.replace(/\r\n/g,"\n")).replace(/\r/g,"\n");const i=String(t).split("\n"),o=i.length;if(e.multi)for(let t=0;t<o;t++){const o=this.splitBlocks(i[t],e.multi);if(void 0!==o)if(0!==o.length){if(e.maxWdt>0)for(let t=0;t<o.length;t++){const e=o[t].mod,i=o[t].text;this.splitStringIntoLines(i,e,!0)}else for(let t=0;t<o.length;t++){const e=o[t].mod,i=o[t].text;this.lines.append(i,e)}this.lines.newLine()}else this.lines.newLine("")}else if(e.maxWdt>0)for(let t=0;t<o;t++)this.splitStringIntoLines(i[t]);else for(let t=0;t<o;t++)this.lines.newLine(i[t]);return this.lines.finalize()}decodeMarkupSystem(t){let e="none";return"markdown"===t||"md"===t?e="markdown":!0!==t&&"html"!==t||(e="html"),e}splitHtmlBlocks(t){const e=new zb(t),i=t=>{if(/&/.test(t)){return e.replace(e.text,"&lt;","<")||e.replace(e.text,"&amp;","&")||e.add("&"),!0}return!1};for(;e.position<e.text.length;){const t=e.text.charAt(e.position);e.parseWS(t)||/</.test(t)&&(e.parseStartTag("bold","<b>")||e.parseStartTag("ital","<i>")||e.parseStartTag("mono","<code>")||e.parseEndTag("bold","</b>")||e.parseEndTag("ital","</i>")||e.parseEndTag("mono","</code>"))||i(t)||e.add(t),e.position++}return e.emitBlock(),e.blocks}splitMarkdownBlocks(t){const e=new zb(t);let i=!0;const o=t=>!!/\\/.test(t)&&(e.position<this.text.length+1&&(e.position++,t=this.text.charAt(e.position),/ \t/.test(t)?e.spacing=!0:(e.add(t),i=!1)),!0);for(;e.position<e.text.length;){const t=e.text.charAt(e.position);e.parseWS(t)||o(t)||(i||e.spacing)&&(e.parseStartTag("bold","*")||e.parseStartTag("ital","_")||e.parseStartTag("mono","`"))||e.parseEndTag("bold","*","afterBold")||e.parseEndTag("ital","_","afterItal")||e.parseEndTag("mono","`","afterMono")||(e.add(t),i=!1),e.position++}return e.emitBlock(),e.blocks}splitBlocks(t,e){const i=this.decodeMarkupSystem(e);return"none"===i?[{text:t,mod:"normal"}]:"markdown"===i?this.splitMarkdownBlocks(t):"html"===i?this.splitHtmlBlocks(t):void 0}overMaxWidth(t){const e=this.ctx.measureText(t).width;return this.lines.curWidth()+e>this.parent.fontOptions.maxWdt}getLongestFit(t){let e="",i=0;for(;i<t.length;){const o=e+(""===e?"":" ")+t[i];if(this.overMaxWidth(o))break;e=o,i++}return i}getLongestFitWord(t){let e=0;for(;e<t.length&&!this.overMaxWidth(Tb(t).call(t,0,e));)e++;return e}splitStringIntoLines(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"normal",i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.parent.getFormattingValues(this.ctx,this.selected,this.hover,e);let o=(t=(t=t.replace(/^( +)/g,"$1\r")).replace(/([^\r][^ ]*)( +)/g,"$1\r$2\r")).split("\r");for(;o.length>0;){let t=this.getLongestFit(o);if(0===t){const t=o[0],i=this.getLongestFitWord(t);this.lines.newLine(Tb(t).call(t,0,i),e),o[0]=Tb(t).call(t,i)}else{let n=t;" "===o[t-1]?t--:" "===o[n]&&n++;const s=Tb(o).call(o,0,t).join("");t==o.length&&i?this.lines.append(s,e):this.lines.newLine(s,e),o=Tb(o).call(o,n)}}}}const Ab=["bold","ital","boldital","mono"];class jb{constructor(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.body=t,this.pointToSelf=!1,this.baseSize=void 0,this.fontOptions={},this.setOptions(e),this.size={top:0,left:0,width:0,height:0,yLine:0},this.isEdgeLabel=i}setOptions(t){if(this.elementOptions=t,this.initFontOptions(t.font),sb(t.label)?this.labelDirty=!0:t.label=void 0,void 0!==t.font&&null!==t.font)if("string"==typeof t.font)this.baseSize=this.fontOptions.size;else if("object"==typeof t.font){const e=t.font.size;void 0!==e&&(this.baseSize=e)}}initFontOptions(t){Ts(Ab,t=>{this.fontOptions[t]={}}),jb.parseFontString(this.fontOptions,t)?this.fontOptions.vadjust=0:Ts(t,(t,e)=>{null!=t&&"object"!=typeof t&&(this.fontOptions[e]=t)})}static parseFontString(t,e){if(!e||"string"!=typeof e)return!1;const i=e.split(" ");return t.size=+i[0].replace("px",""),t.face=i[1],t.color=i[2],!0}constrain(t){const e={constrainWidth:!1,maxWdt:-1,minWdt:-1,constrainHeight:!1,minHgt:-1,valign:"middle"},i=Ys(t,"widthConstraint");if("number"==typeof i)e.maxWdt=Number(i),e.minWdt=Number(i);else if("object"==typeof i){const i=Ys(t,["widthConstraint","maximum"]);"number"==typeof i&&(e.maxWdt=Number(i));const o=Ys(t,["widthConstraint","minimum"]);"number"==typeof o&&(e.minWdt=Number(o))}const o=Ys(t,"heightConstraint");if("number"==typeof o)e.minHgt=Number(o);else if("object"==typeof o){const i=Ys(t,["heightConstraint","minimum"]);"number"==typeof i&&(e.minHgt=Number(i));const o=Ys(t,["heightConstraint","valign"]);"string"==typeof o&&("top"!==o&&"bottom"!==o||(e.valign=o))}return e}update(t,e){this.setOptions(t,!0),this.propagateFonts(e),Es(this.fontOptions,this.constrain(e)),this.fontOptions.chooser=ob("label",e)}adjustSizes(t){const e=t?t.right+t.left:0;this.fontOptions.constrainWidth&&(this.fontOptions.maxWdt-=e,this.fontOptions.minWdt-=e);const i=t?t.top+t.bottom:0;this.fontOptions.constrainHeight&&(this.fontOptions.minHgt-=i)}addFontOptionsToPile(t,e){for(let i=0;i<e.length;++i)this.addFontToPile(t,e[i])}addFontToPile(t,e){if(void 0===e)return;if(void 0===e.font||null===e.font)return;const i=e.font;t.push(i)}getBasicOptions(t){const e={};for(let i=0;i<t.length;++i){let o=t[i];const n={};jb.parseFontString(n,o)&&(o=n),Ts(o,(t,i)=>{void 0!==t&&(Object.prototype.hasOwnProperty.call(e,i)||(-1!==Zr(Ab).call(Ab,i)?e[i]={}:e[i]=t))})}return e}getFontOption(t,e,i){let o;for(let n=0;n<t.length;++n){const s=t[n];if(Object.prototype.hasOwnProperty.call(s,e)){if(o=s[e],null==o)continue;const t={};if(jb.parseFontString(t,o)&&(o=t),Object.prototype.hasOwnProperty.call(o,i))return o[i]}}if(Object.prototype.hasOwnProperty.call(this.fontOptions,i))return this.fontOptions[i];throw new Error("Did not find value for multi-font for property: '"+i+"'")}getFontOptions(t,e){const i={},o=["color","size","face","mod","vadjust"];for(let n=0;n<o.length;++n){const s=o[n];i[s]=this.getFontOption(t,e,s)}return i}propagateFonts(t){const e=[];this.addFontOptionsToPile(e,t),this.fontOptions=this.getBasicOptions(e);for(let t=0;t<Ab.length;++t){const i=Ab[t],o=this.fontOptions[i];Ts(this.getFontOptions(e,i),(t,e)=>{o[e]=t}),o.size=Number(o.size),o.vadjust=Number(o.vadjust)}}draw(t,e,i,o,n){let s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"middle";if(void 0===this.elementOptions.label)return;let r=this.fontOptions.size*this.body.view.scale;this.elementOptions.label&&r<this.elementOptions.scaling.label.drawThreshold-1||(r>=this.elementOptions.scaling.label.maxVisible&&(r=Number(this.elementOptions.scaling.label.maxVisible)/this.body.view.scale),this.calculateLabelSize(t,o,n,e,i,s),this._drawBackground(t),this._drawText(t,e,this.size.yLine,s,r))}_drawBackground(t){if(void 0!==this.fontOptions.background&&"none"!==this.fontOptions.background){t.fillStyle=this.fontOptions.background;const e=this.getSize();t.fillRect(e.left,e.top,e.width,e.height)}}_drawText(t,e,i){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"middle",n=arguments.length>4?arguments[4]:void 0;[e,i]=this._setAlignment(t,e,i,o),t.textAlign="left",e-=this.size.width/2,this.fontOptions.valign&&this.size.height>this.size.labelHeight&&("top"===this.fontOptions.valign&&(i-=(this.size.height-this.size.labelHeight)/2),"bottom"===this.fontOptions.valign&&(i+=(this.size.height-this.size.labelHeight)/2));for(let o=0;o<this.lineCount;o++){const s=this.lines[o];if(s&&s.blocks){let o=0;this.isEdgeLabel||"center"===this.fontOptions.align?o+=(this.size.width-s.width)/2:"right"===this.fontOptions.align&&(o+=this.size.width-s.width);for(let r=0;r<s.blocks.length;r++){const a=s.blocks[r];t.font=a.font;const[h,d]=this._getColor(a.color,n,a.strokeColor);a.strokeWidth>0&&(t.lineWidth=a.strokeWidth,t.strokeStyle=d,t.lineJoin="round"),t.fillStyle=h,a.strokeWidth>0&&t.strokeText(a.text,e+o,i+a.vadjust),t.fillText(a.text,e+o,i+a.vadjust),o+=a.width}i+=s.height}}}_setAlignment(t,e,i,o){if(this.isEdgeLabel&&"horizontal"!==this.fontOptions.align&&!1===this.pointToSelf){e=0,i=0;const o=2;"top"===this.fontOptions.align?(t.textBaseline="alphabetic",i-=2*o):"bottom"===this.fontOptions.align?(t.textBaseline="hanging",i+=2*o):t.textBaseline="middle"}else t.textBaseline=o;return[e,i]}_getColor(t,e,i){let o=t||"#000000",n=i||"#ffffff";if(e<=this.elementOptions.scaling.label.drawThreshold){const t=Math.max(0,Math.min(1,1-(this.elementOptions.scaling.label.drawThreshold-e)));o=Ps(o,t),n=Ps(n,t)}return[o,n]}getTextSize(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this._processLabel(t,e,i),{width:this.size.width,height:this.size.height,lineCount:this.lineCount}}getSize(){let t=this.size.left,e=this.size.top-1;if(this.isEdgeLabel){const i=.5*-this.size.width;switch(this.fontOptions.align){case"middle":t=i,e=.5*-this.size.height;break;case"top":t=i,e=-(this.size.height+2);break;case"bottom":t=i,e=2}}return{left:t,top:e,width:this.size.width,height:this.size.height}}calculateLabelSize(t,e,i){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"middle";this._processLabel(t,e,i),this.size.left=o-.5*this.size.width,this.size.top=n-.5*this.size.height,this.size.yLine=n+.5*(1-this.lineCount)*this.fontOptions.size,"hanging"===s&&(this.size.top+=.5*this.fontOptions.size,this.size.top+=4,this.size.yLine+=4)}getFormattingValues(t,e,i,o){const n=function(t,e,i){return"normal"===e?"mod"===i?"":t[i]:void 0!==t[e][i]?t[e][i]:t[i]},s={color:n(this.fontOptions,o,"color"),size:n(this.fontOptions,o,"size"),face:n(this.fontOptions,o,"face"),mod:n(this.fontOptions,o,"mod"),vadjust:n(this.fontOptions,o,"vadjust"),strokeWidth:this.fontOptions.strokeWidth,strokeColor:this.fontOptions.strokeColor};(e||i)&&("normal"===o&&!0===this.fontOptions.chooser&&this.elementOptions.labelHighlightBold?s.mod="bold":"function"==typeof this.fontOptions.chooser&&this.fontOptions.chooser(s,this.elementOptions.id,e,i));let r="";return void 0!==s.mod&&""!==s.mod&&(r+=s.mod+" "),r+=s.size+"px "+s.face,t.font=r.replace(/"/g,""),s.font=t.font,s.height=s.size,s}differentState(t,e){return t!==this.selectedState||e!==this.hoverState}_processLabelText(t,e,i,o){return new Fb(t,this,e,i).process(o)}_processLabel(t,e,i){if(!1===this.labelDirty&&!this.differentState(e,i))return;const o=this._processLabelText(t,e,i,this.elementOptions.label);this.fontOptions.minWdt>0&&o.width<this.fontOptions.minWdt&&(o.width=this.fontOptions.minWdt),this.size.labelHeight=o.height,this.fontOptions.minHgt>0&&o.height<this.fontOptions.minHgt&&(o.height=this.fontOptions.minHgt),this.lines=o.lines,this.lineCount=o.lines.length,this.size.width=o.width,this.size.height=o.height,this.selectedState=e,this.hoverState=i,this.labelDirty=!1}visible(){if(0===this.size.width||0===this.size.height||void 0===this.elementOptions.label)return!1;return!(this.fontOptions.size*this.body.view.scale<this.elementOptions.scaling.label.drawThreshold-1)}}var Rb,Lb,Hb,Wb,qb,Vb,Ub,Yb,Xb,Gb,Kb,Zb={};function Qb(){if(Lb)return Rb;Lb=1;var t=ve(),e=ci(),i=pi();return Rb=function(o){for(var n=t(this),s=i(n),r=arguments.length,a=e(r>1?arguments[1]:void 0,s),h=r>2?arguments[2]:void 0,d=void 0===h?s:e(h,s);d>a;)n[a++]=o;return n},Rb}function $b(){return qb?Wb:(qb=1,function(){if(Hb)return Zb;Hb=1;var t=hi(),e=Qb(),i=$r();t({target:"Array",proto:!0},{fill:e}),i("fill")}(),Wb=Ji()("Array","fill"))}function Jb(){if(Ub)return Vb;Ub=1;var t=Tt(),e=$b(),i=Array.prototype;return Vb=function(o){var n=o.fill;return o===i||t(i,o)&&n===i.fill?e:n},Vb}function tw(){return Xb?Yb:(Xb=1,Yb=Jb())}var ew=i(Kb?Gb:(Kb=1,Gb=tw()));class iw{constructor(t,e,i){this.body=e,this.labelModule=i,this.setOptions(t),this.top=void 0,this.left=void 0,this.height=void 0,this.width=void 0,this.radius=void 0,this.margin=void 0,this.refreshNeeded=!0,this.boundingBox={top:0,left:0,right:0,bottom:0}}setOptions(t){this.options=t}_setMargins(t){this.margin={},this.options.margin&&("object"==typeof this.options.margin?(this.margin.top=this.options.margin.top,this.margin.right=this.options.margin.right,this.margin.bottom=this.options.margin.bottom,this.margin.left=this.options.margin.left):(this.margin.top=this.options.margin,this.margin.right=this.options.margin,this.margin.bottom=this.options.margin,this.margin.left=this.options.margin)),t.adjustSizes(this.margin)}_distanceToBorder(t,e){const i=this.options.borderWidth;return t&&this.resize(t),Math.min(Math.abs(this.width/2/Math.cos(e)),Math.abs(this.height/2/Math.sin(e)))+i}enableShadow(t,e){e.shadow&&(t.shadowColor=e.shadowColor,t.shadowBlur=e.shadowSize,t.shadowOffsetX=e.shadowX,t.shadowOffsetY=e.shadowY)}disableShadow(t,e){e.shadow&&(t.shadowColor="rgba(0,0,0,0)",t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0)}enableBorderDashes(t,e){if(!1!==e.borderDashes)if(void 0!==t.setLineDash){let i=e.borderDashes;!0===i&&(i=[5,15]),t.setLineDash(i)}else console.warn("setLineDash is not supported in this browser. The dashed borders cannot be used."),this.options.shapeProperties.borderDashes=!1,e.borderDashes=!1}disableBorderDashes(t,e){!1!==e.borderDashes&&(void 0!==t.setLineDash?t.setLineDash([0]):(console.warn("setLineDash is not supported in this browser. The dashed borders cannot be used."),this.options.shapeProperties.borderDashes=!1,e.borderDashes=!1))}needsRefresh(t,e){return!0===this.refreshNeeded?(this.refreshNeeded=!1,!0):void 0===this.width||this.labelModule.differentState(t,e)}initContextForDraw(t,e){const i=e.borderWidth/this.body.view.scale;t.lineWidth=Math.min(this.width,i),t.strokeStyle=e.borderColor,t.fillStyle=e.color}performStroke(t,e){const i=e.borderWidth/this.body.view.scale;t.save(),i>0&&(this.enableBorderDashes(t,e),t.stroke(),this.disableBorderDashes(t,e)),t.restore()}performFill(t,e){t.save(),t.fillStyle=e.color,this.enableShadow(t,e),ew(t).call(t),this.disableShadow(t,e),t.restore(),this.performStroke(t,e)}_addBoundingBoxMargin(t){this.boundingBox.left-=t,this.boundingBox.top-=t,this.boundingBox.bottom+=t,this.boundingBox.right+=t}_updateBoundingBox(t,e,i,o,n){void 0!==i&&this.resize(i,o,n),this.left=t-this.width/2,this.top=e-this.height/2,this.boundingBox.left=this.left,this.boundingBox.top=this.top,this.boundingBox.bottom=this.top+this.height,this.boundingBox.right=this.left+this.width}updateBoundingBox(t,e,i,o,n){this._updateBoundingBox(t,e,i,o,n)}getDimensionsFromLabel(t,e,i){this.textSize=this.labelModule.getTextSize(t,e,i);let o=this.textSize.width,n=this.textSize.height;return 0===o&&(o=14,n=14),{width:o,height:n}}}let ow=class extends iw{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover;if(this.needsRefresh(e,i)){const o=this.getDimensionsFromLabel(t,e,i);this.width=o.width+this.margin.right+this.margin.left,this.height=o.height+this.margin.top+this.margin.bottom,this.radius=this.width/2}}draw(t,e,i,o,n,s){this.resize(t,o,n),this.left=e-this.width/2,this.top=i-this.height/2,this.initContextForDraw(t,s),so(t,this.left,this.top,this.width,this.height,s.borderRadius),this.performFill(t,s),this.updateBoundingBox(e,i,t,o,n),this.labelModule.draw(t,this.left+this.textSize.width/2+this.margin.left,this.top+this.textSize.height/2+this.margin.top,o,n)}updateBoundingBox(t,e,i,o,n){this._updateBoundingBox(t,e,i,o,n);const s=this.options.shapeProperties.borderRadius;this._addBoundingBoxMargin(s)}distanceToBorder(t,e){t&&this.resize(t);const i=this.options.borderWidth;return Math.min(Math.abs(this.width/2/Math.cos(e)),Math.abs(this.height/2/Math.sin(e)))+i}};class nw extends iw{constructor(t,e,i){super(t,e,i),this.labelOffset=0,this.selected=!1}setOptions(t,e,i){this.options=t,void 0===e&&void 0===i||this.setImages(e,i)}setImages(t,e){e&&this.selected?(this.imageObj=e,this.imageObjAlt=t):(this.imageObj=t,this.imageObjAlt=e)}switchImages(t){const e=t&&!this.selected||!t&&this.selected;if(this.selected=t,void 0!==this.imageObjAlt&&e){const t=this.imageObj;this.imageObj=this.imageObjAlt,this.imageObjAlt=t}}_getImagePadding(){const t={top:0,right:0,bottom:0,left:0};if(this.options.imagePadding){const e=this.options.imagePadding;"object"==typeof e?(t.top=e.top,t.right=e.right,t.bottom=e.bottom,t.left=e.left):(t.top=e,t.right=e,t.bottom=e,t.left=e)}return t}_resizeImage(){let t,e;if(!1===this.options.shapeProperties.useImageSize){let i=1,o=1;this.imageObj.width&&this.imageObj.height&&(this.imageObj.width>this.imageObj.height?i=this.imageObj.width/this.imageObj.height:o=this.imageObj.height/this.imageObj.width),t=2*this.options.size*i,e=2*this.options.size*o}else{const i=this._getImagePadding();t=this.imageObj.width+i.left+i.right,e=this.imageObj.height+i.top+i.bottom}this.width=t,this.height=e,this.radius=.5*this.width}_drawRawCircle(t,e,i,o){this.initContextForDraw(t,o),no(t,e,i,o.size),this.performFill(t,o)}_drawImageAtPosition(t,e){if(0!=this.imageObj.width){t.globalAlpha=void 0!==e.opacity?e.opacity:1,this.enableShadow(t,e);let i=1;!0===this.options.shapeProperties.interpolation&&(i=this.imageObj.width/this.width/this.body.view.scale);const o=this._getImagePadding(),n=this.left+o.left,s=this.top+o.top,r=this.width-o.left-o.right,a=this.height-o.top-o.bottom;this.imageObj.drawImageAtPosition(t,i,n,s,r,a),this.disableShadow(t,e)}}_drawImageLabel(t,e,i,o,n){let s=0;if(void 0!==this.height){s=.5*this.height;const e=this.labelModule.getTextSize(t,o,n);e.lineCount>=1&&(s+=e.height/2)}const r=i+s;this.options.label&&(this.labelOffset=s),this.labelModule.draw(t,e,r,o,n,"hanging")}}let sw=class extends nw{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover;if(this.needsRefresh(e,i)){const o=this.getDimensionsFromLabel(t,e,i),n=Math.max(o.width+this.margin.right+this.margin.left,o.height+this.margin.top+this.margin.bottom);this.options.size=n/2,this.width=n,this.height=n,this.radius=this.width/2}}draw(t,e,i,o,n,s){this.resize(t,o,n),this.left=e-this.width/2,this.top=i-this.height/2,this._drawRawCircle(t,e,i,s),this.updateBoundingBox(e,i),this.labelModule.draw(t,this.left+this.textSize.width/2+this.margin.left,i,o,n)}updateBoundingBox(t,e){this.boundingBox.top=e-this.options.size,this.boundingBox.left=t-this.options.size,this.boundingBox.right=t+this.options.size,this.boundingBox.bottom=e+this.options.size}distanceToBorder(t){return t&&this.resize(t),.5*this.width}};class rw extends nw{constructor(t,e,i,o,n){super(t,e,i),this.setImages(o,n)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover;if(void 0===this.imageObj.src||void 0===this.imageObj.width||void 0===this.imageObj.height){const t=2*this.options.size;return this.width=t,this.height=t,void(this.radius=.5*this.width)}this.needsRefresh(e,i)&&this._resizeImage()}draw(t,e,i,o,n,s){this.switchImages(o),this.resize();let r=e,a=i;"top-left"===this.options.shapeProperties.coordinateOrigin?(this.left=e,this.top=i,r+=this.width/2,a+=this.height/2):(this.left=e-this.width/2,this.top=i-this.height/2),this._drawRawCircle(t,r,a,s),t.save(),t.clip(),this._drawImageAtPosition(t,s),t.restore(),this._drawImageLabel(t,r,a,o,n),this.updateBoundingBox(e,i)}updateBoundingBox(t,e){"top-left"===this.options.shapeProperties.coordinateOrigin?(this.boundingBox.top=e,this.boundingBox.left=t,this.boundingBox.right=t+2*this.options.size,this.boundingBox.bottom=e+2*this.options.size):(this.boundingBox.top=e-this.options.size,this.boundingBox.left=t-this.options.size,this.boundingBox.right=t+this.options.size,this.boundingBox.bottom=e+this.options.size),this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelOffset)}distanceToBorder(t){return t&&this.resize(t),.5*this.width}}class aw extends iw{constructor(t,e,i){super(t,e,i)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{size:this.options.size};if(this.needsRefresh(e,i)){var n,s;this.labelModule.getTextSize(t,e,i);const r=2*o.size;this.width=null!==(n=this.customSizeWidth)&&void 0!==n?n:r,this.height=null!==(s=this.customSizeHeight)&&void 0!==s?s:r,this.radius=.5*this.width}}_drawShape(t,e,i,o,n,s,r,a){var h;return this.resize(t,s,r,a),this.left=o-this.width/2,this.top=n-this.height/2,this.initContextForDraw(t,a),(h=e,Object.prototype.hasOwnProperty.call(lo,h)?lo[h]:function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),o=1;o<e;o++)i[o-1]=arguments[o];CanvasRenderingContext2D.prototype[h].call(t,i)})(t,o,n,a.size),this.performFill(t,a),void 0!==this.options.icon&&void 0!==this.options.icon.code&&(t.font=(s?"bold ":"")+this.height/2+"px "+(this.options.icon.face||"FontAwesome"),t.fillStyle=this.options.icon.color||"black",t.textAlign="center",t.textBaseline="middle",t.fillText(this.options.icon.code,o,n)),{drawExternalLabel:()=>{if(void 0!==this.options.label){this.labelModule.calculateLabelSize(t,s,r,o,n,"hanging");const e=n+.5*this.height+.5*this.labelModule.size.height;this.labelModule.draw(t,o,e,s,r,"hanging")}this.updateBoundingBox(o,n)}}}updateBoundingBox(t,e){this.boundingBox.top=e-this.options.size,this.boundingBox.left=t-this.options.size,this.boundingBox.right=t+this.options.size,this.boundingBox.bottom=e+this.options.size,void 0!==this.options.label&&this.labelModule.size.width>0&&(this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelModule.size.height))}}function hw(t,e){var i=Sp(t);if(xy){var o=xy(t);e&&(o=iy(o).call(o,function(e){return Fy(t,e).enumerable})),i.push.apply(i,o)}return i}function dw(t){for(var e=1;e<arguments.length;e++){var i,o,n=null!=arguments[e]?arguments[e]:{};e%2?Qh(i=hw(Object(n),!0)).call(i,function(e){ev(t,e,n[e])}):Xy?eb(t,Xy(n)):Qh(o=hw(Object(n))).call(o,function(e){ib(t,e,Fy(n,e))})}return t}class lw extends aw{constructor(t,e,i,o){super(t,e,i,o),this.ctxRenderer=o}draw(t,e,i,o,n,s){this.resize(t,o,n,s),this.left=e-this.width/2,this.top=i-this.height/2,t.save();const r=this.ctxRenderer({ctx:t,id:this.options.id,x:e,y:i,state:{selected:o,hover:n},style:dw({},s),label:this.options.label});if(null!=r.drawNode&&r.drawNode(),t.restore(),r.drawExternalLabel){const e=r.drawExternalLabel;r.drawExternalLabel=()=>{t.save(),e(),t.restore()}}return r.nodeDimensions&&(this.customSizeWidth=r.nodeDimensions.width,this.customSizeHeight=r.nodeDimensions.height),r}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class cw extends iw{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t,e,i){if(this.needsRefresh(e,i)){const o=this.getDimensionsFromLabel(t,e,i).width+this.margin.right+this.margin.left;this.width=o,this.height=o,this.radius=this.width/2}}draw(t,e,i,o,n,s){this.resize(t,o,n),this.left=e-this.width/2,this.top=i-this.height/2,this.initContextForDraw(t,s),ao(t,e-this.width/2,i-this.height/2,this.width,this.height),this.performFill(t,s),this.updateBoundingBox(e,i,t,o,n),this.labelModule.draw(t,this.left+this.textSize.width/2+this.margin.left,this.top+this.textSize.height/2+this.margin.top,o,n)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}let uw=class extends aw{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,n,s){return this._drawShape(t,"diamond",4,e,i,o,n,s)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}};class pw extends aw{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,n,s){return this._drawShape(t,"circle",2,e,i,o,n,s)}distanceToBorder(t){return t&&this.resize(t),this.options.size}}class fw extends iw{constructor(t,e,i){super(t,e,i)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover;if(this.needsRefresh(e,i)){const o=this.getDimensionsFromLabel(t,e,i);this.height=2*o.height,this.width=o.width+o.height,this.radius=.5*this.width}}draw(t,e,i,o,n,s){this.resize(t,o,n),this.left=e-.5*this.width,this.top=i-.5*this.height,this.initContextForDraw(t,s),ro(t,this.left,this.top,this.width,this.height),this.performFill(t,s),this.updateBoundingBox(e,i,t,o,n),this.labelModule.draw(t,e,i,o,n)}distanceToBorder(t,e){t&&this.resize(t);const i=.5*this.width,o=.5*this.height,n=Math.sin(e)*i,s=Math.cos(e)*o;return i*o/Math.sqrt(n*n+s*s)}}class gw extends iw{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t,e,i){this.needsRefresh(e,i)&&(this.iconSize={width:Number(this.options.icon.size),height:Number(this.options.icon.size)},this.width=this.iconSize.width+this.margin.right+this.margin.left,this.height=this.iconSize.height+this.margin.top+this.margin.bottom,this.radius=.5*this.width)}draw(t,e,i,o,n,s){return this.resize(t,o,n),this.options.icon.size=this.options.icon.size||50,this.left=e-this.width/2,this.top=i-this.height/2,this._icon(t,e,i,o,n,s),{drawExternalLabel:()=>{if(void 0!==this.options.label){const e=5;this.labelModule.draw(t,this.left+this.iconSize.width/2+this.margin.left,i+this.height/2+e,o)}this.updateBoundingBox(e,i)}}}updateBoundingBox(t,e){if(this.boundingBox.top=e-.5*this.options.icon.size,this.boundingBox.left=t-.5*this.options.icon.size,this.boundingBox.right=t+.5*this.options.icon.size,this.boundingBox.bottom=e+.5*this.options.icon.size,void 0!==this.options.label&&this.labelModule.size.width>0){const t=5;this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelModule.size.height+t)}}_icon(t,e,i,o,n,s){const r=Number(this.options.icon.size);void 0!==this.options.icon.code?(t.font=[null!=this.options.icon.weight?this.options.icon.weight:o?"bold":"",(null!=this.options.icon.weight&&o?5:0)+r+"px",this.options.icon.face].join(" "),t.fillStyle=this.options.icon.color||"black",t.textAlign="center",t.textBaseline="middle",this.enableShadow(t,s),t.fillText(this.options.icon.code,e,i),this.disableShadow(t,s)):console.error("When using the icon shape, you need to define the code in the icon options object. This can be done per node or globally.")}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}let mw=class extends nw{constructor(t,e,i,o,n){super(t,e,i),this.setImages(o,n)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover;if(void 0===this.imageObj.src||void 0===this.imageObj.width||void 0===this.imageObj.height){const t=2*this.options.size;return this.width=t,void(this.height=t)}this.needsRefresh(e,i)&&this._resizeImage()}draw(t,e,i,o,n,s){t.save(),this.switchImages(o),this.resize();let r=e,a=i;if("top-left"===this.options.shapeProperties.coordinateOrigin?(this.left=e,this.top=i,r+=this.width/2,a+=this.height/2):(this.left=e-this.width/2,this.top=i-this.height/2),!0===this.options.shapeProperties.useBorderWithImage){const e=this.options.borderWidth,i=this.options.borderWidthSelected||2*this.options.borderWidth,r=(o?i:e)/this.body.view.scale;t.lineWidth=Math.min(this.width,r),t.beginPath();let a=o?this.options.color.highlight.border:n?this.options.color.hover.border:this.options.color.border,h=o?this.options.color.highlight.background:n?this.options.color.hover.background:this.options.color.background;void 0!==s.opacity&&(a=Ps(a,s.opacity),h=Ps(h,s.opacity)),t.strokeStyle=a,t.fillStyle=h,t.rect(this.left-.5*t.lineWidth,this.top-.5*t.lineWidth,this.width+t.lineWidth,this.height+t.lineWidth),ew(t).call(t),this.performStroke(t,s),t.closePath()}this._drawImageAtPosition(t,s),this._drawImageLabel(t,r,a,o,n),this.updateBoundingBox(e,i),t.restore()}updateBoundingBox(t,e){this.resize(),"top-left"===this.options.shapeProperties.coordinateOrigin?(this.left=t,this.top=e):(this.left=t-this.width/2,this.top=e-this.height/2),this.boundingBox.left=this.left,this.boundingBox.top=this.top,this.boundingBox.bottom=this.top+this.height,this.boundingBox.right=this.left+this.width,void 0!==this.options.label&&this.labelModule.size.width>0&&(this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelOffset))}distanceToBorder(t,e){return this._distanceToBorder(t,e)}};class vw extends aw{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,n,s){return this._drawShape(t,"square",2,e,i,o,n,s)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class yw extends aw{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,n,s){return this._drawShape(t,"hexagon",4,e,i,o,n,s)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class bw extends aw{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,n,s){return this._drawShape(t,"star",4,e,i,o,n,s)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class ww extends iw{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t,e,i){this.needsRefresh(e,i)&&(this.textSize=this.labelModule.getTextSize(t,e,i),this.width=this.textSize.width+this.margin.right+this.margin.left,this.height=this.textSize.height+this.margin.top+this.margin.bottom,this.radius=.5*this.width)}draw(t,e,i,o,n,s){this.resize(t,o,n),this.left=e-this.width/2,this.top=i-this.height/2,this.enableShadow(t,s),this.labelModule.draw(t,this.left+this.textSize.width/2+this.margin.left,this.top+this.textSize.height/2+this.margin.top,o,n),this.disableShadow(t,s),this.updateBoundingBox(e,i,t,o,n)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}let _w=class extends aw{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,n,s){return this._drawShape(t,"triangle",3,e,i,o,n,s)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}};class xw extends aw{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,n,s){return this._drawShape(t,"triangleDown",3,e,i,o,n,s)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}function Ew(t,e){var i=Sp(t);if(xy){var o=xy(t);e&&(o=iy(o).call(o,function(e){return Fy(t,e).enumerable})),i.push.apply(i,o)}return i}function Ow(t){for(var e=1;e<arguments.length;e++){var i,o,n=null!=arguments[e]?arguments[e]:{};e%2?Qh(i=Ew(Object(n),!0)).call(i,function(e){ev(t,e,n[e])}):Xy?eb(t,Xy(n)):Qh(o=Ew(Object(n))).call(o,function(e){ib(t,e,Fy(n,e))})}return t}class Cw{constructor(t,e,i,o,n,s){this.options=qs(n),this.globalOptions=n,this.defaultOptions=s,this.body=e,this.edges=[],this.id=void 0,this.imagelist=i,this.grouplist=o,this.x=void 0,this.y=void 0,this.baseSize=this.options.size,this.baseFontSize=this.options.font.size,this.predefinedPosition=!1,this.selected=!1,this.hover=!1,this.labelModule=new jb(this.body,this.options,!1),this.setOptions(t)}attachEdge(t){var e;-1===Zr(e=this.edges).call(e,t)&&this.edges.push(t)}detachEdge(t){var e;const i=Zr(e=this.edges).call(e,t);var o;-1!=i&&Th(o=this.edges).call(o,i,1)}setOptions(t){const e=this.options.shape;if(!t)return;if(void 0!==t.color&&(this._localColor=t.color),void 0!==t.id&&(this.id=t.id),void 0===this.id)throw new Error("Node must have an id");Cw.checkMass(t,this.id),void 0!==t.x&&(null===t.x?(this.x=void 0,this.predefinedPosition=!1):(this.x=Mv(t.x),this.predefinedPosition=!0)),void 0!==t.y&&(null===t.y?(this.y=void 0,this.predefinedPosition=!1):(this.y=Mv(t.y),this.predefinedPosition=!0)),void 0!==t.size&&(this.baseSize=t.size),void 0!==t.value&&(t.value=Vv(t.value)),Cw.parseOptions(this.options,t,!0,this.globalOptions,this.grouplist);const i=[t,this.options,this.defaultOptions];return this.chooser=ob("node",i),this._load_images(),this.updateLabelModule(t),void 0!==t.opacity&&Cw.checkOpacity(t.opacity)&&(this.options.opacity=t.opacity),this.updateShape(e),void 0!==t.hidden||void 0!==t.physics}_load_images(){if(("circularImage"===this.options.shape||"image"===this.options.shape)&&void 0===this.options.image)throw new Error("Option image must be defined for node type '"+this.options.shape+"'");if(void 0!==this.options.image){if(void 0===this.imagelist)throw new Error("Internal Error: No images provided");if("string"==typeof this.options.image)this.imageObj=this.imagelist.load(this.options.image,this.options.brokenImage,this.id);else{if(void 0===this.options.image.unselected)throw new Error("No unselected image provided");this.imageObj=this.imagelist.load(this.options.image.unselected,this.options.brokenImage,this.id),void 0!==this.options.image.selected?this.imageObjAlt=this.imagelist.load(this.options.image.selected,this.options.brokenImage,this.id):this.imageObjAlt=void 0}}}static checkOpacity(t){return 0<=t&&t<=1}static checkCoordinateOrigin(t){return void 0===t||"center"===t||"top-left"===t}static updateGroupOptions(t,e,i){var o;if(void 0===i)return;const n=t.group;if(void 0!==e&&void 0!==e.group&&n!==e.group)throw new Error("updateGroupOptions: group values in options don't match.");if(!("number"==typeof n||"string"==typeof n&&""!=n))return;const s=i.get(n);void 0!==s.opacity&&void 0===e.opacity&&(Cw.checkOpacity(s.opacity)||(console.error("Invalid option for node opacity. Value must be between 0 and 1, found: "+s.opacity),s.opacity=void 0));const r=iy(o=py(e)).call(o,t=>null!=e[t]);r.push("font"),xs(r,t,s),t.color=Bs(t.color)}static parseOptions(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=arguments.length>4?arguments[4]:void 0;if(xs(["color","fixed","shadow"],t,e,i),Cw.checkMass(e),void 0!==t.opacity&&(Cw.checkOpacity(t.opacity)||(console.error("Invalid option for node opacity. Value must be between 0 and 1, found: "+t.opacity),t.opacity=void 0)),void 0!==e.opacity&&(Cw.checkOpacity(e.opacity)||(console.error("Invalid option for node opacity. Value must be between 0 and 1, found: "+e.opacity),e.opacity=void 0)),e.shapeProperties&&!Cw.checkCoordinateOrigin(e.shapeProperties.coordinateOrigin)&&console.error("Invalid option for node coordinateOrigin, found: "+e.shapeProperties.coordinateOrigin),Vs(t,e,"shadow",o),void 0!==e.color&&null!==e.color){const i=Bs(e.color);bs(t.color,i)}else!0===i&&null===e.color&&(t.color=qs(o.color));void 0!==e.fixed&&null!==e.fixed&&("boolean"==typeof e.fixed?(t.fixed.x=e.fixed,t.fixed.y=e.fixed):(void 0!==e.fixed.x&&"boolean"==typeof e.fixed.x&&(t.fixed.x=e.fixed.x),void 0!==e.fixed.y&&"boolean"==typeof e.fixed.y&&(t.fixed.y=e.fixed.y))),!0===i&&null===e.font&&(t.font=qs(o.font)),Cw.updateGroupOptions(t,e,n),void 0!==e.scaling&&Vs(t.scaling,e.scaling,"label",o.scaling)}getFormattingValues(){const t={color:this.options.color.background,opacity:this.options.opacity,borderWidth:this.options.borderWidth,borderColor:this.options.color.border,size:this.options.size,borderDashes:this.options.shapeProperties.borderDashes,borderRadius:this.options.shapeProperties.borderRadius,shadow:this.options.shadow.enabled,shadowColor:this.options.shadow.color,shadowSize:this.options.shadow.size,shadowX:this.options.shadow.x,shadowY:this.options.shadow.y};if(this.selected||this.hover?!0===this.chooser?this.selected?(null!=this.options.borderWidthSelected?t.borderWidth=this.options.borderWidthSelected:t.borderWidth*=2,t.color=this.options.color.highlight.background,t.borderColor=this.options.color.highlight.border,t.shadow=this.options.shadow.enabled):this.hover&&(t.color=this.options.color.hover.background,t.borderColor=this.options.color.hover.border,t.shadow=this.options.shadow.enabled):"function"==typeof this.chooser&&(this.chooser(t,this.options.id,this.selected,this.hover),!1===t.shadow&&(t.shadowColor===this.options.shadow.color&&t.shadowSize===this.options.shadow.size&&t.shadowX===this.options.shadow.x&&t.shadowY===this.options.shadow.y||(t.shadow=!0))):t.shadow=this.options.shadow.enabled,void 0!==this.options.opacity){const e=this.options.opacity;t.borderColor=Ps(t.borderColor,e),t.color=Ps(t.color,e),t.shadowColor=Ps(t.shadowColor,e)}return t}updateLabelModule(t){void 0!==this.options.label&&null!==this.options.label||(this.options.label=""),Cw.updateGroupOptions(this.options,Ow(Ow({},t),{},{color:t&&t.color||this._localColor||void 0}),this.grouplist);const e=this.grouplist.get(this.options.group,!1),i=[t,this.options,e,this.globalOptions,this.defaultOptions];this.labelModule.update(this.options,i),void 0!==this.labelModule.baseSize&&(this.baseFontSize=this.labelModule.baseSize)}updateShape(t){if(t===this.options.shape&&this.shape)this.shape.setOptions(this.options,this.imageObj,this.imageObjAlt);else switch(this.options.shape){case"box":this.shape=new ow(this.options,this.body,this.labelModule);break;case"circle":this.shape=new sw(this.options,this.body,this.labelModule);break;case"circularImage":this.shape=new rw(this.options,this.body,this.labelModule,this.imageObj,this.imageObjAlt);break;case"custom":this.shape=new lw(this.options,this.body,this.labelModule,this.options.ctxRenderer);break;case"database":this.shape=new cw(this.options,this.body,this.labelModule);break;case"diamond":this.shape=new uw(this.options,this.body,this.labelModule);break;case"dot":this.shape=new pw(this.options,this.body,this.labelModule);break;case"ellipse":default:this.shape=new fw(this.options,this.body,this.labelModule);break;case"icon":this.shape=new gw(this.options,this.body,this.labelModule);break;case"image":this.shape=new mw(this.options,this.body,this.labelModule,this.imageObj,this.imageObjAlt);break;case"square":this.shape=new vw(this.options,this.body,this.labelModule);break;case"hexagon":this.shape=new yw(this.options,this.body,this.labelModule);break;case"star":this.shape=new bw(this.options,this.body,this.labelModule);break;case"text":this.shape=new ww(this.options,this.body,this.labelModule);break;case"triangle":this.shape=new _w(this.options,this.body,this.labelModule);break;case"triangleDown":this.shape=new xw(this.options,this.body,this.labelModule)}this.needsRefresh()}select(){this.selected=!0,this.needsRefresh()}unselect(){this.selected=!1,this.needsRefresh()}needsRefresh(){this.shape.refreshNeeded=!0}getTitle(){return this.options.title}distanceToBorder(t,e){return this.shape.distanceToBorder(t,e)}isFixed(){return this.options.fixed.x&&this.options.fixed.y}isSelected(){return this.selected}getValue(){return this.options.value}getLabelSize(){return this.labelModule.size()}setValueRange(t,e,i){if(void 0!==this.options.value){const o=this.options.scaling.customScalingFunction(t,e,i,this.options.value),n=this.options.scaling.max-this.options.scaling.min;if(!0===this.options.scaling.label.enabled){const t=this.options.scaling.label.max-this.options.scaling.label.min;this.options.font.size=this.options.scaling.label.min+o*t}this.options.size=this.options.scaling.min+o*n}else this.options.size=this.baseSize,this.options.font.size=this.baseFontSize;this.updateLabelModule()}draw(t){const e=this.getFormattingValues();return this.shape.draw(t,this.x,this.y,this.selected,this.hover,e)||{}}updateBoundingBox(t){this.shape.updateBoundingBox(this.x,this.y,t)}resize(t){const e=this.getFormattingValues();this.shape.resize(t,this.selected,this.hover,e)}getItemsOnPoint(t){const e=[];return this.labelModule.visible()&&nb(this.labelModule.getSize(),t)&&e.push({nodeId:this.id,labelId:0}),nb(this.shape.boundingBox,t)&&e.push({nodeId:this.id}),e}isOverlappingWith(t){return this.shape.left<t.right&&this.shape.left+this.shape.width>t.left&&this.shape.top<t.bottom&&this.shape.top+this.shape.height>t.top}isBoundingBoxOverlappingWith(t){return this.shape.boundingBox.left<t.right&&this.shape.boundingBox.right>t.left&&this.shape.boundingBox.top<t.bottom&&this.shape.boundingBox.bottom>t.top}static checkMass(t,e){if(void 0!==t.mass&&t.mass<=0){let i="";void 0!==e&&(i=" in node id: "+e),console.error("%cNegative or zero mass disallowed"+i+", setting mass to 1.",nr),t.mass=1}}}class kw{constructor(t,e,i,o){var n;if(this.body=t,this.images=e,this.groups=i,this.layoutEngine=o,this.body.functions.createNode=oo(n=this.create).call(n,this),this.nodesListeners={add:(t,e)=>{this.add(e.items)},update:(t,e)=>{this.update(e.items,e.data,e.oldData)},remove:(t,e)=>{this.remove(e.items)}},this.defaultOptions={borderWidth:1,borderWidthSelected:void 0,brokenImage:void 0,color:{border:"#2B7CE9",background:"#97C2FC",highlight:{border:"#2B7CE9",background:"#D2E5FF"},hover:{border:"#2B7CE9",background:"#D2E5FF"}},opacity:void 0,fixed:{x:!1,y:!1},font:{color:"#343434",size:14,face:"arial",background:"none",strokeWidth:0,strokeColor:"#ffffff",align:"center",vadjust:0,multi:!1,bold:{mod:"bold"},boldital:{mod:"bold italic"},ital:{mod:"italic"},mono:{mod:"",size:15,face:"monospace",vadjust:2}},group:void 0,hidden:!1,icon:{face:"FontAwesome",code:void 0,size:50,color:"#2B7CE9"},image:void 0,imagePadding:{top:0,right:0,bottom:0,left:0},label:void 0,labelHighlightBold:!0,level:void 0,margin:{top:5,right:5,bottom:5,left:5},mass:1,physics:!0,scaling:{min:10,max:30,label:{enabled:!1,min:14,max:30,maxVisible:30,drawThreshold:5},customScalingFunction:function(t,e,i,o){if(e===t)return.5;{const i=1/(e-t);return Math.max(0,(o-t)*i)}}},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:10,x:5,y:5},shape:"ellipse",shapeProperties:{borderDashes:!1,borderRadius:6,interpolation:!0,useImageSize:!1,useBorderWithImage:!1,coordinateOrigin:"center"},size:25,title:void 0,value:void 0,x:void 0,y:void 0},this.defaultOptions.mass<=0)throw"Internal error: mass in defaultOptions of NodesHandler may not be zero or negative";this.options=qs(this.defaultOptions),this.bindEventListeners()}bindEventListeners(){var t,e;this.body.emitter.on("refreshNodes",oo(t=this.refresh).call(t,this)),this.body.emitter.on("refresh",oo(e=this.refresh).call(e,this)),this.body.emitter.on("destroy",()=>{Ts(this.nodesListeners,(t,e)=>{this.body.data.nodes&&this.body.data.nodes.off(e,t)}),delete this.body.functions.createNode,delete this.nodesListeners.add,delete this.nodesListeners.update,delete this.nodesListeners.remove,delete this.nodesListeners})}setOptions(t){if(void 0!==t){if(Cw.parseOptions(this.options,t),void 0!==t.opacity&&(op(t.opacity)||!fp(t.opacity)||t.opacity<0||t.opacity>1?console.error("Invalid option for node opacity. Value must be between 0 and 1, found: "+t.opacity):this.options.opacity=t.opacity),void 0!==t.shape)for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&this.body.nodes[t].updateShape();if(void 0!==t.font||void 0!==t.widthConstraint||void 0!==t.heightConstraint)for(const t of Sp(this.body.nodes))this.body.nodes[t].updateLabelModule(),this.body.nodes[t].needsRefresh();if(void 0!==t.size)for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&this.body.nodes[t].needsRefresh();void 0===t.hidden&&void 0===t.physics||this.body.emitter.emit("_dataChanged")}}setData(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const i=this.body.data.nodes;if(mf("id",t))this.body.data.nodes=t;else if(Vh(t))this.body.data.nodes=new pf,this.body.data.nodes.add(t);else{if(t)throw new TypeError("Array or DataSet expected");this.body.data.nodes=new pf}if(i&&Ts(this.nodesListeners,function(t,e){i.off(e,t)}),this.body.nodes={},this.body.data.nodes){const t=this;Ts(this.nodesListeners,function(e,i){t.body.data.nodes.on(i,e)});const e=this.body.data.nodes.getIds();this.add(e,!0)}!1===e&&this.body.emitter.emit("_dataChanged")}add(t){let e,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const o=[];for(let i=0;i<t.length;i++){e=t[i];const n=this.body.data.nodes.get(e),s=this.create(n);o.push(s),this.body.nodes[e]=s}this.layoutEngine.positionInitially(o),!1===i&&this.body.emitter.emit("_dataChanged")}update(t,e,i){const o=this.body.nodes;let n=!1;for(let i=0;i<t.length;i++){const s=t[i];let r=o[s];const a=e[i];void 0!==r?r.setOptions(a)&&(n=!0):(n=!0,r=this.create(a),o[s]=r)}n||void 0===i||(n=Rp(e).call(e,function(t,e){const o=i[e];return o&&o.level!==t.level})),!0===n?this.body.emitter.emit("_dataChanged"):this.body.emitter.emit("_dataUpdated")}remove(t){const e=this.body.nodes;for(let i=0;i<t.length;i++){delete e[t[i]]}this.body.emitter.emit("_dataChanged")}create(t){return new(arguments.length>1&&void 0!==arguments[1]?arguments[1]:Cw)(t,this.body,this.images,this.groups,this.options,this.defaultOptions)}refresh(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];Ts(this.body.nodes,(e,i)=>{const o=this.body.data.nodes.get(i);void 0!==o&&(!0===t&&e.setOptions({x:null,y:null}),e.setOptions({fixed:!1}),e.setOptions(o))})}getPositions(t){const e={};if(void 0!==t){if(!0===Vh(t)){for(let i=0;i<t.length;i++)if(void 0!==this.body.nodes[t[i]]){const o=this.body.nodes[t[i]];e[t[i]]={x:Math.round(o.x),y:Math.round(o.y)}}}else if(void 0!==this.body.nodes[t]){const i=this.body.nodes[t];e[t]={x:Math.round(i.x),y:Math.round(i.y)}}}else for(let t=0;t<this.body.nodeIndices.length;t++){const i=this.body.nodes[this.body.nodeIndices[t]];e[this.body.nodeIndices[t]]={x:Math.round(i.x),y:Math.round(i.y)}}return e}getPosition(t){if(null==t)throw new TypeError("No id was specified for getPosition method.");if(null==this.body.nodes[t])throw new ReferenceError("NodeId provided for getPosition does not exist. Provided: ".concat(t));return{x:Math.round(this.body.nodes[t].x),y:Math.round(this.body.nodes[t].y)}}storePositions(){const t=[],e=this.body.data.nodes.getDataSet();for(const i of e.get()){const e=i.id,o=this.body.nodes[e],n=Math.round(o.x),s=Math.round(o.y);i.x===n&&i.y===s||t.push({id:e,x:n,y:s})}e.update(t)}getBoundingBox(t){if(void 0!==this.body.nodes[t])return this.body.nodes[t].shape.boundingBox}getConnectedNodes(t,e){const i=[];if(void 0!==this.body.nodes[t]){const o=this.body.nodes[t],n={};for(let t=0;t<o.edges.length;t++){const s=o.edges[t];"to"!==e&&s.toId==o.id?void 0===n[s.fromId]&&(i.push(s.fromId),n[s.fromId]=!0):"from"!==e&&s.fromId==o.id&&void 0===n[s.toId]&&(i.push(s.toId),n[s.toId]=!0)}}return i}getConnectedEdges(t){const e=[];if(void 0!==this.body.nodes[t]){const i=this.body.nodes[t];for(let t=0;t<i.edges.length;t++)e.push(i.edges[t].id)}else console.error("NodeId provided for getConnectedEdges does not exist. Provided: ",t);return e}moveNode(t,e,i){void 0!==this.body.nodes[t]?(this.body.nodes[t].x=Number(e),this.body.nodes[t].y=Number(i),tf(()=>{this.body.emitter.emit("startSimulation")},0)):console.error("Node id supplied to moveNode does not exist. Provided: ",t)}}var Sw,Tw,Dw,Mw,Iw,Pw,Nw,Bw,zw,Fw,Aw,jw,Rw,Lw={};function Hw(){if(Tw)return Sw;Tw=1;var t=li(),e=ka(),i=Et(),o=RangeError;return Sw=function(n){var s=e(i(this)),r="",a=t(n);if(a<0||a===1/0)throw new o("Wrong number of repetitions");for(;a>0;(a>>>=1)&&(s+=s))1&a&&(r+=s);return r}}function Ww(){if(Mw)return Dw;Mw=1;var t=O(),e=ui(),i=ka(),o=Hw(),n=Et(),s=t(o),r=t("".slice),a=Math.ceil,h=function(t){return function(o,h,d){var l,c,u=i(n(o)),p=e(h),f=u.length,g=void 0===d?" ":i(d);return p<=f||""===g?u:((c=s(g,a((l=p-f)/g.length))).length>l&&(c=r(c,0,l)),t?u+c:c+u)}};return Dw={start:h(!1),end:h(!0)}}function qw(){if(Pw)return Iw;Pw=1;var t=O(),e=_(),i=Ww().start,o=RangeError,n=isFinite,s=Math.abs,r=Date.prototype,a=r.toISOString,h=t(r.getTime),d=t(r.getUTCDate),l=t(r.getUTCFullYear),c=t(r.getUTCHours),u=t(r.getUTCMilliseconds),p=t(r.getUTCMinutes),f=t(r.getUTCMonth),g=t(r.getUTCSeconds);return Iw=e(function(){return"0385-07-25T07:06:39.999Z"!==a.call(new Date(-50000000000001))})||!e(function(){a.call(new Date(NaN))})?function(){if(!n(h(this)))throw new o("Invalid time value");var t=this,e=l(t),r=u(t),a=e<0?"-":e>9999?"+":"";return a+i(s(e),a?6:4,0)+"-"+i(f(t)+1,2,0)+"-"+i(d(t),2,0)+"T"+i(c(t),2,0)+":"+i(p(t),2,0)+":"+i(g(t),2,0)+"."+i(r,3,0)+"Z"}:a}function Vw(){if(zw)return Bw;zw=1,function(){if(Nw)return Lw;Nw=1;var t=hi(),e=B(),i=ve(),o=_e(),n=qw(),s=C();t({target:"Date",proto:!0,forced:_()(function(){return null!==new Date(NaN).toJSON()||1!==e(Date.prototype.toJSON,{toISOString:function(){return 1}})})},{toJSON:function(t){var r=i(this),a=o(r,"number");return"number"!=typeof a||isFinite(a)?"toISOString"in r||"Date"!==s(r)?r.toISOString():e(n,r):null}})}(),ag();var t=kt(),e=E();return t.JSON||(t.JSON={stringify:JSON.stringify}),Bw=function(i,o,n){return e(t.JSON.stringify,null,arguments)},Bw}function Uw(){return Aw?Fw:(Aw=1,Fw=Vw())}var Yw,Xw,Gw,Kw,Zw,Qw,$w,Jw=i(Rw?jw:(Rw=1,jw=Uw())),t_={};function e_(){return Gw?Xw:(Gw=1,function(){if(Yw)return t_;Yw=1;var t=hi(),e=Math.hypot,i=Math.abs,o=Math.sqrt;t({target:"Math",stat:!0,arity:2,forced:!!e&&e(1/0,NaN)!==1/0},{hypot:function(t,e){for(var n,s,r=0,a=0,h=arguments.length,d=0;a<h;)d<(n=i(arguments[a++]))?(r=r*(s=d/n)*s+1,d=n):r+=n>0?(s=n/d)*s:n;return d===1/0?1/0:d*o(r)}})}(),Xw=kt().Math.hypot)}function i_(){return Zw?Kw:(Zw=1,Kw=e_())}var o_=i($w?Qw:($w=1,Qw=i_()));class n_{static transform(t,e){Vh(t)||(t=[t]);const i=e.point.x,o=e.point.y,n=e.angle,s=e.length;for(let e=0;e<t.length;++e){const r=t[e],a=r.x*Math.cos(n)-r.y*Math.sin(n),h=r.x*Math.sin(n)+r.y*Math.cos(n);r.x=i+s*a,r.y=o+s*h}}static drawPath(t,e){t.beginPath(),t.moveTo(e[0].x,e[0].y);for(let i=1;i<e.length;++i)t.lineTo(e[i].x,e[i].y);t.closePath()}}let s_=class extends n_{static draw(t,e){if(e.image){t.save(),t.translate(e.point.x,e.point.y),t.rotate(Math.PI/2+e.angle);const i=null!=e.imageWidth?e.imageWidth:e.image.width,o=null!=e.imageHeight?e.imageHeight:e.image.height;e.image.drawImageAtPosition(t,1,-i/2,0,i,o),t.restore()}return!1}};class r_ extends n_{static draw(t,e){const i=[{x:0,y:0},{x:-1,y:.3},{x:-.9,y:0},{x:-1,y:-.3}];return n_.transform(i,e),n_.drawPath(t,i),!0}}class a_{static draw(t,e){const i=[{x:-1,y:0},{x:0,y:.3},{x:-.4,y:0},{x:0,y:-.3}];return n_.transform(i,e),n_.drawPath(t,i),!0}}class h_{static draw(t,e){const i={x:-.4,y:0};n_.transform(i,e),t.strokeStyle=t.fillStyle,t.fillStyle="rgba(0, 0, 0, 0)";const o=Math.PI,n=e.angle-o/2,s=e.angle+o/2;return t.beginPath(),t.arc(i.x,i.y,.4*e.length,n,s,!1),t.stroke(),!0}}class d_{static draw(t,e){const i={x:-.3,y:0};n_.transform(i,e),t.strokeStyle=t.fillStyle,t.fillStyle="rgba(0, 0, 0, 0)";const o=Math.PI,n=e.angle+o/2,s=e.angle+3*o/2;return t.beginPath(),t.arc(i.x,i.y,.4*e.length,n,s,!1),t.stroke(),!0}}class l_{static draw(t,e){const i=[{x:.02,y:0},{x:-1,y:.3},{x:-1,y:-.3}];return n_.transform(i,e),n_.drawPath(t,i),!0}}class c_{static draw(t,e){const i=[{x:0,y:.3},{x:0,y:-.3},{x:-1,y:0}];return n_.transform(i,e),n_.drawPath(t,i),!0}}class u_{static draw(t,e){const i={x:-.4,y:0};return n_.transform(i,e),no(t,i.x,i.y,.4*e.length),!0}}class p_{static draw(t,e){const i=[{x:0,y:.5},{x:0,y:-.5},{x:-.15,y:-.5},{x:-.15,y:.5}];return n_.transform(i,e),n_.drawPath(t,i),!0}}class f_{static draw(t,e){const i=[{x:0,y:.3},{x:0,y:-.3},{x:-.6,y:-.3},{x:-.6,y:.3}];return n_.transform(i,e),n_.drawPath(t,i),!0}}class g_{static draw(t,e){const i=[{x:0,y:0},{x:-.5,y:-.3},{x:-1,y:0},{x:-.5,y:.3}];return n_.transform(i,e),n_.drawPath(t,i),!0}}class m_{static draw(t,e){const i=[{x:-1,y:.3},{x:-.5,y:0},{x:-1,y:-.3},{x:0,y:0}];return n_.transform(i,e),n_.drawPath(t,i),!0}}class v_{static draw(t,e){let i;switch(e.type&&(i=e.type.toLowerCase()),i){case"image":return s_.draw(t,e);case"circle":return u_.draw(t,e);case"box":return f_.draw(t,e);case"crow":return a_.draw(t,e);case"curve":return h_.draw(t,e);case"diamond":return g_.draw(t,e);case"inv_curve":return d_.draw(t,e);case"triangle":return l_.draw(t,e);case"inv_triangle":return c_.draw(t,e);case"bar":return p_.draw(t,e);case"vee":return m_.draw(t,e);default:return r_.draw(t,e)}}}function y_(t,e){var i=Sp(t);if(xy){var o=xy(t);e&&(o=iy(o).call(o,function(e){return Fy(t,e).enumerable})),i.push.apply(i,o)}return i}function b_(t){for(var e=1;e<arguments.length;e++){var i,o,n=null!=arguments[e]?arguments[e]:{};e%2?Qh(i=y_(Object(n),!0)).call(i,function(e){ev(t,e,n[e])}):Xy?eb(t,Xy(n)):Qh(o=y_(Object(n))).call(o,function(e){ib(t,e,Fy(n,e))})}return t}class w_{constructor(t,e,i){ev(this,"color",{}),ev(this,"colorDirty",!0),ev(this,"hoverWidth",1.5),ev(this,"selectionWidth",2),this._body=e,this._labelModule=i,this.setOptions(t),this.fromPoint=this.from,this.toPoint=this.to}connect(){this.from=this._body.nodes[this.options.from],this.to=this._body.nodes[this.options.to]}cleanup(){return!1}setOptions(t){this.options=t,this.from=this._body.nodes[this.options.from],this.to=this._body.nodes[this.options.to],this.id=this.options.id}drawLine(t,e,i,o){let n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.getViaNode();t.strokeStyle=this.getColor(t,e),t.lineWidth=e.width,!1!==e.dashes?this._drawDashedLine(t,e,n):this._drawLine(t,e,n)}_drawLine(t,e,i,o,n){if(this.from!=this.to)this._line(t,e,i,o,n);else{const[i,o,n]=this._getCircleData(t);this._circle(t,e,i,o,n)}}_drawDashedLine(t,e,i,o,n){t.lineCap="round";const s=Vh(e.dashes)?e.dashes:[5,5];if(void 0!==t.setLineDash){if(t.save(),t.setLineDash(s),t.lineDashOffset=0,this.from!=this.to)this._line(t,e,i);else{const[i,o,n]=this._getCircleData(t);this._circle(t,e,i,o,n)}t.setLineDash([0]),t.lineDashOffset=0,t.restore()}else{if(this.from!=this.to)ho(t,this.from.x,this.from.y,this.to.x,this.to.y,s);else{const[i,o,n]=this._getCircleData(t);this._circle(t,e,i,o,n)}this.enableShadow(t,e),t.stroke(),this.disableShadow(t,e)}}findBorderPosition(t,e,i){return this.from!=this.to?this._findBorderPosition(t,e,i):this._findBorderPositionCircle(t,e,i)}findBorderPositions(t){if(this.from!=this.to)return{from:this._findBorderPosition(this.from,t),to:this._findBorderPosition(this.to,t)};{var e;const[i,o]=Tb(e=this._getCircleData(t)).call(e,0,2);return{from:this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:.25,high:.6,direction:-1}),to:this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:.6,high:.8,direction:1})}}}_getCircleData(t){const e=this.options.selfReference.size;void 0!==t&&void 0===this.from.shape.width&&this.from.shape.resize(t);const i=rb(t,this.options.selfReference.angle,e,this.from);return[i.x,i.y,e]}_pointOnCircle(t,e,i,o){const n=2*o*Math.PI;return{x:t+i*Math.cos(n),y:e-i*Math.sin(n)}}_findBorderPositionCircle(t,e,i){const o=i.x,n=i.y;let s=i.low,r=i.high;const a=i.direction,h=this.options.selfReference.size;let d,l=.5*(s+r),c=0;!0===this.options.arrowStrikethrough&&(-1===a?c=this.options.endPointOffset.from:1===a&&(c=this.options.endPointOffset.to));let u=0;do{l=.5*(s+r),d=this._pointOnCircle(o,n,h,l);const i=Math.atan2(t.y-d.y,t.x-d.x),p=t.distanceToBorder(e,i)+c-Math.sqrt(Math.pow(d.x-t.x,2)+Math.pow(d.y-t.y,2));if(Math.abs(p)<.05)break;p>0?a>0?s=l:r=l:a>0?r=l:s=l,++u}while(s<=r&&u<10);return b_(b_({},d),{},{t:l})}getLineWidth(t,e){return!0===t?Math.max(this.selectionWidth,.3/this._body.view.scale):!0===e?Math.max(this.hoverWidth,.3/this._body.view.scale):Math.max(this.options.width,.3/this._body.view.scale)}getColor(t,e){if(!1!==e.inheritsColor){if("both"===e.inheritsColor&&this.from.id!==this.to.id){const i=t.createLinearGradient(this.from.x,this.from.y,this.to.x,this.to.y);let o=this.from.options.color.highlight.border,n=this.to.options.color.highlight.border;return!1===this.from.selected&&!1===this.to.selected?(o=Ps(this.from.options.color.border,e.opacity),n=Ps(this.to.options.color.border,e.opacity)):!0===this.from.selected&&!1===this.to.selected?n=this.to.options.color.border:!1===this.from.selected&&!0===this.to.selected&&(o=this.from.options.color.border),i.addColorStop(0,o),i.addColorStop(1,n),i}return"to"===e.inheritsColor?Ps(this.to.options.color.border,e.opacity):Ps(this.from.options.color.border,e.opacity)}return Ps(e.color,e.opacity)}_circle(t,e,i,o,n){this.enableShadow(t,e);let s=0,r=2*Math.PI;if(!this.options.selfReference.renderBehindTheNode){const e=this.options.selfReference.angle,n=this.options.selfReference.angle+Math.PI,a=this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:e,high:n,direction:-1}),h=this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:e,high:n,direction:1});s=Math.atan2(a.y-o,a.x-i),r=Math.atan2(h.y-o,h.x-i)}t.beginPath(),t.arc(i,o,n,s,r,!1),t.stroke(),this.disableShadow(t,e)}getDistanceToEdge(t,e,i,o,n,s){if(this.from!=this.to)return this._getDistanceToEdge(t,e,i,o,n,s);{const[t,e,i]=this._getCircleData(void 0),o=t-n,r=e-s;return Math.abs(Math.sqrt(o*o+r*r)-i)}}_getDistanceToLine(t,e,i,o,n,s){const r=i-t,a=o-e;let h=((n-t)*r+(s-e)*a)/(r*r+a*a);h>1?h=1:h<0&&(h=0);const d=t+h*r-n,l=e+h*a-s;return Math.sqrt(d*d+l*l)}getArrowData(t,e,i,o,n,s){let r,a,h,d,l,c,u;const p=s.width;"from"===e?(h=this.from,d=this.to,l=s.fromArrowScale<0,c=Math.abs(s.fromArrowScale),u=s.fromArrowType):"to"===e?(h=this.to,d=this.from,l=s.toArrowScale<0,c=Math.abs(s.toArrowScale),u=s.toArrowType):(h=this.to,d=this.from,l=s.middleArrowScale<0,c=Math.abs(s.middleArrowScale),u=s.middleArrowType);const f=15*c+3*p;if(h!=d){const o=f/o_(h.x-d.x,h.y-d.y);if("middle"!==e)if(!0===this.options.smooth.enabled){const n=this._findBorderPosition(h,t,{via:i}),s=this.getPoint(n.t+o*("from"===e?1:-1),i);r=Math.atan2(n.y-s.y,n.x-s.x),a=n}else r=Math.atan2(h.y-d.y,h.x-d.x),a=this._findBorderPosition(h,t);else{const t=(l?-o:o)/2,e=this.getPoint(.5+t,i),n=this.getPoint(.5-t,i);r=Math.atan2(e.y-n.y,e.x-n.x),a=this.getPoint(.5,i)}}else{const[i,o,n]=this._getCircleData(t);if("from"===e){const e=this.options.selfReference.angle,n=this.options.selfReference.angle+Math.PI,s=this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:e,high:n,direction:-1});r=-2*s.t*Math.PI+1.5*Math.PI+.1*Math.PI,a=s}else if("to"===e){const e=this.options.selfReference.angle,n=this.options.selfReference.angle+Math.PI,s=this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:e,high:n,direction:1});r=-2*s.t*Math.PI+1.5*Math.PI-1.1*Math.PI,a=s}else{const t=this.options.selfReference.angle/(2*Math.PI);a=this._pointOnCircle(i,o,n,t),r=-2*t*Math.PI+1.5*Math.PI+.1*Math.PI}}return{point:a,core:{x:a.x-.9*f*Math.cos(r),y:a.y-.9*f*Math.sin(r)},angle:r,length:f,type:u}}drawArrowHead(t,e,i,o,n){t.strokeStyle=this.getColor(t,e),t.fillStyle=t.strokeStyle,t.lineWidth=e.width;v_.draw(t,n)&&(this.enableShadow(t,e),ew(t).call(t),this.disableShadow(t,e))}enableShadow(t,e){!0===e.shadow&&(t.shadowColor=e.shadowColor,t.shadowBlur=e.shadowSize,t.shadowOffsetX=e.shadowX,t.shadowOffsetY=e.shadowY)}disableShadow(t,e){!0===e.shadow&&(t.shadowColor="rgba(0,0,0,0)",t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0)}drawBackground(t,e){if(!1!==e.background){const i={strokeStyle:t.strokeStyle,lineWidth:t.lineWidth,dashes:t.dashes};t.strokeStyle=e.backgroundColor,t.lineWidth=e.backgroundSize,this.setStrokeDashed(t,e.backgroundDashes),t.stroke(),t.strokeStyle=i.strokeStyle,t.lineWidth=i.lineWidth,t.dashes=i.dashes,this.setStrokeDashed(t,e.dashes)}}setStrokeDashed(t,e){if(!1!==e)if(void 0!==t.setLineDash){const i=Vh(e)?e:[5,5];t.setLineDash(i)}else console.warn("setLineDash is not supported in this browser. The dashed stroke cannot be used.");else void 0!==t.setLineDash?t.setLineDash([]):console.warn("setLineDash is not supported in this browser. The dashed stroke cannot be used.")}}function __(t,e){var i=Sp(t);if(xy){var o=xy(t);e&&(o=iy(o).call(o,function(e){return Fy(t,e).enumerable})),i.push.apply(i,o)}return i}function x_(t){for(var e=1;e<arguments.length;e++){var i,o,n=null!=arguments[e]?arguments[e]:{};e%2?Qh(i=__(Object(n),!0)).call(i,function(e){ev(t,e,n[e])}):Xy?eb(t,Xy(n)):Qh(o=__(Object(n))).call(o,function(e){ib(t,e,Fy(n,e))})}return t}class E_ extends w_{constructor(t,e,i){super(t,e,i)}_findBorderPositionBezier(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this._getViaCoordinates();let o,n,s=!1,r=1,a=0,h=this.to,d=this.options.endPointOffset?this.options.endPointOffset.to:0;t.id===this.from.id&&(h=this.from,s=!0,d=this.options.endPointOffset?this.options.endPointOffset.from:0),!1===this.options.arrowStrikethrough&&(d=0);let l=0;do{n=.5*(a+r),o=this.getPoint(n,i);const t=Math.atan2(h.y-o.y,h.x-o.x),c=h.distanceToBorder(e,t)+d-Math.sqrt(Math.pow(o.x-h.x,2)+Math.pow(o.y-h.y,2));if(Math.abs(c)<.2)break;c<0?!1===s?a=n:r=n:!1===s?r=n:a=n,++l}while(a<=r&&l<10);return x_(x_({},o),{},{t:n})}_getDistanceToBezierEdge(t,e,i,o,n,s,r){let a,h,d,l,c,u=1e9,p=t,f=e;for(h=1;h<10;h++)d=.1*h,l=Math.pow(1-d,2)*t+2*d*(1-d)*r.x+Math.pow(d,2)*i,c=Math.pow(1-d,2)*e+2*d*(1-d)*r.y+Math.pow(d,2)*o,h>0&&(a=this._getDistanceToLine(p,f,l,c,n,s),u=a<u?a:u),p=l,f=c;return u}_bezierCurve(t,e,i,o){t.beginPath(),t.moveTo(this.fromPoint.x,this.fromPoint.y),null!=i&&null!=i.x?null!=o&&null!=o.x?t.bezierCurveTo(i.x,i.y,o.x,o.y,this.toPoint.x,this.toPoint.y):t.quadraticCurveTo(i.x,i.y,this.toPoint.x,this.toPoint.y):t.lineTo(this.toPoint.x,this.toPoint.y),this.drawBackground(t,e),this.enableShadow(t,e),t.stroke(),this.disableShadow(t,e)}getViaNode(){return this._getViaCoordinates()}}class O_ extends E_{constructor(t,e,i){super(t,e,i),ev(this,"via",this.via),this._boundFunction=()=>{this.positionBezierNode()},this._body.emitter.on("_repositionBezierNodes",this._boundFunction)}setOptions(t){super.setOptions(t);let e=!1;this.options.physics!==t.physics&&(e=!0),this.options=t,this.id=this.options.id,this.from=this._body.nodes[this.options.from],this.to=this._body.nodes[this.options.to],this.setupSupportNode(),this.connect(),!0===e&&(this.via.setOptions({physics:this.options.physics}),this.positionBezierNode())}connect(){this.from=this._body.nodes[this.options.from],this.to=this._body.nodes[this.options.to],void 0===this.from||void 0===this.to||!1===this.options.physics||this.from.id===this.to.id?this.via.setOptions({physics:!1}):this.via.setOptions({physics:!0})}cleanup(){return this._body.emitter.off("_repositionBezierNodes",this._boundFunction),void 0!==this.via&&(delete this._body.nodes[this.via.id],this.via=void 0,!0)}setupSupportNode(){if(void 0===this.via){const t="edgeId:"+this.id,e=this._body.functions.createNode({id:t,shape:"circle",physics:!0,hidden:!0});this._body.nodes[t]=e,this.via=e,this.via.parentEdgeId=this.id,this.positionBezierNode()}}positionBezierNode(){void 0!==this.via&&void 0!==this.from&&void 0!==this.to?(this.via.x=.5*(this.from.x+this.to.x),this.via.y=.5*(this.from.y+this.to.y)):void 0!==this.via&&(this.via.x=0,this.via.y=0)}_line(t,e,i){this._bezierCurve(t,e,i)}_getViaCoordinates(){return this.via}getViaNode(){return this.via}getPoint(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.via;if(this.from===this.to){const[e,i,o]=this._getCircleData(),n=2*Math.PI*(1-t);return{x:e+o*Math.sin(n),y:i+o-o*(1-Math.cos(n))}}return{x:Math.pow(1-t,2)*this.fromPoint.x+2*t*(1-t)*e.x+Math.pow(t,2)*this.toPoint.x,y:Math.pow(1-t,2)*this.fromPoint.y+2*t*(1-t)*e.y+Math.pow(t,2)*this.toPoint.y}}_findBorderPosition(t,e){return this._findBorderPositionBezier(t,e,this.via)}_getDistanceToEdge(t,e,i,o,n,s){return this._getDistanceToBezierEdge(t,e,i,o,n,s,this.via)}}class C_ extends E_{constructor(t,e,i){super(t,e,i)}_line(t,e,i){this._bezierCurve(t,e,i)}getViaNode(){return this._getViaCoordinates()}_getViaCoordinates(){const t=this.options.smooth.roundness,e=this.options.smooth.type;let i=Math.abs(this.from.x-this.to.x),o=Math.abs(this.from.y-this.to.y);if("discrete"===e||"diagonalCross"===e){let n,s;n=s=i<=o?t*o:t*i,this.from.x>this.to.x&&(n=-n),this.from.y>=this.to.y&&(s=-s);let r=this.from.x+n,a=this.from.y+s;return"discrete"===e&&(i<=o?r=i<t*o?this.from.x:r:a=o<t*i?this.from.y:a),{x:r,y:a}}if("straightCross"===e){let e=(1-t)*i,n=(1-t)*o;return i<=o?(e=0,this.from.y<this.to.y&&(n=-n)):(this.from.x<this.to.x&&(e=-e),n=0),{x:this.to.x+e,y:this.to.y+n}}if("horizontal"===e){let e=(1-t)*i;return this.from.x<this.to.x&&(e=-e),{x:this.to.x+e,y:this.from.y}}if("vertical"===e){let e=(1-t)*o;return this.from.y<this.to.y&&(e=-e),{x:this.from.x,y:this.to.y+e}}if("curvedCW"===e){i=this.to.x-this.from.x,o=this.from.y-this.to.y;const e=Math.sqrt(i*i+o*o),n=Math.PI,s=(Math.atan2(o,i)+(.5*t+.5)*n)%(2*n);return{x:this.from.x+(.5*t+.5)*e*Math.sin(s),y:this.from.y+(.5*t+.5)*e*Math.cos(s)}}if("curvedCCW"===e){i=this.to.x-this.from.x,o=this.from.y-this.to.y;const e=Math.sqrt(i*i+o*o),n=Math.PI,s=(Math.atan2(o,i)+(.5*-t+.5)*n)%(2*n);return{x:this.from.x+(.5*t+.5)*e*Math.sin(s),y:this.from.y+(.5*t+.5)*e*Math.cos(s)}}{let e,n;e=n=i<=o?t*o:t*i,this.from.x>this.to.x&&(e=-e),this.from.y>=this.to.y&&(n=-n);let s=this.from.x+e,r=this.from.y+n;return i<=o?s=this.from.x<=this.to.x?this.to.x<s?this.to.x:s:this.to.x>s?this.to.x:s:r=this.from.y>=this.to.y?this.to.y>r?this.to.y:r:this.to.y<r?this.to.y:r,{x:s,y:r}}}_findBorderPosition(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this._findBorderPositionBezier(t,e,i.via)}_getDistanceToEdge(t,e,i,o,n,s){let r=arguments.length>6&&void 0!==arguments[6]?arguments[6]:this._getViaCoordinates();return this._getDistanceToBezierEdge(t,e,i,o,n,s,r)}getPoint(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._getViaCoordinates();const i=t;return{x:Math.pow(1-i,2)*this.fromPoint.x+2*i*(1-i)*e.x+Math.pow(i,2)*this.toPoint.x,y:Math.pow(1-i,2)*this.fromPoint.y+2*i*(1-i)*e.y+Math.pow(i,2)*this.toPoint.y}}}class k_ extends E_{constructor(t,e,i){super(t,e,i)}_getDistanceToBezierEdge2(t,e,i,o,n,s,r,a){let h=1e9,d=t,l=e;const c=[0,0,0,0];for(let u=1;u<10;u++){const p=.1*u;c[0]=Math.pow(1-p,3),c[1]=3*p*Math.pow(1-p,2),c[2]=3*Math.pow(p,2)*(1-p),c[3]=Math.pow(p,3);const f=c[0]*t+c[1]*r.x+c[2]*a.x+c[3]*i,g=c[0]*e+c[1]*r.y+c[2]*a.y+c[3]*o;if(u>0){const t=this._getDistanceToLine(d,l,f,g,n,s);h=t<h?t:h}d=f,l=g}return h}}class S_ extends k_{constructor(t,e,i){super(t,e,i)}_line(t,e,i){const o=i[0],n=i[1];this._bezierCurve(t,e,o,n)}_getViaCoordinates(){const t=this.from.x-this.to.x,e=this.from.y-this.to.y;let i,o,n,s;const r=this.options.smooth.roundness;return(Math.abs(t)>Math.abs(e)||!0===this.options.smooth.forceDirection||"horizontal"===this.options.smooth.forceDirection)&&"vertical"!==this.options.smooth.forceDirection?(o=this.from.y,s=this.to.y,i=this.from.x-r*t,n=this.to.x+r*t):(o=this.from.y-r*e,s=this.to.y+r*e,i=this.from.x,n=this.to.x),[{x:i,y:o},{x:n,y:s}]}getViaNode(){return this._getViaCoordinates()}_findBorderPosition(t,e){return this._findBorderPositionBezier(t,e)}_getDistanceToEdge(t,e,i,o,n,s){let[r,a]=arguments.length>6&&void 0!==arguments[6]?arguments[6]:this._getViaCoordinates();return this._getDistanceToBezierEdge2(t,e,i,o,n,s,r,a)}getPoint(t){let[e,i]=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._getViaCoordinates();const o=t,n=[Math.pow(1-o,3),3*o*Math.pow(1-o,2),3*Math.pow(o,2)*(1-o),Math.pow(o,3)];return{x:n[0]*this.fromPoint.x+n[1]*e.x+n[2]*i.x+n[3]*this.toPoint.x,y:n[0]*this.fromPoint.y+n[1]*e.y+n[2]*i.y+n[3]*this.toPoint.y}}}class T_ extends w_{constructor(t,e,i){super(t,e,i)}_line(t,e){t.beginPath(),t.moveTo(this.fromPoint.x,this.fromPoint.y),t.lineTo(this.toPoint.x,this.toPoint.y),this.enableShadow(t,e),t.stroke(),this.disableShadow(t,e)}getViaNode(){}getPoint(t){return{x:(1-t)*this.fromPoint.x+t*this.toPoint.x,y:(1-t)*this.fromPoint.y+t*this.toPoint.y}}_findBorderPosition(t,e){let i=this.to,o=this.from;t.id===this.from.id&&(i=this.from,o=this.to);const n=Math.atan2(i.y-o.y,i.x-o.x),s=i.x-o.x,r=i.y-o.y,a=Math.sqrt(s*s+r*r),h=(a-t.distanceToBorder(e,n))/a;return{x:(1-h)*o.x+h*i.x,y:(1-h)*o.y+h*i.y,t:0}}_getDistanceToEdge(t,e,i,o,n,s){return this._getDistanceToLine(t,e,i,o,n,s)}}class D_{constructor(t,e,i,o,n){if(void 0===e)throw new Error("No body provided");this.options=qs(o),this.globalOptions=o,this.defaultOptions=n,this.body=e,this.imagelist=i,this.id=void 0,this.fromId=void 0,this.toId=void 0,this.selected=!1,this.hover=!1,this.labelDirty=!0,this.baseWidth=this.options.width,this.baseFontSize=this.options.font.size,this.from=void 0,this.to=void 0,this.edgeType=void 0,this.connected=!1,this.labelModule=new jb(this.body,this.options,!0),this.setOptions(t)}setOptions(t){if(!t)return;let e=void 0!==t.physics&&this.options.physics!==t.physics||void 0!==t.hidden&&(this.options.hidden||!1)!==(t.hidden||!1)||void 0!==t.from&&this.options.from!==t.from||void 0!==t.to&&this.options.to!==t.to;D_.parseOptions(this.options,t,!0,this.globalOptions),void 0!==t.id&&(this.id=t.id),void 0!==t.from&&(this.fromId=t.from),void 0!==t.to&&(this.toId=t.to),void 0!==t.title&&(this.title=t.title),void 0!==t.value&&(t.value=Vv(t.value));const i=[t,this.options,this.defaultOptions];return this.chooser=ob("edge",i),this.updateLabelModule(t),e=this.updateEdgeType()||e,this._setInteractionWidths(),this.connect(),e}static parseOptions(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(_s(["endPointOffset","arrowStrikethrough","id","from","hidden","hoverWidth","labelHighlightBold","length","line","opacity","physics","scaling","selectionWidth","selfReferenceSize","selfReference","to","title","value","width","font","chosen","widthConstraint"],t,e,i),void 0!==e.endPointOffset&&void 0!==e.endPointOffset.from&&(fp(e.endPointOffset.from)?t.endPointOffset.from=e.endPointOffset.from:(t.endPointOffset.from=void 0!==o.endPointOffset.from?o.endPointOffset.from:0,console.error("endPointOffset.from is not a valid number"))),void 0!==e.endPointOffset&&void 0!==e.endPointOffset.to&&(fp(e.endPointOffset.to)?t.endPointOffset.to=e.endPointOffset.to:(t.endPointOffset.to=void 0!==o.endPointOffset.to?o.endPointOffset.to:0,console.error("endPointOffset.to is not a valid number"))),sb(e.label)?t.label=e.label:sb(t.label)||(t.label=void 0),Vs(t,e,"smooth",o),Vs(t,e,"shadow",o),Vs(t,e,"background",o),void 0!==e.dashes&&null!==e.dashes?t.dashes=e.dashes:!0===i&&null===e.dashes&&(t.dashes=Rr(o.dashes)),void 0!==e.scaling&&null!==e.scaling?(void 0!==e.scaling.min&&(t.scaling.min=e.scaling.min),void 0!==e.scaling.max&&(t.scaling.max=e.scaling.max),Vs(t.scaling,e.scaling,"label",o.scaling)):!0===i&&null===e.scaling&&(t.scaling=Rr(o.scaling)),void 0!==e.arrows&&null!==e.arrows)if("string"==typeof e.arrows){const i=e.arrows.toLowerCase();t.arrows.to.enabled=-1!=Zr(i).call(i,"to"),t.arrows.middle.enabled=-1!=Zr(i).call(i,"middle"),t.arrows.from.enabled=-1!=Zr(i).call(i,"from")}else{if("object"!=typeof e.arrows)throw new Error("The arrow newOptions can only be an object or a string. Refer to the documentation. You used:"+Jw(e.arrows));Vs(t.arrows,e.arrows,"to",o.arrows),Vs(t.arrows,e.arrows,"middle",o.arrows),Vs(t.arrows,e.arrows,"from",o.arrows)}else!0===i&&null===e.arrows&&(t.arrows=Rr(o.arrows));if(void 0!==e.color&&null!==e.color){const s=ms(e.color)?{color:e.color,highlight:e.color,hover:e.color,inherit:!1,opacity:1}:e.color,r=t.color;if(n)Es(r,o.color,!1,i);else for(const t in r)Object.prototype.hasOwnProperty.call(r,t)&&delete r[t];if(ms(r))r.color=r,r.highlight=r,r.hover=r,r.inherit=!1,void 0===s.opacity&&(r.opacity=1);else{let t=!1;void 0!==s.color&&(r.color=s.color,t=!0),void 0!==s.highlight&&(r.highlight=s.highlight,t=!0),void 0!==s.hover&&(r.hover=s.hover,t=!0),void 0!==s.inherit&&(r.inherit=s.inherit),void 0!==s.opacity&&(r.opacity=Math.min(1,Math.max(0,s.opacity))),!0===t?r.inherit=!1:void 0===r.inherit&&(r.inherit="from")}}else!0===i&&null===e.color&&(t.color=qs(o.color));!0===i&&null===e.font&&(t.font=qs(o.font)),Object.prototype.hasOwnProperty.call(e,"selfReferenceSize")&&(console.warn("The selfReferenceSize property has been deprecated. Please use selfReference property instead. The selfReference can be set like thise selfReference:{size:30, angle:Math.PI / 4}"),t.selfReference.size=e.selfReferenceSize)}getFormattingValues(){const t=!0===this.options.arrows.to||!0===this.options.arrows.to.enabled,e=!0===this.options.arrows.from||!0===this.options.arrows.from.enabled,i=!0===this.options.arrows.middle||!0===this.options.arrows.middle.enabled,o=this.options.color.inherit,n={toArrow:t,toArrowScale:this.options.arrows.to.scaleFactor,toArrowType:this.options.arrows.to.type,toArrowSrc:this.options.arrows.to.src,toArrowImageWidth:this.options.arrows.to.imageWidth,toArrowImageHeight:this.options.arrows.to.imageHeight,middleArrow:i,middleArrowScale:this.options.arrows.middle.scaleFactor,middleArrowType:this.options.arrows.middle.type,middleArrowSrc:this.options.arrows.middle.src,middleArrowImageWidth:this.options.arrows.middle.imageWidth,middleArrowImageHeight:this.options.arrows.middle.imageHeight,fromArrow:e,fromArrowScale:this.options.arrows.from.scaleFactor,fromArrowType:this.options.arrows.from.type,fromArrowSrc:this.options.arrows.from.src,fromArrowImageWidth:this.options.arrows.from.imageWidth,fromArrowImageHeight:this.options.arrows.from.imageHeight,arrowStrikethrough:this.options.arrowStrikethrough,color:o?void 0:this.options.color.color,inheritsColor:o,opacity:this.options.color.opacity,hidden:this.options.hidden,length:this.options.length,shadow:this.options.shadow.enabled,shadowColor:this.options.shadow.color,shadowSize:this.options.shadow.size,shadowX:this.options.shadow.x,shadowY:this.options.shadow.y,dashes:this.options.dashes,width:this.options.width,background:this.options.background.enabled,backgroundColor:this.options.background.color,backgroundSize:this.options.background.size,backgroundDashes:this.options.background.dashes};if(this.selected||this.hover)if(!0===this.chooser){if(this.selected){const t=this.options.selectionWidth;"function"==typeof t?n.width=t(n.width):"number"==typeof t&&(n.width+=t),n.width=Math.max(n.width,.3/this.body.view.scale),n.color=this.options.color.highlight,n.shadow=this.options.shadow.enabled}else if(this.hover){const t=this.options.hoverWidth;"function"==typeof t?n.width=t(n.width):"number"==typeof t&&(n.width+=t),n.width=Math.max(n.width,.3/this.body.view.scale),n.color=this.options.color.hover,n.shadow=this.options.shadow.enabled}}else"function"==typeof this.chooser&&(this.chooser(n,this.options.id,this.selected,this.hover),void 0!==n.color&&(n.inheritsColor=!1),!1===n.shadow&&(n.shadowColor===this.options.shadow.color&&n.shadowSize===this.options.shadow.size&&n.shadowX===this.options.shadow.x&&n.shadowY===this.options.shadow.y||(n.shadow=!0)));else n.shadow=this.options.shadow.enabled,n.width=Math.max(n.width,.3/this.body.view.scale);return n}updateLabelModule(t){const e=[t,this.options,this.globalOptions,this.defaultOptions];this.labelModule.update(this.options,e),void 0!==this.labelModule.baseSize&&(this.baseFontSize=this.labelModule.baseSize)}updateEdgeType(){const t=this.options.smooth;let e=!1,i=!0;return void 0!==this.edgeType&&((this.edgeType instanceof O_&&!0===t.enabled&&"dynamic"===t.type||this.edgeType instanceof S_&&!0===t.enabled&&"cubicBezier"===t.type||this.edgeType instanceof C_&&!0===t.enabled&&"dynamic"!==t.type&&"cubicBezier"!==t.type||this.edgeType instanceof T_&&!1===t.type.enabled)&&(i=!1),!0===i&&(e=this.cleanup())),!0===i?!0===t.enabled?"dynamic"===t.type?(e=!0,this.edgeType=new O_(this.options,this.body,this.labelModule)):"cubicBezier"===t.type?this.edgeType=new S_(this.options,this.body,this.labelModule):this.edgeType=new C_(this.options,this.body,this.labelModule):this.edgeType=new T_(this.options,this.body,this.labelModule):this.edgeType.setOptions(this.options),e}connect(){this.disconnect(),this.from=this.body.nodes[this.fromId]||void 0,this.to=this.body.nodes[this.toId]||void 0,this.connected=void 0!==this.from&&void 0!==this.to,!0===this.connected?(this.from.attachEdge(this),this.to.attachEdge(this)):(this.from&&this.from.detachEdge(this),this.to&&this.to.detachEdge(this)),this.edgeType.connect()}disconnect(){this.from&&(this.from.detachEdge(this),this.from=void 0),this.to&&(this.to.detachEdge(this),this.to=void 0),this.connected=!1}getTitle(){return this.title}isSelected(){return this.selected}getValue(){return this.options.value}setValueRange(t,e,i){if(void 0!==this.options.value){const o=this.options.scaling.customScalingFunction(t,e,i,this.options.value),n=this.options.scaling.max-this.options.scaling.min;if(!0===this.options.scaling.label.enabled){const t=this.options.scaling.label.max-this.options.scaling.label.min;this.options.font.size=this.options.scaling.label.min+o*t}this.options.width=this.options.scaling.min+o*n}else this.options.width=this.baseWidth,this.options.font.size=this.baseFontSize;this._setInteractionWidths(),this.updateLabelModule()}_setInteractionWidths(){"function"==typeof this.options.hoverWidth?this.edgeType.hoverWidth=this.options.hoverWidth(this.options.width):this.edgeType.hoverWidth=this.options.hoverWidth+this.options.width,"function"==typeof this.options.selectionWidth?this.edgeType.selectionWidth=this.options.selectionWidth(this.options.width):this.edgeType.selectionWidth=this.options.selectionWidth+this.options.width}draw(t){const e=this.getFormattingValues();if(e.hidden)return;const i=this.edgeType.getViaNode();this.edgeType.drawLine(t,e,this.selected,this.hover,i),this.drawLabel(t,i)}drawArrows(t){const e=this.getFormattingValues();if(e.hidden)return;const i=this.edgeType.getViaNode(),o={};this.edgeType.fromPoint=this.edgeType.from,this.edgeType.toPoint=this.edgeType.to,e.fromArrow&&(o.from=this.edgeType.getArrowData(t,"from",i,this.selected,this.hover,e),!1===e.arrowStrikethrough&&(this.edgeType.fromPoint=o.from.core),e.fromArrowSrc&&(o.from.image=this.imagelist.load(e.fromArrowSrc)),e.fromArrowImageWidth&&(o.from.imageWidth=e.fromArrowImageWidth),e.fromArrowImageHeight&&(o.from.imageHeight=e.fromArrowImageHeight)),e.toArrow&&(o.to=this.edgeType.getArrowData(t,"to",i,this.selected,this.hover,e),!1===e.arrowStrikethrough&&(this.edgeType.toPoint=o.to.core),e.toArrowSrc&&(o.to.image=this.imagelist.load(e.toArrowSrc)),e.toArrowImageWidth&&(o.to.imageWidth=e.toArrowImageWidth),e.toArrowImageHeight&&(o.to.imageHeight=e.toArrowImageHeight)),e.middleArrow&&(o.middle=this.edgeType.getArrowData(t,"middle",i,this.selected,this.hover,e),e.middleArrowSrc&&(o.middle.image=this.imagelist.load(e.middleArrowSrc)),e.middleArrowImageWidth&&(o.middle.imageWidth=e.middleArrowImageWidth),e.middleArrowImageHeight&&(o.middle.imageHeight=e.middleArrowImageHeight)),e.fromArrow&&this.edgeType.drawArrowHead(t,e,this.selected,this.hover,o.from),e.middleArrow&&this.edgeType.drawArrowHead(t,e,this.selected,this.hover,o.middle),e.toArrow&&this.edgeType.drawArrowHead(t,e,this.selected,this.hover,o.to)}drawLabel(t,e){if(void 0!==this.options.label){const i=this.from,o=this.to;let n;if(this.labelModule.differentState(this.selected,this.hover)&&this.labelModule.getTextSize(t,this.selected,this.hover),i.id!=o.id){this.labelModule.pointToSelf=!1,n=this.edgeType.getPoint(.5,e),t.save();const i=this._getRotation(t);0!=i.angle&&(t.translate(i.x,i.y),t.rotate(i.angle)),this.labelModule.draw(t,n.x,n.y,this.selected,this.hover),t.restore()}else{this.labelModule.pointToSelf=!0;const e=rb(t,this.options.selfReference.angle,this.options.selfReference.size,i);n=this._pointOnCircle(e.x,e.y,this.options.selfReference.size,this.options.selfReference.angle),this.labelModule.draw(t,n.x,n.y,this.selected,this.hover)}}}getItemsOnPoint(t){const e=[];if(this.labelModule.visible()){const i=this._getRotation();nb(this.labelModule.getSize(),t,i)&&e.push({edgeId:this.id,labelId:0})}const i={left:t.x,top:t.y};return this.isOverlappingWith(i)&&e.push({edgeId:this.id}),e}isOverlappingWith(t){if(this.connected){const e=10,i=this.from.x,o=this.from.y,n=this.to.x,s=this.to.y,r=t.left,a=t.top;return this.edgeType.getDistanceToEdge(i,o,n,s,r,a)<e}return!1}_getRotation(t){const e=this.edgeType.getViaNode(),i=this.edgeType.getPoint(.5,e);void 0!==t&&this.labelModule.calculateLabelSize(t,this.selected,this.hover,i.x,i.y);const o={x:i.x,y:this.labelModule.size.yLine,angle:0};if(!this.labelModule.visible())return o;if("horizontal"===this.options.font.align)return o;const n=this.from.y-this.to.y,s=this.from.x-this.to.x;let r=Math.atan2(n,s);return(r<-1&&s<0||r>0&&s<0)&&(r+=Math.PI),o.angle=r,o}_pointOnCircle(t,e,i,o){return{x:t+i*Math.cos(o),y:e-i*Math.sin(o)}}select(){this.selected=!0}unselect(){this.selected=!1}cleanup(){return this.edgeType.cleanup()}remove(){this.cleanup(),this.disconnect(),delete this.body.edges[this.id]}endPointsValid(){return void 0!==this.body.nodes[this.fromId]&&void 0!==this.body.nodes[this.toId]}}class M_{constructor(t,e,i){var o;this.body=t,this.images=e,this.groups=i,this.body.functions.createEdge=oo(o=this.create).call(o,this),this.edgesListeners={add:(t,e)=>{this.add(e.items)},update:(t,e)=>{this.update(e.items)},remove:(t,e)=>{this.remove(e.items)}},this.options={},this.defaultOptions={arrows:{to:{enabled:!1,scaleFactor:1,type:"arrow"},middle:{enabled:!1,scaleFactor:1,type:"arrow"},from:{enabled:!1,scaleFactor:1,type:"arrow"}},endPointOffset:{from:0,to:0},arrowStrikethrough:!0,color:{color:"#848484",highlight:"#848484",hover:"#848484",inherit:"from",opacity:1},dashes:!1,font:{color:"#343434",size:14,face:"arial",background:"none",strokeWidth:2,strokeColor:"#ffffff",align:"horizontal",multi:!1,vadjust:0,bold:{mod:"bold"},boldital:{mod:"bold italic"},ital:{mod:"italic"},mono:{mod:"",size:15,face:"courier new",vadjust:2}},hidden:!1,hoverWidth:1.5,label:void 0,labelHighlightBold:!0,length:void 0,physics:!0,scaling:{min:1,max:15,label:{enabled:!0,min:14,max:30,maxVisible:30,drawThreshold:5},customScalingFunction:function(t,e,i,o){if(e===t)return.5;{const i=1/(e-t);return Math.max(0,(o-t)*i)}}},selectionWidth:1.5,selfReference:{size:20,angle:Math.PI/4,renderBehindTheNode:!0},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:10,x:5,y:5},background:{enabled:!1,color:"rgba(111,111,111,1)",size:10,dashes:!1},smooth:{enabled:!0,type:"dynamic",forceDirection:"none",roundness:.5},title:void 0,width:1,value:void 0},Es(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){var t,e,i=this;this.body.emitter.on("_forceDisableDynamicCurves",function(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];"dynamic"===t&&(t="continuous");let o=!1;for(const e in i.body.edges)if(Object.prototype.hasOwnProperty.call(i.body.edges,e)){const n=i.body.edges[e],s=i.body.data.edges.get(e);if(null!=s){const e=s.smooth;void 0!==e&&!0===e.enabled&&"dynamic"===e.type&&(void 0===t?n.setOptions({smooth:!1}):n.setOptions({smooth:{type:t}}),o=!0)}}!0===e&&!0===o&&i.body.emitter.emit("_dataChanged")}),this.body.emitter.on("_dataUpdated",()=>{this.reconnectEdges()}),this.body.emitter.on("refreshEdges",oo(t=this.refresh).call(t,this)),this.body.emitter.on("refresh",oo(e=this.refresh).call(e,this)),this.body.emitter.on("destroy",()=>{Ts(this.edgesListeners,(t,e)=>{this.body.data.edges&&this.body.data.edges.off(e,t)}),delete this.body.functions.createEdge,delete this.edgesListeners.add,delete this.edgesListeners.update,delete this.edgesListeners.remove,delete this.edgesListeners})}setOptions(t){if(void 0!==t){D_.parseOptions(this.options,t,!0,this.defaultOptions,!0);let e=!1;if(void 0!==t.smooth)for(const t in this.body.edges)Object.prototype.hasOwnProperty.call(this.body.edges,t)&&(e=this.body.edges[t].updateEdgeType()||e);if(void 0!==t.font)for(const t in this.body.edges)Object.prototype.hasOwnProperty.call(this.body.edges,t)&&this.body.edges[t].updateLabelModule();void 0===t.hidden&&void 0===t.physics&&!0!==e||this.body.emitter.emit("_dataChanged")}}setData(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const i=this.body.data.edges;if(mf("id",t))this.body.data.edges=t;else if(Vh(t))this.body.data.edges=new pf,this.body.data.edges.add(t);else{if(t)throw new TypeError("Array or DataSet expected");this.body.data.edges=new pf}if(i&&Ts(this.edgesListeners,(t,e)=>{i.off(e,t)}),this.body.edges={},this.body.data.edges){Ts(this.edgesListeners,(t,e)=>{this.body.data.edges.on(e,t)});const t=this.body.data.edges.getIds();this.add(t,!0)}this.body.emitter.emit("_adjustEdgesForHierarchicalLayout"),!1===e&&this.body.emitter.emit("_dataChanged")}add(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const i=this.body.edges,o=this.body.data.edges;for(let e=0;e<t.length;e++){const n=t[e],s=i[n];s&&s.disconnect();const r=o.get(n,{showInternalIds:!0});i[n]=this.create(r)}this.body.emitter.emit("_adjustEdgesForHierarchicalLayout"),!1===e&&this.body.emitter.emit("_dataChanged")}update(t){const e=this.body.edges,i=this.body.data.edges;let o=!1;for(let n=0;n<t.length;n++){const s=t[n],r=i.get(s),a=e[s];void 0!==a?(a.disconnect(),o=a.setOptions(r)||o,a.connect()):(this.body.edges[s]=this.create(r),o=!0)}!0===o?(this.body.emitter.emit("_adjustEdgesForHierarchicalLayout"),this.body.emitter.emit("_dataChanged")):this.body.emitter.emit("_dataUpdated")}remove(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(0===t.length)return;const i=this.body.edges;Ts(t,t=>{const e=i[t];void 0!==e&&e.remove()}),e&&this.body.emitter.emit("_dataChanged")}refresh(){Ts(this.body.edges,(t,e)=>{const i=this.body.data.edges.get(e);void 0!==i&&t.setOptions(i)})}create(t){return new D_(t,this.body,this.images,this.options,this.defaultOptions)}reconnectEdges(){let t;const e=this.body.nodes,i=this.body.edges;for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(e[t].edges=[]);for(t in i)if(Object.prototype.hasOwnProperty.call(i,t)){const e=i[t];e.from=null,e.to=null,e.connect()}}getConnectedNodes(t){const e=[];if(void 0!==this.body.edges[t]){const i=this.body.edges[t];void 0!==i.fromId&&e.push(i.fromId),void 0!==i.toId&&e.push(i.toId)}return e}_updateState(){this._addMissingEdges(),this._removeInvalidEdges()}_removeInvalidEdges(){const t=[];Ts(this.body.edges,(e,i)=>{const o=this.body.nodes[e.toId],n=this.body.nodes[e.fromId];void 0!==o&&!0===o.isCluster||void 0!==n&&!0===n.isCluster||void 0!==o&&void 0!==n||t.push(i)}),this.remove(t,!1)}_addMissingEdges(){const t=this.body.data.edges;if(null==t)return;const e=this.body.edges,i=[];Qh(t).call(t,(t,o)=>{void 0===e[o]&&i.push(o)}),this.add(i,!0)}}var I_,P_,N_,B_,z_,F_,A_,j_={};function R_(){return N_?P_:(N_=1,function(){if(I_)return j_;I_=1;var t=hi(),e=Date,i=O()(e.prototype.getTime);t({target:"Date",stat:!0},{now:function(){return i(new e)}})}(),P_=kt().Date.now)}function L_(){return z_?B_:(z_=1,B_=R_())}var H_=i(A_?F_:(A_=1,F_=L_()));class W_{constructor(t,e,i){this.body=t,this.physicsBody=e,this.barnesHutTree,this.setOptions(i),this._rng=rs("BARNES HUT SOLVER")}setOptions(t){this.options=t,this.thetaInversed=1/this.options.theta,this.overlapAvoidanceFactor=1-Math.max(0,Math.min(1,this.options.avoidOverlap))}solve(){if(0!==this.options.gravitationalConstant&&this.physicsBody.physicsNodeIndices.length>0){let t;const e=this.body.nodes,i=this.physicsBody.physicsNodeIndices,o=i.length,n=this._formBarnesHutTree(e,i);this.barnesHutTree=n;for(let s=0;s<o;s++)t=e[i[s]],t.options.mass>0&&this._getForceContributions(n.root,t)}}_getForceContributions(t,e){this._getForceContribution(t.children.NW,e),this._getForceContribution(t.children.NE,e),this._getForceContribution(t.children.SW,e),this._getForceContribution(t.children.SE,e)}_getForceContribution(t,e){if(t.childrenCount>0){const i=t.centerOfMass.x-e.x,o=t.centerOfMass.y-e.y,n=Math.sqrt(i*i+o*o);n*t.calcSize>this.thetaInversed?this._calculateForces(n,i,o,e,t):4===t.childrenCount?this._getForceContributions(t,e):t.children.data.id!=e.id&&this._calculateForces(n,i,o,e,t)}}_calculateForces(t,e,i,o,n){0===t&&(e=t=.1),this.overlapAvoidanceFactor<1&&o.shape.radius&&(t=Math.max(.1+this.overlapAvoidanceFactor*o.shape.radius,t-o.shape.radius));const s=this.options.gravitationalConstant*n.mass*o.options.mass/Math.pow(t,3),r=e*s,a=i*s;this.physicsBody.forces[o.id].x+=r,this.physicsBody.forces[o.id].y+=a}_formBarnesHutTree(t,e){let i;const o=e.length;let n=t[e[0]].x,s=t[e[0]].y,r=t[e[0]].x,a=t[e[0]].y;for(let i=1;i<o;i++){const o=t[e[i]],h=o.x,d=o.y;o.options.mass>0&&(h<n&&(n=h),h>r&&(r=h),d<s&&(s=d),d>a&&(a=d))}const h=Math.abs(r-n)-Math.abs(a-s);h>0?(s-=.5*h,a+=.5*h):(n+=.5*h,r-=.5*h);const d=Math.max(1e-5,Math.abs(r-n)),l=.5*d,c=.5*(n+r),u=.5*(s+a),p={root:{centerOfMass:{x:0,y:0},mass:0,range:{minX:c-l,maxX:c+l,minY:u-l,maxY:u+l},size:d,calcSize:1/d,children:{data:null},maxWidth:0,level:0,childrenCount:4}};this._splitBranch(p.root);for(let n=0;n<o;n++)i=t[e[n]],i.options.mass>0&&this._placeInTree(p.root,i);return p}_updateBranchMass(t,e){const i=t.centerOfMass,o=t.mass+e.options.mass,n=1/o;i.x=i.x*t.mass+e.x*e.options.mass,i.x*=n,i.y=i.y*t.mass+e.y*e.options.mass,i.y*=n,t.mass=o;const s=Math.max(Math.max(e.height,e.radius),e.width);t.maxWidth=t.maxWidth<s?s:t.maxWidth}_placeInTree(t,e,i){1==i&&void 0!==i||this._updateBranchMass(t,e);const o=t.children.NW.range;let n;n=o.maxX>e.x?o.maxY>e.y?"NW":"SW":o.maxY>e.y?"NE":"SE",this._placeInRegion(t,e,n)}_placeInRegion(t,e,i){const o=t.children[i];switch(o.childrenCount){case 0:o.children.data=e,o.childrenCount=1,this._updateBranchMass(o,e);break;case 1:o.children.data.x===e.x&&o.children.data.y===e.y?(e.x+=this._rng(),e.y+=this._rng()):(this._splitBranch(o),this._placeInTree(o,e));break;case 4:this._placeInTree(o,e)}}_splitBranch(t){let e=null;1===t.childrenCount&&(e=t.children.data,t.mass=0,t.centerOfMass.x=0,t.centerOfMass.y=0),t.childrenCount=4,t.children.data=null,this._insertRegion(t,"NW"),this._insertRegion(t,"NE"),this._insertRegion(t,"SW"),this._insertRegion(t,"SE"),null!=e&&this._placeInTree(t,e)}_insertRegion(t,e){let i,o,n,s;const r=.5*t.size;switch(e){case"NW":i=t.range.minX,o=t.range.minX+r,n=t.range.minY,s=t.range.minY+r;break;case"NE":i=t.range.minX+r,o=t.range.maxX,n=t.range.minY,s=t.range.minY+r;break;case"SW":i=t.range.minX,o=t.range.minX+r,n=t.range.minY+r,s=t.range.maxY;break;case"SE":i=t.range.minX+r,o=t.range.maxX,n=t.range.minY+r,s=t.range.maxY}t.children[e]={centerOfMass:{x:0,y:0},mass:0,range:{minX:i,maxX:o,minY:n,maxY:s},size:.5*t.size,calcSize:2*t.calcSize,children:{data:null},maxWidth:0,level:t.level+1,childrenCount:0}}_debug(t,e){void 0!==this.barnesHutTree&&(t.lineWidth=1,this._drawBranch(this.barnesHutTree.root,t,e))}_drawBranch(t,e,i){void 0===i&&(i="#FF0000"),4===t.childrenCount&&(this._drawBranch(t.children.NW,e),this._drawBranch(t.children.NE,e),this._drawBranch(t.children.SE,e),this._drawBranch(t.children.SW,e)),e.strokeStyle=i,e.beginPath(),e.moveTo(t.range.minX,t.range.minY),e.lineTo(t.range.maxX,t.range.minY),e.stroke(),e.beginPath(),e.moveTo(t.range.maxX,t.range.minY),e.lineTo(t.range.maxX,t.range.maxY),e.stroke(),e.beginPath(),e.moveTo(t.range.maxX,t.range.maxY),e.lineTo(t.range.minX,t.range.maxY),e.stroke(),e.beginPath(),e.moveTo(t.range.minX,t.range.maxY),e.lineTo(t.range.minX,t.range.minY),e.stroke()}}class q_{constructor(t,e,i){this._rng=rs("REPULSION SOLVER"),this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t}solve(){let t,e,i,o,n,s,r,a;const h=this.body.nodes,d=this.physicsBody.physicsNodeIndices,l=this.physicsBody.forces,c=this.options.nodeDistance,u=-2/3/c,p=4/3;for(let f=0;f<d.length-1;f++){r=h[d[f]];for(let g=f+1;g<d.length;g++)a=h[d[g]],t=a.x-r.x,e=a.y-r.y,i=Math.sqrt(t*t+e*e),0===i&&(i=.1*this._rng(),t=i),i<2*c&&(s=i<.5*c?1:u*i+p,s/=i,o=t*s,n=e*s,l[r.id].x-=o,l[r.id].y-=n,l[a.id].x+=o,l[a.id].y+=n)}}}class V_{constructor(t,e,i){this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t,this.overlapAvoidanceFactor=Math.max(0,Math.min(1,this.options.avoidOverlap||0))}solve(){const t=this.body.nodes,e=this.physicsBody.physicsNodeIndices,i=this.physicsBody.forces,o=this.options.nodeDistance;for(let n=0;n<e.length-1;n++){const s=t[e[n]];for(let r=n+1;r<e.length;r++){const n=t[e[r]];if(s.level===n.level){const t=o+this.overlapAvoidanceFactor*((s.shape.radius||0)/2+(n.shape.radius||0)/2),e=n.x-s.x,r=n.y-s.y,a=Math.sqrt(e*e+r*r),h=.05;let d;d=a<t?-Math.pow(h*a,2)+Math.pow(h*t,2):0,0!==a&&(d/=a);const l=e*d,c=r*d;i[s.id].x-=l,i[s.id].y-=c,i[n.id].x+=l,i[n.id].y+=c}}}}}class U_{constructor(t,e,i){this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t}solve(){let t,e;const i=this.physicsBody.physicsEdgeIndices,o=this.body.edges;let n,s,r;for(let a=0;a<i.length;a++)e=o[i[a]],!0===e.connected&&e.toId!==e.fromId&&void 0!==this.body.nodes[e.toId]&&void 0!==this.body.nodes[e.fromId]&&(void 0!==e.edgeType.via?(t=void 0===e.options.length?this.options.springLength:e.options.length,n=e.to,s=e.edgeType.via,r=e.from,this._calculateSpringForce(n,s,.5*t),this._calculateSpringForce(s,r,.5*t)):(t=void 0===e.options.length?1.5*this.options.springLength:e.options.length,this._calculateSpringForce(e.from,e.to,t)))}_calculateSpringForce(t,e,i){const o=t.x-e.x,n=t.y-e.y,s=Math.max(Math.sqrt(o*o+n*n),.01),r=this.options.springConstant*(i-s)/s,a=o*r,h=n*r;void 0!==this.physicsBody.forces[t.id]&&(this.physicsBody.forces[t.id].x+=a,this.physicsBody.forces[t.id].y+=h),void 0!==this.physicsBody.forces[e.id]&&(this.physicsBody.forces[e.id].x-=a,this.physicsBody.forces[e.id].y-=h)}}class Y_{constructor(t,e,i){this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t}solve(){let t,e,i,o,n,s,r,a;const h=this.body.edges,d=.5,l=this.physicsBody.physicsEdgeIndices,c=this.physicsBody.physicsNodeIndices,u=this.physicsBody.forces;for(let t=0;t<c.length;t++){const e=c[t];u[e].springFx=0,u[e].springFy=0}for(let c=0;c<l.length;c++)e=h[l[c]],!0===e.connected&&(t=void 0===e.options.length?this.options.springLength:e.options.length,i=e.from.x-e.to.x,o=e.from.y-e.to.y,a=Math.sqrt(i*i+o*o),a=0===a?.01:a,r=this.options.springConstant*(t-a)/a,n=i*r,s=o*r,e.to.level!=e.from.level?(void 0!==u[e.toId]&&(u[e.toId].springFx-=n,u[e.toId].springFy-=s),void 0!==u[e.fromId]&&(u[e.fromId].springFx+=n,u[e.fromId].springFy+=s)):(void 0!==u[e.toId]&&(u[e.toId].x-=d*n,u[e.toId].y-=d*s),void 0!==u[e.fromId]&&(u[e.fromId].x+=d*n,u[e.fromId].y+=d*s)));let p,f;r=1;for(let t=0;t<c.length;t++){const e=c[t];p=Math.min(r,Math.max(-r,u[e].springFx)),f=Math.min(r,Math.max(-r,u[e].springFy)),u[e].x+=p,u[e].y+=f}let g=0,m=0;for(let t=0;t<c.length;t++){const e=c[t];g+=u[e].x,m+=u[e].y}const v=g/c.length,y=m/c.length;for(let t=0;t<c.length;t++){const e=c[t];u[e].x-=v,u[e].y-=y}}}class X_{constructor(t,e,i){this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t}solve(){let t,e,i,o;const n=this.body.nodes,s=this.physicsBody.physicsNodeIndices,r=this.physicsBody.forces;for(let a=0;a<s.length;a++){o=n[s[a]],t=-o.x,e=-o.y,i=Math.sqrt(t*t+e*e),this._calculateForces(i,t,e,r,o)}}_calculateForces(t,e,i,o,n){const s=0===t?0:this.options.centralGravity/t;o[n.id].x=e*s,o[n.id].y=i*s}}class G_ extends W_{constructor(t,e,i){super(t,e,i),this._rng=rs("FORCE ATLAS 2 BASED REPULSION SOLVER")}_calculateForces(t,e,i,o,n){0===t&&(e=t=.1*this._rng()),this.overlapAvoidanceFactor<1&&o.shape.radius&&(t=Math.max(.1+this.overlapAvoidanceFactor*o.shape.radius,t-o.shape.radius));const s=o.edges.length+1,r=this.options.gravitationalConstant*n.mass*o.options.mass*s/Math.pow(t,2),a=e*r,h=i*r;this.physicsBody.forces[o.id].x+=a,this.physicsBody.forces[o.id].y+=h}}class K_ extends X_{constructor(t,e,i){super(t,e,i)}_calculateForces(t,e,i,o,n){if(t>0){const t=n.edges.length+1,s=this.options.centralGravity*t*n.options.mass;o[n.id].x=e*s,o[n.id].y=i*s}}}class Z_{constructor(t){this.body=t,this.physicsBody={physicsNodeIndices:[],physicsEdgeIndices:[],forces:{},velocities:{}},this.physicsEnabled=!0,this.simulationInterval=1e3/60,this.requiresTimeout=!0,this.previousStates={},this.referenceState={},this.freezeCache={},this.renderTimer=void 0,this.adaptiveTimestep=!1,this.adaptiveTimestepEnabled=!1,this.adaptiveCounter=0,this.adaptiveInterval=3,this.stabilized=!1,this.startedStabilization=!1,this.stabilizationIterations=0,this.ready=!1,this.options={},this.defaultOptions={enabled:!0,barnesHut:{theta:.5,gravitationalConstant:-2e3,centralGravity:.3,springLength:95,springConstant:.04,damping:.09,avoidOverlap:0},forceAtlas2Based:{theta:.5,gravitationalConstant:-50,centralGravity:.01,springConstant:.08,springLength:100,damping:.4,avoidOverlap:0},repulsion:{centralGravity:.2,springLength:200,springConstant:.05,nodeDistance:100,damping:.09,avoidOverlap:0},hierarchicalRepulsion:{centralGravity:0,springLength:100,springConstant:.01,nodeDistance:120,damping:.09},maxVelocity:50,minVelocity:.75,solver:"barnesHut",stabilization:{enabled:!0,iterations:1e3,updateInterval:50,onlyDynamicEdges:!1,fit:!0},timestep:.5,adaptiveTimestep:!0,wind:{x:0,y:0}},Ki(this.options,this.defaultOptions),this.timestep=.5,this.layoutFailed=!1,this.bindEventListeners()}bindEventListeners(){this.body.emitter.on("initPhysics",()=>{this.initPhysics()}),this.body.emitter.on("_layoutFailed",()=>{this.layoutFailed=!0}),this.body.emitter.on("resetPhysics",()=>{this.stopSimulation(),this.ready=!1}),this.body.emitter.on("disablePhysics",()=>{this.physicsEnabled=!1,this.stopSimulation()}),this.body.emitter.on("restorePhysics",()=>{this.setOptions(this.options),!0===this.ready&&this.startSimulation()}),this.body.emitter.on("startSimulation",()=>{!0===this.ready&&this.startSimulation()}),this.body.emitter.on("stopSimulation",()=>{this.stopSimulation()}),this.body.emitter.on("destroy",()=>{this.stopSimulation(!1),this.body.emitter.off()}),this.body.emitter.on("_dataChanged",()=>{this.updatePhysicsData()})}setOptions(t){if(void 0!==t)if(!1===t)this.options.enabled=!1,this.physicsEnabled=!1,this.stopSimulation();else if(!0===t)this.options.enabled=!0,this.physicsEnabled=!0,this.startSimulation();else{this.physicsEnabled=!0,xs(["stabilization"],this.options,t),Vs(this.options,t,"stabilization"),void 0===t.enabled&&(this.options.enabled=!0),!1===this.options.enabled&&(this.physicsEnabled=!1,this.stopSimulation());const e=this.options.wind;e&&(("number"!=typeof e.x||op(e.x))&&(e.x=0),("number"!=typeof e.y||op(e.y))&&(e.y=0)),this.timestep=this.options.timestep}this.init()}init(){let t;"forceAtlas2Based"===this.options.solver?(t=this.options.forceAtlas2Based,this.nodesSolver=new G_(this.body,this.physicsBody,t),this.edgesSolver=new U_(this.body,this.physicsBody,t),this.gravitySolver=new K_(this.body,this.physicsBody,t)):"repulsion"===this.options.solver?(t=this.options.repulsion,this.nodesSolver=new q_(this.body,this.physicsBody,t),this.edgesSolver=new U_(this.body,this.physicsBody,t),this.gravitySolver=new X_(this.body,this.physicsBody,t)):"hierarchicalRepulsion"===this.options.solver?(t=this.options.hierarchicalRepulsion,this.nodesSolver=new V_(this.body,this.physicsBody,t),this.edgesSolver=new Y_(this.body,this.physicsBody,t),this.gravitySolver=new X_(this.body,this.physicsBody,t)):(t=this.options.barnesHut,this.nodesSolver=new W_(this.body,this.physicsBody,t),this.edgesSolver=new U_(this.body,this.physicsBody,t),this.gravitySolver=new X_(this.body,this.physicsBody,t)),this.modelOptions=t}initPhysics(){!0===this.physicsEnabled&&!0===this.options.enabled?!0===this.options.stabilization.enabled?this.stabilize():(this.stabilized=!1,this.ready=!0,this.body.emitter.emit("fit",{},this.layoutFailed),this.startSimulation()):(this.ready=!0,this.body.emitter.emit("fit"))}startSimulation(){var t;!0===this.physicsEnabled&&!0===this.options.enabled?(this.stabilized=!1,this.adaptiveTimestep=!1,this.body.emitter.emit("_resizeNodes"),void 0===this.viewFunction&&(this.viewFunction=oo(t=this.simulationStep).call(t,this),this.body.emitter.on("initRedraw",this.viewFunction),this.body.emitter.emit("_startRendering"))):this.body.emitter.emit("_redraw")}stopSimulation(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.stabilized=!0,!0===t&&this._emitStabilized(),void 0!==this.viewFunction&&(this.body.emitter.off("initRedraw",this.viewFunction),this.viewFunction=void 0,!0===t&&this.body.emitter.emit("_stopRendering"))}simulationStep(){const t=H_();this.physicsTick();(H_()-t<.4*this.simulationInterval||!0===this.runDoubleSpeed)&&!1===this.stabilized&&(this.physicsTick(),this.runDoubleSpeed=!0),!0===this.stabilized&&this.stopSimulation()}_emitStabilized(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.stabilizationIterations;(this.stabilizationIterations>1||!0===this.startedStabilization)&&tf(()=>{this.body.emitter.emit("stabilized",{iterations:t}),this.startedStabilization=!1,this.stabilizationIterations=0},0)}physicsStep(){this.gravitySolver.solve(),this.nodesSolver.solve(),this.edgesSolver.solve(),this.moveNodes()}adjustTimeStep(){!0===this._evaluateStepQuality()?this.timestep=1.2*this.timestep:this.timestep/1.2<this.options.timestep?this.timestep=this.options.timestep:(this.adaptiveCounter=-1,this.timestep=Math.max(this.options.timestep,this.timestep/1.2))}physicsTick(){if(this._startStabilizing(),!0!==this.stabilized){if(!0===this.adaptiveTimestep&&!0===this.adaptiveTimestepEnabled){this.adaptiveCounter%this.adaptiveInterval===0?(this.timestep=2*this.timestep,this.physicsStep(),this.revert(),this.timestep=.5*this.timestep,this.physicsStep(),this.physicsStep(),this.adjustTimeStep()):this.physicsStep(),this.adaptiveCounter+=1}else this.timestep=this.options.timestep,this.physicsStep();!0===this.stabilized&&this.revert(),this.stabilizationIterations++}}updatePhysicsData(){this.physicsBody.forces={},this.physicsBody.physicsNodeIndices=[],this.physicsBody.physicsEdgeIndices=[];const t=this.body.nodes,e=this.body.edges;for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&!0===t[e].options.physics&&this.physicsBody.physicsNodeIndices.push(t[e].id);for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&!0===e[t].options.physics&&this.physicsBody.physicsEdgeIndices.push(e[t].id);for(let t=0;t<this.physicsBody.physicsNodeIndices.length;t++){const e=this.physicsBody.physicsNodeIndices[t];this.physicsBody.forces[e]={x:0,y:0},void 0===this.physicsBody.velocities[e]&&(this.physicsBody.velocities[e]={x:0,y:0})}for(const e in this.physicsBody.velocities)void 0===t[e]&&delete this.physicsBody.velocities[e]}revert(){const t=Sp(this.previousStates),e=this.body.nodes,i=this.physicsBody.velocities;this.referenceState={};for(let o=0;o<t.length;o++){const n=t[o];void 0!==e[n]?!0===e[n].options.physics&&(this.referenceState[n]={positions:{x:e[n].x,y:e[n].y}},i[n].x=this.previousStates[n].vx,i[n].y=this.previousStates[n].vy,e[n].x=this.previousStates[n].x,e[n].y=this.previousStates[n].y):delete this.previousStates[n]}}_evaluateStepQuality(){let t,e,i;const o=this.body.nodes,n=this.referenceState;for(const s in this.referenceState)if(Object.prototype.hasOwnProperty.call(this.referenceState,s)&&void 0!==o[s]&&(t=o[s].x-n[s].positions.x,e=o[s].y-n[s].positions.y,i=Math.sqrt(Math.pow(t,2)+Math.pow(e,2)),i>.3))return!1;return!0}moveNodes(){const t=this.physicsBody.physicsNodeIndices;let e=0,i=0;for(let o=0;o<t.length;o++){const n=t[o],s=this._performStep(n);e=Math.max(e,s),i+=s}this.adaptiveTimestepEnabled=i/t.length<5,this.stabilized=e<this.options.minVelocity}calculateComponentVelocity(t,e,i){t+=(e-this.modelOptions.damping*t)/i*this.timestep;const o=this.options.maxVelocity||1e9;return Math.abs(t)>o&&(t=t>0?o:-o),t}_performStep(t){const e=this.body.nodes[t],i=this.physicsBody.forces[t];this.options.wind&&(i.x+=this.options.wind.x,i.y+=this.options.wind.y);const o=this.physicsBody.velocities[t];this.previousStates[t]={x:e.x,y:e.y,vx:o.x,vy:o.y},!1===e.options.fixed.x?(o.x=this.calculateComponentVelocity(o.x,i.x,e.options.mass),e.x+=o.x*this.timestep):(i.x=0,o.x=0),!1===e.options.fixed.y?(o.y=this.calculateComponentVelocity(o.y,i.y,e.options.mass),e.y+=o.y*this.timestep):(i.y=0,o.y=0);return Math.sqrt(Math.pow(o.x,2)+Math.pow(o.y,2))}_freezeNodes(){const t=this.body.nodes;for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)&&t[e].x&&t[e].y){const i=t[e].options.fixed;this.freezeCache[e]={x:i.x,y:i.y},i.x=!0,i.y=!0}}_restoreFrozenNodes(){const t=this.body.nodes;for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&void 0!==this.freezeCache[e]&&(t[e].options.fixed.x=this.freezeCache[e].x,t[e].options.fixed.y=this.freezeCache[e].y);this.freezeCache={}}stabilize(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.stabilization.iterations;"number"!=typeof t&&(t=this.options.stabilization.iterations,console.error("The stabilize method needs a numeric amount of iterations. Switching to default: ",t)),0!==this.physicsBody.physicsNodeIndices.length?(this.adaptiveTimestep=this.options.adaptiveTimestep,this.body.emitter.emit("_resizeNodes"),this.stopSimulation(),this.stabilized=!1,this.body.emitter.emit("_blockRedraw"),this.targetIterations=t,!0===this.options.stabilization.onlyDynamicEdges&&this._freezeNodes(),this.stabilizationIterations=0,tf(()=>this._stabilizationBatch(),0)):this.ready=!0}_startStabilizing(){return!0!==this.startedStabilization&&(this.body.emitter.emit("startStabilizing"),this.startedStabilization=!0,!0)}_stabilizationBatch(){const t=()=>!1===this.stabilized&&this.stabilizationIterations<this.targetIterations,e=()=>{this.body.emitter.emit("stabilizationProgress",{iterations:this.stabilizationIterations,total:this.targetIterations})};this._startStabilizing()&&e();let i=0;for(;t()&&i<this.options.stabilization.updateInterval;)this.physicsTick(),i++;var o;(e(),t())?tf(oo(o=this._stabilizationBatch).call(o,this),0):this._finalizeStabilization()}_finalizeStabilization(){this.body.emitter.emit("_allowRedraw"),!0===this.options.stabilization.fit&&this.body.emitter.emit("fit"),!0===this.options.stabilization.onlyDynamicEdges&&this._restoreFrozenNodes(),this.body.emitter.emit("stabilizationIterationsDone"),this.body.emitter.emit("_requestRedraw"),!0===this.stabilized?this._emitStabilized():this.startSimulation(),this.ready=!0}_drawForces(t){for(let e=0;e<this.physicsBody.physicsNodeIndices.length;e++){const i=this.physicsBody.physicsNodeIndices[e],o=this.body.nodes[i],n=this.physicsBody.forces[i],s=20,r=.03,a=Math.sqrt(Math.pow(n.x,2)+Math.pow(n.x,2)),h=Math.min(Math.max(5,a),15),d=3*h,l=js((180-180*Math.min(1,Math.max(0,r*a)))/360,1,1),c={x:o.x+s*n.x,y:o.y+s*n.y};t.lineWidth=h,t.strokeStyle=l,t.beginPath(),t.moveTo(o.x,o.y),t.lineTo(c.x,c.y),t.stroke();const u=Math.atan2(n.y,n.x);t.fillStyle=l,v_.draw(t,{type:"arrow",point:c,angle:u,length:d}),ew(t).call(t)}}}var Q_,$_,J_,tx,ex,ix,ox,nx,sx,rx={};function ax(){return J_?$_:(J_=1,function(){if(Q_)return rx;Q_=1;var t=hi(),e=O(),i=hh(),o=e([].reverse),n=[1,2];t({target:"Array",proto:!0,forced:String(n)===String(n.reverse())},{reverse:function(){return i(this)&&(this.length=this.length),o(this)}})}(),$_=Ji()("Array","reverse"))}function hx(){if(ex)return tx;ex=1;var t=Tt(),e=ax(),i=Array.prototype;return tx=function(o){var n=o.reverse;return o===i||t(i,o)&&n===i.reverse?e:n},tx}function dx(){return ox?ix:(ox=1,ix=hx())}var lx,cx,ux,px,fx=i(sx?nx:(sx=1,nx=dx()));class gx{constructor(){}static getRange(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o=1e9,n=-1e9,s=1e9,r=-1e9;if(i.length>0)for(let a=0;a<i.length;a++)e=t[i[a]],s>e.shape.boundingBox.left&&(s=e.shape.boundingBox.left),r<e.shape.boundingBox.right&&(r=e.shape.boundingBox.right),o>e.shape.boundingBox.top&&(o=e.shape.boundingBox.top),n<e.shape.boundingBox.bottom&&(n=e.shape.boundingBox.bottom);return 1e9===s&&-1e9===r&&1e9===o&&-1e9===n&&(o=0,n=0,s=0,r=0),{minX:s,maxX:r,minY:o,maxY:n}}static getRangeCore(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o=1e9,n=-1e9,s=1e9,r=-1e9;if(i.length>0)for(let a=0;a<i.length;a++)e=t[i[a]],s>e.x&&(s=e.x),r<e.x&&(r=e.x),o>e.y&&(o=e.y),n<e.y&&(n=e.y);return 1e9===s&&-1e9===r&&1e9===o&&-1e9===n&&(o=0,n=0,s=0,r=0),{minX:s,maxX:r,minY:o,maxY:n}}static findCenter(t){return{x:.5*(t.maxX+t.minX),y:.5*(t.maxY+t.minY)}}static cloneOptions(t,e){const i={};return void 0===e||"node"===e?(Es(i,t.options,!0),i.x=t.x,i.y=t.y,i.amountOfConnections=t.edges.length):Es(i,t.options,!0),i}}class mx extends Cw{constructor(t,e,i,o,n,s){super(t,e,i,o,n,s),this.isCluster=!0,this.containedNodes={},this.containedEdges={}}_openChildCluster(t){const e=this.body.nodes[t];if(void 0===this.containedNodes[t])throw new Error("node with id: "+t+" not in current cluster");if(!e.isCluster)throw new Error("node with id: "+t+" is not a cluster");delete this.containedNodes[t],Ts(e.edges,t=>{delete this.containedEdges[t.id]}),Ts(e.containedNodes,(t,e)=>{this.containedNodes[e]=t}),e.containedNodes={},Ts(e.containedEdges,(t,e)=>{this.containedEdges[e]=t}),e.containedEdges={},Ts(e.edges,t=>{Ts(this.edges,e=>{var i,o;const n=Zr(i=e.clusteringEdgeReplacingIds).call(i,t.id);-1!==n&&(Ts(t.clusteringEdgeReplacingIds,t=>{e.clusteringEdgeReplacingIds.push(t),this.body.edges[t].edgeReplacedById=e.id}),Th(o=e.clusteringEdgeReplacingIds).call(o,n,1))})}),e.edges=[]}}class vx{constructor(t){this.body=t,this.clusteredNodes={},this.clusteredEdges={},this.options={},this.defaultOptions={},Ki(this.options,this.defaultOptions),this.body.emitter.on("_resetData",()=>{this.clusteredNodes={},this.clusteredEdges={}})}clusterByHubsize(t,e){void 0===t?t=this._getHubSize():"object"==typeof t&&(e=this._checkOptions(t),t=this._getHubSize());const i=[];for(let e=0;e<this.body.nodeIndices.length;e++){const o=this.body.nodes[this.body.nodeIndices[e]];o.edges.length>=t&&i.push(o.id)}for(let t=0;t<i.length;t++)this.clusterByConnection(i[t],e,!0);this.body.emitter.emit("_dataChanged")}cluster(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(void 0===t.joinCondition)throw new Error("Cannot call clusterByNodeData without a joinCondition function in the options.");t=this._checkOptions(t);const i={},o={};Ts(this.body.nodes,(e,n)=>{e.options&&!0===t.joinCondition(e.options)&&(i[n]=e,Ts(e.edges,t=>{void 0===this.clusteredEdges[t.id]&&(o[t.id]=t)}))}),this._cluster(i,o,t,e)}clusterByEdgeCount(t,e){let i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];e=this._checkOptions(e);const o=[],n={};let s,r,a;for(let i=0;i<this.body.nodeIndices.length;i++){const h={},d={},l=this.body.nodeIndices[i],c=this.body.nodes[l];if(void 0===n[l]){a=0,r=[];for(let t=0;t<c.edges.length;t++)s=c.edges[t],void 0===this.clusteredEdges[s.id]&&(s.toId!==s.fromId&&a++,r.push(s));if(a===t){const t=function(t){if(void 0===e.joinCondition||null===e.joinCondition)return!0;const i=gx.cloneOptions(t);return e.joinCondition(i)};let i=!0;for(let e=0;e<r.length;e++){s=r[e];const o=this._getConnectedId(s,l);if(!t(c)){i=!1;break}d[s.id]=s,h[l]=c,h[o]=this.body.nodes[o],n[l]=!0}if(Sp(h).length>0&&Sp(d).length>0&&!0===i){const t=function(){for(let t=0;t<o.length;++t)for(const e in h)if(void 0!==o[t].nodes[e])return o[t]}();if(void 0!==t){for(const e in h)void 0===t.nodes[e]&&(t.nodes[e]=h[e]);for(const e in d)void 0===t.edges[e]&&(t.edges[e]=d[e])}else o.push({nodes:h,edges:d})}}}}for(let t=0;t<o.length;t++)this._cluster(o[t].nodes,o[t].edges,e,!1);!0===i&&this.body.emitter.emit("_dataChanged")}clusterOutliers(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.clusterByEdgeCount(1,t,e)}clusterBridges(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.clusterByEdgeCount(2,t,e)}clusterByConnection(t,e){var i;let o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(void 0===t)throw new Error("No nodeId supplied to clusterByConnection!");if(void 0===this.body.nodes[t])throw new Error("The nodeId given to clusterByConnection does not exist!");const n=this.body.nodes[t];void 0===(e=this._checkOptions(e,n)).clusterNodeProperties.x&&(e.clusterNodeProperties.x=n.x),void 0===e.clusterNodeProperties.y&&(e.clusterNodeProperties.y=n.y),void 0===e.clusterNodeProperties.fixed&&(e.clusterNodeProperties.fixed={},e.clusterNodeProperties.fixed.x=n.options.fixed.x,e.clusterNodeProperties.fixed.y=n.options.fixed.y);const s={},r={},a=n.id,h=gx.cloneOptions(n);s[a]=n;for(let t=0;t<n.edges.length;t++){const i=n.edges[t];if(void 0===this.clusteredEdges[i.id]){const t=this._getConnectedId(i,a);if(void 0===this.clusteredNodes[t])if(t!==a)if(void 0===e.joinCondition)r[i.id]=i,s[t]=this.body.nodes[t];else{const o=gx.cloneOptions(this.body.nodes[t]);!0===e.joinCondition(h,o)&&(r[i.id]=i,s[t]=this.body.nodes[t])}else r[i.id]=i}}const d=Hd(i=Sp(s)).call(i,function(t){return s[t].id});for(const t in s){if(!Object.prototype.hasOwnProperty.call(s,t))continue;const e=s[t];for(let t=0;t<e.edges.length;t++){const i=e.edges[t];Zr(d).call(d,this._getConnectedId(i,e.id))>-1&&(r[i.id]=i)}}this._cluster(s,r,e,o)}_createClusterEdges(t,e,i,o){let n,s,r,a,h,d;const l=Sp(t),c=[];for(let o=0;o<l.length;o++){s=l[o],r=t[s];for(let o=0;o<r.edges.length;o++)n=r.edges[o],void 0===this.clusteredEdges[n.id]&&(n.toId==n.fromId?e[n.id]=n:n.toId==s?(a=i.id,h=n.fromId,d=h):(a=n.toId,h=i.id,d=a),void 0===t[d]&&c.push({edge:n,fromId:h,toId:a}))}const u=[],p=function(t){for(let e=0;e<u.length;e++){const i=u[e],o=t.fromId===i.fromId&&t.toId===i.toId,n=t.fromId===i.toId&&t.toId===i.fromId;if(o||n)return i}return null};for(let t=0;t<c.length;t++){const e=c[t],i=e.edge;let n=p(e);null===n?(n=this._createClusteredEdge(e.fromId,e.toId,i,o),u.push(n)):n.clusteringEdgeReplacingIds.push(i.id),this.body.edges[i.id].edgeReplacedById=n.id,this._backupEdgeOptions(i),i.setOptions({physics:!1})}}_checkOptions(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return void 0===t.clusterEdgeProperties&&(t.clusterEdgeProperties={}),void 0===t.clusterNodeProperties&&(t.clusterNodeProperties={}),t}_cluster(t,e,i){let o=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];const n=[];for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&void 0!==this.clusteredNodes[e]&&n.push(e);for(let e=0;e<n.length;++e)delete t[n[e]];if(0==Sp(t).length)return;if(1==Sp(t).length&&1!=i.clusterNodeProperties.allowSingleNodeCluster)return;let s=Es({},i.clusterNodeProperties);if(void 0!==i.processProperties){const o=[];for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)){const i=gx.cloneOptions(t[e]);o.push(i)}const n=[];for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&"clusterEdge:"!==t.substr(0,12)){const i=gx.cloneOptions(e[t],"edge");n.push(i)}if(s=i.processProperties(s,o,n),!s)throw new Error("The processProperties function does not return properties!")}void 0===s.id&&(s.id="cluster:"+rf());const r=s.id;let a;void 0===s.label&&(s.label="cluster"),void 0===s.x&&(a=this._getClusterPosition(t),s.x=a.x),void 0===s.y&&(void 0===a&&(a=this._getClusterPosition(t)),s.y=a.y),s.id=r;const h=this.body.functions.createNode(s,mx);h.containedNodes=t,h.containedEdges=e,h.clusterEdgeProperties=i.clusterEdgeProperties,this.body.nodes[s.id]=h,this._clusterEdges(t,e,s,i.clusterEdgeProperties),s.id=void 0,!0===o&&this.body.emitter.emit("_dataChanged")}_backupEdgeOptions(t){void 0===this.clusteredEdges[t.id]&&(this.clusteredEdges[t.id]={physics:t.options.physics})}_restoreEdge(t){const e=this.clusteredEdges[t.id];void 0!==e&&(t.setOptions({physics:e.physics}),delete this.clusteredEdges[t.id])}isCluster(t){return void 0!==this.body.nodes[t]?!0===this.body.nodes[t].isCluster:(console.error("Node does not exist."),!1)}_getClusterPosition(t){const e=Sp(t);let i,o=t[e[0]].x,n=t[e[0]].x,s=t[e[0]].y,r=t[e[0]].y;for(let a=1;a<e.length;a++)i=t[e[a]],o=i.x<o?i.x:o,n=i.x>n?i.x:n,s=i.y<s?i.y:s,r=i.y>r?i.y:r;return{x:.5*(o+n),y:.5*(s+r)}}openCluster(t,e){let i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(void 0===t)throw new Error("No clusterNodeId supplied to openCluster.");const o=this.body.nodes[t];if(void 0===o)throw new Error("The clusterNodeId supplied to openCluster does not exist.");if(!0!==o.isCluster||void 0===o.containedNodes||void 0===o.containedEdges)throw new Error("The node:"+t+" is not a valid cluster.");const n=this.findNode(t),s=Zr(n).call(n,t)-1;if(s>=0){const e=n[s];return this.body.nodes[e]._openChildCluster(t),delete this.body.nodes[t],void(!0===i&&this.body.emitter.emit("_dataChanged"))}const r=o.containedNodes,a=o.containedEdges;if(void 0!==e&&void 0!==e.releaseFunction&&"function"==typeof e.releaseFunction){const t={},i={x:o.x,y:o.y};for(const e in r)if(Object.prototype.hasOwnProperty.call(r,e)){const i=this.body.nodes[e];t[e]={x:i.x,y:i.y}}const n=e.releaseFunction(i,t);for(const t in r)if(Object.prototype.hasOwnProperty.call(r,t)){const e=this.body.nodes[t];void 0!==n[t]&&(e.x=void 0===n[t].x?o.x:n[t].x,e.y=void 0===n[t].y?o.y:n[t].y)}}else Ts(r,function(t){!1===t.options.fixed.x&&(t.x=o.x),!1===t.options.fixed.y&&(t.y=o.y)});for(const t in r)if(Object.prototype.hasOwnProperty.call(r,t)){const e=this.body.nodes[t];e.vx=o.vx,e.vy=o.vy,e.setOptions({physics:!0}),delete this.clusteredNodes[t]}const h=[];for(let t=0;t<o.edges.length;t++)h.push(o.edges[t]);for(let e=0;e<h.length;e++){const i=h[e],o=this._getConnectedId(i,t),n=this.clusteredNodes[o];for(let t=0;t<i.clusteringEdgeReplacingIds.length;t++){const e=i.clusteringEdgeReplacingIds[t],s=this.body.edges[e];if(void 0!==s)if(void 0!==n){const t=this.body.nodes[n.clusterId];t.containedEdges[s.id]=s,delete a[s.id];let e=s.fromId,i=s.toId;s.toId==o?i=n.clusterId:e=n.clusterId,this._createClusteredEdge(e,i,s,t.clusterEdgeProperties,{hidden:!1,physics:!0})}else this._restoreEdge(s)}i.remove()}for(const t in a)Object.prototype.hasOwnProperty.call(a,t)&&this._restoreEdge(a[t]);delete this.body.nodes[t],!0===i&&this.body.emitter.emit("_dataChanged")}getNodesInCluster(t){const e=[];if(!0===this.isCluster(t)){const i=this.body.nodes[t].containedNodes;for(const t in i)Object.prototype.hasOwnProperty.call(i,t)&&e.push(this.body.nodes[t].id)}return e}findNode(t){const e=[];let i,o=0;for(;void 0!==this.clusteredNodes[t]&&o<100;){if(i=this.body.nodes[t],void 0===i)return[];e.push(i.id),t=this.clusteredNodes[t].clusterId,o++}return i=this.body.nodes[t],void 0===i?[]:(e.push(i.id),fx(e).call(e),e)}updateClusteredNode(t,e){if(void 0===t)throw new Error("No clusteredNodeId supplied to updateClusteredNode.");if(void 0===e)throw new Error("No newOptions supplied to updateClusteredNode.");if(void 0===this.body.nodes[t])throw new Error("The clusteredNodeId supplied to updateClusteredNode does not exist.");this.body.nodes[t].setOptions(e),this.body.emitter.emit("_dataChanged")}updateEdge(t,e){if(void 0===t)throw new Error("No startEdgeId supplied to updateEdge.");if(void 0===e)throw new Error("No newOptions supplied to updateEdge.");if(void 0===this.body.edges[t])throw new Error("The startEdgeId supplied to updateEdge does not exist.");const i=this.getClusteredEdges(t);for(let t=0;t<i.length;t++){this.body.edges[i[t]].setOptions(e)}this.body.emitter.emit("_dataChanged")}getClusteredEdges(t){const e=[];let i=0;for(;void 0!==t&&void 0!==this.body.edges[t]&&i<100;)e.push(this.body.edges[t].id),t=this.body.edges[t].edgeReplacedById,i++;return fx(e).call(e),e}getBaseEdge(t){return this.getBaseEdges(t)[0]}getBaseEdges(t){const e=[t],i=[],o=[];let n=0;for(;e.length>0&&n<100;){const t=e.pop();if(void 0===t)continue;const s=this.body.edges[t];if(void 0===s)continue;n++;const r=s.clusteringEdgeReplacingIds;if(void 0===r)o.push(t);else for(let t=0;t<r.length;++t){const o=r[t];-1===Zr(e).call(e,r)&&-1===Zr(i).call(i,r)&&e.push(o)}i.push(t)}return o}_getConnectedId(t,e){return t.toId!=e?t.toId:(t.fromId,t.fromId)}_getHubSize(){let t=0,e=0,i=0,o=0;for(let n=0;n<this.body.nodeIndices.length;n++){const s=this.body.nodes[this.body.nodeIndices[n]];s.edges.length>o&&(o=s.edges.length),t+=s.edges.length,e+=Math.pow(s.edges.length,2),i+=1}t/=i,e/=i;const n=e-Math.pow(t,2),s=Math.sqrt(n);let r=Math.floor(t+2*s);return r>o&&(r=o),r}_createClusteredEdge(t,e,i,o,n){const s=gx.cloneOptions(i,"edge");Es(s,o),s.from=t,s.to=e,s.id="clusterEdge:"+rf(),void 0!==n&&Es(s,n);const r=this.body.functions.createEdge(s);return r.clusteringEdgeReplacingIds=[i.id],r.connect(),this.body.edges[r.id]=r,r}_clusterEdges(t,e,i,o){if(e instanceof D_){const t=e,i={};i[t.id]=t,e=i}if(t instanceof Cw){const e=t,i={};i[e.id]=e,t=i}if(null==i)throw new Error("_clusterEdges: parameter clusterNode required");void 0===o&&(o=i.clusterEdgeProperties),this._createClusterEdges(t,e,i,o);for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&void 0!==this.body.edges[t]){const e=this.body.edges[t];this._backupEdgeOptions(e),e.setOptions({physics:!1})}for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(this.clusteredNodes[e]={clusterId:i.id,node:this.body.nodes[e]},this.body.nodes[e].setOptions({physics:!1}))}_getClusterNodeForNode(t){if(void 0===t)return;const e=this.clusteredNodes[t];if(void 0===e)return;const i=e.clusterId;return void 0!==i?this.body.nodes[i]:void 0}_filter(t,e){const i=[];return Ts(t,t=>{e(t)&&i.push(t)}),i}_updateState(){let t;const e=[],i={},o=t=>{Ts(this.body.nodes,e=>{!0===e.isCluster&&t(e)})};for(t in this.clusteredNodes){if(!Object.prototype.hasOwnProperty.call(this.clusteredNodes,t))continue;void 0===this.body.nodes[t]&&e.push(t)}o(function(t){for(let i=0;i<e.length;i++)delete t.containedNodes[e[i]]});for(let t=0;t<e.length;t++)delete this.clusteredNodes[e[t]];Ts(this.clusteredEdges,t=>{const e=this.body.edges[t];void 0!==e&&e.endPointsValid()||(i[t]=t)}),o(function(t){Ts(t.containedEdges,(t,e)=>{t.endPointsValid()||i[e]||(i[e]=e)})}),Ts(this.body.edges,(t,e)=>{let o=!0;const n=t.clusteringEdgeReplacingIds;if(void 0!==n){let t=0;Ts(n,e=>{const i=this.body.edges[e];void 0!==i&&i.endPointsValid()&&(t+=1)}),o=t>0}t.endPointsValid()&&o||(i[e]=e)}),o(t=>{Ts(i,e=>{delete t.containedEdges[e],Ts(t.edges,(o,n)=>{o.id!==e?o.clusteringEdgeReplacingIds=this._filter(o.clusteringEdgeReplacingIds,function(t){return!i[t]}):t.edges[n]=null}),t.edges=this._filter(t.edges,function(t){return null!==t})})}),Ts(i,t=>{delete this.clusteredEdges[t]}),Ts(i,t=>{delete this.body.edges[t]});Ts(Sp(this.body.edges),t=>{const e=this.body.edges[t],i=this._isClusteredNode(e.fromId)||this._isClusteredNode(e.toId);if(i!==this._isClusteredEdge(e.id))if(i){const t=this._getClusterNodeForNode(e.fromId);void 0!==t&&this._clusterEdges(this.body.nodes[e.fromId],e,t);const i=this._getClusterNodeForNode(e.toId);void 0!==i&&this._clusterEdges(this.body.nodes[e.toId],e,i)}else delete this._clusterEdges[t],this._restoreEdge(e)});let n=!1,s=!0;for(;s;){const t=[];o(function(e){const i=Sp(e.containedNodes).length,o=!0===e.options.allowSingleNodeCluster;(o&&i<1||!o&&i<2)&&t.push(e.id)});for(let e=0;e<t.length;++e)this.openCluster(t[e],{},!1);s=t.length>0,n=n||s}n&&this._updateState()}_isClusteredNode(t){return void 0!==this.clusteredNodes[t]}_isClusteredEdge(t){return void 0!==this.clusteredEdges[t]}}class yx{constructor(t,e){this.body=t,this.canvas=e,this.redrawRequested=!1,this.requestAnimationFrameRequestId=void 0,this.renderingActive=!1,this.renderRequests=0,this.allowRedraw=!0,this.dragging=!1,this.zooming=!1,this.options={},this.defaultOptions={hideEdgesOnDrag:!1,hideEdgesOnZoom:!1,hideNodesOnDrag:!1},Ki(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){var t;this.body.emitter.on("dragStart",()=>{this.dragging=!0}),this.body.emitter.on("dragEnd",()=>{this.dragging=!1}),this.body.emitter.on("zoom",()=>{this.zooming=!0,window.clearTimeout(this.zoomTimeoutId),this.zoomTimeoutId=tf(()=>{var t;this.zooming=!1,oo(t=this._requestRedraw).call(t,this)()},250)}),this.body.emitter.on("_resizeNodes",()=>{this._resizeNodes()}),this.body.emitter.on("_redraw",()=>{!1===this.renderingActive&&this._redraw()}),this.body.emitter.on("_blockRedraw",()=>{this.allowRedraw=!1}),this.body.emitter.on("_allowRedraw",()=>{this.allowRedraw=!0,this.redrawRequested=!1}),this.body.emitter.on("_requestRedraw",oo(t=this._requestRedraw).call(t,this)),this.body.emitter.on("_startRendering",()=>{this.renderRequests+=1,this.renderingActive=!0,this._startRendering()}),this.body.emitter.on("_stopRendering",()=>{this.renderRequests-=1,this.renderingActive=this.renderRequests>0,this.requestAnimationFrameRequestId=void 0}),this.body.emitter.on("destroy",()=>{this.renderRequests=0,this.allowRedraw=!1,this.renderingActive=!1,window.cancelAnimationFrame(this.requestAnimationFrameRequestId),this.body.emitter.off()})}setOptions(t){if(void 0!==t){_s(["hideEdgesOnDrag","hideEdgesOnZoom","hideNodesOnDrag"],this.options,t)}}_startRendering(){var t;!0===this.renderingActive&&(void 0===this.requestAnimationFrameRequestId&&(this.requestAnimationFrameRequestId=window.requestAnimationFrame(oo(t=this._renderStep).call(t,this),this.simulationInterval)))}_renderStep(){!0===this.renderingActive&&(this.requestAnimationFrameRequestId=void 0,this._startRendering(),this._redraw())}redraw(){this.body.emitter.emit("setSize"),this._redraw()}_requestRedraw(){!0!==this.redrawRequested&&!1===this.renderingActive&&!0===this.allowRedraw&&(this.redrawRequested=!0,window.requestAnimationFrame(()=>{this._redraw(!1)}))}_redraw(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!0===this.allowRedraw){this.body.emitter.emit("initRedraw"),this.redrawRequested=!1;const e={drawExternalLabels:null};0!==this.canvas.frame.canvas.width&&0!==this.canvas.frame.canvas.height||this.canvas.setSize(),this.canvas.setTransform();const i=this.canvas.getContext(),o=this.canvas.frame.canvas.clientWidth,n=this.canvas.frame.canvas.clientHeight;if(i.clearRect(0,0,o,n),0===this.canvas.frame.clientWidth)return;if(i.save(),i.translate(this.body.view.translation.x,this.body.view.translation.y),i.scale(this.body.view.scale,this.body.view.scale),i.beginPath(),this.body.emitter.emit("beforeDrawing",i),i.closePath(),!1===t&&(!1===this.dragging||!0===this.dragging&&!1===this.options.hideEdgesOnDrag)&&(!1===this.zooming||!0===this.zooming&&!1===this.options.hideEdgesOnZoom)&&this._drawEdges(i),!1===this.dragging||!0===this.dragging&&!1===this.options.hideNodesOnDrag){const{drawExternalLabels:o}=this._drawNodes(i,t);e.drawExternalLabels=o}!1===t&&(!1===this.dragging||!0===this.dragging&&!1===this.options.hideEdgesOnDrag)&&(!1===this.zooming||!0===this.zooming&&!1===this.options.hideEdgesOnZoom)&&this._drawArrows(i),null!=e.drawExternalLabels&&e.drawExternalLabels(),!1===t&&this._drawSelectionBox(i),i.beginPath(),this.body.emitter.emit("afterDrawing",i),i.closePath(),i.restore(),!0===t&&i.clearRect(0,0,o,n)}}_resizeNodes(){this.canvas.setTransform();const t=this.canvas.getContext();t.save(),t.translate(this.body.view.translation.x,this.body.view.translation.y),t.scale(this.body.view.scale,this.body.view.scale);const e=this.body.nodes;let i;for(const o in e)Object.prototype.hasOwnProperty.call(e,o)&&(i=e[o],i.resize(t),i.updateBoundingBox(t,i.selected));t.restore()}_drawNodes(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const i=this.body.nodes,o=this.body.nodeIndices;let n;const s=[],r=[],a=this.canvas.DOMtoCanvas({x:-20,y:-20}),h=this.canvas.DOMtoCanvas({x:this.canvas.frame.canvas.clientWidth+20,y:this.canvas.frame.canvas.clientHeight+20}),d={top:a.y,left:a.x,bottom:h.y,right:h.x},l=[];for(let a=0;a<o.length;a++)if(n=i[o[a]],n.hover)r.push(o[a]);else if(n.isSelected())s.push(o[a]);else if(!0===e){const e=n.draw(t);null!=e.drawExternalLabel&&l.push(e.drawExternalLabel)}else if(!0===n.isBoundingBoxOverlappingWith(d)){const e=n.draw(t);null!=e.drawExternalLabel&&l.push(e.drawExternalLabel)}else n.updateBoundingBox(t,n.selected);let c;const u=s.length,p=r.length;for(c=0;c<u;c++){n=i[s[c]];const e=n.draw(t);null!=e.drawExternalLabel&&l.push(e.drawExternalLabel)}for(c=0;c<p;c++){n=i[r[c]];const e=n.draw(t);null!=e.drawExternalLabel&&l.push(e.drawExternalLabel)}return{drawExternalLabels:()=>{for(const t of l)t()}}}_drawEdges(t){const e=this.body.edges,i=this.body.edgeIndices;for(let o=0;o<i.length;o++){const n=e[i[o]];!0===n.connected&&n.draw(t)}}_drawArrows(t){const e=this.body.edges,i=this.body.edgeIndices;for(let o=0;o<i.length;o++){const n=e[i[o]];!0===n.connected&&n.drawArrows(t)}}_drawSelectionBox(t){if(this.body.selectionBox.show){t.beginPath();const e=this.body.selectionBox.position.end.x-this.body.selectionBox.position.start.x,i=this.body.selectionBox.position.end.y-this.body.selectionBox.position.start.y;t.rect(this.body.selectionBox.position.start.x,this.body.selectionBox.position.start.y,e,i),t.fillStyle="rgba(151, 194, 252, 0.2)",t.fillRect(this.body.selectionBox.position.start.x,this.body.selectionBox.position.start.y,e,i),t.strokeStyle="rgba(151, 194, 252, 1)",t.stroke()}else t.closePath()}}function bx(){return cx?lx:(cx=1,$p(),lx=kt().setInterval)}var wx=i(px?ux:(px=1,ux=bx()));function _x(t,e){e.inputHandler=function(t){t.isFirst&&e(t)},t.on("hammer.input",e.inputHandler)}function xx(t,e){return e.inputHandler=function(t){t.isFinal&&e(t)},t.on("hammer.input",e.inputHandler)}class Ex{constructor(t){this.body=t,this.pixelRatio=1,this.cameraState={},this.initialized=!1,this.canvasViewCenter={},this._cleanupCallbacks=[],this.options={},this.defaultOptions={autoResize:!0,height:"100%",width:"100%"},Ki(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){var t;this.body.emitter.once("resize",t=>{0!==t.width&&(this.body.view.translation.x=.5*t.width),0!==t.height&&(this.body.view.translation.y=.5*t.height)}),this.body.emitter.on("setSize",oo(t=this.setSize).call(t,this)),this.body.emitter.on("destroy",()=>{this.hammerFrame.destroy(),this.hammer.destroy(),this._cleanUp()})}setOptions(t){if(void 0!==t){_s(["width","height","autoResize"],this.options,t)}if(this._cleanUp(),!0===this.options.autoResize){var e;if(window.ResizeObserver){const t=new ResizeObserver(()=>{!0===this.setSize()&&this.body.emitter.emit("_requestRedraw")}),{frame:e}=this;t.observe(e),this._cleanupCallbacks.push(()=>{t.unobserve(e)})}else{const t=wx(()=>{!0===this.setSize()&&this.body.emitter.emit("_requestRedraw")},1e3);this._cleanupCallbacks.push(()=>{clearInterval(t)})}const t=oo(e=this._onResize).call(e,this);window.addEventListener("resize",t),this._cleanupCallbacks.push(()=>{window.removeEventListener("resize",t)})}}_cleanUp(){var t,e,i;Qh(t=fx(e=Th(i=this._cleanupCallbacks).call(i,0)).call(e)).call(t,t=>{try{t()}catch(t){console.error(t)}})}_onResize(){this.setSize(),this.body.emitter.emit("_redraw")}_getCameraState(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.pixelRatio;!0===this.initialized&&(this.cameraState.previousWidth=this.frame.canvas.width/t,this.cameraState.previousHeight=this.frame.canvas.height/t,this.cameraState.scale=this.body.view.scale,this.cameraState.position=this.DOMtoCanvas({x:.5*this.frame.canvas.width/t,y:.5*this.frame.canvas.height/t}))}_setCameraState(){if(void 0!==this.cameraState.scale&&0!==this.frame.canvas.clientWidth&&0!==this.frame.canvas.clientHeight&&0!==this.pixelRatio&&this.cameraState.previousWidth>0&&this.cameraState.previousHeight>0){const t=this.frame.canvas.width/this.pixelRatio/this.cameraState.previousWidth,e=this.frame.canvas.height/this.pixelRatio/this.cameraState.previousHeight;let i=this.cameraState.scale;1!=t&&1!=e?i=.5*this.cameraState.scale*(t+e):1!=t?i=this.cameraState.scale*t:1!=e&&(i=this.cameraState.scale*e),this.body.view.scale=i;const o=this.DOMtoCanvas({x:.5*this.frame.canvas.clientWidth,y:.5*this.frame.canvas.clientHeight}),n={x:o.x-this.cameraState.position.x,y:o.y-this.cameraState.position.y};this.body.view.translation.x+=n.x*this.body.view.scale,this.body.view.translation.y+=n.y*this.body.view.scale}}_prepareValue(t){if("number"==typeof t)return t+"px";if("string"==typeof t){if(-1!==Zr(t).call(t,"%")||-1!==Zr(t).call(t,"px"))return t;if(-1===Zr(t).call(t,"%"))return t+"px"}throw new Error("Could not use the value supplied for width or height:"+t)}_create(){for(;this.body.container.hasChildNodes();)this.body.container.removeChild(this.body.container.firstChild);if(this.frame=document.createElement("div"),this.frame.className="vis-network",this.frame.style.position="relative",this.frame.style.overflow="hidden",this.frame.tabIndex=0,this.frame.canvas=document.createElement("canvas"),this.frame.canvas.style.position="relative",this.frame.appendChild(this.frame.canvas),this.frame.canvas.getContext)this._setPixelRatio(),this.setTransform();else{const t=document.createElement("DIV");t.style.color="red",t.style.fontWeight="bold",t.style.padding="10px",t.innerText="Error: your browser does not support HTML canvas",this.frame.canvas.appendChild(t)}this.body.container.appendChild(this.frame),this.body.view.scale=1,this.body.view.translation={x:.5*this.frame.canvas.clientWidth,y:.5*this.frame.canvas.clientHeight},this._bindHammer()}_bindHammer(){void 0!==this.hammer&&this.hammer.destroy(),this.drag={},this.pinch={},this.hammer=new ir(this.frame.canvas),this.hammer.get("pinch").set({enable:!0}),this.hammer.get("pan").set({threshold:5,direction:ir.DIRECTION_ALL}),_x(this.hammer,t=>{this.body.eventListeners.onTouch(t)}),this.hammer.on("tap",t=>{this.body.eventListeners.onTap(t)}),this.hammer.on("doubletap",t=>{this.body.eventListeners.onDoubleTap(t)}),this.hammer.on("press",t=>{this.body.eventListeners.onHold(t)}),this.hammer.on("panstart",t=>{this.body.eventListeners.onDragStart(t)}),this.hammer.on("panmove",t=>{this.body.eventListeners.onDrag(t)}),this.hammer.on("panend",t=>{this.body.eventListeners.onDragEnd(t)}),this.hammer.on("pinch",t=>{this.body.eventListeners.onPinch(t)}),this.frame.canvas.addEventListener("wheel",t=>{this.body.eventListeners.onMouseWheel(t)}),this.frame.canvas.addEventListener("mousemove",t=>{this.body.eventListeners.onMouseMove(t)}),this.frame.canvas.addEventListener("contextmenu",t=>{this.body.eventListeners.onContext(t)}),this.hammerFrame=new ir(this.frame),xx(this.hammerFrame,t=>{this.body.eventListeners.onRelease(t)})}setSize(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.width,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.options.height;t=this._prepareValue(t),e=this._prepareValue(e);let i=!1;const o=this.frame.canvas.width,n=this.frame.canvas.height,s=this.pixelRatio;if(this._setPixelRatio(),t!=this.options.width||e!=this.options.height||this.frame.style.width!=t||this.frame.style.height!=e)this._getCameraState(s),this.frame.style.width=t,this.frame.style.height=e,this.frame.canvas.style.width="100%",this.frame.canvas.style.height="100%",this.frame.canvas.width=Math.round(this.frame.canvas.clientWidth*this.pixelRatio),this.frame.canvas.height=Math.round(this.frame.canvas.clientHeight*this.pixelRatio),this.options.width=t,this.options.height=e,this.canvasViewCenter={x:.5*this.frame.clientWidth,y:.5*this.frame.clientHeight},i=!0;else{const t=Math.round(this.frame.canvas.clientWidth*this.pixelRatio),e=Math.round(this.frame.canvas.clientHeight*this.pixelRatio);this.frame.canvas.width===t&&this.frame.canvas.height===e||this._getCameraState(s),this.frame.canvas.width!==t&&(this.frame.canvas.width=t,i=!0),this.frame.canvas.height!==e&&(this.frame.canvas.height=e,i=!0)}return!0===i&&(this.body.emitter.emit("resize",{width:Math.round(this.frame.canvas.width/this.pixelRatio),height:Math.round(this.frame.canvas.height/this.pixelRatio),oldWidth:Math.round(o/this.pixelRatio),oldHeight:Math.round(n/this.pixelRatio)}),this._setCameraState()),this.initialized=!0,i}getContext(){return this.frame.canvas.getContext("2d")}_determinePixelRatio(){const t=this.getContext();if(void 0===t)throw new Error("Could not get canvax context");let e=1;"undefined"!=typeof window&&(e=window.devicePixelRatio||1);return e/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1)}_setPixelRatio(){this.pixelRatio=this._determinePixelRatio()}setTransform(){const t=this.getContext();if(void 0===t)throw new Error("Could not get canvax context");t.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0)}_XconvertDOMtoCanvas(t){return(t-this.body.view.translation.x)/this.body.view.scale}_XconvertCanvasToDOM(t){return t*this.body.view.scale+this.body.view.translation.x}_YconvertDOMtoCanvas(t){return(t-this.body.view.translation.y)/this.body.view.scale}_YconvertCanvasToDOM(t){return t*this.body.view.scale+this.body.view.translation.y}canvasToDOM(t){return{x:this._XconvertCanvasToDOM(t.x),y:this._YconvertCanvasToDOM(t.y)}}DOMtoCanvas(t){return{x:this._XconvertDOMtoCanvas(t.x),y:this._YconvertDOMtoCanvas(t.y)}}}class Ox{constructor(t,e){var i,o;this.body=t,this.canvas=e,this.animationSpeed=1/this.renderRefreshRate,this.animationEasingFunction="easeInOutQuint",this.easingTime=0,this.sourceScale=0,this.targetScale=0,this.sourceTranslation=0,this.targetTranslation=0,this.lockedOnNodeId=void 0,this.lockedOnNodeOffset=void 0,this.touchTime=0,this.viewFunction=void 0,this.body.emitter.on("fit",oo(i=this.fit).call(i,this)),this.body.emitter.on("animationFinished",()=>{this.body.emitter.emit("_stopRendering")}),this.body.emitter.on("unlockNode",oo(o=this.releaseNode).call(o,this))}setOptions(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.options=t}fit(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t=function(t,e){const i=Ki({nodes:e,minZoomLevel:Number.MIN_VALUE,maxZoomLevel:1},null!=t?t:{});if(!Vh(i.nodes))throw new TypeError("Nodes has to be an array of ids.");if(0===i.nodes.length&&(i.nodes=e),!("number"==typeof i.minZoomLevel&&i.minZoomLevel>0))throw new TypeError("Min zoom level has to be a number higher than zero.");if(!("number"==typeof i.maxZoomLevel&&i.minZoomLevel<=i.maxZoomLevel))throw new TypeError("Max zoom level has to be a number higher than min zoom level.");return i}(t,this.body.nodeIndices);const i=this.canvas.frame.canvas.clientWidth,o=this.canvas.frame.canvas.clientHeight;let n,s;if(0===i||0===o)s=1,n=gx.getRange(this.body.nodes,t.nodes);else if(!0===e){let e=0;for(const t in this.body.nodes)if(Object.prototype.hasOwnProperty.call(this.body.nodes,t)){!0===this.body.nodes[t].predefinedPosition&&(e+=1)}if(e>.5*this.body.nodeIndices.length)return void this.fit(t,!1);n=gx.getRange(this.body.nodes,t.nodes);s=12.662/(this.body.nodeIndices.length+7.4147)+.0964822;s*=Math.min(i/600,o/600)}else{this.body.emitter.emit("_resizeNodes"),n=gx.getRange(this.body.nodes,t.nodes);const e=i/(1.1*Math.abs(n.maxX-n.minX)),r=o/(1.1*Math.abs(n.maxY-n.minY));s=e<=r?e:r}s>t.maxZoomLevel?s=t.maxZoomLevel:s<t.minZoomLevel&&(s=t.minZoomLevel);const r={position:gx.findCenter(n),scale:s,animation:t.animation};this.moveTo(r)}focus(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==this.body.nodes[t]){const i={x:this.body.nodes[t].x,y:this.body.nodes[t].y};e.position=i,e.lockedOnNode=t,this.moveTo(e)}else console.error("Node: "+t+" cannot be found.")}moveTo(t){if(void 0!==t){if(null!=t.offset){if(null!=t.offset.x){if(t.offset.x=+t.offset.x,!fp(t.offset.x))throw new TypeError('The option "offset.x" has to be a finite number.')}else t.offset.x=0;if(null!=t.offset.y){if(t.offset.y=+t.offset.y,!fp(t.offset.y))throw new TypeError('The option "offset.y" has to be a finite number.')}else t.offset.x=0}else t.offset={x:0,y:0};if(null!=t.position){if(null!=t.position.x){if(t.position.x=+t.position.x,!fp(t.position.x))throw new TypeError('The option "position.x" has to be a finite number.')}else t.position.x=0;if(null!=t.position.y){if(t.position.y=+t.position.y,!fp(t.position.y))throw new TypeError('The option "position.y" has to be a finite number.')}else t.position.x=0}else t.position=this.getViewPosition();if(null!=t.scale){if(t.scale=+t.scale,!(t.scale>0))throw new TypeError('The option "scale" has to be a number greater than zero.')}else t.scale=this.body.view.scale;void 0===t.animation&&(t.animation={duration:0}),!1===t.animation&&(t.animation={duration:0}),!0===t.animation&&(t.animation={}),void 0===t.animation.duration&&(t.animation.duration=1e3),void 0===t.animation.easingFunction&&(t.animation.easingFunction="easeInOutQuad"),this.animateView(t)}else t={}}animateView(t){if(void 0===t)return;this.animationEasingFunction=t.animation.easingFunction,this.releaseNode(),!0===t.locked&&(this.lockedOnNodeId=t.lockedOnNode,this.lockedOnNodeOffset=t.offset),0!=this.easingTime&&this._transitionRedraw(!0),this.sourceScale=this.body.view.scale,this.sourceTranslation=this.body.view.translation,this.targetScale=t.scale,this.body.view.scale=this.targetScale;const e=this.canvas.DOMtoCanvas({x:.5*this.canvas.frame.canvas.clientWidth,y:.5*this.canvas.frame.canvas.clientHeight}),i=e.x-t.position.x,o=e.y-t.position.y;var n,s;(this.targetTranslation={x:this.sourceTranslation.x+i*this.targetScale+t.offset.x,y:this.sourceTranslation.y+o*this.targetScale+t.offset.y},0===t.animation.duration)?null!=this.lockedOnNodeId?(this.viewFunction=oo(n=this._lockedRedraw).call(n,this),this.body.emitter.on("initRedraw",this.viewFunction)):(this.body.view.scale=this.targetScale,this.body.view.translation=this.targetTranslation,this.body.emitter.emit("_requestRedraw")):(this.animationSpeed=1/(60*t.animation.duration*.001)||1/60,this.animationEasingFunction=t.animation.easingFunction,this.viewFunction=oo(s=this._transitionRedraw).call(s,this),this.body.emitter.on("initRedraw",this.viewFunction),this.body.emitter.emit("_startRendering"))}_lockedRedraw(){const t=this.body.nodes[this.lockedOnNodeId].x,e=this.body.nodes[this.lockedOnNodeId].y,i=this.canvas.DOMtoCanvas({x:.5*this.canvas.frame.canvas.clientWidth,y:.5*this.canvas.frame.canvas.clientHeight}),o=i.x-t,n=i.y-e,s=this.body.view.translation,r={x:s.x+o*this.body.view.scale+this.lockedOnNodeOffset.x,y:s.y+n*this.body.view.scale+this.lockedOnNodeOffset.y};this.body.view.translation=r}releaseNode(){void 0!==this.lockedOnNodeId&&void 0!==this.viewFunction&&(this.body.emitter.off("initRedraw",this.viewFunction),this.lockedOnNodeId=void 0,this.lockedOnNodeOffset=void 0)}_transitionRedraw(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.easingTime+=this.animationSpeed,this.easingTime=!0===t?1:this.easingTime;const e=Us[this.animationEasingFunction](this.easingTime);if(this.body.view.scale=this.sourceScale+(this.targetScale-this.sourceScale)*e,this.body.view.translation={x:this.sourceTranslation.x+(this.targetTranslation.x-this.sourceTranslation.x)*e,y:this.sourceTranslation.y+(this.targetTranslation.y-this.sourceTranslation.y)*e},this.easingTime>=1){var i;if(this.body.emitter.off("initRedraw",this.viewFunction),this.easingTime=0,null!=this.lockedOnNodeId)this.viewFunction=oo(i=this._lockedRedraw).call(i,this),this.body.emitter.on("initRedraw",this.viewFunction);this.body.emitter.emit("animationFinished")}}getScale(){return this.body.view.scale}getViewPosition(){return this.canvas.DOMtoCanvas({x:.5*this.canvas.frame.canvas.clientWidth,y:.5*this.canvas.frame.canvas.clientHeight})}}function Cx(t){var e,i=t&&t.preventDefault||!1,o=t&&t.container||window,n={},s={keydown:{},keyup:{}},r={};for(e=97;e<=122;e++)r[String.fromCharCode(e)]={code:e-97+65,shift:!1};for(e=65;e<=90;e++)r[String.fromCharCode(e)]={code:e,shift:!0};for(e=0;e<=9;e++)r[""+e]={code:48+e,shift:!1};for(e=1;e<=12;e++)r["F"+e]={code:111+e,shift:!1};for(e=0;e<=9;e++)r["num"+e]={code:96+e,shift:!1};r["num*"]={code:106,shift:!1},r["num+"]={code:107,shift:!1},r["num-"]={code:109,shift:!1},r["num/"]={code:111,shift:!1},r["num."]={code:110,shift:!1},r.left={code:37,shift:!1},r.up={code:38,shift:!1},r.right={code:39,shift:!1},r.down={code:40,shift:!1},r.space={code:32,shift:!1},r.enter={code:13,shift:!1},r.shift={code:16,shift:void 0},r.esc={code:27,shift:!1},r.backspace={code:8,shift:!1},r.tab={code:9,shift:!1},r.ctrl={code:17,shift:!1},r.alt={code:18,shift:!1},r.delete={code:46,shift:!1},r.pageup={code:33,shift:!1},r.pagedown={code:34,shift:!1},r["="]={code:187,shift:!1},r["-"]={code:189,shift:!1},r["]"]={code:221,shift:!1},r["["]={code:219,shift:!1};var a=function(t){d(t,"keydown")},h=function(t){d(t,"keyup")},d=function(t,e){if(void 0!==s[e][t.keyCode]){for(var o=s[e][t.keyCode],n=0;n<o.length;n++)(void 0===o[n].shift||1==o[n].shift&&1==t.shiftKey||0==o[n].shift&&0==t.shiftKey)&&o[n].fn(t);1==i&&t.preventDefault()}};return n.bind=function(t,e,i){if(void 0===i&&(i="keydown"),void 0===r[t])throw new Error("unsupported key: "+t);void 0===s[i][r[t].code]&&(s[i][r[t].code]=[]),s[i][r[t].code].push({fn:e,shift:r[t].shift})},n.bindAll=function(t,e){for(var i in void 0===e&&(e="keydown"),r)r.hasOwnProperty(i)&&n.bind(i,t,e)},n.getKey=function(t){for(var e in r)if(r.hasOwnProperty(e)){if(1==t.shiftKey&&1==r[e].shift&&t.keyCode==r[e].code)return e;if(0==t.shiftKey&&0==r[e].shift&&t.keyCode==r[e].code)return e;if(t.keyCode==r[e].code&&"shift"==e)return e}return"unknown key, currently not supported"},n.unbind=function(t,e,i){if(void 0===i&&(i="keydown"),void 0===r[t])throw new Error("unsupported key: "+t);if(void 0!==e){var o=[],n=s[i][r[t].code];if(void 0!==n)for(var a=0;a<n.length;a++)n[a].fn==e&&n[a].shift==r[t].shift||o.push(s[i][r[t].code][a]);s[i][r[t].code]=o}else s[i][r[t].code]=[]},n.reset=function(){s={keydown:{},keyup:{}}},n.destroy=function(){s={keydown:{},keyup:{}},o.removeEventListener("keydown",a,!0),o.removeEventListener("keyup",h,!0)},o.addEventListener("keydown",a,!0),o.addEventListener("keyup",h,!0),n}var kx=Object.freeze({__proto__:null,default:Cx});class Sx{constructor(t,e){this.body=t,this.canvas=e,this.iconsCreated=!1,this.navigationHammers=[],this.boundFunctions={},this.touchTime=0,this.activated=!1,this.body.emitter.on("activate",()=>{this.activated=!0,this.configureKeyboardBindings()}),this.body.emitter.on("deactivate",()=>{this.activated=!1,this.configureKeyboardBindings()}),this.body.emitter.on("destroy",()=>{void 0!==this.keycharm&&this.keycharm.destroy()}),this.options={}}setOptions(t){void 0!==t&&(this.options=t,this.create())}create(){!0===this.options.navigationButtons?!1===this.iconsCreated&&this.loadNavigationElements():!0===this.iconsCreated&&this.cleanNavigation(),this.configureKeyboardBindings()}cleanNavigation(){if(0!=this.navigationHammers.length){for(let t=0;t<this.navigationHammers.length;t++)this.navigationHammers[t].destroy();this.navigationHammers=[]}this.navigationDOM&&this.navigationDOM.wrapper&&this.navigationDOM.wrapper.parentNode&&this.navigationDOM.wrapper.parentNode.removeChild(this.navigationDOM.wrapper),this.iconsCreated=!1}loadNavigationElements(){this.cleanNavigation(),this.navigationDOM={};const t=["up","down","left","right","zoomIn","zoomOut","zoomExtends"],e=["_moveUp","_moveDown","_moveLeft","_moveRight","_zoomIn","_zoomOut","_fit"];this.navigationDOM.wrapper=document.createElement("div"),this.navigationDOM.wrapper.className="vis-navigation",this.canvas.frame.appendChild(this.navigationDOM.wrapper);for(let n=0;n<t.length;n++){this.navigationDOM[t[n]]=document.createElement("div"),this.navigationDOM[t[n]].className="vis-button vis-"+t[n],this.navigationDOM.wrapper.appendChild(this.navigationDOM[t[n]]);const s=new ir(this.navigationDOM[t[n]]);var i,o;if("_fit"===e[n])_x(s,oo(i=this._fit).call(i,this));else _x(s,oo(o=this.bindToRedraw).call(o,this,e[n]));this.navigationHammers.push(s)}const n=new ir(this.canvas.frame);xx(n,()=>{this._stopMovement()}),this.navigationHammers.push(n),this.iconsCreated=!0}bindToRedraw(t){var e;void 0===this.boundFunctions[t]&&(this.boundFunctions[t]=oo(e=this[t]).call(e,this),this.body.emitter.on("initRedraw",this.boundFunctions[t]),this.body.emitter.emit("_startRendering"))}unbindFromRedraw(t){void 0!==this.boundFunctions[t]&&(this.body.emitter.off("initRedraw",this.boundFunctions[t]),this.body.emitter.emit("_stopRendering"),delete this.boundFunctions[t])}_fit(){(new Date).valueOf()-this.touchTime>700&&(this.body.emitter.emit("fit",{duration:700}),this.touchTime=(new Date).valueOf())}_stopMovement(){for(const t in this.boundFunctions)Object.prototype.hasOwnProperty.call(this.boundFunctions,t)&&(this.body.emitter.off("initRedraw",this.boundFunctions[t]),this.body.emitter.emit("_stopRendering"));this.boundFunctions={}}_moveUp(){this.body.view.translation.y+=this.options.keyboard.speed.y}_moveDown(){this.body.view.translation.y-=this.options.keyboard.speed.y}_moveLeft(){this.body.view.translation.x+=this.options.keyboard.speed.x}_moveRight(){this.body.view.translation.x-=this.options.keyboard.speed.x}_zoomIn(){const t=this.body.view.scale,e=this.body.view.scale*(1+this.options.keyboard.speed.zoom),i=this.body.view.translation,o=e/t,n=(1-o)*this.canvas.canvasViewCenter.x+i.x*o,s=(1-o)*this.canvas.canvasViewCenter.y+i.y*o;this.body.view.scale=e,this.body.view.translation={x:n,y:s},this.body.emitter.emit("zoom",{direction:"+",scale:this.body.view.scale,pointer:null})}_zoomOut(){const t=this.body.view.scale,e=this.body.view.scale/(1+this.options.keyboard.speed.zoom),i=this.body.view.translation,o=e/t,n=(1-o)*this.canvas.canvasViewCenter.x+i.x*o,s=(1-o)*this.canvas.canvasViewCenter.y+i.y*o;this.body.view.scale=e,this.body.view.translation={x:n,y:s},this.body.emitter.emit("zoom",{direction:"-",scale:this.body.view.scale,pointer:null})}configureKeyboardBindings(){var t,e,i,o,n,s,r,a,h,d,l,c,u,p,f,g,m,v,y,b,w,_,x,E;(void 0!==this.keycharm&&this.keycharm.destroy(),!0===this.options.keyboard.enabled)&&(!0===this.options.keyboard.bindToWindow?this.keycharm=Cx({container:window,preventDefault:!0}):this.keycharm=Cx({container:this.canvas.frame,preventDefault:!0}),this.keycharm.reset(),!0===this.activated&&(oo(t=this.keycharm).call(t,"up",()=>{this.bindToRedraw("_moveUp")},"keydown"),oo(e=this.keycharm).call(e,"down",()=>{this.bindToRedraw("_moveDown")},"keydown"),oo(i=this.keycharm).call(i,"left",()=>{this.bindToRedraw("_moveLeft")},"keydown"),oo(o=this.keycharm).call(o,"right",()=>{this.bindToRedraw("_moveRight")},"keydown"),oo(n=this.keycharm).call(n,"=",()=>{this.bindToRedraw("_zoomIn")},"keydown"),oo(s=this.keycharm).call(s,"num+",()=>{this.bindToRedraw("_zoomIn")},"keydown"),oo(r=this.keycharm).call(r,"num-",()=>{this.bindToRedraw("_zoomOut")},"keydown"),oo(a=this.keycharm).call(a,"-",()=>{this.bindToRedraw("_zoomOut")},"keydown"),oo(h=this.keycharm).call(h,"[",()=>{this.bindToRedraw("_zoomOut")},"keydown"),oo(d=this.keycharm).call(d,"]",()=>{this.bindToRedraw("_zoomIn")},"keydown"),oo(l=this.keycharm).call(l,"pageup",()=>{this.bindToRedraw("_zoomIn")},"keydown"),oo(c=this.keycharm).call(c,"pagedown",()=>{this.bindToRedraw("_zoomOut")},"keydown"),oo(u=this.keycharm).call(u,"up",()=>{this.unbindFromRedraw("_moveUp")},"keyup"),oo(p=this.keycharm).call(p,"down",()=>{this.unbindFromRedraw("_moveDown")},"keyup"),oo(f=this.keycharm).call(f,"left",()=>{this.unbindFromRedraw("_moveLeft")},"keyup"),oo(g=this.keycharm).call(g,"right",()=>{this.unbindFromRedraw("_moveRight")},"keyup"),oo(m=this.keycharm).call(m,"=",()=>{this.unbindFromRedraw("_zoomIn")},"keyup"),oo(v=this.keycharm).call(v,"num+",()=>{this.unbindFromRedraw("_zoomIn")},"keyup"),oo(y=this.keycharm).call(y,"num-",()=>{this.unbindFromRedraw("_zoomOut")},"keyup"),oo(b=this.keycharm).call(b,"-",()=>{this.unbindFromRedraw("_zoomOut")},"keyup"),oo(w=this.keycharm).call(w,"[",()=>{this.unbindFromRedraw("_zoomOut")},"keyup"),oo(_=this.keycharm).call(_,"]",()=>{this.unbindFromRedraw("_zoomIn")},"keyup"),oo(x=this.keycharm).call(x,"pageup",()=>{this.unbindFromRedraw("_zoomIn")},"keyup"),oo(E=this.keycharm).call(E,"pagedown",()=>{this.unbindFromRedraw("_zoomOut")},"keyup")))}}class Tx{constructor(t,e,i){var o,n,s,r,a,h,d,l,c,u,p,f,g;this.body=t,this.canvas=e,this.selectionHandler=i,this.navigationHandler=new Sx(t,e),this.body.eventListeners.onTap=oo(o=this.onTap).call(o,this),this.body.eventListeners.onTouch=oo(n=this.onTouch).call(n,this),this.body.eventListeners.onDoubleTap=oo(s=this.onDoubleTap).call(s,this),this.body.eventListeners.onHold=oo(r=this.onHold).call(r,this),this.body.eventListeners.onDragStart=oo(a=this.onDragStart).call(a,this),this.body.eventListeners.onDrag=oo(h=this.onDrag).call(h,this),this.body.eventListeners.onDragEnd=oo(d=this.onDragEnd).call(d,this),this.body.eventListeners.onMouseWheel=oo(l=this.onMouseWheel).call(l,this),this.body.eventListeners.onPinch=oo(c=this.onPinch).call(c,this),this.body.eventListeners.onMouseMove=oo(u=this.onMouseMove).call(u,this),this.body.eventListeners.onRelease=oo(p=this.onRelease).call(p,this),this.body.eventListeners.onContext=oo(f=this.onContext).call(f,this),this.touchTime=0,this.drag={},this.pinch={},this.popup=void 0,this.popupObj=void 0,this.popupTimer=void 0,this.body.functions.getPointer=oo(g=this.getPointer).call(g,this),this.options={},this.defaultOptions={dragNodes:!0,dragView:!0,hover:!1,keyboard:{enabled:!1,speed:{x:10,y:10,zoom:.02},bindToWindow:!0,autoFocus:!0},navigationButtons:!1,tooltipDelay:300,zoomView:!0,zoomSpeed:1},Ki(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){this.body.emitter.on("destroy",()=>{clearTimeout(this.popupTimer),delete this.body.functions.getPointer})}setOptions(t){if(void 0!==t){xs(["hideEdgesOnDrag","hideEdgesOnZoom","hideNodesOnDrag","keyboard","multiselect","selectable","selectConnectedEdges"],this.options,t),Vs(this.options,t,"keyboard"),t.tooltip&&(Ki(this.options.tooltip,t.tooltip),t.tooltip.color&&(this.options.tooltip.color=Bs(t.tooltip.color)))}this.navigationHandler.setOptions(this.options)}getPointer(t){return{x:t.x-ks(this.canvas.frame.canvas),y:t.y-Ss(this.canvas.frame.canvas)}}onTouch(t){(new Date).valueOf()-this.touchTime>50&&(this.drag.pointer=this.getPointer(t.center),this.drag.pinched=!1,this.pinch.scale=this.body.view.scale,this.touchTime=(new Date).valueOf())}onTap(t){const e=this.getPointer(t.center),i=this.selectionHandler.options.multiselect&&(t.changedPointers[0].ctrlKey||t.changedPointers[0].metaKey);this.checkSelectionChanges(e,i),this.selectionHandler.commitAndEmit(e,t),this.selectionHandler.generateClickEvent("click",t,e)}onDoubleTap(t){const e=this.getPointer(t.center);this.selectionHandler.generateClickEvent("doubleClick",t,e)}onHold(t){const e=this.getPointer(t.center),i=this.selectionHandler.options.multiselect;this.checkSelectionChanges(e,i),this.selectionHandler.commitAndEmit(e,t),this.selectionHandler.generateClickEvent("click",t,e),this.selectionHandler.generateClickEvent("hold",t,e)}onRelease(t){if((new Date).valueOf()-this.touchTime>10){const e=this.getPointer(t.center);this.selectionHandler.generateClickEvent("release",t,e),this.touchTime=(new Date).valueOf()}}onContext(t){const e=this.getPointer({x:t.clientX,y:t.clientY});this.selectionHandler.generateClickEvent("oncontext",t,e)}checkSelectionChanges(t){!0===(arguments.length>1&&void 0!==arguments[1]&&arguments[1])?this.selectionHandler.selectAdditionalOnPoint(t):this.selectionHandler.selectOnPoint(t)}_determineDifference(t,e){const i=function(t,e){const i=[];for(let o=0;o<t.length;o++){const n=t[o];-1===Zr(e).call(e,n)&&i.push(n)}return i};return{nodes:i(t.nodes,e.nodes),edges:i(t.edges,e.edges)}}onDragStart(t){if(this.drag.dragging)return;void 0===this.drag.pointer&&this.onTouch(t);const e=this.selectionHandler.getNodeAt(this.drag.pointer);if(this.drag.dragging=!0,this.drag.selection=[],this.drag.translation=Ki({},this.body.view.translation),this.drag.nodeId=void 0,t.srcEvent.shiftKey){this.body.selectionBox.show=!0;const e=this.getPointer(t.center);this.body.selectionBox.position.start={x:this.canvas._XconvertDOMtoCanvas(e.x),y:this.canvas._YconvertDOMtoCanvas(e.y)},this.body.selectionBox.position.end={x:this.canvas._XconvertDOMtoCanvas(e.x),y:this.canvas._YconvertDOMtoCanvas(e.y)}}else if(void 0!==e&&!0===this.options.dragNodes){this.drag.nodeId=e.id,!1===e.isSelected()&&this.selectionHandler.setSelection({nodes:[e.id]}),this.selectionHandler.generateClickEvent("dragStart",t,this.drag.pointer);for(const t of this.selectionHandler.getSelectedNodes()){const e={id:t.id,node:t,x:t.x,y:t.y,xFixed:t.options.fixed.x,yFixed:t.options.fixed.y};t.options.fixed.x=!0,t.options.fixed.y=!0,this.drag.selection.push(e)}}else this.selectionHandler.generateClickEvent("dragStart",t,this.drag.pointer,void 0,!0)}onDrag(t){if(!0===this.drag.pinched)return;this.body.emitter.emit("unlockNode");const e=this.getPointer(t.center),i=this.drag.selection;if(i&&i.length&&!0===this.options.dragNodes){this.selectionHandler.generateClickEvent("dragging",t,e);const o=e.x-this.drag.pointer.x,n=e.y-this.drag.pointer.y;Qh(i).call(i,t=>{const e=t.node;!1===t.xFixed&&(e.x=this.canvas._XconvertDOMtoCanvas(this.canvas._XconvertCanvasToDOM(t.x)+o)),!1===t.yFixed&&(e.y=this.canvas._YconvertDOMtoCanvas(this.canvas._YconvertCanvasToDOM(t.y)+n))}),this.body.emitter.emit("startSimulation")}else{if(t.srcEvent.shiftKey){if(this.selectionHandler.generateClickEvent("dragging",t,e,void 0,!0),void 0===this.drag.pointer)return void this.onDragStart(t);this.body.selectionBox.position.end={x:this.canvas._XconvertDOMtoCanvas(e.x),y:this.canvas._YconvertDOMtoCanvas(e.y)},this.body.emitter.emit("_requestRedraw")}if(!0===this.options.dragView&&!t.srcEvent.shiftKey){if(this.selectionHandler.generateClickEvent("dragging",t,e,void 0,!0),void 0===this.drag.pointer)return void this.onDragStart(t);const i=e.x-this.drag.pointer.x,o=e.y-this.drag.pointer.y;this.body.view.translation={x:this.drag.translation.x+i,y:this.drag.translation.y+o},this.body.emitter.emit("_requestRedraw")}}}onDragEnd(t){if(this.drag.dragging=!1,this.body.selectionBox.show){var e;this.body.selectionBox.show=!1;const i=this.body.selectionBox.position,o={minX:Math.min(i.start.x,i.end.x),minY:Math.min(i.start.y,i.end.y),maxX:Math.max(i.start.x,i.end.x),maxY:Math.max(i.start.y,i.end.y)},n=iy(e=this.body.nodeIndices).call(e,t=>{const e=this.body.nodes[t];return e.x>=o.minX&&e.x<=o.maxX&&e.y>=o.minY&&e.y<=o.maxY});Qh(n).call(n,t=>this.selectionHandler.selectObject(this.body.nodes[t]));const s=this.getPointer(t.center);this.selectionHandler.commitAndEmit(s,t),this.selectionHandler.generateClickEvent("dragEnd",t,this.getPointer(t.center),void 0,!0),this.body.emitter.emit("_requestRedraw")}else{const e=this.drag.selection;e&&e.length?(Qh(e).call(e,function(t){t.node.options.fixed.x=t.xFixed,t.node.options.fixed.y=t.yFixed}),this.selectionHandler.generateClickEvent("dragEnd",t,this.getPointer(t.center)),this.body.emitter.emit("startSimulation")):(this.selectionHandler.generateClickEvent("dragEnd",t,this.getPointer(t.center),void 0,!0),this.body.emitter.emit("_requestRedraw"))}}onPinch(t){const e=this.getPointer(t.center);this.drag.pinched=!0,void 0===this.pinch.scale&&(this.pinch.scale=1);const i=this.pinch.scale*t.scale;this.zoom(i,e)}zoom(t,e){if(!0===this.options.zoomView){const i=this.body.view.scale;let o;t<1e-5&&(t=1e-5),t>10&&(t=10),void 0!==this.drag&&!0===this.drag.dragging&&(o=this.canvas.DOMtoCanvas(this.drag.pointer));const n=this.body.view.translation,s=t/i,r=(1-s)*e.x+n.x*s,a=(1-s)*e.y+n.y*s;if(this.body.view.scale=t,this.body.view.translation={x:r,y:a},null!=o){const t=this.canvas.canvasToDOM(o);this.drag.pointer.x=t.x,this.drag.pointer.y=t.y}this.body.emitter.emit("_requestRedraw"),i<t?this.body.emitter.emit("zoom",{direction:"+",scale:this.body.view.scale,pointer:e}):this.body.emitter.emit("zoom",{direction:"-",scale:this.body.view.scale,pointer:e})}}onMouseWheel(t){if(!0===this.options.zoomView){if(0!==t.deltaY){let e=this.body.view.scale;e*=1+(t.deltaY<0?1:-1)*(.1*this.options.zoomSpeed);const i=this.getPointer({x:t.clientX,y:t.clientY});this.zoom(e,i)}t.preventDefault()}}onMouseMove(t){const e=this.getPointer({x:t.clientX,y:t.clientY});let i=!1;void 0!==this.popup&&(!1===this.popup.hidden&&this._checkHidePopup(e),!1===this.popup.hidden&&(i=!0,this.popup.setPosition(e.x+3,e.y-5),this.popup.show())),this.options.keyboard.autoFocus&&!1===this.options.keyboard.bindToWindow&&!0===this.options.keyboard.enabled&&this.canvas.frame.focus(),!1===i&&(void 0!==this.popupTimer&&(clearInterval(this.popupTimer),this.popupTimer=void 0),this.drag.dragging||(this.popupTimer=tf(()=>this._checkShowPopup(e),this.options.tooltipDelay))),!0===this.options.hover&&this.selectionHandler.hoverObject(t,e)}_checkShowPopup(t){const e=this.canvas._XconvertDOMtoCanvas(t.x),i=this.canvas._YconvertDOMtoCanvas(t.y),o={left:e,top:i,right:e,bottom:i},n=void 0===this.popupObj?void 0:this.popupObj.id;let s=!1,r="node";if(void 0===this.popupObj){const t=this.body.nodeIndices,e=this.body.nodes;let i;const n=[];for(let r=0;r<t.length;r++)i=e[t[r]],!0===i.isOverlappingWith(o)&&(s=!0,void 0!==i.getTitle()&&n.push(t[r]));n.length>0&&(this.popupObj=e[n[n.length-1]],s=!0)}if(void 0===this.popupObj&&!1===s){const t=this.body.edgeIndices,e=this.body.edges;let i;const n=[];for(let s=0;s<t.length;s++)i=e[t[s]],!0===i.isOverlappingWith(o)&&!0===i.connected&&void 0!==i.getTitle()&&n.push(t[s]);n.length>0&&(this.popupObj=e[n[n.length-1]],r="edge")}void 0!==this.popupObj?this.popupObj.id!==n&&(void 0===this.popup&&(this.popup=new or(this.canvas.frame)),this.popup.popupTargetType=r,this.popup.popupTargetId=this.popupObj.id,this.popup.setPosition(t.x+3,t.y-5),this.popup.setText(this.popupObj.getTitle()),this.popup.show(),this.body.emitter.emit("showPopup",this.popupObj.id)):void 0!==this.popup&&(this.popup.hide(),this.body.emitter.emit("hidePopup"))}_checkHidePopup(t){const e=this.selectionHandler._pointerToPositionObject(t);let i=!1;if("node"===this.popup.popupTargetType){if(void 0!==this.body.nodes[this.popup.popupTargetId]&&(i=this.body.nodes[this.popup.popupTargetId].isOverlappingWith(e),!0===i)){const e=this.selectionHandler.getNodeAt(t);i=void 0!==e&&e.id===this.popup.popupTargetId}}else void 0===this.selectionHandler.getNodeAt(t)&&void 0!==this.body.edges[this.popup.popupTargetId]&&(i=this.body.edges[this.popup.popupTargetId].isOverlappingWith(e));!1===i&&(this.popupObj=void 0,this.popup.hide(),this.body.emitter.emit("hidePopup"))}}var Dx,Mx,Ix={};function Px(){return Mx||(Mx=1,Dx||(Dx=1,ou()("Set",function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},au()))),Ix}var Nx,Bx,zx,Fx,Ax,jx,Rx,Lx,Hx,Wx,qx,Vx,Ux,Yx,Xx,Gx,Kx,Zx,Qx,$x,Jx,tE={};function eE(){if(Bx)return Nx;Bx=1;var t=Bt(),e=TypeError;return Nx=function(i){if("object"==typeof i&&"size"in i&&"has"in i&&"add"in i&&"delete"in i&&"keys"in i)return i;throw new e(t(i)+" is not a set")},Nx}function iE(){if(Fx)return zx;Fx=1;var t=St(),e=gu(),i=t("Set"),o=i.prototype;return zx={Set:i,add:e("add",1),has:e("has",1),remove:e("delete",1),proto:o}}function oE(){if(jx)return Ax;jx=1;var t=B();return Ax=function(e,i,o){for(var n,s,r=o?e:e.iterator,a=e.next;!(n=t(a,r)).done;)if(void 0!==(s=i(n.value)))return s},Ax}function nE(){if(Lx)return Rx;Lx=1;var t=oE();return Rx=function(e,i,o){return o?t(e.keys(),i,!0):e.forEach(i)},Rx}function sE(){if(Wx)return Hx;Wx=1;var t=iE(),e=nE(),i=t.Set,o=t.add;return Hx=function(t){var n=new i;return e(t,function(t){o(n,t)}),n},Hx}function rE(){return Vx||(Vx=1,qx=function(t){return t.size}),qx}function aE(){return Yx?Ux:(Yx=1,Ux=function(t){return{iterator:t,next:t.next,done:!1}})}function hE(){if(Gx)return Xx;Gx=1;var t=zt(),e=si(),i=B(),o=li(),n=aE(),s="Invalid size",r=RangeError,a=TypeError,h=Math.max,d=function(e,i){this.set=e,this.size=h(i,0),this.has=t(e.has),this.keys=t(e.keys)};return d.prototype={getIterator:function(){return n(e(i(this.keys,this.set)))},includes:function(t){return i(this.has,this.set,t)}},Xx=function(t){e(t);var i=+t.size;if(i!=i)throw new a(s);var n=o(i);if(n<0)throw new r(s);return new d(t,n)}}function dE(){if(Zx)return Kx;Zx=1;var t=eE(),e=iE(),i=sE(),o=rE(),n=hE(),s=nE(),r=oE(),a=e.has,h=e.remove;return Kx=function(e){var d=t(this),l=n(e),c=i(d);return o(d)<=l.size?s(d,function(t){l.includes(t)&&h(c,t)}):r(l.getIterator(),function(t){a(c,t)&&h(c,t)}),c}}function lE(){return $x?Qx:($x=1,Qx=function(){return!1})}var cE,uE,pE,fE={};function gE(){if(uE)return cE;uE=1;var t=eE(),e=iE(),i=rE(),o=hE(),n=nE(),s=oE(),r=e.Set,a=e.add,h=e.has;return cE=function(e){var d=t(this),l=o(e),c=new r;return i(d)>l.size?s(l.getIterator(),function(t){h(d,t)&&a(c,t)}):n(d,function(t){l.includes(t)&&a(c,t)}),c}}var mE,vE,yE,bE={};function wE(){if(vE)return mE;vE=1;var t=eE(),e=iE().has,i=rE(),o=hE(),n=nE(),s=oE(),r=tu();return mE=function(a){var h=t(this),d=o(a);if(i(h)<=d.size)return!1!==n(h,function(t){if(d.includes(t))return!1},!0);var l=d.getIterator();return!1!==s(l,function(t){if(e(h,t))return r(l,"normal",!1)})},mE}var _E,xE,EE,OE={};function CE(){if(xE)return _E;xE=1;var t=eE(),e=rE(),i=nE(),o=hE();return _E=function(n){var s=t(this),r=o(n);return!(e(s)>r.size)&&!1!==i(s,function(t){if(!r.includes(t))return!1},!0)}}var kE,SE,TE,DE={};function ME(){if(SE)return kE;SE=1;var t=eE(),e=iE().has,i=rE(),o=hE(),n=oE(),s=tu();return kE=function(r){var a=t(this),h=o(r);if(i(a)<h.size)return!1;var d=h.getIterator();return!1!==n(d,function(t){if(!e(a,t))return s(d,"normal",!1)})},kE}var IE,PE,NE,BE,zE,FE={};function AE(){if(PE)return IE;PE=1;var t=eE(),e=iE(),i=sE(),o=hE(),n=oE(),s=e.add,r=e.has,a=e.remove;return IE=function(e){var h=t(this),d=o(e).getIterator(),l=i(h);return n(d,function(t){r(h,t)?a(l,t):s(l,t)}),l}}function jE(){return BE?NE:(BE=1,NE=function(t){try{var e=new Set,i={size:0,has:function(){return!0},keys:function(){return Object.defineProperty({},"next",{get:function(){return e.clear(),e.add(4),function(){return{done:!0}}}})}},o=e[t](i);return 1===o.size&&4===o.values().next().value}catch(t){return!1}})}var RE,LE,HE,WE,qE,VE,UE,YE,XE,GE={};function KE(){if(LE)return RE;LE=1;var t=eE(),e=iE().add,i=sE(),o=hE(),n=oE();return RE=function(s){var r=t(this),a=o(s).getIterator(),h=i(r);return n(a,function(t){e(h,t)}),h},RE}function ZE(){return qE?WE:(qE=1,ac(),Px(),function(){if(Jx)return tE;Jx=1;var t=hi(),e=dE(),i=_(),o=!lE()("difference",function(t){return 0===t.size})||i(function(){var t={size:1,has:function(){return!0},keys:function(){var t=0;return{next:function(){var i=t++>1;return e.has(1)&&e.clear(),{done:i,value:2}}}}},e=new Set([1,2,3,4]);return 3!==e.difference(t).size});t({target:"Set",proto:!0,real:!0,forced:o},{difference:e})}(),function(){if(pE)return fE;pE=1;var t=hi(),e=_(),i=gE();t({target:"Set",proto:!0,real:!0,forced:!lE()("intersection",function(t){return 2===t.size&&t.has(1)&&t.has(2)})||e(function(){return"3,2"!==String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))})},{intersection:i})}(),function(){if(yE)return bE;yE=1;var t=hi(),e=wE();t({target:"Set",proto:!0,real:!0,forced:!lE()("isDisjointFrom",function(t){return!t})},{isDisjointFrom:e})}(),function(){if(EE)return OE;EE=1;var t=hi(),e=CE();t({target:"Set",proto:!0,real:!0,forced:!lE()("isSubsetOf",function(t){return t})},{isSubsetOf:e})}(),function(){if(TE)return DE;TE=1;var t=hi(),e=ME();t({target:"Set",proto:!0,real:!0,forced:!lE()("isSupersetOf",function(t){return!t})},{isSupersetOf:e})}(),function(){if(zE)return FE;zE=1;var t=hi(),e=AE(),i=jE();t({target:"Set",proto:!0,real:!0,forced:!lE()("symmetricDifference")||!i("symmetricDifference")},{symmetricDifference:e})}(),function(){if(HE)return GE;HE=1;var t=hi(),e=KE(),i=jE();t({target:"Set",proto:!0,real:!0,forced:!lE()("union")||!i("union")},{union:e})}(),Ou(),WE=kt().Set)}function QE(){if(UE)return VE;UE=1;var t=ZE();return zu(),VE=t}var $E,JE,tO,eO,iO,oO,nO,sO,rO,aO,hO=i(XE?YE:(XE=1,YE=QE())),dO={},lO={};function cO(){if(JE)return $E;JE=1;var t=O(),e=su(),i=Zc().getWeakData,o=iu(),n=si(),s=xt(),r=Ct(),a=eu(),h=Yh(),d=ye(),l=Yl(),c=l.set,u=l.getterFor,p=h.find,f=h.findIndex,g=t([].splice),m=0,v=function(t){return t.frozen||(t.frozen=new y)},y=function(){this.entries=[]},b=function(t,e){return p(t.entries,function(t){return t[0]===e})};return y.prototype={get:function(t){var e=b(this,t);if(e)return e[1]},has:function(t){return!!b(this,t)},set:function(t,e){var i=b(this,t);i?i[1]=e:this.entries.push([t,e])},delete:function(t){var e=f(this.entries,function(e){return e[0]===t});return~e&&g(this.entries,e,1),!!~e}},$E={getConstructor:function(t,h,l,p){var f=t(function(t,e){o(t,g),c(t,{type:h,id:m++,frozen:null}),s(e)||a(e,t[p],{that:t,AS_ENTRIES:l})}),g=f.prototype,y=u(h),b=function(t,e,o){var s=y(t),r=i(n(e),!0);return!0===r?v(s).set(e,o):r[s.id]=o,t};return e(g,{delete:function(t){var e=y(this);if(!r(t))return!1;var o=i(t);return!0===o?v(e).delete(t):o&&d(o,e.id)&&delete o[e.id]},has:function(t){var e=y(this);if(!r(t))return!1;var o=i(t);return!0===o?v(e).has(t):o&&d(o,e.id)}}),e(g,l?{get:function(t){var e=y(this);if(r(t)){var o=i(t);if(!0===o)return v(e).get(t);if(o)return o[e.id]}},set:function(t,e){return b(this,t,e)}}:{add:function(t){return b(this,t,!0)}}),f}}}function uO(){return eO||(eO=1,function(){if(tO)return lO;tO=1;var t,e=Kc(),i=w(),o=O(),n=su(),s=Zc(),r=ou(),a=cO(),h=Ct(),d=Yl().enforce,l=_(),c=Ul(),u=Object,p=Array.isArray,f=u.isExtensible,g=u.isFrozen,m=u.isSealed,v=u.freeze,y=u.seal,b=!i.ActiveXObject&&"ActiveXObject"in i,x=function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},E=r("WeakMap",x,a),C=E.prototype,k=o(C.set);if(c)if(b){t=a.getConstructor(x,"WeakMap",!0),s.enable();var S=o(C.delete),T=o(C.has),D=o(C.get);n(C,{delete:function(e){if(h(e)&&!f(e)){var i=d(this);return i.frozen||(i.frozen=new t),S(this,e)||i.frozen.delete(e)}return S(this,e)},has:function(e){if(h(e)&&!f(e)){var i=d(this);return i.frozen||(i.frozen=new t),T(this,e)||i.frozen.has(e)}return T(this,e)},get:function(e){if(h(e)&&!f(e)){var i=d(this);return i.frozen||(i.frozen=new t),T(this,e)?D(this,e):i.frozen.get(e)}return D(this,e)},set:function(e,i){if(h(e)&&!f(e)){var o=d(this);o.frozen||(o.frozen=new t),T(this,e)?k(this,e,i):o.frozen.set(e,i)}else k(this,e,i);return this}})}else e&&l(function(){var t=v([]);return k(new E,t,1),!g(t)})&&n(C,{set:function(t,e){var i;return p(t)&&(g(t)?i=v:m(t)&&(i=y)),k(this,t,e),i&&i(t),this}})}()),dO}function pO(){return oO?iO:(oO=1,ac(),uO(),iO=kt().WeakMap)}function fO(){if(sO)return nO;sO=1;var t=pO();return zu(),nO=t}var gO=i(aO?rO:(aO=1,rO=fO()));function mO(t,e,i){(function(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")})(t,e),e.set(t,i)}function vO(t,e,i){return t.set(bO(t,e),i),i}function yO(t,e){return t.get(bO(t,e))}function bO(t,e,i){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:i;throw new TypeError("Private element is not present on this object")}function wO(t,e){const i=new hO;for(const o of e)t.has(o)||i.add(o);return i}var _O=new gO,xO=new gO;class EO{constructor(){mO(this,_O,new hO),mO(this,xO,new hO)}get size(){return yO(xO,this).size}add(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];for(const t of e)yO(xO,this).add(t)}delete(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];for(const t of e)yO(xO,this).delete(t)}clear(){yO(xO,this).clear()}getSelection(){return[...yO(xO,this)]}getChanges(){return{added:[...wO(yO(_O,this),yO(xO,this))],deleted:[...wO(yO(xO,this),yO(_O,this))],previous:[...new hO(yO(_O,this))],current:[...new hO(yO(xO,this))]}}commit(){const t=this.getChanges();vO(_O,this,yO(xO,this)),vO(xO,this,new hO(yO(_O,this)));for(const e of t.added)e.select();for(const e of t.deleted)e.unselect();return t}}var OO=new gO,CO=new gO,kO=new gO;class SO{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{};mO(this,OO,new EO),mO(this,CO,new EO),mO(this,kO,void 0),vO(kO,this,t)}get sizeNodes(){return yO(OO,this).size}get sizeEdges(){return yO(CO,this).size}getNodes(){return yO(OO,this).getSelection()}getEdges(){return yO(CO,this).getSelection()}addNodes(){yO(OO,this).add(...arguments)}addEdges(){yO(CO,this).add(...arguments)}deleteNodes(t){yO(OO,this).delete(t)}deleteEdges(t){yO(CO,this).delete(t)}clear(){yO(OO,this).clear(),yO(CO,this).clear()}commit(){const t={nodes:yO(OO,this).commit(),edges:yO(CO,this).commit()};for(var e=arguments.length,i=new Array(e),o=0;o<e;o++)i[o]=arguments[o];return yO(kO,this).call(this,t,...i),t}}class TO{constructor(t,e){this.body=t,this.canvas=e,this._selectionAccumulator=new SO,this.hoverObj={nodes:{},edges:{}},this.options={},this.defaultOptions={multiselect:!1,selectable:!0,selectConnectedEdges:!0,hoverConnectedEdges:!0},Ki(this.options,this.defaultOptions),this.body.emitter.on("_dataChanged",()=>{this.updateSelection()})}setOptions(t){if(void 0!==t){_s(["multiselect","hoverConnectedEdges","selectable","selectConnectedEdges"],this.options,t)}}selectOnPoint(t){let e=!1;if(!0===this.options.selectable){const i=this.getNodeAt(t)||this.getEdgeAt(t);this.unselectAll(),void 0!==i&&(e=this.selectObject(i)),this.body.emitter.emit("_requestRedraw")}return e}selectAdditionalOnPoint(t){let e=!1;if(!0===this.options.selectable){const i=this.getNodeAt(t)||this.getEdgeAt(t);void 0!==i&&(e=!0,!0===i.isSelected()?this.deselectObject(i):this.selectObject(i),this.body.emitter.emit("_requestRedraw"))}return e}_initBaseEvent(t,e){const i={};return i.pointer={DOM:{x:e.x,y:e.y},canvas:this.canvas.DOMtoCanvas(e)},i.event=t,i}generateClickEvent(t,e,i,o){let n=arguments.length>4&&void 0!==arguments[4]&&arguments[4];const s=this._initBaseEvent(e,i);if(!0===n)s.nodes=[],s.edges=[];else{const t=this.getSelection();s.nodes=t.nodes,s.edges=t.edges}void 0!==o&&(s.previousSelection=o),"click"==t&&(s.items=this.getClickedItems(i)),void 0!==e.controlEdge&&(s.controlEdge=e.controlEdge),this.body.emitter.emit(t,s)}selectObject(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.options.selectConnectedEdges;return void 0!==t&&(t instanceof Cw?(!0===e&&this._selectionAccumulator.addEdges(...t.edges),this._selectionAccumulator.addNodes(t)):this._selectionAccumulator.addEdges(t),!0)}deselectObject(t){!0===t.isSelected()&&(t.selected=!1,this._removeFromSelection(t))}_getAllNodesOverlappingWith(t){const e=[],i=this.body.nodes;for(let o=0;o<this.body.nodeIndices.length;o++){const n=this.body.nodeIndices[o];i[n].isOverlappingWith(t)&&e.push(n)}return e}_pointerToPositionObject(t){const e=this.canvas.DOMtoCanvas(t);return{left:e.x-1,top:e.y+1,right:e.x+1,bottom:e.y-1}}getNodeAt(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const i=this._pointerToPositionObject(t),o=this._getAllNodesOverlappingWith(i);return o.length>0?!0===e?this.body.nodes[o[o.length-1]]:o[o.length-1]:void 0}_getEdgesOverlappingWith(t,e){const i=this.body.edges;for(let o=0;o<this.body.edgeIndices.length;o++){const n=this.body.edgeIndices[o];i[n].isOverlappingWith(t)&&e.push(n)}}_getAllEdgesOverlappingWith(t){const e=[];return this._getEdgesOverlappingWith(t,e),e}getEdgeAt(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const i=this.canvas.DOMtoCanvas(t);let o=10,n=null;const s=this.body.edges;for(let t=0;t<this.body.edgeIndices.length;t++){const e=this.body.edgeIndices[t],r=s[e];if(r.connected){const t=r.from.x,s=r.from.y,a=r.to.x,h=r.to.y,d=r.edgeType.getDistanceToEdge(t,s,a,h,i.x,i.y);d<o&&(n=e,o=d)}}return null!==n?!0===e?this.body.edges[n]:n:void 0}_addToHover(t){t instanceof Cw?this.hoverObj.nodes[t.id]=t:this.hoverObj.edges[t.id]=t}_removeFromSelection(t){t instanceof Cw?(this._selectionAccumulator.deleteNodes(t),this._selectionAccumulator.deleteEdges(...t.edges)):this._selectionAccumulator.deleteEdges(t)}unselectAll(){this._selectionAccumulator.clear()}getSelectedNodeCount(){return this._selectionAccumulator.sizeNodes}getSelectedEdgeCount(){return this._selectionAccumulator.sizeEdges}_hoverConnectedEdges(t){for(let e=0;e<t.edges.length;e++){const i=t.edges[e];i.hover=!0,this._addToHover(i)}}emitBlurEvent(t,e,i){const o=this._initBaseEvent(t,e);!0===i.hover&&(i.hover=!1,i instanceof Cw?(o.node=i.id,this.body.emitter.emit("blurNode",o)):(o.edge=i.id,this.body.emitter.emit("blurEdge",o)))}emitHoverEvent(t,e,i){const o=this._initBaseEvent(t,e);let n=!1;return!1===i.hover&&(i.hover=!0,this._addToHover(i),n=!0,i instanceof Cw?(o.node=i.id,this.body.emitter.emit("hoverNode",o)):(o.edge=i.id,this.body.emitter.emit("hoverEdge",o))),n}hoverObject(t,e){let i=this.getNodeAt(e);void 0===i&&(i=this.getEdgeAt(e));let o=!1;for(const n in this.hoverObj.nodes)Object.prototype.hasOwnProperty.call(this.hoverObj.nodes,n)&&(void 0===i||i instanceof Cw&&i.id!=n||i instanceof D_)&&(this.emitBlurEvent(t,e,this.hoverObj.nodes[n]),delete this.hoverObj.nodes[n],o=!0);for(const n in this.hoverObj.edges)Object.prototype.hasOwnProperty.call(this.hoverObj.edges,n)&&(!0===o?(this.hoverObj.edges[n].hover=!1,delete this.hoverObj.edges[n]):(void 0===i||i instanceof D_&&i.id!=n||i instanceof Cw&&!i.hover)&&(this.emitBlurEvent(t,e,this.hoverObj.edges[n]),delete this.hoverObj.edges[n],o=!0));if(void 0!==i){const n=Sp(this.hoverObj.edges).length,s=Sp(this.hoverObj.nodes).length;(o||i instanceof D_&&0===n&&0===s||i instanceof Cw&&0===n&&0===s)&&(o=this.emitHoverEvent(t,e,i)),i instanceof Cw&&!0===this.options.hoverConnectedEdges&&this._hoverConnectedEdges(i)}!0===o&&this.body.emitter.emit("_requestRedraw")}commitWithoutEmitting(){this._selectionAccumulator.commit()}commitAndEmit(t,e){let i=!1;const o=this._selectionAccumulator.commit(),n={nodes:o.nodes.previous,edges:o.edges.previous};o.edges.deleted.length>0&&(this.generateClickEvent("deselectEdge",e,t,n),i=!0),o.nodes.deleted.length>0&&(this.generateClickEvent("deselectNode",e,t,n),i=!0),o.nodes.added.length>0&&(this.generateClickEvent("selectNode",e,t),i=!0),o.edges.added.length>0&&(this.generateClickEvent("selectEdge",e,t),i=!0),!0===i&&this.generateClickEvent("select",e,t)}getSelection(){return{nodes:this.getSelectedNodeIds(),edges:this.getSelectedEdgeIds()}}getSelectedNodes(){return this._selectionAccumulator.getNodes()}getSelectedEdges(){return this._selectionAccumulator.getEdges()}getSelectedNodeIds(){var t;return Hd(t=this._selectionAccumulator.getNodes()).call(t,t=>t.id)}getSelectedEdgeIds(){var t;return Hd(t=this._selectionAccumulator.getEdges()).call(t,t=>t.id)}setSelection(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t||!t.nodes&&!t.edges)throw new TypeError("Selection must be an object with nodes and/or edges properties");if((e.unselectAll||void 0===e.unselectAll)&&this.unselectAll(),t.nodes)for(const i of t.nodes){const t=this.body.nodes[i];if(!t)throw new RangeError('Node with id "'+i+'" not found');this.selectObject(t,e.highlightEdges)}if(t.edges)for(const e of t.edges){const t=this.body.edges[e];if(!t)throw new RangeError('Edge with id "'+e+'" not found');this.selectObject(t)}this.body.emitter.emit("_requestRedraw"),this._selectionAccumulator.commit()}selectNodes(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(!t||void 0===t.length)throw"Selection must be an array with ids";this.setSelection({nodes:t},{highlightEdges:e})}selectEdges(t){if(!t||void 0===t.length)throw"Selection must be an array with ids";this.setSelection({edges:t})}updateSelection(){for(const t in this._selectionAccumulator.getNodes())Object.prototype.hasOwnProperty.call(this.body.nodes,t.id)||this._selectionAccumulator.deleteNodes(t);for(const t in this._selectionAccumulator.getEdges())Object.prototype.hasOwnProperty.call(this.body.edges,t.id)||this._selectionAccumulator.deleteEdges(t)}getClickedItems(t){const e=this.canvas.DOMtoCanvas(t),i=[],o=this.body.nodeIndices,n=this.body.nodes;for(let t=o.length-1;t>=0;t--){const s=n[o[t]].getItemsOnPoint(e);i.push.apply(i,s)}const s=this.body.edgeIndices,r=this.body.edges;for(let t=s.length-1;t>=0;t--){const o=r[s[t]].getItemsOnPoint(e);i.push.apply(i,o)}return i}}var DO,MO,IO,PO,NO,BO,zO,FO,AO,jO,RO,LO,HO,WO,qO,VO,UO,YO={};function XO(){if(MO)return DO;MO=1;var t=Qi(),e=Math.floor,i=function(o,n){var s=o.length;if(s<8)for(var r,a,h=1;h<s;){for(a=h,r=o[h];a&&n(o[a-1],r)>0;)o[a]=o[--a];a!==h++&&(o[a]=r)}else for(var d=e(s/2),l=i(t(o,0,d),n),c=i(t(o,d),n),u=l.length,p=c.length,f=0,g=0;f<u||g<p;)o[f+g]=f<u&&g<p?n(l[f],c[g])<=0?l[f++]:c[g++]:f<u?l[f++]:c[g++];return o};return DO=i}function GO(){if(PO)return IO;PO=1;var t=Dt().match(/firefox\/(\d+)/i);return IO=!!t&&+t[1]}function KO(){return BO?NO:(BO=1,NO=/MSIE|Trident/.test(Dt()))}function ZO(){if(FO)return zO;FO=1;var t=Dt().match(/AppleWebKit\/(\d+)\./);return zO=!!t&&+t[1]}function QO(){if(AO)return YO;AO=1;var t=hi(),e=O(),i=zt(),o=ve(),n=pi(),s=mh(),r=ka(),a=_(),h=XO(),d=Hr(),l=GO(),c=KO(),u=Mt(),p=ZO(),f=[],g=e(f.sort),m=e(f.push),v=a(function(){f.sort(void 0)}),y=a(function(){f.sort(null)}),b=d("sort"),w=!a(function(){if(u)return u<70;if(!(l&&l>3)){if(c)return!0;if(p)return p<603;var t,e,i,o,n="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:i=3;break;case 68:case 71:i=4;break;default:i=2}for(o=0;o<47;o++)f.push({k:e+o,v:i})}for(f.sort(function(t,e){return e.v-t.v}),o=0;o<f.length;o++)e=f[o].k.charAt(0),n.charAt(n.length-1)!==e&&(n+=e);return"DGBEFHACIJK"!==n}});return t({target:"Array",proto:!0,forced:v||!y||!b||!w},{sort:function(t){void 0!==t&&i(t);var e=o(this);if(w)return void 0===t?g(e):g(e,t);var a,d,l=[],c=n(e);for(d=0;d<c;d++)d in e&&m(l,e[d]);for(h(l,function(t){return function(e,i){return void 0===i?-1:void 0===e?1:void 0!==t?+t(e,i)||0:r(e)>r(i)?1:-1}}(t)),a=n(l),d=0;d<a;)e[d]=l[d++];for(;d<c;)s(e,d++);return e}}),YO}function $O(){return RO?jO:(RO=1,QO(),jO=Ji()("Array","sort"))}function JO(){if(HO)return LO;HO=1;var t=Tt(),e=$O(),i=Array.prototype;return LO=function(o){var n=o.sort;return o===i||t(i,o)&&n===i.sort?e:n},LO}function tC(){return qO?WO:(qO=1,WO=JO())}var eC,iC,oC,nC,sC,rC,aC,hC,dC,lC=i(UO?VO:(UO=1,VO=tC())),cC={};function uC(){if(iC)return eC;iC=1;var t=N(),e=_(),i=O(),o=Kl(),n=yi(),s=Ot(),r=i(bt().f),a=i([].push),h=t&&e(function(){var t=Object.create(null);return t[2]=2,!r(t,2)}),d=function(e){return function(i){for(var d,l=s(i),c=n(l),u=h&&null===o(l),p=c.length,f=0,g=[];p>f;)d=c[f++],t&&!(u?d in l:r(l,d))||a(g,e?[d,l[d]]:l[d]);return g}};return eC={entries:d(!0),values:d(!1)}}function pC(){return sC?nC:(sC=1,function(){if(oC)return cC;oC=1;var t=hi(),e=uC().values;t({target:"Object",stat:!0},{values:function(t){return e(t)}})}(),nC=kt().Object.values)}function fC(){return aC?rC:(aC=1,rC=pC())}var gC,mC,vC,yC,bC,wC,_C,xC,EC,OC,CC,kC,SC,TC=i(dC?hC:(dC=1,hC=fC())),DC={};function MC(){if(mC)return gC;mC=1;var t=zt(),e=ve(),i=_t(),o=pi(),n=TypeError,s="Reduce of empty array with no initial value",r=function(r){return function(a,h,d,l){var c=e(a),u=i(c),p=o(c);if(t(h),0===p&&d<2)throw new n(s);var f=r?p-1:0,g=r?-1:1;if(d<2)for(;;){if(f in u){l=u[f],f+=g;break}if(f+=g,r?f<0:p<=f)throw new n(s)}for(;r?f>=0:p>f;f+=g)f in u&&(l=h(l,u[f],f,c));return l}};return gC={left:r(!1),right:r(!0)}}function IC(){return yC?vC:(yC=1,vC="NODE"===Wp())}function PC(){return _C?wC:(_C=1,function(){if(bC)return DC;bC=1;var t=hi(),e=MC().left,i=Hr(),o=Mt();t({target:"Array",proto:!0,forced:!IC()&&o>79&&o<83||!i("reduce")},{reduce:function(t){var i=arguments.length;return e(this,t,i,i>1?arguments[1]:void 0)}})}(),wC=Ji()("Array","reduce"))}function NC(){if(EC)return xC;EC=1;var t=Tt(),e=PC(),i=Array.prototype;return xC=function(o){var n=o.reduce;return o===i||t(i,o)&&n===i.reduce?e:n},xC}function BC(){return CC?OC:(CC=1,OC=NC())}var zC=i(SC?kC:(SC=1,kC=BC()));class FC{abstract(){throw new Error("Can't instantiate abstract class!")}fake_use(){}curveType(){return this.abstract()}getPosition(t){return this.fake_use(t),this.abstract()}setPosition(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;this.fake_use(t,e,i),this.abstract()}getTreeSize(t){return this.fake_use(t),this.abstract()}sort(t){this.fake_use(t),this.abstract()}fix(t,e){this.fake_use(t,e),this.abstract()}shift(t,e){this.fake_use(t,e),this.abstract()}}class AC extends FC{constructor(t){super(),this.layout=t}curveType(){return"horizontal"}getPosition(t){return t.x}setPosition(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;void 0!==i&&this.layout.hierarchical.addToOrdering(t,i),t.x=e}getTreeSize(t){const e=this.layout.hierarchical.getTreeSize(this.layout.body.nodes,t);return{min:e.min_x,max:e.max_x}}sort(t){lC(t).call(t,function(t,e){return t.x-e.x})}fix(t,e){t.y=this.layout.options.hierarchical.levelSeparation*e,t.options.fixed.y=!0}shift(t,e){this.layout.body.nodes[t].x+=e}}class jC extends FC{constructor(t){super(),this.layout=t}curveType(){return"vertical"}getPosition(t){return t.y}setPosition(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;void 0!==i&&this.layout.hierarchical.addToOrdering(t,i),t.y=e}getTreeSize(t){const e=this.layout.hierarchical.getTreeSize(this.layout.body.nodes,t);return{min:e.min_y,max:e.max_y}}sort(t){lC(t).call(t,function(t,e){return t.y-e.y})}fix(t,e){t.x=this.layout.options.hierarchical.levelSeparation*e,t.options.fixed.x=!0}shift(t,e){this.layout.body.nodes[t].y+=e}}var RC,LC,HC,WC,qC,VC,UC,YC,XC,GC={};function KC(){return HC?LC:(HC=1,function(){if(RC)return GC;RC=1;var t=hi(),e=Yh().every;t({target:"Array",proto:!0,forced:!Hr()("every")},{every:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0)}})}(),LC=Ji()("Array","every"))}function ZC(){if(qC)return WC;qC=1;var t=Tt(),e=KC(),i=Array.prototype;return WC=function(o){var n=o.every;return o===i||t(i,o)&&n===i.every?e:n},WC}function QC(){return UC?VC:(UC=1,VC=ZC())}var $C=i(XC?YC:(XC=1,YC=QC()));function JC(t,e){const i=new hO;return Qh(t).call(t,t=>{var e;Qh(e=t.edges).call(e,t=>{t.connected&&i.add(t)})}),Qh(i).call(i,t=>{const i=t.from.id,o=t.to.id;null==e[i]&&(e[i]=0),(null==e[o]||e[i]>=e[o])&&(e[o]=e[i]+1)}),e}function tk(t,e,i,o){var n;const s=Rr(null),r=zC(n=[...Pb(o).call(o)]).call(n,(t,e)=>t+1+e.edges.length,0),a=i+"Id",h="to"===i?1:-1;for(const[n,c]of o){if(!o.has(n)||!t(c))continue;s[n]=0;const u=[c];let p,f=0;for(;p=u.pop();){var d,l;if(!o.has(n))continue;const t=s[p.id]+h;if(Qh(d=iy(l=p.edges).call(l,t=>t.connected&&t.to!==t.from&&t[i]!==p&&o.has(t.toId)&&o.has(t.fromId))).call(d,o=>{const n=o[a],r=s[n];(null==r||e(t,r))&&(s[n]=t,u.push(o[i]))}),f>r)return JC(o,s);++f}}return s}class ek{constructor(){this.childrenReference={},this.parentReference={},this.trees={},this.distributionOrdering={},this.levels={},this.distributionIndex={},this.isTree=!1,this.treeIndex=-1}addRelation(t,e){void 0===this.childrenReference[t]&&(this.childrenReference[t]=[]),this.childrenReference[t].push(e),void 0===this.parentReference[e]&&(this.parentReference[e]=[]),this.parentReference[e].push(t)}checkIfTree(){for(const t in this.parentReference)if(this.parentReference[t].length>1)return void(this.isTree=!1);this.isTree=!0}numTrees(){return this.treeIndex+1}setTreeIndex(t,e){void 0!==e&&void 0===this.trees[t.id]&&(this.trees[t.id]=e,this.treeIndex=Math.max(e,this.treeIndex))}ensureLevel(t){void 0===this.levels[t]&&(this.levels[t]=0)}getMaxLevel(t){const e={},i=t=>{if(void 0!==e[t])return e[t];let o=this.levels[t];if(this.childrenReference[t]){const e=this.childrenReference[t];if(e.length>0)for(let t=0;t<e.length;t++)o=Math.max(o,i(e[t]))}return e[t]=o,o};return i(t)}levelDownstream(t,e){void 0===this.levels[e.id]&&(void 0===this.levels[t.id]&&(this.levels[t.id]=0),this.levels[e.id]=this.levels[t.id]+1)}setMinLevelToZero(){var t;const e=new Au;let i=0;const o=lC(t=[...new hO(TC(this.levels))]).call(t,(t,e)=>t-e);for(const t of o)e.set(t,i++);for(const t in this.levels)Object.prototype.hasOwnProperty.call(this.levels,t)&&(this.levels[t]=e.get(this.levels[t]))}getTreeSize(t,e){let i=1e9,o=-1e9,n=1e9,s=-1e9;for(const r in this.trees)if(Object.prototype.hasOwnProperty.call(this.trees,r)&&this.trees[r]===e){const e=t[r];i=Math.min(e.x,i),o=Math.max(e.x,o),n=Math.min(e.y,n),s=Math.max(e.y,s)}return{min_x:i,max_x:o,min_y:n,max_y:s}}hasSameParent(t,e){const i=this.parentReference[t.id],o=this.parentReference[e.id];if(void 0===i||void 0===o)return!1;for(let t=0;t<i.length;t++)for(let e=0;e<o.length;e++)if(i[t]==o[e])return!0;return!1}inSameSubNetwork(t,e){return this.trees[t.id]===this.trees[e.id]}getLevels(){return Sp(this.distributionOrdering)}addToOrdering(t,e){void 0===this.distributionOrdering[e]&&(this.distributionOrdering[e]=[]);let i=!1;const o=this.distributionOrdering[e];for(const e in o)if(o[e]===t){i=!0;break}i||(this.distributionOrdering[e].push(t),this.distributionIndex[t.id]=this.distributionOrdering[e].length-1)}}class ik{constructor(t){this.body=t,this._resetRNG(Math.random()+":"+H_()),this.setPhysics=!1,this.options={},this.optionsBackup={physics:{}},this.defaultOptions={randomSeed:void 0,improvedLayout:!0,clusterThreshold:150,hierarchical:{enabled:!1,levelSeparation:150,nodeSpacing:100,treeSpacing:200,blockShifting:!0,edgeMinimization:!0,parentCentralization:!0,direction:"UD",sortMethod:"hubsize"}},Ki(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){this.body.emitter.on("_dataChanged",()=>{this.setupHierarchicalLayout()}),this.body.emitter.on("_dataLoaded",()=>{this.layoutNetwork()}),this.body.emitter.on("_resetHierarchicalLayout",()=>{this.setupHierarchicalLayout()}),this.body.emitter.on("_adjustEdgesForHierarchicalLayout",()=>{if(!0!==this.options.hierarchical.enabled)return;const t=this.direction.curveType();this.body.emitter.emit("_forceDisableDynamicCurves",t,!1)})}setOptions(t,e){if(void 0!==t){const i=this.options.hierarchical,o=i.enabled;if(_s(["randomSeed","improvedLayout","clusterThreshold"],this.options,t),Vs(this.options,t,"hierarchical"),void 0!==t.randomSeed&&this._resetRNG(t.randomSeed),!0===i.enabled)return!0===o&&this.body.emitter.emit("refresh",!0),"RL"===i.direction||"DU"===i.direction?i.levelSeparation>0&&(i.levelSeparation*=-1):i.levelSeparation<0&&(i.levelSeparation*=-1),this.setDirectionStrategy(),this.body.emitter.emit("_resetHierarchicalLayout"),this.adaptAllOptionsForHierarchicalLayout(e);if(!0===o)return this.body.emitter.emit("refresh"),Es(e,this.optionsBackup)}return e}_resetRNG(t){this.initialRandomSeed=t,this._rng=rs(this.initialRandomSeed)}adaptAllOptionsForHierarchicalLayout(t){if(!0===this.options.hierarchical.enabled){const e=this.optionsBackup.physics;void 0===t.physics||!0===t.physics?(t.physics={enabled:void 0===e.enabled||e.enabled,solver:"hierarchicalRepulsion"},e.enabled=void 0===e.enabled||e.enabled,e.solver=e.solver||"barnesHut"):"object"==typeof t.physics?(e.enabled=void 0===t.physics.enabled||t.physics.enabled,e.solver=t.physics.solver||"barnesHut",t.physics.solver="hierarchicalRepulsion"):!1!==t.physics&&(e.solver="barnesHut",t.physics={solver:"hierarchicalRepulsion"});let i=this.direction.curveType();if(void 0===t.edges)this.optionsBackup.edges={smooth:{enabled:!0,type:"dynamic"}},t.edges={smooth:!1};else if(void 0===t.edges.smooth)this.optionsBackup.edges={smooth:{enabled:!0,type:"dynamic"}},t.edges.smooth=!1;else if("boolean"==typeof t.edges.smooth)this.optionsBackup.edges={smooth:t.edges.smooth},t.edges.smooth={enabled:t.edges.smooth,type:i};else{const e=t.edges.smooth;void 0!==e.type&&"dynamic"!==e.type&&(i=e.type),this.optionsBackup.edges={smooth:{enabled:void 0===e.enabled||e.enabled,type:void 0===e.type?"dynamic":e.type,roundness:void 0===e.roundness?.5:e.roundness,forceDirection:void 0!==e.forceDirection&&e.forceDirection}},t.edges.smooth={enabled:void 0===e.enabled||e.enabled,type:i,roundness:void 0===e.roundness?.5:e.roundness,forceDirection:void 0!==e.forceDirection&&e.forceDirection}}this.body.emitter.emit("_forceDisableDynamicCurves",i)}return t}positionInitially(t){if(!0!==this.options.hierarchical.enabled){this._resetRNG(this.initialRandomSeed);const e=t.length+50;for(let i=0;i<t.length;i++){const o=t[i],n=2*Math.PI*this._rng();void 0===o.x&&(o.x=e*Math.cos(n)),void 0===o.y&&(o.y=e*Math.sin(n))}}}layoutNetwork(){if(!0!==this.options.hierarchical.enabled&&!0===this.options.improvedLayout){const t=this.body.nodeIndices;let e=0;for(let i=0;i<t.length;i++){!0===this.body.nodes[t[i]].predefinedPosition&&(e+=1)}if(e<.5*t.length){const e=10;let i=0;const o=this.options.clusterThreshold,n={clusterNodeProperties:{shape:"ellipse",label:"",group:"",font:{multi:!1}},clusterEdgeProperties:{label:"",font:{multi:!1},smooth:{enabled:!1}}};if(t.length>o){const s=t.length;for(;t.length>o&&i<=e;){i+=1;const e=t.length;i%3==0?this.body.modules.clustering.clusterBridges(n):this.body.modules.clustering.clusterOutliers(n);if(e==t.length&&i%3!=0)return this._declusterAll(),this.body.emitter.emit("_layoutFailed"),void console.info("This network could not be positioned by this version of the improved layout algorithm. Please disable improvedLayout for better performance.")}this.body.modules.kamadaKawai.setOptions({springLength:Math.max(150,2*s)})}i>e&&console.info("The clustering didn't succeed within the amount of interations allowed, progressing with partial result."),this.body.modules.kamadaKawai.solve(t,this.body.edgeIndices,!0),this._shiftToCenter();const s=70;for(let e=0;e<t.length;e++){const i=this.body.nodes[t[e]];!1===i.predefinedPosition&&(i.x+=(.5-this._rng())*s,i.y+=(.5-this._rng())*s)}this._declusterAll(),this.body.emitter.emit("_repositionBezierNodes")}}}_shiftToCenter(){const t=gx.getRangeCore(this.body.nodes,this.body.nodeIndices),e=gx.findCenter(t);for(let t=0;t<this.body.nodeIndices.length;t++){const i=this.body.nodes[this.body.nodeIndices[t]];i.x-=e.x,i.y-=e.y}}_declusterAll(){let t=!0;for(;!0===t;){t=!1;for(let e=0;e<this.body.nodeIndices.length;e++)!0===this.body.nodes[this.body.nodeIndices[e]].isCluster&&(t=!0,this.body.modules.clustering.openCluster(this.body.nodeIndices[e],{},!1));!0===t&&this.body.emitter.emit("_dataChanged")}}getSeed(){return this.initialRandomSeed}setupHierarchicalLayout(){if(!0===this.options.hierarchical.enabled&&this.body.nodeIndices.length>0){let t,e,i=!1,o=!1;for(e in this.lastNodeOnLevel={},this.hierarchical=new ek,this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,e)&&(t=this.body.nodes[e],void 0!==t.options.level?(i=!0,this.hierarchical.levels[e]=t.options.level):o=!0);if(!0===o&&!0===i)throw new Error("To use the hierarchical layout, nodes require either no predefined levels or levels have to be defined for all nodes.");{if(!0===o){const t=this.options.hierarchical.sortMethod;"hubsize"===t?this._determineLevelsByHubsize():"directed"===t?this._determineLevelsDirected():"custom"===t&&this._determineLevelsCustomCallback()}for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&this.hierarchical.ensureLevel(t);const t=this._getDistribution();this._generateMap(),this._placeNodesByHierarchy(t),this._condenseHierarchy(),this._shiftToCenter()}}}_condenseHierarchy(){var t=this;let e=!1;const i={},o=(t,e)=>{const i=this.hierarchical.trees;for(const o in i)Object.prototype.hasOwnProperty.call(i,o)&&i[o]===t&&this.direction.shift(o,e)},n=()=>{const t=[];for(let e=0;e<this.hierarchical.numTrees();e++)t.push(this.direction.getTreeSize(e));return t},s=(t,e)=>{if(!e[t.id]&&(e[t.id]=!0,this.hierarchical.childrenReference[t.id])){const i=this.hierarchical.childrenReference[t.id];if(i.length>0)for(let t=0;t<i.length;t++)s(this.body.nodes[i[t]],e)}},r=function(e){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e9,o=1e9,n=1e9,s=1e9,r=-1e9;for(const a in e)if(Object.prototype.hasOwnProperty.call(e,a)){const h=t.body.nodes[a],d=t.hierarchical.levels[h.id],l=t.direction.getPosition(h),[c,u]=t._getSpaceAroundNode(h,e);o=Math.min(c,o),n=Math.min(u,n),d<=i&&(s=Math.min(l,s),r=Math.max(l,r))}return[s,r,o,n]},a=(t,e)=>{const i=this.hierarchical.getMaxLevel(t.id),o=this.hierarchical.getMaxLevel(e.id);return Math.min(i,o)},h=(t,e,i)=>{const o=this.hierarchical;for(let n=0;n<e.length;n++){const s=e[n],r=o.distributionOrdering[s];if(r.length>1)for(let e=0;e<r.length-1;e++){const n=r[e],s=r[e+1];o.hasSameParent(n,s)&&o.inSameSubNetwork(n,s)&&t(n,s,i)}}},d=function(i,o){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const h=t.direction.getPosition(i),d=t.direction.getPosition(o),l=Math.abs(d-h),c=t.options.hierarchical.nodeSpacing;if(l>c){const h={},d={};s(i,h),s(o,d);const l=a(i,o),u=r(h,l),p=r(d,l),f=u[1],g=p[0],m=p[2];if(Math.abs(f-g)>c){let i=f-g+c;i<-m+c&&(i=-m+c),i<0&&(t._shiftBlock(o.id,i),e=!0,!0===n&&t._centerParent(o))}}},l=(t,o)=>{const n=o.id,a=o.edges,h=this.hierarchical.levels[o.id],d=this.options.hierarchical.levelSeparation*this.options.hierarchical.levelSeparation,l={},c=[];for(let t=0;t<a.length;t++){const e=a[t];if(e.toId!=e.fromId){const i=e.toId==n?e.from:e.to;l[a[t].id]=i,this.hierarchical.levels[i.id]<h&&c.push(e)}}const u=(t,e)=>{let i=0;for(let o=0;o<e.length;o++)if(void 0!==l[e[o].id]){const n=this.direction.getPosition(l[e[o].id])-t;i+=n/Math.sqrt(n*n+d)}return i},p=(t,e)=>{let i=0;for(let o=0;o<e.length;o++)if(void 0!==l[e[o].id]){const n=this.direction.getPosition(l[e[o].id])-t;i-=d*Math.pow(n*n+d,-1.5)}return i},f=(t,e)=>{let i=this.direction.getPosition(o);const n={};for(let o=0;o<t;o++){const t=u(i,e),s=p(i,e),r=40;if(i-=Math.max(-r,Math.min(r,Math.round(t/s))),void 0!==n[i])break;n[i]=o}return i};let g=f(t,c);(t=>{const n=this.direction.getPosition(o);if(void 0===i[o.id]){const t={};s(o,t),i[o.id]=t}const a=r(i[o.id]),h=a[2],d=a[3],l=t-n;let c=0;l>0?c=Math.min(l,d-this.options.hierarchical.nodeSpacing):l<0&&(c=-Math.min(-l,h-this.options.hierarchical.nodeSpacing)),0!=c&&(this._shiftBlock(o.id,c),e=!0)})(g),g=f(t,a),(t=>{const i=this.direction.getPosition(o),[n,s]=this._getSpaceAroundNode(o),r=t-i;let a=i;r>0?a=Math.min(i+(s-this.options.hierarchical.nodeSpacing),t):r<0&&(a=Math.max(i-(n-this.options.hierarchical.nodeSpacing),t)),a!==i&&(this.direction.setPosition(o,a),e=!0)})(g)},c=t=>{let i=this.hierarchical.getLevels();i=fx(i).call(i);for(let o=0;o<t;o++){e=!1;for(let t=0;t<i.length;t++){const e=i[t],o=this.hierarchical.distributionOrdering[e];for(let t=0;t<o.length;t++)l(1e3,o[t])}if(!0!==e)break}},u=t=>{let i=this.hierarchical.getLevels();i=fx(i).call(i);for(let o=0;o<t&&(e=!1,h(d,i,!0),!0===e);o++);},p=()=>{for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&this._centerParent(this.body.nodes[t])},f=()=>{let t=this.hierarchical.getLevels();t=fx(t).call(t);for(let e=0;e<t.length;e++){const i=t[e],o=this.hierarchical.distributionOrdering[i];for(let t=0;t<o.length;t++)this._centerParent(o[t])}};!0===this.options.hierarchical.blockShifting&&(u(5),p()),!0===this.options.hierarchical.edgeMinimization&&c(20),!0===this.options.hierarchical.parentCentralization&&f(),(()=>{const t=n();let e=0;for(let i=0;i<t.length-1;i++){e+=t[i].max-t[i+1].min+this.options.hierarchical.treeSpacing,o(i+1,e)}})()}_getSpaceAroundNode(t,e){let i=!0;void 0===e&&(i=!1);const o=this.hierarchical.levels[t.id];if(void 0!==o){const n=this.hierarchical.distributionIndex[t.id],s=this.direction.getPosition(t),r=this.hierarchical.distributionOrdering[o];let a=1e9,h=1e9;if(0!==n){const t=r[n-1];if(!0===i&&void 0===e[t.id]||!1===i){a=s-this.direction.getPosition(t)}}if(n!=r.length-1){const t=r[n+1];if(!0===i&&void 0===e[t.id]||!1===i){const e=this.direction.getPosition(t);h=Math.min(h,e-s)}}return[a,h]}return[0,0]}_centerParent(t){if(this.hierarchical.parentReference[t.id]){const e=this.hierarchical.parentReference[t.id];for(let t=0;t<e.length;t++){const i=e[t],o=this.body.nodes[i],n=this.hierarchical.childrenReference[i];if(void 0!==n){const t=this._getCenterPosition(n),e=this.direction.getPosition(o),[i,s]=this._getSpaceAroundNode(o),r=e-t;(r<0&&Math.abs(r)<s-this.options.hierarchical.nodeSpacing||r>0&&Math.abs(r)<i-this.options.hierarchical.nodeSpacing)&&this.direction.setPosition(o,t)}}}}_placeNodesByHierarchy(t){this.positionedNodes={};for(const i in t)if(Object.prototype.hasOwnProperty.call(t,i)){var e;let o=Sp(t[i]);o=this._indexArrayToNodes(o),lC(e=this.direction).call(e,o);let n=0;for(let t=0;t<o.length;t++){const e=o[t];if(void 0===this.positionedNodes[e.id]){const s=this.options.hierarchical.nodeSpacing;let r=s*n;n>0&&(r=this.direction.getPosition(o[t-1])+s),this.direction.setPosition(e,r,i),this._validatePositionAndContinue(e,i,r),n++}}}}_placeBranchNodes(t,e){var i;const o=this.hierarchical.childrenReference[t];if(void 0===o)return;const n=[];for(let t=0;t<o.length;t++)n.push(this.body.nodes[o[t]]);lC(i=this.direction).call(i,n);for(let i=0;i<n.length;i++){const o=n[i],s=this.hierarchical.levels[o.id];if(!(s>e&&void 0===this.positionedNodes[o.id]))return;{const e=this.options.hierarchical.nodeSpacing;let r;r=0===i?this.direction.getPosition(this.body.nodes[t]):this.direction.getPosition(n[i-1])+e,this.direction.setPosition(o,r,s),this._validatePositionAndContinue(o,s,r)}}const s=this._getCenterPosition(n);this.direction.setPosition(this.body.nodes[t],s,e)}_validatePositionAndContinue(t,e,i){if(this.hierarchical.isTree){if(void 0!==this.lastNodeOnLevel[e]){const o=this.direction.getPosition(this.body.nodes[this.lastNodeOnLevel[e]]);if(i-o<this.options.hierarchical.nodeSpacing){const n=o+this.options.hierarchical.nodeSpacing-i,s=this._findCommonParent(this.lastNodeOnLevel[e],t.id);this._shiftBlock(s.withChild,n)}}this.lastNodeOnLevel[e]=t.id,this.positionedNodes[t.id]=!0,this._placeBranchNodes(t.id,e)}}_indexArrayToNodes(t){const e=[];for(let i=0;i<t.length;i++)e.push(this.body.nodes[t[i]]);return e}_getDistribution(){const t={};let e,i;for(e in this.body.nodes)if(Object.prototype.hasOwnProperty.call(this.body.nodes,e)){i=this.body.nodes[e];const o=void 0===this.hierarchical.levels[e]?0:this.hierarchical.levels[e];this.direction.fix(i,o),void 0===t[o]&&(t[o]={}),t[o][e]=i}return t}_getActiveEdges(t){const e=[];return Ts(t.edges,t=>{var i;-1!==Zr(i=this.body.edgeIndices).call(i,t.id)&&e.push(t)}),e}_getHubSizes(){const t={};Ts(this.body.nodeIndices,e=>{const i=this.body.nodes[e],o=this._getActiveEdges(i).length;t[o]=!0});const e=[];return Ts(t,t=>{e.push(Number(t))}),lC(e).call(e,function(t,e){return e-t}),e}_determineLevelsByHubsize(){const t=(t,e)=>{this.hierarchical.levelDownstream(t,e)},e=this._getHubSizes();for(let i=0;i<e.length;++i){const o=e[i];if(0===o)break;Ts(this.body.nodeIndices,e=>{const i=this.body.nodes[e];o===this._getActiveEdges(i).length&&this._crawlNetwork(t,e)})}}_determineLevelsCustomCallback(){this._crawlNetwork((t,e,i)=>{let o=this.hierarchical.levels[t.id];void 0===o&&(o=this.hierarchical.levels[t.id]=1e5);const n=(gx.cloneOptions(t,"node"),gx.cloneOptions(e,"node"),void gx.cloneOptions(i,"edge"));this.hierarchical.levels[e.id]=o+n}),this.hierarchical.setMinLevelToZero()}_determineLevelsDirected(){var t;const e=zC(t=this.body.nodeIndices).call(t,(t,e)=>(t.set(e,this.body.nodes[e]),t),new Au);"roots"===this.options.hierarchical.shakeTowards?this.hierarchical.levels=function(t){return tk(e=>{var i,o;return $C(i=iy(o=e.edges).call(o,e=>t.has(e.toId))).call(i,t=>t.from===e)},(t,e)=>e<t,"to",t)}(e):this.hierarchical.levels=function(t){return tk(e=>{var i,o;return $C(i=iy(o=e.edges).call(o,e=>t.has(e.toId))).call(i,t=>t.to===e)},(t,e)=>e>t,"from",t)}(e),this.hierarchical.setMinLevelToZero()}_generateMap(){this._crawlNetwork((t,e)=>{this.hierarchical.levels[e.id]>this.hierarchical.levels[t.id]&&this.hierarchical.addRelation(t.id,e.id)}),this.hierarchical.checkIfTree()}_crawlNetwork(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){},e=arguments.length>1?arguments[1]:void 0;const i={},o=(e,n)=>{if(void 0===i[e.id]){let s;this.hierarchical.setTreeIndex(e,n),i[e.id]=!0;const r=this._getActiveEdges(e);for(let i=0;i<r.length;i++){const a=r[i];!0===a.connected&&(s=a.toId==e.id?a.from:a.to,e.id!=s.id&&(t(e,s,a),o(s,n)))}}};if(void 0===e){let t=0;for(let e=0;e<this.body.nodeIndices.length;e++){const n=this.body.nodeIndices[e];if(void 0===i[n]){const e=this.body.nodes[n];o(e,t),t+=1}}}else{const t=this.body.nodes[e];if(void 0===t)return void console.error("Node not found:",e);o(t)}}_shiftBlock(t,e){const i={},o=t=>{if(i[t])return;i[t]=!0,this.direction.shift(t,e);const n=this.hierarchical.childrenReference[t];if(void 0!==n)for(let t=0;t<n.length;t++)o(n[t])};o(t)}_findCommonParent(t,e){const i={},o=(t,e)=>{const i=this.hierarchical.parentReference[e];if(void 0!==i)for(let e=0;e<i.length;e++){const n=i[e];t[n]=!0,o(t,n)}},n=(t,e)=>{const i=this.hierarchical.parentReference[e];if(void 0!==i)for(let o=0;o<i.length;o++){const s=i[o];if(void 0!==t[s])return{foundParent:s,withChild:e};const r=n(t,s);if(null!==r.foundParent)return r}return{foundParent:null,withChild:e}};return o(i,t),n(i,e)}setDirectionStrategy(){const t="UD"===this.options.hierarchical.direction||"DU"===this.options.hierarchical.direction;this.direction=t?new AC(this):new jC(this)}_getCenterPosition(t){let e=1e9,i=-1e9;for(let o=0;o<t.length;o++){let n;if(void 0!==t[o].id)n=t[o];else{const e=t[o];n=this.body.nodes[e]}const s=this.direction.getPosition(n);e=Math.min(e,s),i=Math.max(i,s)}return.5*(e+i)}}class ok{constructor(t,e,i,o){var n,s;this.body=t,this.canvas=e,this.selectionHandler=i,this.interactionHandler=o,this.editMode=!1,this.manipulationDiv=void 0,this.editModeDiv=void 0,this.closeDiv=void 0,this._domEventListenerCleanupQueue=[],this.temporaryUIFunctions={},this.temporaryEventFunctions=[],this.touchTime=0,this.temporaryIds={nodes:[],edges:[]},this.guiEnabled=!1,this.inMode=!1,this.selectedControlNode=void 0,this.options={},this.defaultOptions={enabled:!1,initiallyActive:!1,addNode:!0,addEdge:!0,editNode:void 0,editEdge:!0,deleteNode:!0,deleteEdge:!0,controlNodeStyle:{shape:"dot",size:6,color:{background:"#ff0000",border:"#3c3c3c",highlight:{background:"#07f968",border:"#3c3c3c"}},borderWidth:2,borderWidthSelected:2}},Ki(this.options,this.defaultOptions),this.body.emitter.on("destroy",()=>{this._clean()}),this.body.emitter.on("_dataChanged",oo(n=this._restore).call(n,this)),this.body.emitter.on("_resetData",oo(s=this._restore).call(s,this))}_restore(){!1!==this.inMode&&(!0===this.options.initiallyActive?this.enableEditMode():this.disableEditMode())}setOptions(t,e,i){void 0!==e&&(void 0!==e.locale?this.options.locale=e.locale:this.options.locale=i.locale,void 0!==e.locales?this.options.locales=e.locales:this.options.locales=i.locales),void 0!==t&&("boolean"==typeof t?this.options.enabled=t:(this.options.enabled=!0,Es(this.options,t)),!0===this.options.initiallyActive&&(this.editMode=!0),this._setup())}toggleEditMode(){!0===this.editMode?this.disableEditMode():this.enableEditMode()}enableEditMode(){this.editMode=!0,this._clean(),!0===this.guiEnabled&&(this.manipulationDiv.style.display="block",this.closeDiv.style.display="block",this.editModeDiv.style.display="none",this.showManipulatorToolbar())}disableEditMode(){this.editMode=!1,this._clean(),!0===this.guiEnabled&&(this.manipulationDiv.style.display="none",this.closeDiv.style.display="none",this.editModeDiv.style.display="block",this._createEditButton())}showManipulatorToolbar(){if(this._clean(),this.manipulationDOM={},!0===this.guiEnabled){var t,e;this.editMode=!0,this.manipulationDiv.style.display="block",this.closeDiv.style.display="block";const i=this.selectionHandler.getSelectedNodeCount(),o=this.selectionHandler.getSelectedEdgeCount(),n=i+o,s=this.options.locales[this.options.locale];let r=!1;!1!==this.options.addNode&&(this._createAddNodeButton(s),r=!0),!1!==this.options.addEdge&&(!0===r?this._createSeperator(1):r=!0,this._createAddEdgeButton(s)),1===i&&"function"==typeof this.options.editNode?(!0===r?this._createSeperator(2):r=!0,this._createEditNodeButton(s)):1===o&&0===i&&!1!==this.options.editEdge&&(!0===r?this._createSeperator(3):r=!0,this._createEditEdgeButton(s)),0!==n&&(i>0&&!1!==this.options.deleteNode||0===i&&!1!==this.options.deleteEdge)&&(!0===r&&this._createSeperator(4),this._createDeleteButton(s)),this._bindElementEvents(this.closeDiv,oo(t=this.toggleEditMode).call(t,this)),this._temporaryBindEvent("select",oo(e=this.showManipulatorToolbar).call(e,this))}this.body.emitter.emit("_redraw")}addNodeMode(){var t;if(!0!==this.editMode&&this.enableEditMode(),this._clean(),this.inMode="addNode",!0===this.guiEnabled){var e;const t=this.options.locales[this.options.locale];this.manipulationDOM={},this._createBackButton(t),this._createSeperator(),this._createDescription(t.addDescription||this.options.locales.en.addDescription),this._bindElementEvents(this.closeDiv,oo(e=this.toggleEditMode).call(e,this))}this._temporaryBindEvent("click",oo(t=this._performAddNode).call(t,this))}editNode(){!0!==this.editMode&&this.enableEditMode(),this._clean();const t=this.selectionHandler.getSelectedNodes()[0];if(void 0!==t){if(this.inMode="editNode","function"!=typeof this.options.editNode)throw new Error("No function has been configured to handle the editing of nodes.");if(!0!==t.isCluster){const e=Es({},t.options,!1);if(e.x=t.x,e.y=t.y,2!==this.options.editNode.length)throw new Error("The function for edit does not support two arguments (data, callback)");this.options.editNode(e,t=>{null!=t&&"editNode"===this.inMode&&this.body.data.nodes.getDataSet().update(t),this.showManipulatorToolbar()})}else alert(this.options.locales[this.options.locale].editClusterError||this.options.locales.en.editClusterError)}else this.showManipulatorToolbar()}addEdgeMode(){var t,e,i,o,n;if(!0!==this.editMode&&this.enableEditMode(),this._clean(),this.inMode="addEdge",!0===this.guiEnabled){var s;const t=this.options.locales[this.options.locale];this.manipulationDOM={},this._createBackButton(t),this._createSeperator(),this._createDescription(t.edgeDescription||this.options.locales.en.edgeDescription),this._bindElementEvents(this.closeDiv,oo(s=this.toggleEditMode).call(s,this))}this._temporaryBindUI("onTouch",oo(t=this._handleConnect).call(t,this)),this._temporaryBindUI("onDragEnd",oo(e=this._finishConnect).call(e,this)),this._temporaryBindUI("onDrag",oo(i=this._dragControlNode).call(i,this)),this._temporaryBindUI("onRelease",oo(o=this._finishConnect).call(o,this)),this._temporaryBindUI("onDragStart",oo(n=this._dragStartEdge).call(n,this)),this._temporaryBindUI("onHold",()=>{})}editEdgeMode(){if(!0!==this.editMode&&this.enableEditMode(),this._clean(),this.inMode="editEdge","object"==typeof this.options.editEdge&&"function"==typeof this.options.editEdge.editWithoutDrag&&(this.edgeBeingEditedId=this.selectionHandler.getSelectedEdgeIds()[0],void 0!==this.edgeBeingEditedId)){const t=this.body.edges[this.edgeBeingEditedId];return void this._performEditEdge(t.from.id,t.to.id)}if(!0===this.guiEnabled){var t;const e=this.options.locales[this.options.locale];this.manipulationDOM={},this._createBackButton(e),this._createSeperator(),this._createDescription(e.editEdgeDescription||this.options.locales.en.editEdgeDescription),this._bindElementEvents(this.closeDiv,oo(t=this.toggleEditMode).call(t,this))}if(this.edgeBeingEditedId=this.selectionHandler.getSelectedEdgeIds()[0],void 0!==this.edgeBeingEditedId){var e,i,o,n;const t=this.body.edges[this.edgeBeingEditedId],s=this._getNewTargetNode(t.from.x,t.from.y),r=this._getNewTargetNode(t.to.x,t.to.y);this.temporaryIds.nodes.push(s.id),this.temporaryIds.nodes.push(r.id),this.body.nodes[s.id]=s,this.body.nodeIndices.push(s.id),this.body.nodes[r.id]=r,this.body.nodeIndices.push(r.id),this._temporaryBindUI("onTouch",oo(e=this._controlNodeTouch).call(e,this)),this._temporaryBindUI("onTap",()=>{}),this._temporaryBindUI("onHold",()=>{}),this._temporaryBindUI("onDragStart",oo(i=this._controlNodeDragStart).call(i,this)),this._temporaryBindUI("onDrag",oo(o=this._controlNodeDrag).call(o,this)),this._temporaryBindUI("onDragEnd",oo(n=this._controlNodeDragEnd).call(n,this)),this._temporaryBindUI("onMouseMove",()=>{}),this._temporaryBindEvent("beforeDrawing",e=>{const i=t.edgeType.findBorderPositions(e);!1===s.selected&&(s.x=i.from.x,s.y=i.from.y),!1===r.selected&&(r.x=i.to.x,r.y=i.to.y)}),this.body.emitter.emit("_redraw")}else this.showManipulatorToolbar()}deleteSelected(){!0!==this.editMode&&this.enableEditMode(),this._clean(),this.inMode="delete";const t=this.selectionHandler.getSelectedNodeIds(),e=this.selectionHandler.getSelectedEdgeIds();let i;if(t.length>0){for(let e=0;e<t.length;e++)if(!0===this.body.nodes[t[e]].isCluster)return void alert(this.options.locales[this.options.locale].deleteClusterError||this.options.locales.en.deleteClusterError);"function"==typeof this.options.deleteNode&&(i=this.options.deleteNode)}else e.length>0&&"function"==typeof this.options.deleteEdge&&(i=this.options.deleteEdge);if("function"==typeof i){const o={nodes:t,edges:e};if(2!==i.length)throw new Error("The function for delete does not support two arguments (data, callback)");i(o,t=>{null!=t&&"delete"===this.inMode?(this.body.data.edges.getDataSet().remove(t.edges),this.body.data.nodes.getDataSet().remove(t.nodes),this.body.emitter.emit("startSimulation"),this.showManipulatorToolbar()):(this.body.emitter.emit("startSimulation"),this.showManipulatorToolbar())})}else this.body.data.edges.getDataSet().remove(e),this.body.data.nodes.getDataSet().remove(t),this.body.emitter.emit("startSimulation"),this.showManipulatorToolbar()}_setup(){!0===this.options.enabled?(this.guiEnabled=!0,this._createWrappers(),!1===this.editMode?this._createEditButton():this.showManipulatorToolbar()):(this._removeManipulationDOM(),this.guiEnabled=!1)}_createWrappers(){var t,e;(void 0===this.manipulationDiv&&(this.manipulationDiv=document.createElement("div"),this.manipulationDiv.className="vis-manipulation",!0===this.editMode?this.manipulationDiv.style.display="block":this.manipulationDiv.style.display="none",this.canvas.frame.appendChild(this.manipulationDiv)),void 0===this.editModeDiv&&(this.editModeDiv=document.createElement("div"),this.editModeDiv.className="vis-edit-mode",!0===this.editMode?this.editModeDiv.style.display="none":this.editModeDiv.style.display="block",this.canvas.frame.appendChild(this.editModeDiv)),void 0===this.closeDiv)&&(this.closeDiv=document.createElement("button"),this.closeDiv.className="vis-close",this.closeDiv.setAttribute("aria-label",null!==(t=null===(e=this.options.locales[this.options.locale])||void 0===e?void 0:e.close)&&void 0!==t?t:this.options.locales.en.close),this.closeDiv.style.display=this.manipulationDiv.style.display,this.canvas.frame.appendChild(this.closeDiv))}_getNewTargetNode(t,e){const i=Es({},this.options.controlNodeStyle);i.id="targetNode"+rf(),i.hidden=!1,i.physics=!1,i.x=t,i.y=e;const o=this.body.functions.createNode(i);return o.shape.boundingBox={left:t,right:t,top:e,bottom:e},o}_createEditButton(){var t;this._clean(),this.manipulationDOM={},gs(this.editModeDiv);const e=this.options.locales[this.options.locale],i=this._createButton("editMode","vis-edit vis-edit-mode",e.edit||this.options.locales.en.edit);this.editModeDiv.appendChild(i),this._bindElementEvents(i,oo(t=this.toggleEditMode).call(t,this))}_clean(){this.inMode=!1,!0===this.guiEnabled&&(gs(this.editModeDiv),gs(this.manipulationDiv),this._cleanupDOMEventListeners()),this._cleanupTemporaryNodesAndEdges(),this._unbindTemporaryUIs(),this._unbindTemporaryEvents(),this.body.emitter.emit("restorePhysics")}_cleanupDOMEventListeners(){for(const e of Th(t=this._domEventListenerCleanupQueue).call(t,0)){var t;e()}}_removeManipulationDOM(){this._clean(),gs(this.manipulationDiv),gs(this.editModeDiv),gs(this.closeDiv),this.manipulationDiv&&this.canvas.frame.removeChild(this.manipulationDiv),this.editModeDiv&&this.canvas.frame.removeChild(this.editModeDiv),this.closeDiv&&this.canvas.frame.removeChild(this.closeDiv),this.manipulationDiv=void 0,this.editModeDiv=void 0,this.closeDiv=void 0}_createSeperator(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this.manipulationDOM["seperatorLineDiv"+t]=document.createElement("div"),this.manipulationDOM["seperatorLineDiv"+t].className="vis-separator-line",this.manipulationDiv.appendChild(this.manipulationDOM["seperatorLineDiv"+t])}_createAddNodeButton(t){var e;const i=this._createButton("addNode","vis-add",t.addNode||this.options.locales.en.addNode);this.manipulationDiv.appendChild(i),this._bindElementEvents(i,oo(e=this.addNodeMode).call(e,this))}_createAddEdgeButton(t){var e;const i=this._createButton("addEdge","vis-connect",t.addEdge||this.options.locales.en.addEdge);this.manipulationDiv.appendChild(i),this._bindElementEvents(i,oo(e=this.addEdgeMode).call(e,this))}_createEditNodeButton(t){var e;const i=this._createButton("editNode","vis-edit",t.editNode||this.options.locales.en.editNode);this.manipulationDiv.appendChild(i),this._bindElementEvents(i,oo(e=this.editNode).call(e,this))}_createEditEdgeButton(t){var e;const i=this._createButton("editEdge","vis-edit",t.editEdge||this.options.locales.en.editEdge);this.manipulationDiv.appendChild(i),this._bindElementEvents(i,oo(e=this.editEdgeMode).call(e,this))}_createDeleteButton(t){var e;let i;i=this.options.rtl?"vis-delete-rtl":"vis-delete";const o=this._createButton("delete",i,t.del||this.options.locales.en.del);this.manipulationDiv.appendChild(o),this._bindElementEvents(o,oo(e=this.deleteSelected).call(e,this))}_createBackButton(t){var e;const i=this._createButton("back","vis-back",t.back||this.options.locales.en.back);this.manipulationDiv.appendChild(i),this._bindElementEvents(i,oo(e=this.showManipulatorToolbar).call(e,this))}_createButton(t,e,i){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"vis-label";return this.manipulationDOM[t+"Div"]=document.createElement("button"),this.manipulationDOM[t+"Div"].className="vis-button "+e,this.manipulationDOM[t+"Label"]=document.createElement("div"),this.manipulationDOM[t+"Label"].className=o,this.manipulationDOM[t+"Label"].innerText=i,this.manipulationDOM[t+"Div"].appendChild(this.manipulationDOM[t+"Label"]),this.manipulationDOM[t+"Div"]}_createDescription(t){this.manipulationDOM.descriptionLabel=document.createElement("div"),this.manipulationDOM.descriptionLabel.className="vis-none",this.manipulationDOM.descriptionLabel.innerText=t,this.manipulationDiv.appendChild(this.manipulationDOM.descriptionLabel)}_temporaryBindEvent(t,e){this.temporaryEventFunctions.push({event:t,boundFunction:e}),this.body.emitter.on(t,e)}_temporaryBindUI(t,e){if(void 0===this.body.eventListeners[t])throw new Error("This UI function does not exist. Typo? You tried: "+t+" possible are: "+Jw(Sp(this.body.eventListeners)));this.temporaryUIFunctions[t]=this.body.eventListeners[t],this.body.eventListeners[t]=e}_unbindTemporaryUIs(){for(const t in this.temporaryUIFunctions)Object.prototype.hasOwnProperty.call(this.temporaryUIFunctions,t)&&(this.body.eventListeners[t]=this.temporaryUIFunctions[t],delete this.temporaryUIFunctions[t]);this.temporaryUIFunctions={}}_unbindTemporaryEvents(){for(let t=0;t<this.temporaryEventFunctions.length;t++){const e=this.temporaryEventFunctions[t].event,i=this.temporaryEventFunctions[t].boundFunction;this.body.emitter.off(e,i)}this.temporaryEventFunctions=[]}_bindElementEvents(t,e){const i=new ir(t,{});_x(i,e),this._domEventListenerCleanupQueue.push(()=>{i.destroy()});const o=t=>{let{keyCode:i,key:o}=t;"Enter"!==o&&" "!==o&&13!==i&&32!==i||e()};t.addEventListener("keyup",o,!1),this._domEventListenerCleanupQueue.push(()=>{t.removeEventListener("keyup",o,!1)})}_cleanupTemporaryNodesAndEdges(){for(let i=0;i<this.temporaryIds.edges.length;i++){var t;this.body.edges[this.temporaryIds.edges[i]].disconnect(),delete this.body.edges[this.temporaryIds.edges[i]];const o=Zr(t=this.body.edgeIndices).call(t,this.temporaryIds.edges[i]);var e;if(-1!==o)Th(e=this.body.edgeIndices).call(e,o,1)}for(let t=0;t<this.temporaryIds.nodes.length;t++){var i;delete this.body.nodes[this.temporaryIds.nodes[t]];const e=Zr(i=this.body.nodeIndices).call(i,this.temporaryIds.nodes[t]);var o;if(-1!==e)Th(o=this.body.nodeIndices).call(o,e,1)}this.temporaryIds={nodes:[],edges:[]}}_controlNodeTouch(t){this.selectionHandler.unselectAll(),this.lastTouch=this.body.functions.getPointer(t.center),this.lastTouch.translation=Ki({},this.body.view.translation)}_controlNodeDragStart(){const t=this.lastTouch,e=this.selectionHandler._pointerToPositionObject(t),i=this.body.nodes[this.temporaryIds.nodes[0]],o=this.body.nodes[this.temporaryIds.nodes[1]],n=this.body.edges[this.edgeBeingEditedId];this.selectedControlNode=void 0;const s=i.isOverlappingWith(e),r=o.isOverlappingWith(e);!0===s?(this.selectedControlNode=i,n.edgeType.from=i):!0===r&&(this.selectedControlNode=o,n.edgeType.to=o),void 0!==this.selectedControlNode&&this.selectionHandler.selectObject(this.selectedControlNode),this.body.emitter.emit("_redraw")}_controlNodeDrag(t){this.body.emitter.emit("disablePhysics");const e=this.body.functions.getPointer(t.center),i=this.canvas.DOMtoCanvas(e);void 0!==this.selectedControlNode?(this.selectedControlNode.x=i.x,this.selectedControlNode.y=i.y):this.interactionHandler.onDrag(t),this.body.emitter.emit("_redraw")}_controlNodeDragEnd(t){const e=this.body.functions.getPointer(t.center),i=this.selectionHandler._pointerToPositionObject(e),o=this.body.edges[this.edgeBeingEditedId];if(void 0===this.selectedControlNode)return;this.selectionHandler.unselectAll();const n=this.selectionHandler._getAllNodesOverlappingWith(i);let s;for(let t=n.length-1;t>=0;t--)if(n[t]!==this.selectedControlNode.id){s=this.body.nodes[n[t]];break}if(void 0!==s&&void 0!==this.selectedControlNode)if(!0===s.isCluster)alert(this.options.locales[this.options.locale].createEdgeError||this.options.locales.en.createEdgeError);else{const t=this.body.nodes[this.temporaryIds.nodes[0]];this.selectedControlNode.id===t.id?this._performEditEdge(s.id,o.to.id):this._performEditEdge(o.from.id,s.id)}else o.updateEdgeType(),this.body.emitter.emit("restorePhysics");this.body.emitter.emit("_redraw")}_handleConnect(t){if((new Date).valueOf()-this.touchTime>100){this.lastTouch=this.body.functions.getPointer(t.center),this.lastTouch.translation=Ki({},this.body.view.translation),this.interactionHandler.drag.pointer=this.lastTouch,this.interactionHandler.drag.translation=this.lastTouch.translation;const e=this.lastTouch,i=this.selectionHandler.getNodeAt(e);if(void 0!==i)if(!0===i.isCluster)alert(this.options.locales[this.options.locale].createEdgeError||this.options.locales.en.createEdgeError);else{const t=this._getNewTargetNode(i.x,i.y);this.body.nodes[t.id]=t,this.body.nodeIndices.push(t.id);const e=this.body.functions.createEdge({id:"connectionEdge"+rf(),from:i.id,to:t.id,physics:!1,smooth:{enabled:!0,type:"continuous",roundness:.5}});this.body.edges[e.id]=e,this.body.edgeIndices.push(e.id),this.temporaryIds.nodes.push(t.id),this.temporaryIds.edges.push(e.id)}this.touchTime=(new Date).valueOf()}}_dragControlNode(t){const e=this.body.functions.getPointer(t.center),i=this.selectionHandler._pointerToPositionObject(e);let o;void 0!==this.temporaryIds.edges[0]&&(o=this.body.edges[this.temporaryIds.edges[0]].fromId);const n=this.selectionHandler._getAllNodesOverlappingWith(i);let s;for(let t=n.length-1;t>=0;t--){var r;if(-1===Zr(r=this.temporaryIds.nodes).call(r,n[t])){s=this.body.nodes[n[t]];break}}if(t.controlEdge={from:o,to:s?s.id:void 0},this.selectionHandler.generateClickEvent("controlNodeDragging",t,e),void 0!==this.temporaryIds.nodes[0]){const t=this.body.nodes[this.temporaryIds.nodes[0]];t.x=this.canvas._XconvertDOMtoCanvas(e.x),t.y=this.canvas._YconvertDOMtoCanvas(e.y),this.body.emitter.emit("_redraw")}else this.interactionHandler.onDrag(t)}_finishConnect(t){const e=this.body.functions.getPointer(t.center),i=this.selectionHandler._pointerToPositionObject(e);let o;void 0!==this.temporaryIds.edges[0]&&(o=this.body.edges[this.temporaryIds.edges[0]].fromId);const n=this.selectionHandler._getAllNodesOverlappingWith(i);let s;for(let t=n.length-1;t>=0;t--){var r;if(-1===Zr(r=this.temporaryIds.nodes).call(r,n[t])){s=this.body.nodes[n[t]];break}}this._cleanupTemporaryNodesAndEdges(),void 0!==s&&(!0===s.isCluster?alert(this.options.locales[this.options.locale].createEdgeError||this.options.locales.en.createEdgeError):void 0!==this.body.nodes[o]&&void 0!==this.body.nodes[s.id]&&this._performAddEdge(o,s.id)),t.controlEdge={from:o,to:s?s.id:void 0},this.selectionHandler.generateClickEvent("controlNodeDragEnd",t,e),this.body.emitter.emit("_redraw")}_dragStartEdge(t){const e=this.lastTouch;this.selectionHandler.generateClickEvent("dragStart",t,e,void 0,!0)}_performAddNode(t){const e={id:rf(),x:t.pointer.canvas.x,y:t.pointer.canvas.y,label:"new"};if("function"==typeof this.options.addNode){if(2!==this.options.addNode.length)throw this.showManipulatorToolbar(),new Error("The function for add does not support two arguments (data,callback)");this.options.addNode(e,t=>{null!=t&&"addNode"===this.inMode&&this.body.data.nodes.getDataSet().add(t),this.showManipulatorToolbar()})}else this.body.data.nodes.getDataSet().add(e),this.showManipulatorToolbar()}_performAddEdge(t,e){const i={from:t,to:e};if("function"==typeof this.options.addEdge){if(2!==this.options.addEdge.length)throw new Error("The function for connect does not support two arguments (data,callback)");this.options.addEdge(i,t=>{null!=t&&"addEdge"===this.inMode&&(this.body.data.edges.getDataSet().add(t),this.selectionHandler.unselectAll(),this.showManipulatorToolbar())})}else this.body.data.edges.getDataSet().add(i),this.selectionHandler.unselectAll(),this.showManipulatorToolbar()}_performEditEdge(t,e){const i={id:this.edgeBeingEditedId,from:t,to:e,label:this.body.data.edges.get(this.edgeBeingEditedId).label};let o=this.options.editEdge;if("object"==typeof o&&(o=o.editWithoutDrag),"function"==typeof o){if(2!==o.length)throw new Error("The function for edit does not support two arguments (data, callback)");o(i,t=>{null==t||"editEdge"!==this.inMode?(this.body.edges[i.id].updateEdgeType(),this.body.emitter.emit("_redraw"),this.showManipulatorToolbar()):(this.body.data.edges.getDataSet().update(t),this.selectionHandler.unselectAll(),this.showManipulatorToolbar())})}else this.body.data.edges.getDataSet().update(i),this.selectionHandler.unselectAll(),this.showManipulatorToolbar()}}const nk="string",sk="boolean",rk="number",ak="array",hk="object",dk=["arrow","bar","box","circle","crow","curve","diamond","image","inv_curve","inv_triangle","triangle","vee"],lk={borderWidth:{number:rk},borderWidthSelected:{number:rk,undefined:"undefined"},brokenImage:{string:nk,undefined:"undefined"},chosen:{label:{boolean:sk,function:"function"},node:{boolean:sk,function:"function"},__type__:{object:hk,boolean:sk}},color:{border:{string:nk},background:{string:nk},highlight:{border:{string:nk},background:{string:nk},__type__:{object:hk,string:nk}},hover:{border:{string:nk},background:{string:nk},__type__:{object:hk,string:nk}},__type__:{object:hk,string:nk}},opacity:{number:rk,undefined:"undefined"},fixed:{x:{boolean:sk},y:{boolean:sk},__type__:{object:hk,boolean:sk}},font:{align:{string:nk},color:{string:nk},size:{number:rk},face:{string:nk},background:{string:nk},strokeWidth:{number:rk},strokeColor:{string:nk},vadjust:{number:rk},multi:{boolean:sk,string:nk},bold:{color:{string:nk},size:{number:rk},face:{string:nk},mod:{string:nk},vadjust:{number:rk},__type__:{object:hk,string:nk}},boldital:{color:{string:nk},size:{number:rk},face:{string:nk},mod:{string:nk},vadjust:{number:rk},__type__:{object:hk,string:nk}},ital:{color:{string:nk},size:{number:rk},face:{string:nk},mod:{string:nk},vadjust:{number:rk},__type__:{object:hk,string:nk}},mono:{color:{string:nk},size:{number:rk},face:{string:nk},mod:{string:nk},vadjust:{number:rk},__type__:{object:hk,string:nk}},__type__:{object:hk,string:nk}},group:{string:nk,number:rk,undefined:"undefined"},heightConstraint:{minimum:{number:rk},valign:{string:nk},__type__:{object:hk,boolean:sk,number:rk}},hidden:{boolean:sk},icon:{face:{string:nk},code:{string:nk},size:{number:rk},color:{string:nk},weight:{string:nk,number:rk},__type__:{object:hk}},id:{string:nk,number:rk},image:{selected:{string:nk,undefined:"undefined"},unselected:{string:nk,undefined:"undefined"},__type__:{object:hk,string:nk}},imagePadding:{top:{number:rk},right:{number:rk},bottom:{number:rk},left:{number:rk},__type__:{object:hk,number:rk}},label:{string:nk,undefined:"undefined"},labelHighlightBold:{boolean:sk},level:{number:rk,undefined:"undefined"},margin:{top:{number:rk},right:{number:rk},bottom:{number:rk},left:{number:rk},__type__:{object:hk,number:rk}},mass:{number:rk},physics:{boolean:sk},scaling:{min:{number:rk},max:{number:rk},label:{enabled:{boolean:sk},min:{number:rk},max:{number:rk},maxVisible:{number:rk},drawThreshold:{number:rk},__type__:{object:hk,boolean:sk}},customScalingFunction:{function:"function"},__type__:{object:hk}},shadow:{enabled:{boolean:sk},color:{string:nk},size:{number:rk},x:{number:rk},y:{number:rk},__type__:{object:hk,boolean:sk}},shape:{string:["custom","ellipse","circle","database","box","text","image","circularImage","diamond","dot","star","triangle","triangleDown","square","icon","hexagon"]},ctxRenderer:{function:"function"},shapeProperties:{borderDashes:{boolean:sk,array:ak},borderRadius:{number:rk},interpolation:{boolean:sk},useImageSize:{boolean:sk},useBorderWithImage:{boolean:sk},coordinateOrigin:{string:["center","top-left"]},__type__:{object:hk}},size:{number:rk},title:{string:nk,dom:"dom",undefined:"undefined"},value:{number:rk,undefined:"undefined"},widthConstraint:{minimum:{number:rk},maximum:{number:rk},__type__:{object:hk,boolean:sk,number:rk}},x:{number:rk},y:{number:rk},__type__:{object:hk}},ck={configure:{enabled:{boolean:sk},filter:{boolean:sk,string:nk,array:ak,function:"function"},container:{dom:"dom"},showButton:{boolean:sk},__type__:{object:hk,boolean:sk,string:nk,array:ak,function:"function"}},edges:{arrows:{to:{enabled:{boolean:sk},scaleFactor:{number:rk},type:{string:dk},imageHeight:{number:rk},imageWidth:{number:rk},src:{string:nk},__type__:{object:hk,boolean:sk}},middle:{enabled:{boolean:sk},scaleFactor:{number:rk},type:{string:dk},imageWidth:{number:rk},imageHeight:{number:rk},src:{string:nk},__type__:{object:hk,boolean:sk}},from:{enabled:{boolean:sk},scaleFactor:{number:rk},type:{string:dk},imageWidth:{number:rk},imageHeight:{number:rk},src:{string:nk},__type__:{object:hk,boolean:sk}},__type__:{string:["from","to","middle"],object:hk}},endPointOffset:{from:{number:rk},to:{number:rk},__type__:{object:hk,number:rk}},arrowStrikethrough:{boolean:sk},background:{enabled:{boolean:sk},color:{string:nk},size:{number:rk},dashes:{boolean:sk,array:ak},__type__:{object:hk,boolean:sk}},chosen:{label:{boolean:sk,function:"function"},edge:{boolean:sk,function:"function"},__type__:{object:hk,boolean:sk}},color:{color:{string:nk},highlight:{string:nk},hover:{string:nk},inherit:{string:["from","to","both"],boolean:sk},opacity:{number:rk},__type__:{object:hk,string:nk}},dashes:{boolean:sk,array:ak},font:{color:{string:nk},size:{number:rk},face:{string:nk},background:{string:nk},strokeWidth:{number:rk},strokeColor:{string:nk},align:{string:["horizontal","top","middle","bottom"]},vadjust:{number:rk},multi:{boolean:sk,string:nk},bold:{color:{string:nk},size:{number:rk},face:{string:nk},mod:{string:nk},vadjust:{number:rk},__type__:{object:hk,string:nk}},boldital:{color:{string:nk},size:{number:rk},face:{string:nk},mod:{string:nk},vadjust:{number:rk},__type__:{object:hk,string:nk}},ital:{color:{string:nk},size:{number:rk},face:{string:nk},mod:{string:nk},vadjust:{number:rk},__type__:{object:hk,string:nk}},mono:{color:{string:nk},size:{number:rk},face:{string:nk},mod:{string:nk},vadjust:{number:rk},__type__:{object:hk,string:nk}},__type__:{object:hk,string:nk}},hidden:{boolean:sk},hoverWidth:{function:"function",number:rk},label:{string:nk,undefined:"undefined"},labelHighlightBold:{boolean:sk},length:{number:rk,undefined:"undefined"},physics:{boolean:sk},scaling:{min:{number:rk},max:{number:rk},label:{enabled:{boolean:sk},min:{number:rk},max:{number:rk},maxVisible:{number:rk},drawThreshold:{number:rk},__type__:{object:hk,boolean:sk}},customScalingFunction:{function:"function"},__type__:{object:hk}},selectionWidth:{function:"function",number:rk},selfReferenceSize:{number:rk},selfReference:{size:{number:rk},angle:{number:rk},renderBehindTheNode:{boolean:sk},__type__:{object:hk}},shadow:{enabled:{boolean:sk},color:{string:nk},size:{number:rk},x:{number:rk},y:{number:rk},__type__:{object:hk,boolean:sk}},smooth:{enabled:{boolean:sk},type:{string:["dynamic","continuous","discrete","diagonalCross","straightCross","horizontal","vertical","curvedCW","curvedCCW","cubicBezier"]},roundness:{number:rk},forceDirection:{string:["horizontal","vertical","none"],boolean:sk},__type__:{object:hk,boolean:sk}},title:{string:nk,undefined:"undefined"},width:{number:rk},widthConstraint:{maximum:{number:rk},__type__:{object:hk,boolean:sk,number:rk}},value:{number:rk,undefined:"undefined"},__type__:{object:hk}},groups:{useDefaultGroups:{boolean:sk},__any__:lk,__type__:{object:hk}},interaction:{dragNodes:{boolean:sk},dragView:{boolean:sk},hideEdgesOnDrag:{boolean:sk},hideEdgesOnZoom:{boolean:sk},hideNodesOnDrag:{boolean:sk},hover:{boolean:sk},keyboard:{enabled:{boolean:sk},speed:{x:{number:rk},y:{number:rk},zoom:{number:rk},__type__:{object:hk}},bindToWindow:{boolean:sk},autoFocus:{boolean:sk},__type__:{object:hk,boolean:sk}},multiselect:{boolean:sk},navigationButtons:{boolean:sk},selectable:{boolean:sk},selectConnectedEdges:{boolean:sk},hoverConnectedEdges:{boolean:sk},tooltipDelay:{number:rk},zoomView:{boolean:sk},zoomSpeed:{number:rk},__type__:{object:hk}},layout:{randomSeed:{undefined:"undefined",number:rk,string:nk},improvedLayout:{boolean:sk},clusterThreshold:{number:rk},hierarchical:{enabled:{boolean:sk},levelSeparation:{number:rk},nodeSpacing:{number:rk},treeSpacing:{number:rk},blockShifting:{boolean:sk},edgeMinimization:{boolean:sk},parentCentralization:{boolean:sk},direction:{string:["UD","DU","LR","RL"]},sortMethod:{string:["hubsize","directed"]},shakeTowards:{string:["leaves","roots"]},__type__:{object:hk,boolean:sk}},__type__:{object:hk}},manipulation:{enabled:{boolean:sk},initiallyActive:{boolean:sk},addNode:{boolean:sk,function:"function"},addEdge:{boolean:sk,function:"function"},editNode:{function:"function"},editEdge:{editWithoutDrag:{function:"function"},__type__:{object:hk,boolean:sk,function:"function"}},deleteNode:{boolean:sk,function:"function"},deleteEdge:{boolean:sk,function:"function"},controlNodeStyle:lk,__type__:{object:hk,boolean:sk}},nodes:lk,physics:{enabled:{boolean:sk},barnesHut:{theta:{number:rk},gravitationalConstant:{number:rk},centralGravity:{number:rk},springLength:{number:rk},springConstant:{number:rk},damping:{number:rk},avoidOverlap:{number:rk},__type__:{object:hk}},forceAtlas2Based:{theta:{number:rk},gravitationalConstant:{number:rk},centralGravity:{number:rk},springLength:{number:rk},springConstant:{number:rk},damping:{number:rk},avoidOverlap:{number:rk},__type__:{object:hk}},repulsion:{centralGravity:{number:rk},springLength:{number:rk},springConstant:{number:rk},nodeDistance:{number:rk},damping:{number:rk},__type__:{object:hk}},hierarchicalRepulsion:{centralGravity:{number:rk},springLength:{number:rk},springConstant:{number:rk},nodeDistance:{number:rk},damping:{number:rk},avoidOverlap:{number:rk},__type__:{object:hk}},maxVelocity:{number:rk},minVelocity:{number:rk},solver:{string:["barnesHut","repulsion","hierarchicalRepulsion","forceAtlas2Based"]},stabilization:{enabled:{boolean:sk},iterations:{number:rk},updateInterval:{number:rk},onlyDynamicEdges:{boolean:sk},fit:{boolean:sk},__type__:{object:hk,boolean:sk}},timestep:{number:rk},adaptiveTimestep:{boolean:sk},wind:{x:{number:rk},y:{number:rk},__type__:{object:hk}},__type__:{object:hk,boolean:sk}},autoResize:{boolean:sk},clickToUse:{boolean:sk},locale:{string:nk},locales:{__any__:{any:"any"},__type__:{object:hk}},height:{string:nk},width:{string:nk},__type__:{object:hk}},uk={nodes:{borderWidth:[1,0,10,1],borderWidthSelected:[2,0,10,1],color:{border:["color","#2B7CE9"],background:["color","#97C2FC"],highlight:{border:["color","#2B7CE9"],background:["color","#D2E5FF"]},hover:{border:["color","#2B7CE9"],background:["color","#D2E5FF"]}},opacity:[0,0,1,.1],fixed:{x:!1,y:!1},font:{color:["color","#343434"],size:[14,0,100,1],face:["arial","verdana","tahoma"],background:["color","none"],strokeWidth:[0,0,50,1],strokeColor:["color","#ffffff"]},hidden:!1,labelHighlightBold:!0,physics:!0,scaling:{min:[10,0,200,1],max:[30,0,200,1],label:{enabled:!1,min:[14,0,200,1],max:[30,0,200,1],maxVisible:[30,0,200,1],drawThreshold:[5,0,20,1]}},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:[10,0,20,1],x:[5,-30,30,1],y:[5,-30,30,1]},shape:["ellipse","box","circle","database","diamond","dot","square","star","text","triangle","triangleDown","hexagon"],shapeProperties:{borderDashes:!1,borderRadius:[6,0,20,1],interpolation:!0,useImageSize:!1},size:[25,0,200,1]},edges:{arrows:{to:{enabled:!1,scaleFactor:[1,0,3,.05],type:"arrow"},middle:{enabled:!1,scaleFactor:[1,0,3,.05],type:"arrow"},from:{enabled:!1,scaleFactor:[1,0,3,.05],type:"arrow"}},endPointOffset:{from:[0,-10,10,1],to:[0,-10,10,1]},arrowStrikethrough:!0,color:{color:["color","#848484"],highlight:["color","#848484"],hover:["color","#848484"],inherit:["from","to","both",!0,!1],opacity:[1,0,1,.05]},dashes:!1,font:{color:["color","#343434"],size:[14,0,100,1],face:["arial","verdana","tahoma"],background:["color","none"],strokeWidth:[2,0,50,1],strokeColor:["color","#ffffff"],align:["horizontal","top","middle","bottom"]},hidden:!1,hoverWidth:[1.5,0,5,.1],labelHighlightBold:!0,physics:!0,scaling:{min:[1,0,100,1],max:[15,0,100,1],label:{enabled:!0,min:[14,0,200,1],max:[30,0,200,1],maxVisible:[30,0,200,1],drawThreshold:[5,0,20,1]}},selectionWidth:[1.5,0,5,.1],selfReferenceSize:[20,0,200,1],selfReference:{size:[20,0,200,1],angle:[Math.PI/2,-6*Math.PI,6*Math.PI,Math.PI/8],renderBehindTheNode:!0},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:[10,0,20,1],x:[5,-30,30,1],y:[5,-30,30,1]},smooth:{enabled:!0,type:["dynamic","continuous","discrete","diagonalCross","straightCross","horizontal","vertical","curvedCW","curvedCCW","cubicBezier"],forceDirection:["horizontal","vertical","none"],roundness:[.5,0,1,.05]},width:[1,0,30,1]},layout:{hierarchical:{enabled:!1,levelSeparation:[150,20,500,5],nodeSpacing:[100,20,500,5],treeSpacing:[200,20,500,5],blockShifting:!0,edgeMinimization:!0,parentCentralization:!0,direction:["UD","DU","LR","RL"],sortMethod:["hubsize","directed"],shakeTowards:["leaves","roots"]}},interaction:{dragNodes:!0,dragView:!0,hideEdgesOnDrag:!1,hideEdgesOnZoom:!1,hideNodesOnDrag:!1,hover:!1,keyboard:{enabled:!1,speed:{x:[10,0,40,1],y:[10,0,40,1],zoom:[.02,0,.1,.005]},bindToWindow:!0,autoFocus:!0},multiselect:!1,navigationButtons:!1,selectable:!0,selectConnectedEdges:!0,hoverConnectedEdges:!0,tooltipDelay:[300,0,1e3,25],zoomView:!0,zoomSpeed:[1,.1,2,.1]},manipulation:{enabled:!1,initiallyActive:!1},physics:{enabled:!0,barnesHut:{theta:[.5,.1,1,.05],gravitationalConstant:[-2e3,-3e4,0,50],centralGravity:[.3,0,10,.05],springLength:[95,0,500,5],springConstant:[.04,0,1.2,.005],damping:[.09,0,1,.01],avoidOverlap:[0,0,1,.01]},forceAtlas2Based:{theta:[.5,.1,1,.05],gravitationalConstant:[-50,-500,0,1],centralGravity:[.01,0,1,.005],springLength:[95,0,500,5],springConstant:[.08,0,1.2,.005],damping:[.4,0,1,.01],avoidOverlap:[0,0,1,.01]},repulsion:{centralGravity:[.2,0,10,.05],springLength:[200,0,500,5],springConstant:[.05,0,1.2,.005],nodeDistance:[100,0,500,5],damping:[.09,0,1,.01]},hierarchicalRepulsion:{centralGravity:[.2,0,10,.05],springLength:[100,0,500,5],springConstant:[.01,0,1.2,.005],nodeDistance:[120,0,500,5],damping:[.09,0,1,.01],avoidOverlap:[0,0,1,.01]},maxVelocity:[50,0,150,1],minVelocity:[.1,.01,.5,.01],solver:["barnesHut","forceAtlas2Based","repulsion","hierarchicalRepulsion"],timestep:[.5,.01,1,.01],wind:{x:[0,-10,10,.1],y:[0,-10,10,.1]}}},pk=(t,e,i)=>{var o;return!(!rh(t).call(t,"physics")||!rh(o=uk.physics.solver).call(o,e)||i.physics.solver===e||"wind"===e)};var fk=Object.freeze({__proto__:null,allOptions:ck,configuratorHideOption:pk,configureOptions:uk});class gk{constructor(){}getDistances(t,e,i){const o={},n=t.edges;for(let t=0;t<e.length;t++){const i={};o[e[t]]=i;for(let o=0;o<e.length;o++)i[e[o]]=t==o?0:1e9}for(let t=0;t<i.length;t++){const e=n[i[t]];!0===e.connected&&void 0!==o[e.fromId]&&void 0!==o[e.toId]&&(o[e.fromId][e.toId]=1,o[e.toId][e.fromId]=1)}const s=e.length;for(let t=0;t<s;t++){const i=e[t],n=o[i];for(let t=0;t<s-1;t++){const r=e[t],a=o[r];for(let h=t+1;h<s;h++){const t=e[h],s=o[t],d=Math.min(a[t],a[i]+n[t]);a[t]=d,s[r]=d}}}return o}}class mk{constructor(t,e,i){this.body=t,this.springLength=e,this.springConstant=i,this.distanceSolver=new gk}setOptions(t){t&&(t.springLength&&(this.springLength=t.springLength),t.springConstant&&(this.springConstant=t.springConstant))}solve(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const o=this.distanceSolver.getDistances(this.body,t,e);this._createL_matrix(o),this._createK_matrix(o),this._createE_matrix();let n=0;const s=Math.max(1e3,Math.min(10*this.body.nodeIndices.length,6e3));let r=1e9,a=0,h=0,d=0,l=0,c=0;for(;r>.01&&n<s;)for(n+=1,[a,r,h,d]=this._getHighestEnergyNode(i),l=r,c=0;l>1&&c<5;)c+=1,this._moveNode(a,h,d),[l,h,d]=this._getEnergy(a)}_getHighestEnergyNode(t){const e=this.body.nodeIndices,i=this.body.nodes;let o=0,n=e[0],s=0,r=0;for(let a=0;a<e.length;a++){const h=e[a];if(!0!==i[h].predefinedPosition||!0===i[h].isCluster&&!0===t||!0!==i[h].options.fixed.x||!0!==i[h].options.fixed.y){const[t,e,i]=this._getEnergy(h);o<t&&(o=t,n=h,s=e,r=i)}}return[n,o,s,r]}_getEnergy(t){const[e,i]=this.E_sums[t];return[Math.sqrt(e**2+i**2),e,i]}_moveNode(t,e,i){const o=this.body.nodeIndices,n=this.body.nodes;let s=0,r=0,a=0;const h=n[t].x,d=n[t].y,l=this.K_matrix[t],c=this.L_matrix[t];for(let e=0;e<o.length;e++){const i=o[e];if(i!==t){const t=n[i].x,e=n[i].y,o=l[i],u=c[i],p=1/((h-t)**2+(d-e)**2)**1.5;s+=o*(1-u*(d-e)**2*p),r+=o*(u*(h-t)*(d-e)*p),a+=o*(1-u*(h-t)**2*p)}}const u=(e/s+i/r)/(r/s-a/r),p=-(r*u+e)/s;n[t].x+=p,n[t].y+=u,this._updateE_matrix(t)}_createL_matrix(t){const e=this.body.nodeIndices,i=this.springLength;this.L_matrix=[];for(let o=0;o<e.length;o++){this.L_matrix[e[o]]={};for(let n=0;n<e.length;n++)this.L_matrix[e[o]][e[n]]=i*t[e[o]][e[n]]}}_createK_matrix(t){const e=this.body.nodeIndices,i=this.springConstant;this.K_matrix=[];for(let o=0;o<e.length;o++){this.K_matrix[e[o]]={};for(let n=0;n<e.length;n++)this.K_matrix[e[o]][e[n]]=i*t[e[o]][e[n]]**-2}}_createE_matrix(){const t=this.body.nodeIndices,e=this.body.nodes;this.E_matrix={},this.E_sums={};for(let e=0;e<t.length;e++)this.E_matrix[t[e]]=[];for(let i=0;i<t.length;i++){const o=t[i],n=e[o].x,s=e[o].y;let r=0,a=0;for(let h=i;h<t.length;h++){const d=t[h];if(d!==o){const t=e[d].x,l=e[d].y,c=1/Math.sqrt((n-t)**2+(s-l)**2);this.E_matrix[o][h]=[this.K_matrix[o][d]*(n-t-this.L_matrix[o][d]*(n-t)*c),this.K_matrix[o][d]*(s-l-this.L_matrix[o][d]*(s-l)*c)],this.E_matrix[d][i]=this.E_matrix[o][h],r+=this.E_matrix[o][h][0],a+=this.E_matrix[o][h][1]}}this.E_sums[o]=[r,a]}}_updateE_matrix(t){const e=this.body.nodeIndices,i=this.body.nodes,o=this.E_matrix[t],n=this.K_matrix[t],s=this.L_matrix[t],r=i[t].x,a=i[t].y;let h=0,d=0;for(let l=0;l<e.length;l++){const c=e[l];if(c!==t){const t=o[l],e=t[0],u=t[1],p=i[c].x,f=i[c].y,g=1/Math.sqrt((r-p)**2+(a-f)**2),m=n[c]*(r-p-s[c]*(r-p)*g),v=n[c]*(a-f-s[c]*(a-f)*g);o[l]=[m,v],h+=m,d+=v;const y=this.E_sums[c];y[0]+=m-e,y[1]+=v-u}}this.E_sums[t]=[h,d]}}function vk(t,e,i){var o,n,s,r;if(!(this instanceof vk))throw new SyntaxError("Constructor must be called with the new operator");this.options={},this.defaultOptions={locale:"en",locales:Jd,clickToUse:!1},Ki(this.options,this.defaultOptions),this.body={container:t,nodes:{},nodeIndices:[],edges:{},edgeIndices:[],emitter:{on:oo(o=this.on).call(o,this),off:oo(n=this.off).call(n,this),emit:oo(s=this.emit).call(s,this),once:oo(r=this.once).call(r,this)},eventListeners:{onTap:function(){},onTouch:function(){},onDoubleTap:function(){},onHold:function(){},onDragStart:function(){},onDrag:function(){},onDragEnd:function(){},onMouseWheel:function(){},onPinch:function(){},onMouseMove:function(){},onRelease:function(){},onContext:function(){}},data:{nodes:null,edges:null},functions:{createNode:function(){},createEdge:function(){},getPointer:function(){}},modules:{},view:{scale:1,translation:{x:0,y:0}},selectionBox:{show:!1,position:{start:{x:0,y:0},end:{x:0,y:0}}}},this.bindEventListeners(),this.images=new ql(()=>this.body.emitter.emit("_requestRedraw")),this.groups=new ju,this.canvas=new Ex(this.body),this.selectionHandler=new TO(this.body,this.canvas),this.interactionHandler=new Tx(this.body,this.canvas,this.selectionHandler),this.view=new Ox(this.body,this.canvas),this.renderer=new yx(this.body,this.canvas),this.physics=new Z_(this.body),this.layoutEngine=new ik(this.body),this.clustering=new vx(this.body),this.manipulation=new ok(this.body,this.canvas,this.selectionHandler,this.interactionHandler),this.nodesHandler=new kw(this.body,this.images,this.groups,this.layoutEngine),this.edgesHandler=new M_(this.body,this.images,this.groups),this.body.modules.kamadaKawai=new mk(this.body,150,.05),this.body.modules.clustering=this.clustering,this.canvas._create(),this.setOptions(i),this.setData(e)}function yk(t){for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(t[e].redundant=t[e].used,t[e].used=[])}function bk(t){for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)&&t[e].redundant){for(let i=0;i<t[e].redundant.length;i++)t[e].redundant[i].parentNode.removeChild(t[e].redundant[i]);t[e].redundant=[]}}function wk(t,e,i){let o;return Object.prototype.hasOwnProperty.call(e,t)?e[t].redundant.length>0?(o=e[t].redundant[0],e[t].redundant.shift()):(o=document.createElementNS("http://www.w3.org/2000/svg",t),i.appendChild(o)):(o=document.createElementNS("http://www.w3.org/2000/svg",t),e[t]={used:[],redundant:[]},i.appendChild(o)),e[t].used.push(o),o}go(vk.prototype),vk.prototype.setOptions=function(t){if(null===t&&(t=void 0),void 0!==t){!0===sr.validate(t,ck)&&console.error("%cErrors have been found in the supplied options object.",nr);if(_s(["locale","locales","clickToUse"],this.options,t),void 0!==t.locale&&(t.locale=function(t,e){try{const[o,n]=e.split(/[-_ /]/,2),s=null!=o?o.toLowerCase():null,r=null!=n?n.toUpperCase():null;if(s&&r){const e=s+"-"+r;if(Object.prototype.hasOwnProperty.call(t,e))return e;var i;console.warn(Hl(i="Unknown variant ".concat(r," of language ")).call(i,s,"."))}if(s){const e=s;if(Object.prototype.hasOwnProperty.call(t,e))return e;console.warn("Unknown language ".concat(s))}return console.warn("Unknown locale ".concat(e,", falling back to English.")),"en"}catch(t){return console.error(t),console.warn("Unexpected error while normalizing locale ".concat(e,", falling back to English.")),"en"}}(t.locales||this.options.locales,t.locale)),t=this.layoutEngine.setOptions(t.layout,t),this.canvas.setOptions(t),this.groups.setOptions(t.groups),this.nodesHandler.setOptions(t.nodes),this.edgesHandler.setOptions(t.edges),this.physics.setOptions(t.physics),this.manipulation.setOptions(t.manipulation,t,this.options),this.interactionHandler.setOptions(t.interaction),this.renderer.setOptions(t.interaction),this.selectionHandler.setOptions(t.interaction),void 0!==t.groups&&this.body.emitter.emit("refreshNodes"),"configure"in t&&(this.configurator||(this.configurator=new er(this,this.body.container,uk,this.canvas.pixelRatio,pk)),this.configurator.setOptions(t.configure)),this.configurator&&!0===this.configurator.options.enabled){const t={nodes:{},edges:{},layout:{},interaction:{},manipulation:{},physics:{},global:{}};Es(t.nodes,this.nodesHandler.options),Es(t.edges,this.edgesHandler.options),Es(t.layout,this.layoutEngine.options),Es(t.interaction,this.selectionHandler.options),Es(t.interaction,this.renderer.options),Es(t.interaction,this.interactionHandler.options),Es(t.manipulation,this.manipulation.options),Es(t.physics,this.physics.options),Es(t.global,this.canvas.options),Es(t.global,this.options),this.configurator.setModuleOptions(t)}void 0!==t.clickToUse?!0===t.clickToUse?void 0===this.activator&&(this.activator=new Js(this.canvas.frame),this.activator.on("change",()=>{this.body.emitter.emit("activate")})):(void 0!==this.activator&&(this.activator.destroy(),delete this.activator),this.body.emitter.emit("activate")):this.body.emitter.emit("activate"),this.canvas.setSize(),this.body.emitter.emit("startSimulation")}},vk.prototype._updateVisibleIndices=function(){const t=this.body.nodes,e=this.body.edges;this.body.nodeIndices=[],this.body.edgeIndices=[];for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(this.clustering._isClusteredNode(e)||!1!==t[e].options.hidden||this.body.nodeIndices.push(t[e].id));for(const i in e)if(Object.prototype.hasOwnProperty.call(e,i)){const o=e[i],n=t[o.fromId],s=t[o.toId],r=void 0!==n&&void 0!==s;!this.clustering._isClusteredEdge(i)&&!1===o.options.hidden&&r&&!1===n.options.hidden&&!1===s.options.hidden&&this.body.edgeIndices.push(o.id)}},vk.prototype.bindEventListeners=function(){this.body.emitter.on("_dataChanged",()=>{this.edgesHandler._updateState(),this.body.emitter.emit("_dataUpdated")}),this.body.emitter.on("_dataUpdated",()=>{this.clustering._updateState(),this._updateVisibleIndices(),this._updateValueRange(this.body.nodes),this._updateValueRange(this.body.edges),this.body.emitter.emit("startSimulation"),this.body.emitter.emit("_requestRedraw")})},vk.prototype.setData=function(t){if(this.body.emitter.emit("resetPhysics"),this.body.emitter.emit("_resetData"),this.selectionHandler.unselectAll(),t&&t.dot&&(t.nodes||t.edges))throw new SyntaxError('Data must contain either parameter "dot" or  parameter pair "nodes" and "edges", but not both.');if(this.setOptions(t&&t.options),t&&t.dot){console.warn("The dot property has been deprecated. Please use the static convertDot method to convert DOT into vis.network format and use the normal data format with nodes and edges. This converter is used like this: var data = vis.network.convertDot(dotString);");const e=kd(t.dot);return void this.setData(e)}if(t&&t.gephi){console.warn("The gephi property has been deprecated. Please use the static convertGephi method to convert gephi into vis.network format and use the normal data format with nodes and edges. This converter is used like this: var data = vis.network.convertGephi(gephiJson);");const e=Wd(t.gephi);return void this.setData(e)}this.nodesHandler.setData(t&&t.nodes,!0),this.edgesHandler.setData(t&&t.edges,!0),this.body.emitter.emit("_dataChanged"),this.body.emitter.emit("_dataLoaded"),this.body.emitter.emit("initPhysics")},vk.prototype.destroy=function(){this.body.emitter.emit("destroy"),this.body.emitter.off(),this.off(),delete this.groups,delete this.canvas,delete this.selectionHandler,delete this.interactionHandler,delete this.view,delete this.renderer,delete this.physics,delete this.layoutEngine,delete this.clustering,delete this.manipulation,delete this.nodesHandler,delete this.edgesHandler,delete this.configurator,delete this.images;for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&delete this.body.nodes[t];for(const t in this.body.edges)Object.prototype.hasOwnProperty.call(this.body.edges,t)&&delete this.body.edges[t];gs(this.body.container)},vk.prototype._updateValueRange=function(t){let e,i,o,n=0;for(e in t)if(Object.prototype.hasOwnProperty.call(t,e)){const s=t[e].getValue();void 0!==s&&(i=void 0===i?s:Math.min(s,i),o=void 0===o?s:Math.max(s,o),n+=s)}if(void 0!==i&&void 0!==o)for(e in t)Object.prototype.hasOwnProperty.call(t,e)&&t[e].setValueRange(i,o,n)},vk.prototype.isActive=function(){return!this.activator||this.activator.active},vk.prototype.setSize=function(){return this.canvas.setSize.apply(this.canvas,arguments)},vk.prototype.canvasToDOM=function(){return this.canvas.canvasToDOM.apply(this.canvas,arguments)},vk.prototype.DOMtoCanvas=function(){return this.canvas.DOMtoCanvas.apply(this.canvas,arguments)},vk.prototype.findNode=function(){return this.clustering.findNode.apply(this.clustering,arguments)},vk.prototype.isCluster=function(){return this.clustering.isCluster.apply(this.clustering,arguments)},vk.prototype.openCluster=function(){return this.clustering.openCluster.apply(this.clustering,arguments)},vk.prototype.cluster=function(){return this.clustering.cluster.apply(this.clustering,arguments)},vk.prototype.getNodesInCluster=function(){return this.clustering.getNodesInCluster.apply(this.clustering,arguments)},vk.prototype.clusterByConnection=function(){return this.clustering.clusterByConnection.apply(this.clustering,arguments)},vk.prototype.clusterByHubsize=function(){return this.clustering.clusterByHubsize.apply(this.clustering,arguments)},vk.prototype.updateClusteredNode=function(){return this.clustering.updateClusteredNode.apply(this.clustering,arguments)},vk.prototype.getClusteredEdges=function(){return this.clustering.getClusteredEdges.apply(this.clustering,arguments)},vk.prototype.getBaseEdge=function(){return this.clustering.getBaseEdge.apply(this.clustering,arguments)},vk.prototype.getBaseEdges=function(){return this.clustering.getBaseEdges.apply(this.clustering,arguments)},vk.prototype.updateEdge=function(){return this.clustering.updateEdge.apply(this.clustering,arguments)},vk.prototype.clusterOutliers=function(){return this.clustering.clusterOutliers.apply(this.clustering,arguments)},vk.prototype.getSeed=function(){return this.layoutEngine.getSeed.apply(this.layoutEngine,arguments)},vk.prototype.enableEditMode=function(){return this.manipulation.enableEditMode.apply(this.manipulation,arguments)},vk.prototype.disableEditMode=function(){return this.manipulation.disableEditMode.apply(this.manipulation,arguments)},vk.prototype.addNodeMode=function(){return this.manipulation.addNodeMode.apply(this.manipulation,arguments)},vk.prototype.editNode=function(){return this.manipulation.editNode.apply(this.manipulation,arguments)},vk.prototype.editNodeMode=function(){return console.warn("Deprecated: Please use editNode instead of editNodeMode."),this.manipulation.editNode.apply(this.manipulation,arguments)},vk.prototype.addEdgeMode=function(){return this.manipulation.addEdgeMode.apply(this.manipulation,arguments)},vk.prototype.editEdgeMode=function(){return this.manipulation.editEdgeMode.apply(this.manipulation,arguments)},vk.prototype.deleteSelected=function(){return this.manipulation.deleteSelected.apply(this.manipulation,arguments)},vk.prototype.getPositions=function(){return this.nodesHandler.getPositions.apply(this.nodesHandler,arguments)},vk.prototype.getPosition=function(){return this.nodesHandler.getPosition.apply(this.nodesHandler,arguments)},vk.prototype.storePositions=function(){return this.nodesHandler.storePositions.apply(this.nodesHandler,arguments)},vk.prototype.moveNode=function(){return this.nodesHandler.moveNode.apply(this.nodesHandler,arguments)},vk.prototype.getBoundingBox=function(){return this.nodesHandler.getBoundingBox.apply(this.nodesHandler,arguments)},vk.prototype.getConnectedNodes=function(t){return void 0!==this.body.nodes[t]?this.nodesHandler.getConnectedNodes.apply(this.nodesHandler,arguments):this.edgesHandler.getConnectedNodes.apply(this.edgesHandler,arguments)},vk.prototype.getConnectedEdges=function(){return this.nodesHandler.getConnectedEdges.apply(this.nodesHandler,arguments)},vk.prototype.startSimulation=function(){return this.physics.startSimulation.apply(this.physics,arguments)},vk.prototype.stopSimulation=function(){return this.physics.stopSimulation.apply(this.physics,arguments)},vk.prototype.stabilize=function(){return this.physics.stabilize.apply(this.physics,arguments)},vk.prototype.getSelection=function(){return this.selectionHandler.getSelection.apply(this.selectionHandler,arguments)},vk.prototype.setSelection=function(){return this.selectionHandler.setSelection.apply(this.selectionHandler,arguments)},vk.prototype.getSelectedNodes=function(){return this.selectionHandler.getSelectedNodeIds.apply(this.selectionHandler,arguments)},vk.prototype.getSelectedEdges=function(){return this.selectionHandler.getSelectedEdgeIds.apply(this.selectionHandler,arguments)},vk.prototype.getNodeAt=function(){const t=this.selectionHandler.getNodeAt.apply(this.selectionHandler,arguments);return void 0!==t&&void 0!==t.id?t.id:t},vk.prototype.getEdgeAt=function(){const t=this.selectionHandler.getEdgeAt.apply(this.selectionHandler,arguments);return void 0!==t&&void 0!==t.id?t.id:t},vk.prototype.selectNodes=function(){return this.selectionHandler.selectNodes.apply(this.selectionHandler,arguments)},vk.prototype.selectEdges=function(){return this.selectionHandler.selectEdges.apply(this.selectionHandler,arguments)},vk.prototype.unselectAll=function(){this.selectionHandler.unselectAll.apply(this.selectionHandler,arguments),this.selectionHandler.commitWithoutEmitting.apply(this.selectionHandler),this.redraw()},vk.prototype.redraw=function(){return this.renderer.redraw.apply(this.renderer,arguments)},vk.prototype.getScale=function(){return this.view.getScale.apply(this.view,arguments)},vk.prototype.getViewPosition=function(){return this.view.getViewPosition.apply(this.view,arguments)},vk.prototype.fit=function(){return this.view.fit.apply(this.view,arguments)},vk.prototype.moveTo=function(){return this.view.moveTo.apply(this.view,arguments)},vk.prototype.focus=function(){return this.view.focus.apply(this.view,arguments)},vk.prototype.releaseNode=function(){return this.view.releaseNode.apply(this.view,arguments)},vk.prototype.getOptionsFromConfigurator=function(){let t={};return this.configurator&&(t=this.configurator.getOptions.apply(this.configurator)),t};var _k=Object.freeze({__proto__:null,cleanupElements:bk,drawBar:function(t,e,i,o,n,s,r,a){if(0!=o){o<0&&(e-=o*=-1);const h=wk("rect",s,r);h.setAttributeNS(null,"x",t-.5*i),h.setAttributeNS(null,"y",e),h.setAttributeNS(null,"width",i),h.setAttributeNS(null,"height",o),h.setAttributeNS(null,"class",n),a&&h.setAttributeNS(null,"style",a)}},drawPoint:function(t,e,i,o,n,s){let r;if("circle"==i.style?(r=wk("circle",o,n),r.setAttributeNS(null,"cx",t),r.setAttributeNS(null,"cy",e),r.setAttributeNS(null,"r",.5*i.size)):(r=wk("rect",o,n),r.setAttributeNS(null,"x",t-.5*i.size),r.setAttributeNS(null,"y",e-.5*i.size),r.setAttributeNS(null,"width",i.size),r.setAttributeNS(null,"height",i.size)),void 0!==i.styles&&r.setAttributeNS(null,"style",i.styles),r.setAttributeNS(null,"class",i.className+" vis-point"),s){const i=wk("text",o,n);s.xOffset&&(t+=s.xOffset),s.yOffset&&(e+=s.yOffset),s.content&&(i.textContent=s.content),s.className&&i.setAttributeNS(null,"class",s.className+" vis-label"),i.setAttributeNS(null,"x",t),i.setAttributeNS(null,"y",e)}return r},getDOMElement:function(t,e,i,o){let n;return Object.prototype.hasOwnProperty.call(e,t)?e[t].redundant.length>0?(n=e[t].redundant[0],e[t].redundant.shift()):(n=document.createElement(t),void 0!==o?i.insertBefore(n,o):i.appendChild(n)):(n=document.createElement(t),e[t]={used:[],redundant:[]},void 0!==o?i.insertBefore(n,o):i.appendChild(n)),e[t].used.push(n),n},getSVGElement:wk,prepareElements:yk,resetElements:function(t){yk(t),bk(t),yk(t)}});const xk={Images:ql,dotparser:Fd,gephiParser:qd,allOptions:fk,convertDot:kd,convertGephi:Wd};var Ek=Object.freeze({__proto__:null,DOMutil:_k,DataSet:pf,DataView:ff,Hammer:ir,Network:vk,Queue:lf,data:Sf,keycharm:kx,network:xk,util:wr});t.DOMutil=_k,t.DataSet=pf,t.DataView=ff,t.Hammer=ir,t.Network=vk,t.Queue=lf,t.data=Sf,t.default=Ek,t.keycharm=kx,t.network=xk,t.util=wr,Object.defineProperty(t,"__esModule",{value:!0})});
//# sourceMappingURL=vis-network.min.js.map
