# Markmap迁移方案

## 一、迁移背景与目标

### 1.1 为什么要迁移到Markmap

**当前D3.js自定义渲染器的局限性**：
- 布局算法简单，缺乏智能排布
- 缺乏成熟的交互功能（展开/折叠、平滑动画等）
- 性能优化不足，大文档渲染慢
- 维护成本高，需要自己实现所有功能

**Markmap的优势**：
- 专业的思维导图渲染引擎
- 丰富的交互功能和动画效果
- 优秀的性能表现
- 活跃的社区支持和持续更新
- 与Markdown天然兼容

### 1.2 迁移目标

1. **功能对等**：保持现有功能不变
2. **体验提升**：获得更好的视觉效果和交互体验
3. **性能优化**：提升大文档的渲染性能
4. **扩展性**：为未来功能扩展奠定基础

## 二、技术方案设计

### 2.1 依赖管理

**新增依赖**：
```json
{
  "dependencies": {
    "markmap-lib": "^0.15.0",
    "markmap-view": "^0.15.0",
    "markmap-toolbar": "^0.15.0"
  }
}
```

**安装命令**：
```bash
npm install markmap-lib markmap-view markmap-toolbar
```

### 2.2 架构设计

```mermaid
graph TD
    A[Markdown文档] --> B[MarkdownParser]
    B --> C[标准Markdown格式]
    C --> D[Markmap Transformer]
    D --> E[Markmap数据结构]
    E --> F[Markmap渲染器]
    F --> G[SVG思维导图]
    
    H[用户交互] --> F
    F --> I[事件回调]
    I --> J[同步到Markdown]
```

### 2.3 新的渲染器实现

**文件位置**：`src/core/MarkmapRenderer.ts`

```typescript
import { Markmap } from 'markmap-view';
import { Transformer } from 'markmap-lib';
import { Toolbar } from 'markmap-toolbar';
import { MindMapAPI, NodeData } from '../types';
import { Logger } from '../utils';

export class MarkmapRenderer implements MindMapAPI {
  private markmap: Markmap | null = null;
  private transformer: Transformer;
  private toolbar: Toolbar | null = null;
  private container: HTMLElement;
  private logger: Logger;
  private onNodeChangeCallback?: (nodeId: string, changes: any) => void;
  private currentMarkdown = '';

  constructor(container: HTMLElement, logger: Logger) {
    this.container = container;
    this.logger = logger;
    this.transformer = new Transformer();
    this.initializeMarkmap();
  }

  private initializeMarkmap(): void {
    // 清空容器
    this.container.innerHTML = '';
    
    // 创建工具栏容器
    const toolbarContainer = document.createElement('div');
    toolbarContainer.className = 'markmap-toolbar-container';
    toolbarContainer.style.position = 'absolute';
    toolbarContainer.style.top = '10px';
    toolbarContainer.style.right = '10px';
    toolbarContainer.style.zIndex = '1000';
    this.container.appendChild(toolbarContainer);

    // 创建SVG容器
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.style.width = '100%';
    svg.style.height = '100%';
    svg.style.background = 'var(--background-primary)';
    this.container.appendChild(svg);

    // 初始化Markmap
    this.markmap = Markmap.create(svg, {
      // 基础配置
      autoFit: true,
      pan: true,
      zoom: true,
      
      // 样式配置
      color: this.getColorScheme(),
      colorFreezeLevel: 2,
      duration: 500,
      maxWidth: 300,
      spacingVertical: 10,
      spacingHorizontal: 80,
      
      // 字体配置
      fontFamily: 'var(--font-text)',
      fontSize: '14px',
      
      // 交互配置
      fitRatio: 0.95,
      paddingX: 8,
      paddingY: 8
    });

    // 初始化工具栏
    this.toolbar = new Toolbar();
    this.toolbar.attach(this.markmap);
    this.toolbar.setItems(['zoomIn', 'zoomOut', 'fit']);
    toolbarContainer.appendChild(this.toolbar.render());

    // 设置事件监听
    this.setupEventListeners();
  }

  private getColorScheme(): string[] {
    // 根据Obsidian主题动态生成颜色方案
    const isDark = document.body.classList.contains('theme-dark');
    
    if (isDark) {
      return [
        '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b',
        '#ef4444', '#ec4899', '#6366f1', '#84cc16'
      ];
    } else {
      return [
        '#7c3aed', '#0891b2', '#059669', '#d97706',
        '#dc2626', '#db2777', '#4f46e5', '#65a30d'
      ];
    }
  }

  private setupEventListeners(): void {
    if (!this.markmap) return;

    // 监听节点点击事件
    this.markmap.on('click', (node: any) => {
      this.logger.debug('节点被点击', node);
      this.handleNodeClick(node);
    });

    // 监听节点展开/折叠事件
    this.markmap.on('toggle', (node: any) => {
      this.logger.debug('节点展开/折叠', node);
      this.handleNodeToggle(node);
    });
  }

  render(markdown: string): void {
    if (!this.markmap) {
      this.logger.error('Markmap未初始化');
      return;
    }

    try {
      this.currentMarkdown = markdown;
      
      // 转换Markdown为Markmap数据
      const { root, features } = this.transformer.transform(markdown);
      
      // 应用自定义样式
      this.applyCustomStyles(root);
      
      // 渲染思维导图
      this.markmap.setData(root);
      this.markmap.fit();
      
      // 加载所需的资源
      if (features) {
        this.markmap.setOptions({ ...this.markmap.options, ...features });
      }
      
      this.logger.debug('Markmap渲染完成');
    } catch (error) {
      this.logger.error('Markmap渲染失败', error);
      this.renderErrorState(error);
    }
  }

  private applyCustomStyles(root: any): void {
    // 递归应用自定义样式
    const applyStyles = (node: any, depth: number = 0) => {
      if (!node) return;
      
      // 根据节点类型和深度设置样式
      if (node.payload) {
        // 标题节点样式
        if (depth === 0) {
          node.payload.fontSize = '18px';
          node.payload.fontWeight = 'bold';
        } else if (depth === 1) {
          node.payload.fontSize = '16px';
          node.payload.fontWeight = '600';
        } else {
          node.payload.fontSize = '14px';
          node.payload.fontWeight = 'normal';
        }
        
        // AI生成节点特殊标识
        if (node.content && node.content.includes('🤖')) {
          node.payload.color = 'var(--color-green)';
          node.payload.backgroundColor = 'rgba(34, 197, 94, 0.1)';
        }
      }
      
      // 递归处理子节点
      if (node.children) {
        node.children.forEach((child: any) => applyStyles(child, depth + 1));
      }
    };
    
    applyStyles(root);
  }

  private handleNodeClick(node: any): void {
    // 高亮选中的节点
    this.highlightNode(node);
    
    // 触发回调
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(node.id || '', { action: 'click', node });
    }
  }

  private handleNodeToggle(node: any): void {
    // 记录展开/折叠状态
    this.logger.debug(`节点${node.payload?.fold ? '折叠' : '展开'}: ${node.content}`);
    
    // 触发回调
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(node.id || '', { 
        action: 'toggle', 
        folded: node.payload?.fold,
        node 
      });
    }
  }

  private highlightNode(node: any): void {
    // 移除之前的高亮
    this.markmap?.svg.selectAll('.markmap-node').classed('highlighted', false);
    
    // 添加新的高亮
    if (node.state?.el) {
      node.state.el.classed('highlighted', true);
    }
  }

  private renderErrorState(error: any): void {
    this.container.innerHTML = `
      <div class="markmap-error">
        <div class="error-icon">⚠️</div>
        <div class="error-message">思维导图渲染失败</div>
        <div class="error-detail">${error.message || '未知错误'}</div>
      </div>
    `;
  }

  // 实现MindMapAPI接口
  updateNode(nodeId: string, data: Partial<NodeData>): void {
    this.logger.debug('更新节点', { nodeId, data });
    // Markmap不支持直接更新单个节点，需要重新渲染
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'update', data });
    }
  }

  addNode(parentId: string, data: NodeData): string {
    this.logger.debug('添加节点', { parentId, data });
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(data.id, { action: 'add', parentId, data });
    }
    return data.id;
  }

  deleteNode(nodeId: string): void {
    this.logger.debug('删除节点', { nodeId });
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'delete' });
    }
  }

  moveNode(nodeId: string, targetParentId: string): void {
    this.logger.debug('移动节点', { nodeId, targetParentId });
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'move', targetParentId });
    }
  }

  getNodeByPosition(pos: { line: number; ch: number }): NodeData | null {
    // Markmap不直接支持位置映射，需要通过其他方式实现
    return null;
  }

  getPositionByNode(nodeId: string): { line: number; ch: number } | null {
    // Markmap不直接支持位置映射，需要通过其他方式实现
    return null;
  }

  setNodeChangeCallback(callback: (nodeId: string, changes: any) => void): void {
    this.onNodeChangeCallback = callback;
  }

  fit(): void {
    this.markmap?.fit();
  }

  destroy(): void {
    if (this.toolbar) {
      this.toolbar.detach();
      this.toolbar = null;
    }
    
    if (this.markmap) {
      this.markmap.destroy();
      this.markmap = null;
    }
    
    this.container.innerHTML = '';
  }
}
```

## 三、迁移步骤

### 3.1 第一阶段：准备工作（1天）

1. **安装依赖**
```bash
cd obsidain开发\.obsidian\plugins\ob-sample
npm install markmap-lib markmap-view markmap-toolbar
```

2. **创建新的渲染器文件**
- 创建 `src/core/MarkmapRenderer.ts`
- 实现基础的Markmap集成

3. **更新类型定义**
- 确保接口兼容性
- 添加Markmap相关类型

### 3.2 第二阶段：功能迁移（2-3天）

1. **替换渲染器**
- 在 `MindMapView.ts` 中切换到新的渲染器
- 保留原渲染器作为备份

2. **功能测试**
- 基础渲染功能
- 交互功能
- 样式效果

3. **性能优化**
- 大文档测试
- 内存使用优化

### 3.3 第三阶段：完善与优化（2-3天）

1. **样式定制**
- 适配Obsidian主题
- 自定义节点样式

2. **功能增强**
- 工具栏集成
- 快捷键支持

3. **错误处理**
- 异常情况处理
- 用户反馈优化

## 四、风险控制

### 4.1 回退方案

保留原有的D3.js渲染器，通过配置开关控制：

```typescript
// 在设置中添加渲染器选择
export interface MindMapSyncSettings {
  // ... 其他设置
  renderEngine: 'd3' | 'markmap';
}

// 在MindMapView中动态选择渲染器
private createRenderer(): MindMapRenderer {
  if (this.plugin.settings.renderEngine === 'markmap') {
    return new MarkmapRenderer(this.container, this.logger);
  } else {
    return new D3MindMapRenderer(this.container, this.logger);
  }
}
```

### 4.2 兼容性测试

1. **不同Obsidian版本测试**
2. **不同操作系统测试**
3. **不同主题测试**
4. **大文档性能测试**

## 五、预期收益

### 5.1 用户体验提升

- **更流畅的动画效果**：节点展开/折叠动画
- **更好的交互体验**：拖拽、缩放、平移
- **更清晰的视觉效果**：专业的布局算法

### 5.2 开发效率提升

- **减少维护成本**：使用成熟的库
- **更快的功能迭代**：基于Markmap的丰富功能
- **更好的扩展性**：为未来功能奠定基础

### 5.3 性能优化

- **更快的渲染速度**：优化的算法
- **更低的内存占用**：高效的数据结构
- **更好的大文档支持**：虚拟化渲染

这个迁移方案提供了完整的实施路径，确保平滑过渡到Markmap渲染引擎。
